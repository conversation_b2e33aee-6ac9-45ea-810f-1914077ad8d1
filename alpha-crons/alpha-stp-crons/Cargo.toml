[package]
name = "alpha-stp-crons"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { workspace = true }
dotenv = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

alpha-core-db = { path = "../../alpha-core-db" }
alpha-utils = { path = "../../alpha-utils" }
alpha-oms ={ path = "../../alpha-oms" }

actlogica_logs = { workspace = true }
