pub mod installments;
pub mod redemptions;

use alpha_core_db::schema::stp::exchange_holidays::NSEHolidays;
use chrono::{Datelike, NaiveDateTime, Weekday};

// Checks the given date is a Market Holiday
pub async fn is_holiday(date: NaiveDateTime) -> Result<bool, String> {
    let holidays = NSEHolidays::get_nse_holidays_list().await?;
    Ok(holidays.contains(&date.date()))
}

// Returns the next Market working day
pub async fn find_next_working_day(date: NaiveDateTime) -> Result<NaiveDateTime, String> {
    let mut current_date = date;

    while is_holiday(current_date).await?
        || current_date.weekday() == Weekday::Sun
        || current_date.weekday() == Weekday::Sat
    {
        current_date = match current_date.weekday() {
            Weekday::Sun => current_date + chrono::Duration::days(1),
            Weekday::Sat => current_date + chrono::Duration::days(2),
            _ => current_date + chrono::Duration::days(1),
        };
    }

    Ok(current_date)
}
