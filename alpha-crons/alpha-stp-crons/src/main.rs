use actlogica_logs::{
    builder::LogBuilder,
    log_error, log_info,
    setting::{init_logger, LogOutput},
};
use alpha_stp_crons::stp::{installments, redemptions};
use serde_json::json;
use tracing_subscriber::filter::LevelFilter;
#[tokio::main]
async fn main() {
    println!("Starting STP crons ---");
    dotenv::dotenv().ok();
    if let Err(err) = init_logger("STP-Cron-Service", LevelFilter::INFO, LogOutput::StdOut).await {
        log_error(LogBuilder::system("Failed to initialize logger").add_metadata("error", &err.to_string()));
    }

    // Temporary fix: to see Logs in Cronicle
    // let subscriber = tracing_subscriber::FmtSubscriber::builder()
    //     .with_level(true)
    //     .with_writer(std::io::stdout)
    //     .finish();
    // // set tracing subscriber globally
    // tracing::subscriber::set_global_default(subscriber)
    //     .map_err(|err| format!("Failed to set global subscriber: {}", err))
    //     .unwrap();

    log_info(LogBuilder::system("Logger service initialized in STP-Cron-Service"));

    // add cron-jobs here
    redemptions::trigger_redemption_for_today().await; // BUY
    installments::trigger_installments_for_today().await; // SELL

    println!("Finished running crons");
    // *NOTE: This completion signal required by Cronicle.net to mark the task as successfully finished
    println!("{}", json!({ "complete": 1, "output": "cron-task" }));
}
