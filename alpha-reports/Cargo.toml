[package]
name = "alpha-reports"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = {workspace = true}
alpha-core-db ={path="../alpha-core-db" }
alpha-utils ={path="../alpha-utils" }
tracing = {workspace = true}
tracing-subscriber = {workspace = true}
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.122"
csv = "1.3.0"
futures = "0.3.30"
futures-util = "0.3.30"
lapin = "2.5.0"
futures-lite = "2.3.0"
native-tls = "0.2.12"
dotenv = "0.15.0"
zip = "2.2.2"
anyhow = {workspace = true}
chrono ={workspace = true}
actlogica_logs = {workspace = true}