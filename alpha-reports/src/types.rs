use alpha_core_db::connection::pool::tiberius::time::chrono::{NaiveDate, NaiveDateTime};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug)]
#[serde(tag = "messageType")]
pub enum ReportMessage {
    Xml(ReportMessagMeta),
    OffSiteReports(OffSiteReportMeta),
    SebiMonthly(ReportMessagMeta)
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all="camelCase")]
pub struct OffSiteReportMeta {
    pub message: OffSiteReportMessage,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all="camelCase")]
pub struct OffSiteReportMessage {
    pub id: String,
    pub from_date: NaiveDateTime,
    pub to_date: NaiveDateTime,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all="camelCase")]
pub struct ReportMessagMeta {
    pub message: XmlReportMessage,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all="camelCase")]
pub struct XmlReportMessage {
    pub id: String,
    pub from_date: NaiveDateTime,
    pub to_date: NaiveDateTime,
}
