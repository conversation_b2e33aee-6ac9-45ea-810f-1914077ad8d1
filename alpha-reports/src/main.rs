use actlogica_logs::{
    builder::{create_log_span, create_module_span, LogBuilder},
    log_error, log_info, log_warn,
    setting::{init_logger, LogOutput},
};
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, tiberius::xml, Manager, Pool},
    schema::{holding_recon::HoldingReconRequest, report_requests::ReportRequest},
};
use alpha_utils::{
    holding_recon::compute_holding_recon,
    rabbit_mq::RabbitMq,
    reports::{
        self,
        blob::{upload_offsite_report_to_blob, upload_report_to_blob},
        xml::{client_cap_transactions::ReportOutputType, sebi_monthly},
    },
    settlement::process_settlement,
    types::MessageContent,
};
use anyhow::anyhow;
use anyhow::Context;
use chrono::NaiveDate;
use futures::lock::Mutex;
use futures_lite::StreamExt;
use lapin::options::BasicAckOptions;
use serde_json::Value;
use std::{
    error::Error,
    future::Future,
    io::{Cursor, Write},
    pin::Pin,
    sync::Arc,
};
use tracing::{Instrument, Span};
use tracing_subscriber::filter::LevelFilter;
use types::ReportMessage;
use zip::{write::FileOptions, ZipWriter};
mod types;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();

    let queue_name = std::env::var("QUEUE_NAME").expect("QUEUE NAME NOT FOUND");

    // Initialize logging framework...
    if let Err(err) = actlogica_logs::setting::init_logger("Alpha-Reports", LevelFilter::INFO, LogOutput::StdOut).await {
        log_error(
            LogBuilder::system("Failed to initialize logger in ALPHA-REPORT").add_metadata("error", &err.to_string()),
        );
    };
    log_info(LogBuilder::system("Logger service initialized in ALPHA-REPORT"));

    let pool = alpha_core_db::connection::connect_to_mssql(2).await;
    let _master_data_pool = alpha_core_db::connection::connect_to_master_data(2).await;
    let _redis_pool = alpha_core_db::redis::connect_to_redis().await;

    let connection = RabbitMq::connect_rabbit_mq().await;

    let mut queue_consumer = connection.create_consumer(&format!("{queue_name}/reports")).await;
    let pool_for_reports = pool.clone();

    /* --------- Create parent tracing span for logs with module name ---------- */
    let span = create_log_span("Messaging-Queue-Consumer");
    let _enter = span.enter();

    while let Some(delivery) = queue_consumer.next().await {
        let pool_for_reports_clone = pool_for_reports.clone();
        tokio::spawn(
            async move {
                let delivery = delivery
                    .map_err(|err| {
                        log_error(
                            LogBuilder::system("Failed to process queue delivery msg")
                                .add_metadata("error", &err.to_string()),
                        )
                    })
                    .expect("error in consumer");
                let value: Value = match serde_json::from_slice(&delivery.data) {
                    Ok(value) => value,
                    Err(err) => {
                        log_error(
                            LogBuilder::system("Failed: deserialize queue message. Not a valid JSON")
                                .add_metadata("error", &err.to_string()),
                        );
                        return;
                    }
                };

                let message: ReportMessage = match serde_json::from_value(value) {
                    Ok(msg) => msg,
                    Err(err) => {
                        log_error(
                            LogBuilder::system("Failed: parse json to ReportMessage")
                                .add_metadata("error", &err.to_string()),
                        );
                        return;
                    }
                };

                log_info(LogBuilder::system(&format!(
                    "Received message from Queue = {:?} ",
                    &message
                )));
                match message {
                    ReportMessage::Xml(xml) => {
                        /* --------- Create child tracing span for logs with new Module name ---------- */
                        let span = create_module_span("XML-Reports"); // Update the module name only not event_id
                        let _enter = span.enter();

                        let id = xml.message.id;

                        // Acknowledge the message
                        if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                            log_error(
                                LogBuilder::system("Failed: Acknowledge Queue-msg, in XML-Reports")
                                    .add_metadata("error", &err.to_string()),
                            );
                        };

                        //Get Connection From Pool
                        let mut pool_conn = match pool_for_reports_clone.get().await {
                            Ok(conn) => conn,
                            Err(err) => {
                                log_error(
                                    LogBuilder::system("Failed: MSSQL Connection from pool, in XML-Reports")
                                        .add_metadata("error", &err.to_string()),
                                );
                                return;
                            }
                        };

                        log_error(
                            LogBuilder::system(&format!("Searching for Report Request in DB, with RequestID: {}", &id))
                                .add_metadata("id", &id),
                        );
                        let report_request = ReportRequest::get_by_id(&mut pool_conn, &id).await;

                        match report_request {
                            Err(report_request_err) => {
                                log_error(
                                    LogBuilder::system("Failed: Get Report Request from DB")
                                        .add_metadata("error", &report_request_err.to_string()),
                                );
                                return;
                            }
                            Ok(None) => {
                                log_error(LogBuilder::system("Report Request not found in DB").add_metadata("id", &id));
                                return;
                            }
                            Ok(Some(report_request)) => {
                                log_info(
                                    LogBuilder::system(&format!("Report Request found in DB. RequestId: {}", &id))
                                        .add_metadata("id", &id),
                                );
                                report_request; // TODO: handle the response
                            }
                        }
                    }
                    ReportMessage::OffSiteReports(offsite) => {
                        /* --------- Create child tracing span for logs with new Module name ---------- */
                        let span = create_module_span("Offsite-Reports"); // Update the module name only not event_id
                        let _enter = span.enter();

                        let id = offsite.message.id;

                        // Acknowledge the message first to prevent redelivery
                        if let Err(ack_err) = delivery.ack(BasicAckOptions::default()).await {
                            log_error(
                                LogBuilder::system("Failed: Acknowledge Queue-msg, in Offsite-Reports")
                                    .add_metadata("error", &ack_err.to_string()),
                            );
                            return;
                        }

                        // Get database connection
                        let mut pool_conn = match pool_for_reports_clone.get().await {
                            Ok(conn) => conn,
                            Err(e) => {
                                log_error(
                                    LogBuilder::system("Failed: MSSQL Connection from pool, in Offsite-Reports")
                                        .add_metadata("error", &e.to_string()),
                                );
                                return;
                            }
                        };

                        log_info(
                            LogBuilder::system(&format!("Generating Offsite Report with ID: {}", &id))
                                .add_metadata("id", &id)
                                .add_metadata("from_date", &offsite.message.from_date.to_string())
                                .add_metadata("to_date", &offsite.message.to_date.to_string()),
                        );
                        let report_generator = ReportGenerator::new(&mut pool_conn, id.clone());
                        let buffer = match report_generator
                            .generate_report(offsite.message.from_date.date(), offsite.message.to_date.date())
                            .await
                        {
                            Ok(buffer) => {
                                log_info(
                                    LogBuilder::system("Success: Offsite Report Generated").add_metadata("id", &id),
                                );
                                buffer
                            }
                            Err(err) => {
                                log_error(
                                    LogBuilder::system("Failed: Generate Offsite Report")
                                        .add_metadata("error", &err.to_string()),
                                );
                                log_warn(LogBuilder::system("Updating error to Report-Request"));
                                let _res = ReportRequest::update_error(&mut pool_conn, &id, &err.to_string()).await;
                                return;
                            }
                        };

                        // Upload to blob storage
                        let path = match upload_offsite_report_to_blob(&id, buffer).await {
                            Ok(path) => {
                                log_info(
                                    LogBuilder::system("Successfully uploaded report to blob storage")
                                        .add_metadata("path", &path),
                                );
                                path
                            }
                            Err(e) => {
                                let err_msg = format!("Failed to upload report to blob storage: {}", e);
                                log_error(
                                    LogBuilder::system("Failed: Upload report to blob storage")
                                        .add_metadata("error", &e),
                                );
                                let _res = ReportRequest::update_error(&mut pool_conn, &id, &err_msg).await;
                                return;
                            }
                        };

                        // Update report path in database
                        if let Err(e) = ReportRequest::update_report_path(&mut pool_conn, &id, &path).await {
                            let err_msg = format!("Failed to update report path in database: {}", e);
                            log_error(
                                LogBuilder::system("Failed: Update report path in database")
                                    .add_metadata("error", &err_msg),
                            );
                            let _res = ReportRequest::update_error(&mut pool_conn, &id, &err_msg).await;
                            return;
                        }
                    }
                    ReportMessage::SebiMonthly(report) => {
                        /* --------- Create child tracing span for logs with new Module name ---------- */
                        let span = create_module_span("SEBI-Monthly-Report"); // Update the module name only not event_id
                        let _enter = span.enter();

                        // Acknowledge the message
                        let id = report.message.id;
                        let from_date = report.message.from_date;
                        let to_date = report.message.to_date;

                        if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                            log_error(
                                LogBuilder::system("Failed: Acknowledge Queue-msg, in SEBI-Monthly-Report")
                                    .add_metadata("error", &err.to_string()),
                            );
                        };

                        // Get database connection
                        let mut pool_conn = match pool_for_reports_clone.get().await {
                            Ok(conn) => conn,
                            Err(e) => {
                                log_error(
                                    LogBuilder::system("Failed: MSSQL Connection from pool, in SEBI-Monthly-Report")
                                        .add_metadata("error", &e.to_string()),
                                );
                                return;
                            }
                        };

                        log_info(
                            LogBuilder::system(&format!("Generating SEBI Monthly Report, with id: {}", id))
                                .add_metadata("id", &id)
                                .add_metadata("from_date", &from_date.to_string())
                                .add_metadata("to_date", &to_date.to_string()),
                        );
                        if let Ok(sebi_monthly) =
                            sebi_monthly::generate_sebi_monthly(&mut pool_conn, from_date.into(), to_date.into()).await
                        {
                            // Upload to blob storage
                            log_info(
                                LogBuilder::system("Generated SEBI Monthly Report. Uploading to blob storage...")
                                    .add_metadata("id", &id),
                            );

                            let path = match upload_report_to_blob(&id, sebi_monthly.into(), "sebi_monthly").await {
                                Ok(path) => {
                                    log_info(
                                        LogBuilder::system("Successfully uploaded report to blob storage")
                                            .add_metadata("path", &path)
                                            .add_metadata("id", &id),
                                    );
                                    path
                                }
                                Err(e) => {
                                    let err_msg = format!("Failed to upload report to blob storage: {}", e);
                                    log_error(
                                        LogBuilder::system("Failed: Upload report to blob storage")
                                            .add_metadata("error", &e),
                                    );
                                    log_warn(LogBuilder::system("Updating error to Report-Request"));
                                    let _res = ReportRequest::update_error(&mut pool_conn, &id, &err_msg).await;
                                    return;
                                }
                            };

                            // Update report path in database
                            if let Err(e) = ReportRequest::update_report_path(&mut pool_conn, &id, &path).await {
                                let err_msg = format!("Failed to update report path in database: {}", e);
                                log_error(
                                    LogBuilder::system("Failed: Update report path in database")
                                        .add_metadata("error", &err_msg),
                                );
                                log_warn(LogBuilder::system("Updating error to Report-Request"));
                                let _res = ReportRequest::update_error(&mut pool_conn, &id, &err_msg).await;
                                return;
                            }
                        };
                    }
                }
            }
            .instrument(Span::current()),
        );
    }
}

#[allow(unused)]
struct ReportGenerator<'a> {
    pool_conn: &'a mut Object<Manager>,
    zip: ZipWriter<Cursor<Vec<u8>>>,
    id: String,
    options: FileOptions<'static, ()>,
}

impl<'a> ReportGenerator<'a> {
    fn new(pool_conn: &'a mut Object<Manager>, id: String) -> Self {
        let buffer = Cursor::new(Vec::new());
        let zip = ZipWriter::new(buffer);
        let options = FileOptions::default()
            .compression_method(zip::CompressionMethod::Deflated)
            .unix_permissions(0o755);

        Self {
            pool_conn,
            zip,
            id,
            options,
        }
    }

    async fn add_xml_file<F>(&mut self, filename: &str, generator: F) -> anyhow::Result<()>
    where
        F: FnOnce(&mut Object<Manager>) -> Pin<Box<dyn Future<Output = anyhow::Result<String>> + Send + '_>>,
    {
        self.zip
            .start_file(filename, self.options)
            .with_context(|| format!("Failed to create {} in ZIP", filename))?;

        let xml_content = generator(self.pool_conn)
            .await
            .with_context(|| format!("Failed to generate XML for {}", filename))?;

        self.zip
            .write_all(xml_content.as_bytes())
            .with_context(|| format!("Failed to write {} to ZIP", filename))?;

        Ok(())
    }

    async fn add_xml_file_with_dates<F>(
        &mut self,
        filename: &str,
        generator: F,
        from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> anyhow::Result<()>
    where
        F: FnOnce(
            &mut Object<Manager>,
            NaiveDate,
            NaiveDate,
        ) -> Pin<Box<dyn Future<Output = anyhow::Result<String>> + Send + '_>>,
    {
        self.zip
            .start_file(filename, self.options)
            .with_context(|| format!("Failed to create {} in ZIP", filename))?;

        let xml_content = generator(self.pool_conn, from_date, to_date)
            .await
            .with_context(|| format!("Failed to generate XML for {}", filename))?;

        self.zip
            .write_all(xml_content.as_bytes())
            .with_context(|| format!("Failed to write {} to ZIP", filename))?;

        Ok(())
    }

    async fn generate_report(mut self, from_date: NaiveDate, to_date: NaiveDate) -> anyhow::Result<Vec<u8>> {
        // Define report configurations
        let reports_with_dates: Vec<(
            &str,
            fn(
                &mut Object<Manager>,
                NaiveDate,
                NaiveDate,
            ) -> Pin<Box<dyn Future<Output = anyhow::Result<String>> + Send + '_>>,
        )> = vec![
            ("client_cap_transactions.xml", |conn, from_date, to_date| {
                Box::pin(
                    reports::xml::client_cap_transactions::generate_xml_client_cap_transactions(
                        conn,
                        from_date,
                        to_date,
                        ReportOutputType::Xml,
                    ),
                )
            }),
            ("sebi_monthly.xml", |conn, from_date, to_date| {
                Box::pin(reports::xml::sebi_monthly::generate_sebi_monthly(
                    conn, from_date, to_date,
                ))
            }),
            ("client_expense_master.xml", |conn, from_date, to_date| {
                Box::pin(reports::xml::client_expense_master::generate_xml_client_expense_master(
                    conn, from_date, to_date,
                ))
            }),
            ("client_folio_master.xml", |conn, from_date, to_date| {
                Box::pin(reports::xml::client_folio_master::generate_xml_client_folio_master(
                    conn, from_date, to_date,
                ))
            }),
            ("client_holding.xml", |conn, from_date, to_date| {
                Box::pin(reports::xml::client_holding::generate_xml_client_holding(
                    conn, from_date, to_date,
                ))
            }),
            ("pms_inspection_details.xml", |conn, from_date, to_date| {
                Box::pin(
                    reports::xml::pms_inspection_details::generate_xml_pms_inspection_details(conn, from_date, to_date),
                )
            }),
        ];

        let reports_without_dates: Vec<(
            &str,
            fn(&mut Object<Manager>) -> Pin<Box<dyn Future<Output = anyhow::Result<String>> + Send + '_>>,
        )> = vec![
            ("pm_operating_expense.xml", |conn| {
                Box::pin(reports::xml::pm_operating_expense::generate_xml_pm_operating_expense(
                    conn,
                ))
            }),
            ("pm_pool_acc_master.xml", |conn| {
                Box::pin(reports::xml::pm_pool_acc_master::generate_xml_pm_pool_acc_master(conn))
            }),
            ("pm_master.xml", |conn| {
                Box::pin(reports::xml::pm_master::generate_xml_pm_master(conn))
            }),
        ];

        // Generate each report
        for (filename, generator) in reports_with_dates {
            self.add_xml_file_with_dates(filename, generator, from_date, to_date)
                .await?;
        }

        for (filename, generator) in reports_without_dates {
            self.add_xml_file(filename, generator).await?;
        }

        // Finish ZIP file
        let buffer = self
            .zip
            .finish()
            .map_err(|_e| anyhow::anyhow!("Failed to finish ZIP file"))?;

        Ok(buffer.into_inner())
    }
}

#[cfg(test)]
mod tests {
    use std::fs::File;

    use alpha_core_db::connection::pool::tiberius::time::chrono::NaiveDate;

    use super::*;

    #[tokio::test]
    async fn generate_all_report() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let id = "Hi".to_string();
        let report = ReportGenerator::new(&mut pool_conn, id);

        let from_date = NaiveDate::from_ymd_opt(2024, 09, 04).unwrap();
        let to_date = NaiveDate::from_ymd_opt(2024, 09, 04).unwrap();

        let bytes = report.generate_report(from_date, to_date).await.unwrap();
        let mut file = File::create("new.zip").unwrap();
        file.write_all(&bytes).unwrap();
    }
}
