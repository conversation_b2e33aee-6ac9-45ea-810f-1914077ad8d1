#!/bin/bash

# make sure mc is installed
if ! command -v mc &> /dev/null
then
    echo "mc could not be found, installing..."
    curl -L https://dl.min.io/client/mc/release/linux-amd64/mc -o /usr/local/bin/mc
    chmod +x /usr/local/bin/mc
    #add to path
    export PATH=$PATH:/usr/local/bin
fi

# Config
ALIAS="alpha"
ENDPOINT="http://minio.common.alphap.actlogica.com" # make sure the port is included
ACCESS_KEY="5WMt8XAbra9XQL2O43lT"
SECRET_KEY="4ghPpIsDAoBLmGWKwqSQx71ANSxJ6y4pYdyPS7yr"
BUCKET="alpha-binaries"
FILE_TO_UPLOAD="./target/release/alpha-stp-crons" # Configure the path, according to deployment env.
DOWNLOADED_FILE="alpha-stp-crons"

# Set alias (one-time setup)
mc alias set $ALIAS $ENDPOINT $ACCESS_KEY $SECRET_KEY

# Upload
mc cp $FILE_TO_UPLOAD $ALIAS/$BUCKET/
