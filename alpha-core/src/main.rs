use actlogica_logs::{
    builder::{create_log_span, create_module_span, LogBuilder},
    log_error, log_info,
    setting::{init_logger, LogOutput},
};
use alpha_core_db::schema::holding_recon::HoldingReconRequest;
use alpha_utils::{
    holding_recon::compute_holding_recon, rabbit_mq::RabbitMq, settlement::process_settlement, types::MessageContent,
};
use futures::lock::Mutex;
use futures_lite::StreamExt;
use lapin::options::BasicAckOptions;
use serde_json::Value;
use std::sync::Arc;
use tracing::{Instrument, Span};
use tracing_subscriber::filter::LevelFilter;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();

    let queue_name = std::env::var("QUEUE_NAME").expect("QUEUE NAME NOT FOUND");

    // Initialize logging framework...
    if let Err(err) = init_logger("Alpha-Core-Service", LevelFilter::INFO, LogOutput::Kafka).await {
        log_error(
            LogBuilder::system("Failed to initialize logger in ALPHA-CORE").add_metadata("error", &err.to_string()),
        );
    };
    log_info(LogBuilder::system("Logger service initialized in ALPHA-CORE"));

    let pool = alpha_core_db::connection::connect_to_mssql(10).await;
    let master_data_pool = alpha_core_db::connection::connect_to_master_data(5).await;
    let redis_pool = alpha_core_db::redis::connect_to_redis().await;

    let connection = RabbitMq::connect_rabbit_mq().await;

    let mut queue_consumer = connection.create_consumer(&queue_name).await;
    let pool_for_recon = pool.clone();

    // A Mutex to only allow single settlement at a time
    let settlement_lock = Arc::new(Mutex::new(()));

    /* --------- Create parent tracing span for logs with module name ---------- */
    let span = create_log_span("Messaging-Queue-Consumer");
    let _enter = span.enter();

    while let Some(delivery) = queue_consumer.next().await {
        let pool_for_recon_clone = pool_for_recon.clone();
        let master_data_pool_clone = master_data_pool.clone();
        let redis_pool = redis_pool.clone();
        let settlement_lock = settlement_lock.clone();
        tokio::spawn(
            async move {
                log_info(LogBuilder::system("Start: Processing new message from the queue"));

                let delivery = delivery
                    .map_err(|err| {
                        log_error(
                            LogBuilder::system("Failed to process queue delivery msg")
                                .add_metadata("error", &err.to_string()),
                        )
                    })
                    .expect("error in consumer");
                let value: Value = match serde_json::from_slice(&delivery.data) {
                    Ok(value) => value,
                    Err(err) => {
                        log_error(
                            LogBuilder::system("Failed: deserialize queue message. Not a valid JSON")
                                .add_metadata("error", &err.to_string()),
                        );
                        return;
                    }
                };

                let message: MessageContent = match serde_json::from_value(value) {
                    Ok(msg) => msg,
                    Err(err) => {
                        log_error(
                            LogBuilder::system("Failed: parse json to MessageContent")
                                .add_metadata("error", &err.to_string()),
                        );
                        return;
                    }
                };

                log_info(LogBuilder::system(&format!(
                    "Received message from Queue = {:?} ",
                    &message
                )));

                match message {
                    MessageContent::HoldingRecon(message) => {
                        /* --------- Create child tracing span for logs with new Module name ---------- */
                        let span = create_module_span("Holding-Recon"); // Update the module name only not event_id
                        let _enter = span.enter();

                        tokio::spawn(
                            async move {
                                let id = message.message.id;
                                // Acknowledge the message
                                if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                                    log_error(
                                        LogBuilder::system("Failed: Acknowledge Queue-msg, in HoldingRecon")
                                            .add_metadata("error", &err.to_string()),
                                    );
                                };

                                //Get Connection From Pool
                                let mut pool_conn = match pool_for_recon_clone.get().await {
                                    Err(err) => {
                                        log_error(
                                            LogBuilder::system("Failed: MSSQL Connection from pool, in HoldingRecon")
                                                .add_metadata("error", &err.to_string()),
                                        );
                                        return;
                                    }
                                    Ok(pool) => pool,
                                };

                                let recon_request =
                                    HoldingReconRequest::get_holding_recon_request_by_id(&mut pool_conn, id).await;

                                if let Err(recon_request) = &recon_request {
                                    log_error(
                                        LogBuilder::system("Failed: Holding-Recon request")
                                            .add_metadata("error", &recon_request.to_string()),
                                    );
                                    return;
                                }

                                let recon_request = recon_request.unwrap(); // safe

                                //Update The Status to Processing and Add Remarks
                                recon_request
                                    .update_recon_request_status_and_remark(
                                        &mut pool_conn,
                                        String::from("Processing"),
                                        String::from("Started Processing the File"),
                                    )
                                    .await;

                                //Compute the Recon and Get the File Path In storage Table
                                let res = compute_holding_recon(&mut pool_conn, recon_request.clone()).await;

                                // Update the Database based on the computed Result
                                match res {
                                    Ok(file_path) => {
                                        log_info(LogBuilder::system("Success: Holding-Recon Computed Successfully"));
                                        recon_request
                                            .update_recon_result_file_path(
                                                &mut pool_conn,
                                                file_path,
                                                String::from("Succesfully Computed the File"),
                                            )
                                            .await;
                                    }
                                    Err(err) => {
                                        log_error(
                                            LogBuilder::system("Failed: Holding-Recon Computation Failed")
                                                .add_metadata("error", &err),
                                        );

                                        recon_request
                                            .update_recon_request_status_and_remark(
                                                &mut pool_conn,
                                                String::from("Failed"),
                                                err,
                                            )
                                            .await;
                                    }
                                }
                            }
                            .instrument(Span::current()),
                        );
                    }
                    MessageContent::Settlement(message) => {
                        /* --------- Create chilld tracing span for logs with New Module name ---------- */
                        let span = create_module_span("Settlement"); // Update the module name only not event_id
                        let _enter = span.enter();

                        // Acknowledge the message
                        if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                            log_error(
                                LogBuilder::system("Failed: acknowledge Queue-msg, in Settlement")
                                    .add_metadata("error", &err.to_string()),
                            );
                        };

                        tokio::spawn(
                            async move {
                                log_info(LogBuilder::system(&format!("Message Received: {:?}", message)));

                                // Get the Lock first and DO the Settlement
                                // This makes sure that only one settlement is happening at a time
                                let _settlement_lock_guard = settlement_lock.lock().await;
                                process_settlement(
                                    pool_for_recon_clone,
                                    master_data_pool_clone,
                                    redis_pool,
                                    message.message.id,
                                    message.message.cancel_pending_orders,
                                    message.message.release_payout,
                                )
                                .await;
                            }
                            .instrument(Span::current()),
                        );
                    }
                }
            }
            .instrument(Span::current()),
        );
    }
}
