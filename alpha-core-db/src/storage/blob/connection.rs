use azure_storage::StorageCredentials;
use azure_storage_blobs::prelude::{BlobClient, ClientBuilder, ContainerClient};

pub struct AzureBlob {
    pub client: ContainerClient,
}

impl AzureBlob {
    pub fn new(container_name: String) -> Self {
        let account = std::env::var("STORAGE_ACCOUNT").expect("missing STORAGE_ACCOUNT");
        let access_key = std::env::var("STORAGE_ACCESS_KEY").expect("missing STORAGE_ACCOUNT_KEY");
        let storage_credentials = StorageCredentials::access_key(account.clone(), access_key);

        let client = ClientBuilder::new(account, storage_credentials).container_client(container_name);
        Self { client }
    }

    pub fn get_blob(&mut self, blob_name: &str) -> BlobClient {
        self.client.clone().blob_client(blob_name)
    }
}
