use super::AzureStorageTable;
use chrono::NaiveDateTime;
use futures::StreamExt;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Default, Debug)]
pub struct PortfolioAumDaily {
    // #[serde(rename = "InvestmentId")]
    // pub investment_id: String,
    #[serde(rename = "PortfolioId")]
    pub portfolio_id: Option<String>,
    // #[serde(rename = "ClientId")]
    // pub client_id: String,
    // #[serde(rename = "ModelPortfolioId")]
    // pub model_portfolio_id: Option<String>,
    #[serde(rename = "AsAtDate")]
    pub as_at_date: String,
    // #[serde(rename = "AggregateType")]
    // pub aggregate_type: String,
    // #[serde(rename = "Isin")]
    // pub isin: String,
    // #[serde(rename = "Symbol")]
    // pub symbol: String,
    // #[serde(rename = "MfRtaCode")]
    // pub mf_rta_code: String,
    // #[serde(rename = "Series")]
    // pub series: String,
    // #[serde(rename = "AmfiCode")]
    // pub amfi_code: String,
    // #[serde(rename = "Exchange")]
    // pub exchange: String,
    // #[serde(rename = "InvestmentName")]
    // pub investment_name: String,
    // #[serde(rename = "AmcName")]
    // pub amc_name: String,
    // #[serde(rename = "LogoImgUrl")]
    // pub logo_img_url: String,
    // #[serde(rename = "AssetClass")]
    // pub asset_class: String,
    // #[serde(rename = "AssetType")]
    // pub asset_type: String,
    // #[serde(rename = "FundClass")]
    // pub fund_class: String,
    // #[serde(rename = "CreditRating")]
    // pub credit_rating: String,
    // #[serde(rename = "IndexName")]
    // pub index_name: String,
    // #[serde(rename = "Industry")]
    // pub industry: String,
    // #[serde(rename = "MarketCap")]
    // pub market_cap: String,
    // #[serde(rename = "ClosePrice")]
    // pub close_price: f64,
    // #[serde(rename = "ClosePriceDate")]
    // pub close_price_date: String,
    // #[serde(rename = "ClosePriceChange")]
    // pub close_price_change: f64,
    // #[serde(rename = "AbsoluteReturnPercentage")]
    // pub absolute_return_percentage: f64,
    // #[serde(rename = "AbsoluteReturnValue")]
    // pub absolute_return_value: f64,
    // #[serde(rename = "AveragePurchasePrice")]
    // pub average_purchase_price: f64,
    // #[serde(rename = "AllUnits")]
    // pub all_units: f64,
    // #[serde(rename = "LongTermUnits")]
    // pub long_term_units: f64,
    // #[serde(rename = "ShortTermUnits")]
    // pub short_term_units: f64,
    // #[serde(rename = "BenchmarkReturn")]
    // pub benchmark_return: f64,
    // #[serde(rename = "Cagr")]
    // pub cagr: f64,
    // #[serde(rename = "TotalCapital")]
    // pub total_capital: f64,
    // #[serde(rename = "InvestedCapital")]
    // pub invested_capital: f64,
    // #[serde(rename = "Withdrawals")]
    // pub withdrawals: f64,
    #[serde(rename = "MarketValue")]
    pub market_value: f64,
    // #[serde(rename = "Weight")]
    // pub weight: f64,
    // #[serde(rename = "MarketValueChange")]
    // pub market_value_change: f64,
    // #[serde(rename = "MarketValueChangePercentage")]
    // pub market_value_change_percentage: f64,
    // #[serde(rename = "DividendPaid")]
    // pub dividend_paid: f64,
    // #[serde(rename = "DividendReinvested")]
    // pub dividend_reinvested: f64,
    // #[serde(rename = "RealisedGainLoss")]
    // pub realised_gain_loss: f64,
    // #[serde(rename = "UnRealisedGainLoss")]
    // pub unrealised_gain_loss: f64,
    // #[serde(rename = "Xirr")]
    // pub xirr: f64,
    // #[serde(rename = "Twrr")]
    // pub twrr: f64,
    // #[serde(rename = "FirstDateOfInvestment")]
    // pub first_date_of_investment: String,
    // #[serde(rename = "DateOfMaturity")]
    // pub date_of_maturity: String,
    // #[serde(rename = "GrowthAt10000")]
    // pub growth_at_10000: f64,
    // #[serde(rename = "GrowthAt10000Percentage")]
    // pub growth_at_10000_percentage: f64,
    // #[serde(rename = "UniqueKey")]
    // pub unique_key: String,
    // #[serde(rename = "ComparePrice")]
    // pub compare_price: f64,
    // #[serde(rename = "ComparePriceDate")]
    // pub compare_price_date: String,
    // #[serde(rename = "NumberOfInvestments")]
    // pub number_of_investments: i32,
}

impl PortfolioAumDaily {
    pub async fn get_holdings(
        from_date: NaiveDateTime,
        to_date: NaiveDateTime,
        portfolio_id: String,
    ) -> Vec<PortfolioAumDaily> {
        let mut analytics = Vec::new();
        let storage_table = AzureStorageTable::new();
        let client = storage_table.get_table_client(String::from("PortfolioAumDaily")).await;

        // Generate filter conditions
        let partition_key_filter = format!("PartitionKey eq '{}'", portfolio_id);
        let to_date_filter = format!("AsAtDate le datetime'{}'", to_date.format("%Y-%m-%dT%H:%M:%S"));
        let from_date_filter = format!("AsAtDate ge datetime'{}'", from_date.format("%Y-%m-%dT%H:%M:%S"));

        // Combine filters
        let combined_filter = format!(
            "{} and {} and {}",
            partition_key_filter, to_date_filter, from_date_filter
        );

        let res = client.query().filter(combined_filter);
        let mut streams = res.into_stream::<PortfolioAumDaily>();

        while let Some(resp) = streams.next().await {
            analytics.append(&mut resp.unwrap().entities);
        }

        analytics
    }
}
