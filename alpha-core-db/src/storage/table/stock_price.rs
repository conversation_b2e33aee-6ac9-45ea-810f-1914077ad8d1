use chrono::{DateTime, Duration, NaiveDate, Utc};
use futures::StreamExt;
use actlogica_logs::{builder::LogBuilder, log_warn};

use super::AzureStorageTable;
use serde::{Deserialize, Serialize};

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct StockPrice {
    #[serde(rename = "Symbol")]
    pub symbol: String,

    #[serde(rename = "Exchange")]
    pub exchange: String,

    #[serde(rename = "Price")]
    pub price: f64,

    #[serde(rename = "Timestamp")]
    pub timetamp: DateTime<Utc>,
}

pub async fn fetch_price(identifier: String, exchange: String) -> f64 {
    let storage_table = AzureStorageTable::finflo_storage();
    let client = storage_table.get_table_client(String::from("EquityHistory")).await;
    let mut retry_count = 0;
    let mut prices = Vec::new();

    let partition_key_filter = {
        let formatted_symbol = if exchange == "NSE" {
            format!("{}:{}.NS", exchange, identifier)
        } else {
            format!("{}:{}.BO", exchange, identifier)
        };

        format!("PartitionKey eq '{}'", formatted_symbol)
    };
    // Get the current date and time
    let mut now = Utc::now();

    while prices.len() == 0 && retry_count < 30 {
        let date = now.date_naive();

        let date_filter = format!("RowKey eq '{}'", date.format("%d%m%Y").to_string());

        let combined_filter = format!("{} and {} ", partition_key_filter, date_filter);

        let res = client.query().filter(combined_filter);
        let mut streams = res.into_stream::<StockPrice>();

        while let Some(resp) = streams.next().await {
            let p = resp.unwrap().entities;
            for i in p {
                prices.push(i);
            }
        }

        now = now - Duration::days(1);
        retry_count += 1;
    }

    if prices.len() > 0 {
        return prices[0].price;
    } else {
        log_warn(LogBuilder::system(&format!(
            "Failed to Find the Price for the Identifier : {:?}. Assigning price as zero",
            identifier
        )));
        return 0f64;
    }
}

pub async fn fetch_price_as_at(identifier: String, exchange: String, date: NaiveDate) -> f64 {
    let storage_table = AzureStorageTable::finflo_storage();
    let client = storage_table.get_table_client(String::from("EquityHistory")).await;
    let mut retry_count = 0;
    let mut prices = Vec::new();
    let partition_key_filter = {
        let formatted_symbol = if exchange == "NSE" {
            format!("{}:{}.NS", exchange, identifier)
        } else {
            format!("{}:{}.BO", exchange, identifier)
        };

        format!("PartitionKey eq '{}'", formatted_symbol)
    };
    // Get the current date and time
    let mut now = date;

    while prices.len() == 0 && retry_count < 30 {
        let date = now;

        let date_filter = format!("RowKey eq '{}'", date.format("%d%m%Y").to_string());

        let combined_filter = format!("{} and {} ", partition_key_filter, date_filter);
        println!("{:?}", combined_filter);
        let res = client.query().filter(combined_filter);
        let mut streams = res.into_stream::<StockPrice>();

        while let Some(resp) = streams.next().await {
            let p = resp.unwrap().entities;
            for i in p {
                prices.push(i);
            }
        }

        now = now - Duration::days(1);
        retry_count += 1;
    }

    if prices.len() > 0 {
        return prices[0].price;
    } else {
        log_warn(LogBuilder::system(&format!(
            "Failed to Find the Price for the Identifier : {:?}. Assigning price as zero",
            identifier
        )));
        return 0f64;
    }
}
