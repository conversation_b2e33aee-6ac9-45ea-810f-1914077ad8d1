use azure_data_tables::prelude::*;
use azure_storage::StorageCredentials;
pub mod historical_mutual_fund_nav;
pub mod portfolio_analytics;
pub mod portfolio_aum_daily;

pub struct AzureStorageTable {
    pub table_client: TableServiceClient,
}

impl AzureStorageTable {
    pub fn new() -> Self {
        let account = std::env::var("STORAGE_ACCOUNT").expect("missing STORAGE_ACCOUNT");
        let access_key = std::env::var("STORAGE_ACCESS_KEY").expect("missing STORAGE_ACCOUNT_KEY");
        let storage_credentials = StorageCredentials::access_key(account.clone(), access_key);
        let table_client = TableServiceClient::new(account, storage_credentials);

        Self { table_client }
    }

    pub fn finflo_storage() -> Self {
        let account = std::env::var("STORAGE_ACCOUNT_FINFLO").expect("missing STORAGE_ACCOUNT_FINFLO");
        let access_key = std::env::var("STORAGE_ACCESS_KEY_FINFLO").expect("missing STORAGE_ACCESS_KEY_FINFLO");
        let storage_credentials = StorageCredentials::access_key(account.clone(), access_key);
        let table_client = TableServiceClient::new(account, storage_credentials);

        Self { table_client }
    }

    pub async fn get_table_client(&self, table_name: String) -> TableClient {
        let client = self.table_client.table_client(table_name);
        client
    }
}

pub mod stock_price;
