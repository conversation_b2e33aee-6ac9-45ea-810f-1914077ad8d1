use chrono::{NaiveDate, NaiveDateTime, Utc};
use futures::StreamExt;
use serde::{Deserialize, Serialize};

use super::AzureStorageTable;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct HistoricalMutualFundNav {
    #[serde(rename = "PartitionKey")]
    pub partition_key: String, // amfiCode

    #[serde(rename = "RowKey")]
    pub row_key: String, // priceDate

    #[serde(rename = "AmcName")]
    pub amc_name: String,

    #[serde(rename = "Date")]
    date: String,
    #[serde(rename = "Isin")]
    isin: String,
    #[serde(rename = "Nav")]
    pub nav: String,
    #[serde(rename = "NavChange")]
    nav_change: String,
    #[serde(rename = "NavDate")]
    nav_date: NaiveDateTime,
    #[serde(rename = "NetChange")]
    net_change: String,
    #[serde(rename = "SchemeCode")]
    scheme_code: String,
    #[serde(rename = "SchemeName")]
    scheme_name: String,
    #[serde(rename = "RepurchasePrice")]
    repurchase_price: String,
    #[serde(rename = "SalePrice")]
    sale_price: String,
    #[serde(rename = "AmfiMutualFundId")]
    amfi_mutual_fund_id: String,
    #[serde(rename = "SchemeType")]
    scheme_type: String,
}

impl HistoricalMutualFundNav {
    pub async fn get_price_on_as_at_date(
        amfi_code: String,
        date: NaiveDate,
    ) -> Result<Option<HistoricalMutualFundNav>, String> {
        let mut nav = Vec::new();
        let storage_table = AzureStorageTable::new();
        let client = storage_table
            .get_table_client(String::from("PortfolioAnalyticsDaily"))
            .await;

        // Generate filter conditions
        let partition_key_filter = format!("PartitionKey eq '{}'", amfi_code);
        let as_at_date_filter = format!("AsAtDate ge datetime'{}'", date.format("%Y-%m-%dT%H:%M:%S"));

        // Combine filters
        let combined_filter = format!("{} and {}", partition_key_filter, as_at_date_filter);

        let res = client.query().filter(combined_filter);
        let mut streams = res.into_stream::<HistoricalMutualFundNav>();

        let mut _latest_nav: Option<HistoricalMutualFundNav> = None;
        let mut _latest_date: Option<chrono::DateTime<Utc>> = None;

        while let Some(resp) = streams.next().await {
            nav.append(&mut resp.unwrap().entities);
        }

        Ok(Some(nav[0].clone()))
    }
}
