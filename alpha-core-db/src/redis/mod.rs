pub mod schema;
use std::time::Duration;

use bb8::Pool;
use bb8_redis::RedisConnectionManager;

pub async fn connect_to_redis() -> Pool<RedisConnectionManager> {
    let redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
    let manager = RedisConnectionManager::new(redis_url).unwrap();
    let pool = bb8::Pool::builder()
        .connection_timeout(Duration::new(10, 0))
        .build(manager)
        .await
        .unwrap();

    pool
}
