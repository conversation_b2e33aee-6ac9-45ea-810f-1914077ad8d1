use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct StockData {
    #[serde(rename = "Id")]
    pub id: i32,
    #[serde(rename = "Fincode")]
    pub fin_code: String,
    #[serde(rename = "Scripcode")]
    pub scrip_code: Option<String>,
    #[serde(rename = "Scrip_Name")]
    pub scrip_name: String,
    #[serde(rename = "Scrip_Group")]
    pub scrip_group: Option<String>,
    #[serde(rename = "CompName")]
    pub company_name: String,
    #[serde(rename = "Ind_Code")]
    pub industry_code: Option<String>,
    #[serde(rename = "Hse_Code")]
    pub house_code: Option<String>,
    #[serde(rename = "Symbol")]
    pub symbol: Option<String>,
    #[serde(rename = "Series")]
    pub series: Option<String>,
    #[serde(rename = "Isin")]
    pub isin: String,
    #[serde(rename = "Status")]
    pub status: String,
    #[serde(rename = "Sublisting")]
    pub sublisting: Option<String>,
    #[serde(rename = "Industry")]
    pub industry: Option<String>,
    #[serde(rename = "Ind_shortname")]
    pub industry_shortname: Option<String>,
    #[serde(rename = "MarketCap")]
    pub market_cap: Option<String>,
    #[serde(rename = "Bse_sublisting")]
    pub bse_sublisting: Option<String>,
    #[serde(rename = "Nse_sublisting")]
    pub nse_sublisting: Option<String>,
    #[serde(rename = "BsePrice")]
    pub bse_price: f64,
    #[serde(rename = "NsePrice")]
    pub nse_price: f64,
    // #[serde(rename = "BsePriceDate")]
    // pub bse_price_date: DateTime<Utc>,
    // #[serde(rename = "NsePriceDate")]
    // pub nse_price_date: DateTime<Utc>,
}

impl StockData {
    /// If Symbol is there Return NSE Price
    /// If not return Bse Price
    pub fn get_latest_price(&self) -> f64 {
        if self.symbol.is_some() && self.nse_price > 0f64 {
            return self.nse_price;
        }

        self.bse_price
    }

    pub fn get_latest_price_by_exchange(&self, exchange: &str) -> f64 {
        if exchange.to_lowercase() == "nse" {
            self.nse_price
        } else {
            self.bse_price
        }
    }

    pub fn get_identifier(&self) -> String {
        if let Some(s) = &self.symbol {
            return s.clone();
        }
        return self.scrip_code.clone().unwrap();
    }

    pub fn get_identifier_by_exchange(&self, exchange: &str) -> String {
        if exchange.to_lowercase() == "nse" {
            self.symbol.clone().unwrap()
        } else {
            self.scrip_code.clone().unwrap()
        }
    }

    pub fn get_exchange(&self) -> String {
        if self.symbol.is_some() {
            return String::from("NSE");
        }

        return String::from("BSE");
    }

    pub fn set_price(&mut self, price: f64) {
        if self.symbol.is_some() {
            self.nse_price = price;
        }

        self.bse_price = price;
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct MutualFundData {
    #[serde(rename = "Id")]
    pub id: i32,
    #[serde(rename = "ISIN")]
    pub isin: String,
    #[serde(rename = "SchemeName")]
    pub scheme_name: String,
    #[serde(rename = "AmfiCode")]
    pub amfi_code: String,
    #[serde(rename = "PurchaseMode")]
    pub purchase_mode: String,
    #[serde(rename = "DistributionStatus")]
    pub distribution_status: String,
    #[serde(rename = "CategoryName")]
    pub category_name: String,
    #[serde(rename = "InvestmentPlan")]
    pub investment_plan: String,
    #[serde(rename = "FundName")]
    pub fund_name: String,
    #[serde(rename = "NetExpenseRatio")]
    pub net_expense_ratio: f64,
    #[serde(rename = "ExchangeTradedShare")]
    pub exchange_traded_share: String,
    #[serde(rename = "FundManagerName")]
    pub fund_manager_name: String,
    #[serde(rename = "FundManagerStartDate")]
    pub fund_manager_start_date: Option<NaiveDateTime>,
    #[serde(rename = "InceptionDate")]
    pub inception_date: NaiveDateTime,
    #[serde(rename = "AssetClass")]
    pub asset_class: String,
    #[serde(rename = "FundClass")]
    pub fund_class: String,
    #[serde(rename = "Amc")]
    pub amc: String,
    #[serde(rename = "RTACode")]
    pub rta_code: String,
    #[serde(rename = "FundLegalName")]
    pub fund_legal_name: String,
    #[serde(rename = "Benchmark")]
    pub benchmark: Option<String>,
    #[serde(rename = "MorningStarBenchmarkId")]
    pub morning_star_benchmark_id: Option<String>,
    #[serde(rename = "MorningStarBenchmarkName")]
    pub morning_star_benchmark_name: Option<String>,
    #[serde(rename = "DebtAllocation")]
    pub debt_allocation: Option<String>,
    #[serde(rename = "CashAllocation")]
    pub cash_allocation: Option<String>,
    #[serde(rename = "EquityAllocation")]
    pub equity_allocation: Option<String>,
    #[serde(rename = "CompanyId")]
    pub company_id: Option<String>,
    #[serde(rename = "Registrar")]
    pub registrar: Option<String>,
    #[serde(rename = "CompanyName")]
    pub company_name: Option<String>,
    #[serde(rename = "CompanyCity")]
    pub company_city: Option<String>,
    #[serde(rename = "CompanyState")]
    pub company_state: Option<String>,
    #[serde(rename = "FundStatus")]
    pub fund_status: String,
    #[serde(rename = "RiskCategory")]
    pub risk_category: Option<String>,
    #[serde(rename = "AmfiType")]
    pub amfi_type: String,
    #[serde(rename = "AmcCode")]
    pub amc_code: String,
    #[serde(rename = "SchemeCode")]
    pub scheme_code: String,
    #[serde(rename = "Primary_fd_code")]
    pub primary_fd_code: Option<String>,
    #[serde(rename = "Price")]
    pub price: f64,
    #[serde(rename = "PriceDate")]
    pub price_date: NaiveDateTime,
}

impl MutualFundData {
    pub fn set_price(&mut self, price: f64) {
        self.price = price;
    }
}
