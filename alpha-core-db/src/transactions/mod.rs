use crate::connection::pool::Manager;
use actlogica_logs::{builder::LogBuilder, log_error};
use deadpool::managed::Object;

pub async fn start_transaction(conn: &mut Object<Manager>) -> Result<(), String> {
    // begin the transaction
    conn.simple_query("BEGIN TRANSACTION").await.map_err(|e| {
        log_error(LogBuilder::system("Failed to begin transaction"));
    });
    Ok(())
}

pub async fn commit_transaction(conn: &mut Object<Manager>) -> Result<(), String> {
    // commit the transaction
    conn.simple_query("COMMIT").await.map_err(|e| {
        log_error(LogBuilder::system("Failed to commit transaction"));
    });
    Ok(())
}

pub async fn rollback_transaction(conn: &mut Object<Manager>) -> Result<(), String> {
    // rollback the transaction
    conn.simple_query("ROLLBACK").await.map_err(|e| {
        log_error(LogBuilder::system("Failed to rollback transaction"));
    });
    Ok(())
}
