use clickhouse::{Client, Row};

use serde::{Deserialize, Serialize};
use time::{Date, OffsetDateTime};

/// Historical Security Prices for NSE stored in Clickhouse
#[derive(Debug, Clone, Serialize, Deserialize, Row)]
pub struct NseSecurityPrice {
    /// International Securities Identification Number
    pub isin: String,

    pub symbol: String,

    pub series: String,

    /// Trading date
    #[serde(with = "clickhouse::serde::time::date")]
    pub date: Date,

    /// Stock price with precision of 4 decimal places
    pub price: i64,

    /// Record creation timestamp
    #[serde(with = "clickhouse::serde::time::datetime")]
    pub created_at: OffsetDateTime,
}

impl NseSecurityPrice {
    /// This fetches price of isin at date T if not found then (T-1,T-2,....T-n) where n is an integer 1>=1
    pub async fn get_by_date(client: Client, date: Date, isin: &str) -> Result<Option<Self>, clickhouse::error::Error> {
        let price = client
            .query("SELECT * FROM AlphaMasterDb.NseStockPrices WHERE date <= ? AND isin =?  ORDER BY date DESC LIMIT 1")
            .bind(date.to_string())
            .bind(isin)
            .fetch_optional::<Self>()
            .await?;

        Ok(price)
    }
}

#[cfg(test)]
mod tests {
    use time::{Date, Month};

    use crate::clickhouse::create_clickhouse_client;
    use crate::clickhouse::nse_stock_price::nse_stock_price::NseSecurityPrice;
    #[tokio::test]
    pub async fn get_historical_stock_price() {
        dotenv::dotenv().ok();
        let client = create_clickhouse_client();
        let date = Date::from_calendar_date(2024, Month::April, 27).unwrap();

        let foo = NseSecurityPrice::get_by_date(client, date, "INE428Q01011")
            .await
            .unwrap();

        println!("{:?}", foo);
    }
}
