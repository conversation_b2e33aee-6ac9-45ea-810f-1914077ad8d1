use clickhouse::{Client, Row};

use serde::{Deserialize, Serialize};
use time::{Date, OffsetDateTime};

/// Historical Security Prices for NSE stored in Clickhouse
#[derive(Debug, Clone, Serialize, Deserialize, Row)]
pub struct MutualFundSecurityPrice {
    /// International Securities Identification Number
    pub isin: String,

    /// Trading date
    #[serde(with = "clickhouse::serde::time::date")]
    pub date: Date,

    /// Stock price with precision of 4 decimal places
    pub price: i64,

    /// Record creation timestamp
    #[serde(with = "clickhouse::serde::time::datetime")]
    pub created_at: OffsetDateTime,
}

impl MutualFundSecurityPrice {
    pub async fn get_by_date(client: Client, date: Date, isin: &str) -> Result<Option<Self>, clickhouse::error::Error> {
        let price = client
            .query(
                "SELECT * FROM AlphaMasterDb.MutualFundPrices WHERE date <= ? AND isin =?  ORDER BY date DESC LIMIT 1",
            )
            .bind(date.to_string())
            .bind(isin)
            .fetch_optional::<Self>()
            .await?;

        Ok(price)
    }
}
