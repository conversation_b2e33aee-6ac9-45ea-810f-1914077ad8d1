use chrono::NaiveDate;
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use time::Date;

#[derive(Row, Deserialize, Debug, Serialize)]
pub struct PortfolioAnalytics {
    #[serde(with = "clickhouse::serde::time::date")]
    pub date: Date,
    pub portfolio_id: String,
    pub client_id: String,
    pub name: String,
    pub absolute_return_value: f64,
    pub absolute_return_percent: f64,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub withdrawals: f64,
    pub market_value: f64,
    pub dividends_paid: f64,
    pub dividend_reinvested: f64,
    pub market_value_change: f64,
    pub market_value_change_percent: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub xirr: f64,
    pub xirr_unrealised: f64,
}

impl Default for PortfolioAnalytics {
    fn default() -> Self {
        Self {
            date: Date::from_calendar_date(1970, time::Month::January, 1).unwrap(),
            portfolio_id: String::default(),
            client_id: String::default(),
            name: String::default(),
            absolute_return_value: 0.0,
            absolute_return_percent: 0.0,
            total_capital: 0.0,
            invested_capital: 0.0,
            withdrawals: 0.0,
            market_value: 0.0,
            dividends_paid: 0.0,
            dividend_reinvested: 0.0,
            market_value_change: 0.0,
            market_value_change_percent: 0.0,
            realised_gain_loss: 0.0,
            unrealised_gain_loss: 0.0,
            xirr: 0.0,
            xirr_unrealised: 0.0,
        }
    }
}

impl PortfolioAnalytics {
    pub async fn get_by_date_and_portfolio_id(
        ch_client: clickhouse::Client,
        date: NaiveDate,
        portfolio_id: String,
    ) -> Self {
        let mut cursor = ch_client
            .query("SELECT * FROM some PortfolioAnalytics WHERE date =? AND portfolio_id= ?")
            .bind(date)
            .bind(portfolio_id)
            .fetch::<PortfolioAnalytics>()
            .unwrap();

        let data = cursor.next().await.unwrap().unwrap();
        data
    }
}
