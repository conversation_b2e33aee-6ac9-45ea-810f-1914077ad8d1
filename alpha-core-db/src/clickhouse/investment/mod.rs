use chrono::{NaiveDate, NaiveDateTime};
use clickhouse::Row;
use serde::Deserialize;
use time::{Date, Month};

#[derive(Row, Deserialize, Clone, Debug)]
pub struct InvestmentAnalytics {
    pub date: Date,
    pub portfolio_id: String,
    pub investment_id: String,
    pub client_id: String,
    pub isin: String,
    pub symbol: String,
    pub series: String,
    pub exchange: String,
    pub name: String,
    pub asset_class: String,
    pub asset_type: String,
    pub industry: String,
    pub market_cap: String,
    pub market_value: f64,

    /// The Number of Quantity in the portfolio of this Security
    pub holdings: f64,

    /// The Closing Price of Stock on the day
    pub close_price: f64,
    pub close_price_date: NaiveDateTime,
    pub close_price_change: f64,
    pub current_price: f64,
    pub absolute_return_value: f64,
    pub absolute_return_percent: f64,
    pub long_term_units: f64,
    pub short_term_units: f64,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub withdrawals: f64,
    pub dividends_paid: f64,
    pub market_value_change: f64,
    pub market_value_change_percent: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub xirr: f64,
}

impl Default for InvestmentAnalytics {
    fn default() -> Self {
        Self {
            date: Date::from_calendar_date(1970, Month::January, 1).unwrap(),
            portfolio_id: String::new(),
            investment_id: String::new(),
            client_id: String::new(),
            isin: String::new(),
            symbol: String::new(),
            series: String::new(),
            exchange: String::new(),
            name: String::new(),
            asset_class: String::new(),
            asset_type: String::new(),
            industry: String::new(),
            market_cap: String::new(),
            market_value: 0.0,
            holdings: 0.0,
            close_price: 0.0,
            close_price_date: NaiveDateTime::from_timestamp_opt(0, 0).unwrap(),
            close_price_change: 0.0,
            current_price: 0.0,
            absolute_return_value: 0.0,
            absolute_return_percent: 0.0,
            long_term_units: 0.0,
            short_term_units: 0.0,
            total_capital: 0.0,
            invested_capital: 0.0,
            withdrawals: 0.0,
            dividends_paid: 0.0,
            market_value_change: 0.0,
            market_value_change_percent: 0.0,
            realised_gain_loss: 0.0,
            unrealised_gain_loss: 0.0,
            xirr: 0.0,
        }
    }
}

impl InvestmentAnalytics {
    pub async fn get_by_date(ch_client: clickhouse::Client, date: NaiveDate) -> Vec<Self> {
        let mut inv = Vec::new();
        let mut cursor = ch_client
            .query("SELECT * FROM some InvestmentAnalytics WHERE date <= ?")
            .bind(date)
            .fetch::<InvestmentAnalytics>()
            .unwrap();

        while let Some(row) = cursor.next().await.unwrap() {
            inv.push(row);
        }

        inv
    }
}
