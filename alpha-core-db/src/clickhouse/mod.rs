use clickhouse::Client;

pub mod bse_stock_price;
pub mod investment;
pub mod isin_history;
pub mod mutual_fund_price;
pub mod nse_stock_price;
pub mod portfolio;
pub mod benchmark_price;

pub fn create_clickhouse_client() -> Client {
    let url = std::env::var("MASTER_CLICKHOUSE_URL").expect("Failed to load database host from env");
    let user_name = std::env::var("MASTER_CLICKHOUSE_USER_NAME").expect("Failed to load database host from env");
    let password = std::env::var("MASTER_CLICKHOUSE_PASSWORD").expect("Failed to load database host from env");
    let database_name = std::env::var("MASTER_CLICKHOUSE_DB_NAME").expect("Failed to load database host from env");

    Client::default()
        .with_url(url)
        .with_user(user_name)
        .with_password(password)
        .with_database(database_name)
}
