use std::path::Path;

use minio::s3::builders::ObjectContent;
use minio::s3::creds::StaticProvider;
use minio::s3::http::BaseUrl;
use minio::s3::response::BucketExistsResponse;
use minio::s3::types::S3Api;
use minio::s3::{Client, ClientBuilder};

pub async fn connect_to_minio() -> Result<Client, Box<dyn std::error::Error + Send + Sync>> {
    let access_keys = std::env::var("MINIO_ACCESS_KEY").expect("MINIO_ACCESS_KEY not found");
    let secret_keys = std::env::var("MINIO_SECRET_KEY").expect("MINIO_SECRET_KEY not found");
    let base_url = std::env::var("MINIO_ENDPOINT").expect("MINIO_ENDPOINT not found");

    let static_provider = StaticProvider::new(&access_keys, &secret_keys, None);

    let client = ClientBuilder::new(base_url.parse::<BaseUrl>()?)
        .provider(Some(Box::new(static_provider)))
        .build()?;

    Ok(client)
}


// function to create a bucket if it doesn't exist
pub async fn create_bucket_if_not_exists(
    bucket_name: &str,
    client: &Client,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Check 'bucket_name' bucket exist or not.
    let resp: BucketExistsResponse = client.bucket_exists(bucket_name).send().await?;

    // Make 'bucket_name' bucket if not exist.
    if !resp.exists {
        client.create_bucket(bucket_name).send().await.unwrap();
    };
    Ok(())
}
    
pub async fn download_file(client: &Client, bucket: &str, file_name: &str) -> Result<String, String> {
    let object_key = format!("golden-source/{}", file_name);
    let response = client
        .get_object(bucket, &object_key)
        .send()
        .await
        .unwrap();

    let download_path = format!("/tmp/golden-source/{}", file_name);

    response
        .content
        .to_file(Path::new(&download_path))
        .await
        .unwrap();
    Ok(download_path)
}