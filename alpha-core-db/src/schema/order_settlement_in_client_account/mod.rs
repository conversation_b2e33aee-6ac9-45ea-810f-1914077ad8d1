use chrono::{DateTime, Utc};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Debug, Serialize, Deserialize)]
pub struct OrderSettlementInClientAccount {
    pub sr_no: Option<String>,
    pub contract_number: String,
    pub party_code: String,
    pub client_short_name: String,
    pub custody_clearing_code: String,
    pub client_custody_code: String,
    pub client_fa_code: Option<String>,
    pub client_demat_number: String,
    pub scrip_code: String,
    pub isin: String,
    pub exchange: String,
    pub folio_no: Option<String>,
    pub scrip_name: String,
    pub series: Option<String>,
    pub sell_buy: String,
    pub trade_date: DateTime<Utc>,
    pub settlement_date: DateTime<Utc>,
    pub original_quantity_ordered: f64,
    pub quantity: f64,
    pub settlement_allocation_percentage: f64,
    pub market_rate: f64,
    pub market_amount: f64,
    pub actual_brokerage: f64,
    pub actual_brokerage_per_unit: f64,
    pub expected_brokerage: f64,
    pub expected_brokerage_per_unit: f64,
    pub service_tax: f64,
    pub net_rate: f64,
    pub stt_amount: f64,
    pub turn_tax_exchange_txn_tax: f64,
    pub stamp_duty_and_other_charges: f64,
    pub net_amount: f64,
    pub client_order_entry_id: String,
    pub strategy_modelid: String,
    pub client_id: String,
    pub strategy_name: String,
    pub trade_order_settlement_file_id: String,
}

impl OrderSettlementInClientAccount {
    fn from_row(row: &Row) -> Self {
        Self {
            sr_no: row.get::<&str, _>("SrNo").map(String::from),
            contract_number: row.get::<&str, _>("ContractNumber").unwrap().to_string(),
            party_code: row.get::<&str, _>("PartyCode").unwrap().to_string(),
            client_short_name: row.get::<&str, _>("ClientShortName").unwrap().to_string(),
            custody_clearing_code: row.get::<&str, _>("CustodyClearingCode").unwrap().to_string(),
            client_custody_code: row.get::<&str, _>("ClientCustodyCode").unwrap().to_string(),
            client_fa_code: row.get::<&str, _>("ClientFaCode").map(String::from),
            client_demat_number: row.get::<&str, _>("ClientDematNumber").unwrap().to_string(),
            scrip_code: row.get::<&str, _>("ScripCode").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            folio_no: row.get::<&str, _>("FolioNo").map(String::from),
            scrip_name: row.get::<&str, _>("ScripName").unwrap().to_string(),
            series: row.get::<&str, _>("Series").map(String::from),
            sell_buy: row.get::<&str, _>("SellBuy").unwrap().to_string(),
            trade_date: row.get::<DateTime<Utc>, _>("TradeDate").unwrap(),
            settlement_date: row.get::<DateTime<Utc>, _>("SettlementDate").unwrap(),
            original_quantity_ordered: row.get::<f64, _>("OriginalQuantityOrdered").unwrap(),
            quantity: row.get::<f64, _>("Quantity").unwrap(),
            settlement_allocation_percentage: row.get::<f64, _>("SettlementAllocationPercentage").unwrap(),
            market_rate: row.get::<f64, _>("MarketRate").unwrap(),
            market_amount: row.get::<f64, _>("MarketAmount").unwrap(),
            actual_brokerage: row.get::<f64, _>("ActualBrokerage").unwrap(),
            actual_brokerage_per_unit: row.get::<f64, _>("ActualBrokeragePerUnit").unwrap(),
            expected_brokerage: row.get::<f64, _>("ExpectedBrokerage").unwrap(),
            expected_brokerage_per_unit: row.get::<f64, _>("ExpectedBrokeragePerUnit").unwrap(),
            service_tax: row.get::<f64, _>("ServiceTax").unwrap(),
            net_rate: row.get::<f64, _>("NetRate").unwrap(),
            stt_amount: row.get::<f64, _>("SttAmount").unwrap(),
            turn_tax_exchange_txn_tax: row.get::<f64, _>("TurnTaxExchangeTxnTax").unwrap(),
            stamp_duty_and_other_charges: row.get::<f64, _>("StampDutyAndOtherCharges").unwrap(),
            net_amount: row.get::<f64, _>("NetAmount").unwrap(),
            client_order_entry_id: row.get::<&str, _>("ClientOrderEntryId").unwrap().to_string(),
            strategy_modelid: row.get::<&str, _>("StrategyModelid").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
            trade_order_settlement_file_id: row.get::<&str, _>("TradeOrderSettlementFileId").unwrap().to_string(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            OrderSettlementInClientAccounts
        WHERE
            TradeOrderSettlementFileId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(res)
    }
}
