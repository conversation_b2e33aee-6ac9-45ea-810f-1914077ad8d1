use std::{fmt::Display, str::FromStr};

use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use tiberius::Row;

pub mod receivables_report;
use crate::{connection::pool::Manager, error::DatabaseError};

use super::client_order_entry::{TransactionSubType, TransactionType};

#[derive(<PERSON><PERSON>, Default, PartialEq)]
pub enum ReceivableStatus {
    #[default]
    Hold,
    Received,
    Paid,
}

impl Display for ReceivableStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Hold => write!(f, "Hold"),
            Self::Received => write!(f, "Received"),
            Self::Paid => write!(f, "Paid"),
        }
    }
}

impl std::str::FromStr for ReceivableStatus {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_ascii_lowercase().as_str() {
            "hold" => Ok(ReceivableStatus::Hold),
            "received" => Ok(ReceivableStatus::Received),
            "paid" => Ok(ReceivableStatus::Paid),
            _ => Err(()),
        }
    }
}

#[derive(Default)]
pub struct PortfolioReceivables {
    pub id: String,
    pub client_id: String,
    pub portfolio_id: String,
    pub investment_id: String,
    pub isin: String,
    pub symbol: String,
    pub investment_name: String,
    pub exchange: String,
    pub corporate_action_type: Option<String>,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub cgt_date: NaiveDateTime,
    pub amount: f64,
    pub quantity: f64,
    pub price: f64,
    pub receivable_status: ReceivableStatus,
    pub remarks: String,
}


impl PortfolioReceivables {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            investment_id: row.get::<&str, _>("InvestmentId").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            investment_name: row.get::<&str, _>("InvestmentName").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            corporate_action_type: row.get::<&str, _>("CorporateActionType").map(String::from),
            transaction_type: TransactionType::from_str(row.get("TransactionType").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get("TransactionSubType").unwrap()).unwrap(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            cgt_date: row.get::<NaiveDateTime, _>("cgt_date").unwrap(),
            amount: row.get::<f64, _>("amount").unwrap(),
            quantity: row.get::<f64, _>("quantity").unwrap(),
            price: row.get::<f64, _>("price").unwrap(),
            receivable_status: ReceivableStatus::from_str(row.get::<&str, _>("ReceivableStatus").unwrap()).unwrap(),
            remarks: row.get::<&str, _>("Remarks").unwrap().to_string(),
        }
    }

    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<(), DatabaseError> {
        let query = r#"
            INSERT INTO PortfolioReceivables (
                ClientId, PortfolioId, InvestmentId, isin, symbol,
                Investmentname, exchange, CorporateActionType, TransactionType,
                TransactionSubType, TransactionDate, SettlementDate, CgtDate,
                amount, quantity, price, ReceivableStatus, remarks
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6,
                @P7, @P8, @P9, @P10,
                @P11, @P12, @P13, @P14,
                @P15, @P16, @P17, @P18
            )
        "#;

        conn.execute(
            query,
            &[
                &self.client_id,
                &self.portfolio_id,
                &self.investment_id,
                &self.isin,
                &self.symbol,
                &self.investment_name,
                &self.exchange,
                &self.corporate_action_type,
                &self.transaction_type.to_string(),
                &self.transaction_sub_type.to_string(),
                &self.transaction_date,
                &self.settlement_date,
                &self.cgt_date,
                &self.amount,
                &self.quantity,
                &self.price,
                &self.receivable_status.to_string(),
                &self.remarks,
            ],
        )
        .await
        .map_err(|err| DatabaseError::QueryError(err))?;

        Ok(())
    }


}

