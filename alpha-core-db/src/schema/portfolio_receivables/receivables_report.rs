use std::str::FromStr;

use chrono::NaiveDate;
use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError, schema::client_order_entry::TransactionType};

pub struct RptPortfolioReceivables {
    pub transaction_type: TransactionType,
    pub portfolio_id: String,
    pub investment_id: String,
    pub amount: f64,
}

impl RptPortfolioReceivables {
    pub fn from_row(row: &Row) -> Self {
        Self {
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            investment_id: row.get::<&str, _>("InvestmentId").unwrap().to_string(),
            amount: row.get::<f64, _>("Amount").unwrap(),
        }
    }

    pub async fn get_as_at(conn: &mut Object<Manager>, as_at: NaiveDate) -> Result<Vec<Self>, DatabaseError> {
        let query = format!(
            "DECLARE	@return_value int

							EXEC	@return_value = [dbo].[sp_get_TenantReceivablesPayables]
									@AsAtDate = N'{}'",
            as_at.format("%Y-%m-%d").to_string()
        );

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| DatabaseError::QueryError(e))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError(e))?;

        let holdings: Vec<Self> = rows_iter.into_iter().map(|row| Self::from_row(&row)).collect();
        Ok(holdings)
    }
}

mod test {
    use chrono::NaiveDate;

    use crate::{
        connection::connect_to_mssql, schema::portfolio_receivables::receivables_report::RptPortfolioReceivables,
    };

    #[tokio::test]
    async fn test_get_receivable() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let as_at_date = NaiveDate::from_ymd_opt(2025, 06, 01).unwrap();
        let holdings = RptPortfolioReceivables::get_as_at(&mut pool_conn, as_at_date).await;
        assert!(holdings.is_ok());
    }
}
