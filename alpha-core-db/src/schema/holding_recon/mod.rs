use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct HoldingReconRequest {
    pub id: String,
    pub custody_file_path: String,
    pub custodian_id: String,
    pub fund_accountant_file_path: Option<String>,
    pub recon_result_file_path: Option<String>,
    pub status: String,
    pub requested_by: String,
}
impl HoldingReconRequest {
    pub fn from_row(row: Row) -> HoldingReconRequest {
        HoldingReconRequest {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            custody_file_path: row.get::<&str, _>("CustodyFilePath").unwrap().to_string(),
            custodian_id: row.get::<&str, _>("CustodianId").unwrap().to_string(),
            fund_accountant_file_path: row.get::<&str, _>("FundAccountantFilePath").map(String::from),
            recon_result_file_path: row.get::<&str, _>("ReconResultFilePath").map(String::from),
            requested_by: row.get::<&str, _>("RequestedBy").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
        }
    }
    pub async fn get_holding_recon_request_by_id(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<HoldingReconRequest, DatabaseError> {
        let query = r#"
        Select
            *
        FROM 
            HoldingReconRequest 
        WHERE
            Id = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Err(DatabaseError::RowNotFound())
        } else {
            Ok(Self::from_row(rows_iter.unwrap()))
        }
    }

    pub async fn update_recon_result_file_path(&self, conn: &mut Object<Manager>, path: String, remark: String) {
        let query = r#"
        UPDATE 
            HoldingReconRequest
        SET
            ReconResultFilePath = @P1,
            Status = 'Success',
            Remarks = @P2
        WHERE
            ID = @P3
        
        "#;

        let rows_iter = conn.execute(query, &[&path, &remark, &self.id]).await.unwrap();
    }

    pub async fn update_recon_request_status_and_remark(
        &self,
        conn: &mut Object<Manager>,
        status: String,
        remark: String,
    ) {
        let query = r#"
        UPDATE 
            HoldingReconRequest
        SET
            Status = @P1,
            Remarks = @P2
        WHERE
            ID = @P3
        
        "#;

        let rows_iter = conn.execute(query, &[&status, &remark, &self.id]).await.unwrap();
    }
}
