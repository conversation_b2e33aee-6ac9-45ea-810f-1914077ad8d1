use deadpool::managed::Object;
use tiberius::{
    time::time::{Date, Month, PrimitiveDateTime, Time},
    Row,
};

use crate::{connection::pool::Manager, error::DatabaseError};

pub async fn get_capital_register_details_for_mis(conn: &mut Object<Manager>) -> Result<Vec<Row>, DatabaseError> {
    let date = Date::from_calendar_date(2023, Month::June, 15).unwrap();
    let time = Time::from_hms(12, 0, 0).unwrap();
    let as_at_date = PrimitiveDateTime::new(date, time);
    let query = r#"
    SELECT
        c.ClientCode,
        c.FirstName + ' ' + c.LastName AS ClientName, 
        p.Name AS StrategyName,
        p.ClientStrategyCode,
        p.CustodianPortfolioCode,
        TransactionDate,
        TransactionType,
        TransactionSubType,
        Amount,
        @P1 AS AsAtDate,
        Description,
        pcl.Id AS PortfolioCashLedgerId
    FROM
        PortfolioCapitalRegisters pcl
        JOIN Portfolios p ON pcl.PortfolioId = p.id
        JOIN clients c ON p.ClientId = c.id
    WHERE
        TransactionDate <= @P1
    ORDER BY
        p.Name, TransactionDate
    "#;

    let rows_iter = conn
        .query(query, &[&as_at_date])
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?
        .into_first_result()
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?;

    Ok(rows_iter)
}
