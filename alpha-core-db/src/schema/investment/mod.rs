use crate::{connection::pool::Manager, error::DatabaseError};
use chrono::NaiveDateTime;
use deadpool::managed::Object;
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, str::FromStr};
use tiberius::{error::Error, ExecuteResult, Query, Row};
use tracing::error;
pub mod mis_holdings;
use super::{
    client_order_entry::{SecuritySubType, SecurityType},

};

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct InvestmentsForHoldingRecon {
    pub client_id: String,
    pub client_name: String,
    pub portfolio_id: String,
    pub portfolio_name: String,
    pub custodian_id: String,
    pub custodian_name: String,
    pub custodian_portfolio_code: String,
    pub client_strategy_code: String,
    pub fa_account_number: Option<String>,
    pub security_name: Option<String>,
    pub symbol: Option<String>,
    pub isin: String,
    pub exchange: String,
    pub current_price: u64,
    pub current_holding: f64,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct PortfolioMetaself {
    pub client_id: String,
    pub client_name: String,
    pub portfolio_id: String,
    pub portfolio_name: String,
    pub custodian_id: String,
    pub custodian_name: String,
    pub custodian_portfolio_code: String,
    pub client_strategy_code: String,
    pub fa_account_number: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Default, Clone)]
pub struct Investments {
    pub id: String,
    pub portfolio_id: Option<String>,
    pub modelportfolio_id: Option<String>,
    pub client_id: Option<String>,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub symbol: String,
    pub name: String,
    pub isin: String,
    pub exchange: String,
    pub current_price: f64,
    pub current_price_date: NaiveDateTime,
    pub current_holding: f64,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub market_value: f64,
    pub average_price: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub total_realisations: f64,
    pub dividends: f64,
    pub irr_since_inception: f64,
    pub irr_current: f64,
    pub first_transaction_date: NaiveDateTime,
    pub last_transaction_date: NaiveDateTime,
    pub market_cap: Option<String>,
    pub sector: Option<String>,
    pub fund_class: Option<String>,
    pub category: Option<String>,
    pub asset_class: String,
    pub amfi_code: Option<String>,
    pub rating: Option<String>,
    pub security_sub_type: SecuritySubType,
    pub security_type: SecurityType,
    pub weight: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InvestmentsForDeviation {
    #[serde(rename = "investmentMarketValue")]
    pub name: String,
    pub isin: String,
    pub symbol: String,
    pub security_type: SecurityType,
    pub exchange: String,
    pub current_holding: f64,
    pub market_value: f64,
    pub weight: f64,
}

impl InvestmentsForDeviation {
    pub fn from_row(row: &Row) -> Self {
        Self {
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            market_value: row.get::<f64, _>("InvestmentMarketValue").unwrap(),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap(),
            security_type: SecurityType::from_str(row.get::<&str, _>("SecurityType").unwrap()).unwrap(),
            weight: 0f64,
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            name: row.get::<&str, _>("InvestmentName").unwrap().to_string(),
        }
    }
}

impl Investments {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").map(String::from),
            modelportfolio_id: row.get::<&str, _>("ModelportfolioId").map(String::from),
            client_id: row.get::<&str, _>("ClientId").map(String::from),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            current_price: row.get::<f64, _>("CurrentPrice").unwrap(),
            current_price_date: row.get::<NaiveDateTime, _>("CurrentPriceDate").unwrap(),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap(),
            total_capital: row.get::<f64, _>("TotalCapital").unwrap(),
            invested_capital: row.get::<f64, _>("InvestedCapital").unwrap(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
            average_price: row.get::<f64, _>("AveragePrice").unwrap(),
            realised_gain_loss: row.get::<f64, _>("RealisedGainLoss").unwrap(),
            unrealised_gain_loss: row.get::<f64, _>("UnRealisedGainLoss").unwrap(),
            total_realisations: row.get::<f64, _>("TotalRealisations").unwrap(),
            dividends: row.get::<f64, _>("Dividends").unwrap(),
            irr_since_inception: row.get::<f64, _>("IrrSinceInception").unwrap(),
            irr_current: row.get::<f64, _>("IrrCurrent").unwrap(),
            first_transaction_date: row.get::<NaiveDateTime, _>("FirstTransactionDate").unwrap(),
            last_transaction_date: row.get::<NaiveDateTime, _>("LastTransactionDate").unwrap(),
            market_cap: row.get::<&str, _>("MarketCap").map(String::from),
            sector: row.get::<&str, _>("Sector").map(String::from),
            fund_class: row.get::<&str, _>("FundClass").map(String::from),
            category: row.get::<&str, _>("Category").map(String::from),
            asset_class: row.get::<&str, _>("AssetClass").unwrap().to_string(),
            amfi_code: row.get::<&str, _>("AmfiCode").map(String::from),
            rating: row.get::<&str, _>("Rating").map(String::from),
            security_sub_type: SecuritySubType::from_str(row.get::<&str, _>("SecuritySubType").unwrap()).unwrap(),
            security_type: SecurityType::from_str(row.get::<&str, _>("SecurityType").unwrap()).unwrap(),
            weight: 0f64,
        }
    }
}

impl InvestmentsForHoldingRecon {
    pub fn from_row(row: &Row) -> InvestmentsForHoldingRecon {
        InvestmentsForHoldingRecon {
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            client_name: row.get::<&str, _>("ClientName").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            portfolio_name: row.get::<&str, _>("PortfolioName").unwrap().to_string(),
            custodian_id: row.get::<&str, _>("CustodianId").unwrap().to_string(),
            custodian_name: row.get::<&str, _>("CustodianName").unwrap().to_string(),
            custodian_portfolio_code: row.get::<&str, _>("CustodianPortfolioCode").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            fa_account_number: row.get::<&str, _>("FAAccountNo").map(String::from),
            security_name: row.get::<&str, _>("SecurityName").map(String::from),
            symbol: row.get::<&str, _>("Symbol").map(String::from),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            current_price: row.get::<f64, _>("CurrentPrice").unwrap().to_u64().unwrap(),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap().to_f64().unwrap(),
        }
    }
}

impl Investments {
    pub async fn get_by_isin_and_model_portfolio_id(
        conn: &mut Object<Manager>,
        isin: String,
        portfolio_id: String,
    ) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Investments
            WHERE
                isin = @P1 
            AND
                ModelPortfolioId = @P2
        "#;

        let rows_iter = conn
            .query(query, &[&isin, &portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_by_isin_and_portfolio_id(
        conn: &mut Object<Manager>,
        isin: String,
        portfolio_id: String,
    ) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Investments
            WHERE
                isin = @P1 
            AND
                PortfolioId = @P2
        "#;

        let rows_iter = conn
            .query(query, &[&isin, &portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Investments
            WHERE
                Id=@P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_portfolio_investments_for_holding_recon(
        conn: &mut Object<Manager>,
    ) -> Result<
        (
            HashMap<String, PortfolioMetaself>,
            HashMap<String, Vec<InvestmentsForHoldingRecon>>,
        ),
        DatabaseError,
    > {
        // SQL query to fetch specific fields from Investments and Portfolios
        let query = r#"
        SELECT 
            i.ClientId,
            CONCAT_WS(' ', cl.FirstName, cl.MiddleName, cl.LastName) AS ClientName,
            i.PortfolioId,
            p.Name AS PortfolioName,
            p.FAAccountNo,
            i.Name AS SecurityName,
            p.CustodianPortfolioCode,
            p.ClientStrategyCode,
            i.Symbol,
            i.Isin,
            i.Exchange,
            i.CurrentPrice,
            i.CurrentHolding,
            c.Name AS CustodianName,
            c.Id As CustodianId
        FROM 
            Investments i
        JOIN 
            Portfolios p ON i.PortfolioId = p.Id
        JOIN 
            ClientCustodians cc ON p.Id = cc.PortfolioId
        JOIN 
            Custodians c ON cc.CustodianId = c.Id
        JOIN
            Clients cl ON i.ClientId = cl.Id
        WHERE
            i.PortfolioId IS NOT NULL 
            AND i.ClientId IS NOT NULL
            AND i.CurrentHolding > 0
        ORDER BY 
            i.PortfolioId, i.Name
        "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap()
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap();

        let portfolio_metaself =
            rows_iter
                .iter()
                .map(InvestmentsForHoldingRecon::from_row)
                .fold(HashMap::new(), |mut acc, portfolio| {
                    acc.insert(
                        portfolio.custodian_portfolio_code.clone(),
                        PortfolioMetaself {
                            client_id: portfolio.client_id,
                            client_name: portfolio.client_name,
                            client_strategy_code: portfolio.client_strategy_code,
                            custodian_id: portfolio.custodian_id,
                            custodian_name: portfolio.custodian_name,
                            custodian_portfolio_code: portfolio.custodian_portfolio_code,
                            fa_account_number: portfolio.fa_account_number,
                            portfolio_id: portfolio.portfolio_id,
                            portfolio_name: portfolio.portfolio_name,
                        },
                    );
                    acc
                });

        let portfolo_holdings =
            rows_iter
                .iter()
                .map(InvestmentsForHoldingRecon::from_row)
                .fold(HashMap::new(), |mut acc, investment| {
                    acc.entry(investment.custodian_portfolio_code.clone())
                        .or_insert_with(Vec::new)
                        .push(investment);
                    acc
                });

        Ok((portfolio_metaself, portfolo_holdings))
    }

    pub async fn get_by_portfolio_id_for_performance_engine(
        conn: &mut Object<Manager>,
        portfolio_id: String,
    ) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Investments
            WHERE
                PortfolioId = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from Investments From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCashLedger From Database ");
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(res)
    }
}

impl Investments {
    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<String, DatabaseError> {
        let mut query = Query::new(
            "INSERT INTO Investments (
                Name, PortfolioId, ModelportfolioId, ClientId, Symbol,
                Isin, Exchange, CurrentPrice, CurrentPriceDate, CurrentHolding,
                TotalCapital, InvestedCapital, MarketValue, TotalRealisations, RealisedGainLoss,
                UnRealisedGainLoss, Dividends, IrrSinceInception, IrrCurrent, FirstTransactionDate,
                LastTransactionDate, MarketCap, Sector, FundClass, Category,
                AssetClass, Rating, SecuritySubType, SecurityType, AveragePrice,
                AmfiCode
            )
            OUTPUT INSERTED.Id
             VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                @P11, @P12, @P13, @P14, @P15, @P16, @P17, @P18, @P19, @P20,
                @P21, @P22, @P23, @P24, @P25, @P26, @P27, @P28, @P29, @P30, @P31
            )",
        );

        query.bind(&self.name);
        query.bind(self.portfolio_id.to_owned());
        query.bind(self.modelportfolio_id.to_owned());
        query.bind(self.client_id.to_owned());
        query.bind(&self.symbol);
        query.bind(&self.isin);
        query.bind(&self.exchange);
        query.bind(self.current_price);
        query.bind(self.current_price_date);
        query.bind(self.current_holding);
        query.bind(self.total_capital);
        query.bind(self.invested_capital);
        query.bind(self.market_value);
        query.bind(self.total_realisations);
        query.bind(self.realised_gain_loss);
        query.bind(self.unrealised_gain_loss);
        query.bind(self.dividends);
        query.bind(self.irr_since_inception);
        query.bind(self.irr_current);
        query.bind(self.first_transaction_date);
        query.bind(self.last_transaction_date);
        query.bind(self.market_cap.to_owned());
        query.bind(self.sector.to_owned());
        query.bind(self.fund_class.to_owned());
        query.bind(self.category.to_owned());
        query.bind(&self.asset_class);
        query.bind(self.rating.to_owned());
        query.bind(self.security_sub_type.to_string());
        query.bind(self.security_type.to_string());
        query.bind(self.average_price);
        query.bind(self.amfi_code.to_owned());

        let result = query.query(conn).await?;

        let row = result.into_row().await?;

        if let Some(row) = row {
            let id = row.get::<&str, _>("Id").unwrap().to_string();
            return Ok(id);
        } else {
            return Err(DatabaseError::Io);
        }
    }

    pub async fn update_on_settlement(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE 
                Investments
            SET
                CurrentHolding = @P1,
                MarketValue = @P2,
                FirstTransactionDate = @P3,
                LastTransactionDate = @P4
            WHERE
                Id = @P5
        ",
        );

        query.bind(self.current_holding);
        query.bind(self.market_value);
        query.bind(self.first_transaction_date);
        query.bind(self.last_transaction_date);
        query.bind(self.id.clone());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError(e))
    }

    pub async fn update_on_performance_engine(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE 
                Investments
            SET
                TotalCapital = @P1,
                InvestedCapital = @P2,
                IrrCurrent = @P3,
                RealisedGainLoss = @P4,
                UnRealisedGainLoss = @P5,
                Dividends = @P6,
                MarketValue = @P7,
                AveragePrice = @P8,
                FirstTransactionDate = @P9,
                LastTransactionDate = @P10,
                CurrentHolding = @P11,
                LastUpdatedDate = @P12
            WHERE
                Id = @P13
        ",
        );

        query.bind(self.total_capital);
        query.bind(self.invested_capital);
        query.bind(self.irr_current);
        query.bind(self.realised_gain_loss);
        query.bind(self.unrealised_gain_loss);
        query.bind(self.dividends);
        query.bind(self.market_value);
        query.bind(self.average_price);
        query.bind(self.first_transaction_date);
        query.bind(self.last_transaction_date);
        query.bind(self.current_holding);
        query.bind(self.last_updated_date);
        query.bind(self.id.clone());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError(e))
    }

    pub async fn get_by_portfolio_id(conn: &mut Object<Manager>, id: &str) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
        	Case
            when ((p.MarketValue + p.CurrentCashBalance) > 0.00)
			then
				Round((i.MarketValue / (p.MarketValue + p.CurrentCashBalance)) * 100, 2)
			else
			    0.00
			End as Weight,
            i.*
        FROM
        	Investments i JOIN Portfolios p on i.PortfolioId = p.Id
        WHERE
            PortfolioId = @P1
    "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(res)
    }

    pub async fn get_by_model_portfolio_id_for_deviation(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<InvestmentsForDeviation>, DatabaseError> {
        let query = r#"
        SELECT
        	Case
            when ((p.MarketValue + p.CurrentCashBalance) > 0.00)
			then
				Round((i.MarketValue / (p.MarketValue + p.CurrentCashBalance)) * 100, 2)
			else
			    0.00
			End as Weight,
            i.Name as InvestmentName,
            i.Isin,
            i.Symbol,
            i.Exchange,
            i.SecurityType,
            i.CurrentHolding,
            i.MarketValue as InvestmentMarketValue
        FROM
        	Investments i JOIN ModelPortfolios p on i.ModelPortfolioId = p.Id
        WHERE
            ModelPortfolioId = @P1
    "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<InvestmentsForDeviation> = rows_iter.iter().map(InvestmentsForDeviation::from_row).collect();

        Ok(res)
    }

    pub async fn get_by_portfolio_id_for_deviation(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<InvestmentsForDeviation>, DatabaseError> {
        let query = r#"
            SELECT
                Name as InvestmentName,
                Isin,
                Symbol,
                Exchange,
                SecurityType,
                CurrentHolding,
                MarketValue as InvestmentMarketValue
            FROM
            	Investments
            WHERE
                PortfolioId = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<InvestmentsForDeviation> = rows_iter.iter().map(InvestmentsForDeviation::from_row).collect();

        Ok(res)
    }

    pub async fn get_current_holding_of_isin(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
        isin: &str,
    ) -> Result<Option<f64>, DatabaseError> {
        let query = "

            SELECT
             CurrentHolding
            FROM
                Investments
            WHERE
                PortfolioId= @P1
                AND
                ISIN = @P2
        ";

        let rows_iter = conn
            .query(query, &[&portfolio_id, &isin])
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError(e))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(rows_iter.unwrap().get::<f64, _>("CurrentHolding").unwrap()))
        }
    }
}
