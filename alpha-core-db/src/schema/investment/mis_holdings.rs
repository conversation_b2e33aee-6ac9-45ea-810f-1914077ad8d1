use std::str::FromStr;

use chrono::NaiveDate;
use deadpool::managed::Object;
use tiberius::Row;

use crate::{
    connection::pool::Manager,
    error::DatabaseError,
    schema::client_order_entry::{SecuritySubType, SecurityType},
};

#[derive(Clone, Debug)]
pub struct RptHoldingDetailsForMis {
    pub portfolio_id: String,

    /// If its None then its an entry for cash
    pub investment_id: Option<String>,
    pub client_name: String,
    pub strategy_name: String,
    pub client_strategy_code: String,
    pub custodian_portfolio_code: String,
    pub fa_account_no: Option<String>,
    pub isin: String,
    pub holding_name: String,
    pub symbol: String,
    pub unrealised_qty: f64,
    pub total_cost: f64,
    pub market_value: f64,
    pub average_price: f64,
    pub price: f64,
    pub accrued_income: f64,
    pub receivable: f64,
    pub payable: f64,
    pub security_type: SecurityType,
    pub security_sub_type: SecuritySubType,
    pub asset_class: String,
}

impl RptHoldingDetailsForMis {
    fn from_row(row: &Row) -> RptHoldingDetailsForMis {
        RptHoldingDetailsForMis {
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            investment_id: row.get::<&str, _>("InvestmentId").map(String::from),
            client_name: row.get::<&str, _>("ClientName").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            custodian_portfolio_code: row.get::<&str, _>("CustodianPortfolioCode").unwrap().to_string(),
            fa_account_no: row.get::<&str, _>("FAAccountNo").map(String::from),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            holding_name: row.get::<&str, _>("HoldingName").unwrap().to_string(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            unrealised_qty: row.get::<f64, _>("UnrealisedQty").unwrap(),
            total_cost: row.get::<f64, _>("TotalCost").unwrap_or_default(),
            market_value: 0f64,
            average_price: 0f64,
            price: 0f64,
            accrued_income: 0f64,
            receivable: 0f64,
            payable: 0f64,
            security_sub_type: SecuritySubType::from_str(row.get::<&str, _>("SecuritySubType").unwrap_or("Other"))
                .unwrap(),
            security_type: SecurityType::from_str(row.get::<&str, _>("SecurityType").unwrap()).unwrap(),
            asset_class: row.get::<&str, _>("AssetClass").unwrap().to_string(),
        }
    }
}

impl RptHoldingDetailsForMis {
    pub async fn get(
        conn: &mut Object<Manager>,
        as_at_date: NaiveDate,
        with_acquistion_rate: bool,
    ) -> Result<Vec<Self>, DatabaseError> {
        let query = format!(
            "DECLARE @return_value int

							EXEC	@return_value = [dbo].[sp_TenantHoldingMisAsAt]
									@AsAt = N'{}',
									@isAcquisitionBased = N'{with_acquistion_rate}'",
            as_at_date.format("%Y-%m-%d").to_string()
        );

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| DatabaseError::QueryError(e))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError(e))?;

        let holdings: Vec<Self> = rows_iter.into_iter().map(|row| Self::from_row(&row)).collect();
        Ok(holdings)
    }
}

mod test {
    use chrono::NaiveDate;

    use crate::{connection::connect_to_mssql, schema::investment::mis_holdings::RptHoldingDetailsForMis};

    #[tokio::test]
    async fn test_mis_holdings() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let as_at_date = NaiveDate::from_ymd_opt(2025, 06, 01).unwrap();
        let holdings = RptHoldingDetailsForMis::get(&mut pool_conn, as_at_date, true).await;
        assert!(holdings.is_ok());
    }
}
