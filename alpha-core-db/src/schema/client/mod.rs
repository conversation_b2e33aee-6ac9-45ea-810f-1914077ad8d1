use std::fmt;

use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use crate::connection::pool::Manager;
use crate::error::DatabaseError;

use super::{oms::ClientForOms, portfolio::Portfolio};

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum ClientType {
    Individual,
    NonIndividual,
}

#[derive(Debug, <PERSON>lone, PartialEq, Serialize, Deserialize, Default)]
pub enum DomicileType {
    #[default]
    Resident,
    NonResident,
}

impl fmt::Display for DomicileType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DomicileType::Resident => write!(f, "Resident"),
            DomicileType::NonResident => write!(f, "NonResident"),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum ClientTitle {
    Mr,
    Ms,
    Mrs,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct Client {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub client_code: String,
    pub first_name: String,
    pub middle_name: Option<String>,
    pub last_name: String,
    pub date_of_birth: NaiveDateTime,
    pub pan: String,
    pub aadhar: Option<String>,
    pub email: String,
    pub phone: String,
    pub client_type: ClientType,
    pub domicile: DomicileType,
    pub bse_star_ucc: Option<String>,
    pub title: ClientTitle,
    pub user_name: Option<String>,
    pub user_id: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ClientPersonalDetail {
    pub id: String,
    pub client_id: String,
    pub marital_status: Option<MaritalStatus>,
    pub occupation: Option<ClientOccupation>,
    pub nationality: Option<Nationality>,
    pub family_or_group: String,
    pub head_of_family: Option<bool>,
    pub kyc_valid: bool,
    pub tax_status: ClientTaxStatus,
    pub category: ClientCategory,
    pub reporting_currency: Option<Currency>,
    pub anniversary_date: Option<NaiveDateTime>,
    pub spouse: String,
    pub spouse_dob: Option<NaiveDateTime>,
    pub qualification: Option<ClientQualification>,
    pub work_experience: Option<ClientWorkExperience>,
    pub organization_name: String,
    pub employer_id: String,
    pub industry_type: Option<IndustryType>,
    pub address_organisation: String,
    pub place_of_birth: String,
    pub country_of_birth: String,
    pub gross_annual_income: Option<ClientGrossAnnualIncome>,
    pub source_of_wealth: String,
    pub estimated_financial_wealth: Option<ClientEstimatedFinancialWealth>,
    pub political_exposure: bool,
    pub aml_certified: Option<bool>,
    pub gender: Gender,
}

#[derive(Debug, Clone, PartialEq)]
pub enum MaritalStatus {
    Single,
    Married,
    Others,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientOccupation {
    PublicSector,
    PrivateSector,
    GovernmentService,
    Business,
    Professional,
    Agricultarist,
    Retired,
    Housewife,
    Student,
    Others,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Nationality {
    Indian,
    NRI,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientTaxStatus {
    Resident,
    NonResident,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientCategory {
    Individual,
    Corporate,
    Trust,
    HUF,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Currency {
    Rupees,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientQualification {
    HighSchool,
    Diploma,
    Bachelors,
    Masters,
    Professional,
    Doctorate,
    Others,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientWorkExperience {
    ZeroToOne,
    OneToThree,
    ThreeToFive,
    MoreThanFive,
}

#[derive(Debug, Clone, PartialEq)]
pub enum IndustryType {
    Health,
    Education,
    Agriculture,
    Pharmaceutical,
    InformationTechnology,
    Construction,
    Others,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientGrossAnnualIncome {
    BelowOneLakh,
    OneToFiveLakh,
    FiveToTenLakh,
    TenToTwentyFiveLakh,
    MoreThanTwentyFiveLakh,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ClientEstimatedFinancialWealth {
    BelowOneLakh,
    OneToFiveLakh,
    FiveToTenLakh,
    TenToTwentyFiveLakh,
    MoreThanTwentyFiveLakh,
}

#[derive(Debug, Clone, PartialEq)]
pub enum Gender {
    Male,
    Female,
    Others,
}

#[derive(Debug, Clone)]
pub struct ClientBank {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub name: String,
    pub address_line1: String,
    pub address_line2: String,
    pub city: String,
    pub state: String,
    pub postcode: String,
    pub account_name: String,
    pub account_status: AccountStatus,
    pub account_number: String,
    pub ifsc: String,
    pub micr: String,
    pub bank_account_type: BankAccountType,
    pub client_id: String,
    pub portfolio_id: String,
    pub account_link: String,
    pub branch_name: String,
    pub currency: Option<Currency>,
    pub swift_code: String,
    pub from_date: Option<NaiveDateTime>,
    pub to_date: Option<NaiveDateTime>,
    pub second_holder_name: String,
    pub third_holder_name: String,
}

#[derive(Debug, Clone)]
pub struct ClientContactDetail {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub client_id: String,
    pub address_line1: String,
    pub address_line2: String,
    pub city: String,
    pub state: String,
    pub country: String,
    pub pin_code: String,
    pub mobile_country_code: String,
    pub mobile_secondary: String,
    pub email_secondary: String,
}

impl ClientContactDetail {
    pub fn from_row(row: Row) {}
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum AccountStatus {
    Active,
    Inactive,
    Frozen,
    Closed,
}
#[derive(Debug, Clone, PartialEq)]
pub enum BankAccountType {
    Savings,
    Current,
    Other,
}

#[derive(PartialEq, Clone, Debug)]
pub struct PortfoliosWithStartDateOfClient {
    pub id: String,
    pub start_date: NaiveDateTime,
}

impl PortfoliosWithStartDateOfClient {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            start_date: row.get::<NaiveDateTime, _>("StartDate").unwrap(),
        }
    }
}

impl ClientBank {
    pub async fn get_client_bank_account_number(
        conn: &mut Object<Manager>,
        client_id: &str,
    ) -> Result<Option<String>, DatabaseError> {
        let query = r#"
        SELECT
            AccountNumber
        FROM
            ClientBanks
        WHERE
            ClientId = @P1

    "#;

        let rows_iter = conn
            .query(query, &[&client_id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(
                rows_iter.unwrap().get::<&str, _>("AccountNumber").unwrap().to_string(),
            ))
        }
    }
}
impl Client {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            first_name: row.get::<&str, _>("FirstName").unwrap().to_string(),
            middle_name: row.get::<&str, _>("MiddleName").map(String::from),
            last_name: row.get::<&str, _>("LastName").unwrap().to_string(),
            date_of_birth: row.get::<NaiveDateTime, _>("DateOfBirth").unwrap(),
            pan: row.get::<&str, _>("Pan").unwrap().to_string(),
            aadhar: row.get::<&str, _>("Aadhar").map(String::from),
            email: row.get::<&str, _>("Email").unwrap().to_string(),
            phone: row.get::<&str, _>("Phone").unwrap().to_string(),
            client_type: match row.get::<&str, _>("ClientType").unwrap().to_lowercase().as_str() {
                "individual" => ClientType::Individual,
                "nonindividual" => ClientType::NonIndividual,
                _ => panic!("Invalid client type"),
            },
            domicile: match row.get::<&str, _>("Domicile").unwrap().to_lowercase().as_ref() {
                "resident" => DomicileType::Resident,
                "nonresident" => DomicileType::NonResident,
                _ => panic!("Invalid domicile type"),
            },
            bse_star_ucc: row.get::<&str, _>("BseStarUcc").map(String::from),
            title: match row.get::<&str, _>("Title").unwrap() {
                "Mr" => ClientTitle::Mr,
                "Mrs" => ClientTitle::Mrs,
                "Ms" => ClientTitle::Ms,
                _ => panic!("Invalid title"),
            },
            user_name: row.get::<&str, _>("UserName").map(String::from),
            user_id: row.get::<&str, _>("UserId").map(String::from),
        }
    }

    // fetch ClientMaster data, return vector of rows
    pub async fn get_client_master_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT 
            c.clientcode as UNIQUE_CLIENT_CODE,
            p.clientstrategycode as CLIENT_FOLIO_NO, 
            c.pan as CLIENT_PAN,
            cu.DPId+cc.CustodyAccountNumber as CLIENT_BOID,
            cpd.Category as CLIENT_CATEGORY,
            cpd.SubCategory as CLIENT_SUB_CATEGORY,
            p.PortfolioType as SERVICE_CATEGORY,
            c.FirstName as CLIENT_FIRST_NAME,
            c.MiddleName as CLIENT_MIDDLE_NAME,
            c.LastName as CLIENT_LAST_NAME,
            ccd.AddressLine1+ ccd.AddressLine2 as CLIENT_ADDRESS,
            ccd.City as CLIENT_CITY,
            ccd.State as CLIENT_STATE,
            ccd.Pincode as CLIENT_PINCODE,
            ccd.Country as CLIENT_COUNTRY,
            ccd.MobileSecondary as CLIENT_PRIMARY_MOBILE_NO,
            c.Email as CLIENT_EMAIL,
            cc.SecondHolderName as JOINT_HOLDER_1_NAME,
            cc.SecondHolderPAN as JOINT_HOLDER_1_PAN,
            cc.ThirdHolderName as JOINT_HOLDER_2_NAME,
            cc.ThirdHolderPAN as JOINT_HOLDER_2_PAN,
            cc.ModeOfHolding as HOLDING_NATURE,
            pnd.NomineeName as NOMINEE_1_NAME,
            pnd.NomineePanNo as NOMINEE_1_PAN_NO,
            cpd.Gender as FIRST_HOLDER_GENDER,
            c.DateOfBirth as FIRST_HOLDER_DOB,
            cpd.Nationality as FIRST_HOLDER_NATIONALITY,
            cpd.Occupation as FIRST_HOLDER_OCCUPATION,
            p.StartDate as DATE_OF_PMS_ACCOUNT_ACTIVATION,
            p.AccountStatus as IS_ACCOUNT_ACTIVE,
            p.InactiveSince as INACTIVE_SINCE,
            p.AccountInactivityDesc as ACCOUNT_INACTIVITY_DESC,
            p.DateOfPMSAccountClosure as DATE_OF_PMS_ACCOUNT_CLOSURE
        FROM 
            Portfolios p 
            Join clients c on c.id=p.ClientId
            Join ClientCustodians cc on cc.clientid = c.id
            Join Custodians cu on cu.id=cc.CustodianId
            join ClientPersonalDetails cpd on cpd.clientid=c.id
            join ClientContactDetails ccd on ccd.clientid=c.id
            join PortfolioNomineeDetails pnd on pnd.portfolioid=p.id
        WHERE
            CAST(p.CreatedDate AS DATE) BETWEEN @P1 AND @P2
        "#;

        let rows_iter = conn
            .query(query, &[&from_date, &to_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?;

        Ok(rows_iter)
    }

    // fetch ClientHolding data, return vector of rows
    pub async fn get_client_holding_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT 
            i.CurrentPriceDate                  AS HOLDING_DATE,
            c.ClientCode                        AS UNIQUE_CLIENT_CODE,
            p.ClientStrategyCode                AS CLIENT_FOLIO_NO,
            i.SecurityType                      AS INVESTMENT_TYPE,
            i.AssetClass                        AS ASSET_TYPE,
            i.IssuerName                        AS ISSUER_NAME,
            i.Name                              AS SECURITY_NAME,
            i.Isin                              AS SECURITY_ISIN,
            i.Symbol                            AS SECURITY_CODE,
            i.IsSecurityAssociated              AS IS_SECURITY_ASSOCIATED,
            i.SecuritySubType                   AS IS_SECURITY_LISTED,
            i.SecurityRating                    AS SECURITY_RATING,
            i.RatingAgency                      AS RATING_AGENCY,
            i.CurrentHolding                    AS QUANTITY,
            (i.MarketValue / i.CurrentHolding)  AS UNIT_PRICE,
            i.MarketValue                       AS MARKET_VALUE,
            i.MaturityDate                      AS MATURITY_DATE,
            i.OptionType                        AS OPTION_TYPE 
        FROM 
            Investments i
        JOIN
            Portfolios p on p.Id=i.PortfolioId
        JOIN 
            Clients c on c.Id=p.ClientId
        WHERE
            i.CurrentHolding > 0
            AND CAST(i.CreatedDate AS DATE) BETWEEN @P1 AND @P2
        "#;

        let rows_iter = conn
            .query(query, &[&from_date, &to_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?;

        Ok(rows_iter)
    }

    // fetch ClientExpenseMaster data, return vector of rows
    pub async fn get_client_expense_master_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT 
            pcl.TransactionDate     AS TRANSACTION_DATE,    
            c.clientcode            AS UNIQUE_CLIENT_CODE,
            p.clientstrategycode    AS CLIENT_FOLIO_NO,
            pcl.TransactionSubType  AS EXPENSE_TYPE,
            pcl.TransactionSubType  AS EXPENSE_SUB_TYPE,
            pcl.Amount              AS EXPENSE_VALUE
        FROM 
            Portfoliocashledgers pcl
            JOIN Portfolios p ON p.Id=pcl.PortfolioId
            JOIN Clients c ON c.Id=p.ClientId
        WHERE
            CAST(pcl.TransactionDate AS DATE) BETWEEN @P1 AND @P2
            AND pcl.TransactionType = 'Debit'
            AND pcl.TransactionSubType not in ('Buy', 'CapitalOut')
        ORDER BY
            pcl.SettlementDate
        "#;
        let rows_iter = conn
            .query(query, &[&from_date, &to_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?;

        Ok(rows_iter)
    }

    // fetch ClientFolioMaster data, return vector of rows
    pub async fn get_client_folio_master_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT 
            c.clientcode AS UNIQUE_CLIENT_CODE,
            p.clientstrategycode AS CLIENT_FOLIO_NO,
            p.AgreementSigningDate AS AGREEMENT_DATE,
            p.Name AS INVESTMENT_APPROACH,
            p.StratergyAssetClass AS INVESTMENT_STRATEGY,
            p.BenchmarkIndexName AS BENCHMARK,
            ISNULL(cu.DPId, '') + ISNULL(cc.CustodyAccountNumber, '') AS CLIENT_BOID,
            cu.CustodianRegNo AS CUSTODIAN_REG_NO,
            cu.name AS CUSTODIAN_NAME,
            p.POAonDemat AS IS_POWER_OF_ATTORNEY_EXECUTED,
            p.IsPermissionInvestInAssociates AS IS_PERMISSION_INVST_IN_ASSOCIATES,
            p.PctIndEquityConsent AS PERCENTAGE_INDIVIDUAL_EQUITY_CONSENT,
            c.PctTotalEquityConsent AS PERCENTAGE_TOTAL_EQUITY_CONSENT,
            p.PctIndDebtConsent AS PERCENTAGE_INDIVIDUAL_DEBT_CONSENT,
            c.PctTotalDebtConsent AS PERCENTAGE_TOTAL_DEBT_CONSENT,
            c.PctTotalLimitConsent AS PERCENTAGE_TOTAL_LIMIT_CONSENT,
            p.IsConsentRebalancePassive AS IS_CONSENT_REBALANCE_PASSIVE,
            p.IsConsentInvestInEquityDerivate AS IS_CONSENT_INVST_IN_EQUITY_DERIVATIVE,
            p.IsConsentInvestInCommodityDerivate AS IS_CONSENT_INVST_IN_COMMODITY_DERIVATIVE,
            p.PctDerivateConsent AS PERCENTAGE_DERIVATIVE_CONSENT,
            p.IsConsentLending AS IS_CONSENT_LENDING,
            p.CustodianPortfolioCode AS CLIENT_CUSTODIAN_CODE,
            dm.name AS PM_DISTRIBUTOR_NAME,
            dm.CompanyPAN AS PM_DISTRIBUTOR_PAN,
            CAST(pft.PerformanceFeeSharingPercentage AS VARCHAR(50)) 
                + ' Fee over ' 
                + CAST(pft.PerformanceFeeHurdleRate AS VARCHAR(50)) 
                + ' of returns' AS PERFORMANCE_FEE_DESCRIPTION,
            pft.PerformanceFeeSharingPercentage AS PERCENTAGE_PERFORMANCE_FEE
        FROM 
            Portfolios p 
        JOIN 
            Clients c ON c.id = p.ClientId
        LEFT JOIN 
            ClientCustodians cc ON cc.clientid = c.id
        LEFT JOIN 
            Custodians cu ON cu.id = cc.CustodianId
        LEFT JOIN 
            PortfolioDistributorMappings pdm ON pdm.PortfolioId = p.Id 
        LEFT JOIN 
            DistributorMasters dm ON pdm.DistributorMasterId = dm.Id  
        LEFT JOIN 
            PortfolioFeeTemplates pft ON pft.PortfolioId = p.Id
        WHERE
            CAST(p.CreatedDate AS DATE) BETWEEN @P1 AND @P2
        "#;
        let rows_iter = conn
            .query(query, &[&from_date, &to_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?;

        Ok(rows_iter)
    }

    // fetch ClientCapTransactions data
    pub async fn get_client_cap_transactions_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT 
            c.ClientCode as UNIQUE_CLIENT_CODE,
            p.ClientStrategyCode as CLIENT_FOLIO_NO,
            pcr.TransactionType as TRANSACTION_TYPE,
            pcr.TransactionDate as TRANSACTION_DATE,
            pcr.Amount as TRANSACTION_AMOUNT,
            pcr.Units as TRANSACTION_UNITS,
            pcr.ExitLoadAmount as EXIT_LOAD
        FROM 
            PortfolioCapitalRegisters pcr 
        JOIN 
            Portfolios p on p.Id=pcr.portfolioId
        JOIN 
            Clients c on c.Id=p.ClientId
        WHERE
            CAST(pcr.TransactionDate AS DATE) BETWEEN @P1 AND @P2
        "#;
        let rows_iter = conn
        .query(query, &[&from_date, &to_date])
        .await
        .map_err(|e| {
            println!("{:?}", e);
            DatabaseError::QueryError((e))
        })?
        .into_first_result()
        .await
        .map_err(|e| {
            println!("{:?}", e);
            DatabaseError::QueryError((e))
        })?;

        Ok(rows_iter)
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Client>, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Clients
            WHERE
                Id = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<(), DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                Clients    
        "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e))
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(())
    }

    pub async fn get_by_ids(conn: &mut Object<Manager>, params: &[String]) -> Result<Vec<Self>, DatabaseError> {
        let formatted_ids = params
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");
        let query = format!("Select * from Clients WHERE Id IN ({})", formatted_ids);

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }

    pub async fn get_by_ids_for_oms(
        conn: &mut Object<Manager>,
        params: &[String],
    ) -> Result<Vec<ClientForOms>, DatabaseError> {
        let formatted_ids = params
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
                Select         
                c.Id as ClientId,
                c.Domicile,
                c.ClientCode,
                c.FirstName,
                c.MiddleName,
                c.LastName,
                c.BseStarUcc,
                cb.AccountNumber
                from Clients c
                LEFT JOIN
                    ClientBanks cb ON cb.ClientId = c.Id
                WHERE c.Id IN ({})",
            formatted_ids
        );

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<ClientForOms> = rows_iter.iter().map(ClientForOms::from_row).collect();

        Ok(rows)
    }

    pub async fn get_all_portfolios(conn: &mut Object<Manager>, client_id: &str) -> Result<Vec<Portfolio>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                Portfolios p
            WHERE
                p.ClientId = @P1

        ";

        let rows_iter = conn
            .query(query, &[&client_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Portfolio> = rows_iter.iter().map(Portfolio::from_row).collect();

        Ok(rows)
    }

    pub async fn get_count_by_type_and_domicile(
        conn: &mut Object<Manager>,
        portfolio_type: &str,
        domicile: &str,
        client_type: &str,
        _as_at_date: NaiveDateTime,
    ) -> Result<i32, DatabaseError> {
        let query = "
            SELECT COUNT(c.Id) as Count
            FROM clients c
            JOIN Portfolios p ON p.ClientId = c.Id
            WHERE p.PortfolioType = @P1
            AND c.Domicile = @P2
            AND c.ClientType = @P3
        ";

        let row = conn
            .query(query, &[&portfolio_type, &domicile, &client_type])
            .await?
            .into_row()
            .await?
            .ok_or_else(|| {
                return DatabaseError::RowNotFound();
            })?;

        let count = row.get("Count").unwrap();
        Ok(count)
    }
}

#[cfg(test)]
mod tests {

    use crate::{
        connection::{connect_to_master_data, connect_to_mssql},
        schema::{client::Client, portfolio::PortfolioType},
    };

    #[tokio::test]
    async fn get_all_clients() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let portfolio_type = String::from("Discretionary");
        let domicile = String::from("Resident");
        let client_type = String::from("Individual");

        // let count = Client::get_count_by_type_and_domicile(&mut pool_conn, &portfolio_type, &domicile, &client_type).await.unwrap();
        // println!("Number of clients: {}", count);

        // let portfolio_type = String::from("NonDiscretionary");
        // let domicile = String::from("Resident");
        // let client_type = String::from("Individual");

        // let count = Client::get_count_by_type_and_domicile(&mut pool_conn, &portfolio_type, &domicile, &client_type).await.unwrap();
        // println!("Number of clients: {}", count);
    }
}
