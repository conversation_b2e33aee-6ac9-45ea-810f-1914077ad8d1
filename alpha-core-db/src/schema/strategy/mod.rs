use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, types::compute_order::StrategyForOrderComputation};

pub mod strategy_bank;
pub mod strategy_custodian;
pub mod strategy_model;
pub mod strategy_model_securities;
pub struct Strategies {
    pub strategy_code: String,

    pub name: String,

    pub start_date: NaiveDateTime,

    pub is_open: bool,

    pub is_mf_demat: bool,

    pub strategy_bank_id: String,
}


pub struct PortfoliosStrategy {
    pub portfolio_id: String,
    pub start_date: NaiveDateTime,
    pub model_id:String,
    pub client_id:String
}

impl PortfoliosStrategy {
    pub fn from_row(row: &Row) -> Self {
        Self {
            portfolio_id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            start_date: row.get::<NaiveDateTime, _>("StartDate").unwrap(),
        }
    }
}


impl Strategies {
    pub fn from_row(row: &Row) -> Self {
        Self {
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            strategy_code: row.get::<bool, _>("StrategyCode").unwrap().to_string(),
            is_mf_demat: row.get::<bool, _>("IsMFDemat").unwrap(),
            is_open: row.get::<bool, _>("IsOpen").unwrap(),
            start_date: row.get::<NaiveDateTime, _>("StartDate").unwrap(),
            strategy_bank_id: row.get::<bool, _>("StrategyBankId").unwrap().to_string(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, String> {
        let query = r#"
        SELECT
            *
        FROM
            Strategies
        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| String::from("Failed To Get Strategies from Db"))?
            .into_row()
            .await
            .map_err(|e| String::from("Failed To Get Strategies from Db"))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_strategy_for_order_computation_by_model_id(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Option<StrategyForOrderComputation>, String> {
        let query = r#"
             SELECT
                s.Id As StrategyId,
                s.Name As StrategyName,
                s.IsOpen,
                s.StrategyCode,
                sm.Id As ModelId,
                sm.Name As ModelName,
                sm.ModelCode
            FROM
                Strategies s
            JOIN
                StrategyModels sm ON s.Id = sm.StrategyId
             WHERE
                sm.Id = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| String::from("Failed To Get Strategies from Db"))?
            .into_row()
            .await
            .map_err(|e| String::from("Failed To Get Strategies from Db"))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(StrategyForOrderComputation::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_portfolios_in_strategy(
        conn: &mut Object<Manager>,
        strategy_id: &str,
    ) -> Result<Vec<PortfoliosStrategy>, tiberius::error::Error> {
        let query = r#"
            SELECT
                p.Id,
                p.StartDate,
                p.ClientId,
                p.ModelId
            FROM
                Portfolios p
            JOIN
                StrategyModels sm ON sm.Id = p.ModelId
            WHERE
                sm.StrategyId = @P1

            "#;

        let rows_iter = conn
            .query(query, &[&strategy_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let rows: Vec<PortfoliosStrategy> = rows_iter.iter().map(PortfoliosStrategy::from_row).collect();

        Ok(rows)
    }
}
