use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError, types::compute_order::StrategyForOrderComputation};

pub struct StrategyModels {
    pub id: String,
    pub name: String,
    pub is_open: bool,
    pub is_discretionary: bool,
    pub is_non_discretionary: bool,
    pub is_advisory: bool,
    pub strategy_id: String,
    pub model_code: Option<String>,
}

pub struct PortfoliosInModel {
    pub portfolio_id: String,
    pub start_date: NaiveDateTime,
}

impl PortfoliosInModel {
    pub fn from_row(row: &Row) -> Self {
        Self {
            portfolio_id: row.get::<&str, _>("Id").unwrap().to_string(),
            start_date: row.get::<NaiveDateTime, _>("StartDate").unwrap(),
        }
    }
}

impl StrategyModels {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            model_code: row.get::<&str, _>("ModelCode").map(String::from),
            is_discretionary: row.get::<bool, _>("IsDiscretionary").unwrap(),
            is_non_discretionary: row.get::<bool, _>("IsNonDiscretionary").unwrap(),
            is_open: row.get::<bool, _>("IsOpen").unwrap(),
            strategy_id: row.get::<bool, _>("StrategyBankId").unwrap().to_string(),
            is_advisory: row.get::<bool, _>("IsAdvisory").unwrap(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            StrategyModels

        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_portfolios_in_model(
        conn: &mut Object<Manager>,
        model_id: &str,
    ) -> Result<Vec<PortfoliosInModel>, DatabaseError> {
        let query = r#"
            SELECT
                Id,
                StartDate
            FROM
                Portfolios
            WHERE
                ModelId = @P1

            "#;

        let rows_iter = conn
            .query(query, &[&model_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<PortfoliosInModel> = rows_iter.iter().map(PortfoliosInModel::from_row).collect();

        Ok(rows)
    }
}
