use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

pub struct StrategyBanks {
    pub dp_id: String,
    pub custodian_id: String,
    pub strategy_id: String,
}

impl StrategyBanks {
    pub fn from_row(row: &Row) -> Self {
        Self {
            custodian_id: row.get::<bool, _>("CustodianId").unwrap().to_string(),
            dp_id: row.get::<bool, _>("DpId").unwrap().to_string(),
            strategy_id: row.get::<bool, _>("StrategyId").unwrap().to_string(),
        }
    }
}

impl StrategyBanks {
    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            StrategyCustodians

        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
