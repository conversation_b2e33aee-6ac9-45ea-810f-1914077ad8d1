use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

pub struct StrategyModelSecurities {
    pub name: String,
    pub comp_name: Option<String>,
    pub isin: String,
    pub symbol: Option<String>,
    pub scrip_code: Option<String>,
    pub industry: Option<String>,
    pub exchange: String,
    pub weight: f64,
    pub is_mutual_fund: bool,
}

pub struct StrategyModelSecuritiesForOms {
    pub isin: String,
    pub exchange: String,
    pub weight: f64,
    pub is_mutual_fund: bool,
}

impl StrategyModelSecuritiesForOms {
    pub fn from_row(row: &Row) -> Self {
        Self {
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            is_mutual_fund: row.get::<bool, _>("IsMutualFund").unwrap(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            weight: row.get::<f64, _>("Weight").unwrap(),
        }
    }
}

impl StrategyModelSecurities {
    pub fn from_row(row: &Row) -> Self {
        Self {
            comp_name: row.get::<&str, _>("CompName").map(String::from),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            industry: row.get::<&str, _>("Industry").map(String::from),
            is_mutual_fund: row.get::<bool, _>("IsMutualFund").unwrap(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            scrip_code: row.get::<&str, _>("Scripcode").map(String::from),
            symbol: row.get::<&str, _>("Symbol").map(String::from),
            weight: row.get::<f64, _>("Weight").unwrap(),
        }
    }
}

impl StrategyModelSecurities {
    pub async fn get_for_compute_order_by_model_id(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            StrategyModelSecurities

        WHERE
            ModelId = @P1

        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }

    /// This Applied restricted stocks condition on StrategyModelSecurities and return based on that
    /// If not restriction is found directly return the security from the StrategyModelSecurities Row
    /// This should check for restriction at ORG level (TODO:)
    pub async fn get_for_compute_order_by_model_id_and_apply_restricted_stocks(
        conn: &mut Object<Manager>,
        id: String,
        portfolio_id: &str,
    ) -> Result<Vec<StrategyModelSecuritiesForOms>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            StrategyModelSecurities
        WHERE
            ModelId = @P1

        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<StrategyModelSecuritiesForOms> =
            rows_iter.iter().map(StrategyModelSecuritiesForOms::from_row).collect();

        Ok(rows)
    }

    pub async fn get_by_model_id_and_isin(
        conn: &mut Object<Manager>,
        isin: String,
        model_id: String,
    ) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            StrategyModelSecurities
        WHERE
            Isin = @P1
        AND
            ModelId = @P2
        "#;

        let rows_iter = conn
            .query(query, &[&isin, &model_id])
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
