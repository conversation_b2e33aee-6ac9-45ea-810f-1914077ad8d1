use std::str::FromStr;

use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::{ExecuteResult, Row};

use crate::{connection::pool::Manager, error::DatabaseError};

pub enum ReportType {
    Xml,
    OffSiteReports
}

impl std::str::FromStr for ReportType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "offsitereports" => Ok(ReportType::Xml),
            _ => Err(()),
        }
    }
}

pub struct ReportRequest {
    pub id: String,
    pub request_date: NaiveDateTime,
    pub requested_by: String,
    pub report_type: ReportType,
    pub request_payload: String,     //JSON string,
    pub status: String,              //Should be enum
    pub report_path: Option<String>, //Path to find the report data
}

impl ReportRequest {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            report_path: row.get::<&str, _>("ReportPath").map(String::from),
            report_type: ReportType::from_str(row.get::<&str, _>("ReportType").unwrap()).unwrap(),
            request_date: row.get::<NaiveDateTime, _>("RequestDate").unwrap(),
            request_payload: row.get::<&str, _>("RequestPayload").unwrap().to_string(),
            requested_by: row.get::<&str, _>("RequestedBy").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
        }
    }
}

impl ReportRequest {
    pub async fn get_by_id(conn: &mut Object<Manager>, id: &str) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            ReportRequests
        WHERE
            Id = @P1
            
    "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn update_report_path(
        conn: &mut Object<Manager>,
        id: &str,
        path: &str,
    ) -> Result<ExecuteResult, DatabaseError> {
        let query = r#"
            UPDATE
                ReportRequests
            SET
                ReportPath = @P1,
                Status = 'Successful'
            WHERE
                Id = @P2
            
            "#;

        conn.execute(query, &[&path, &id]).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn update_error(
        conn: &mut Object<Manager>,
        id: &str,
        error_message: &str,
    ) -> Result<ExecuteResult, DatabaseError> {
        let query = r#"
        UPDATE
            ReportRequests
        SET
            Status = 'Failed'
        WHERE
            Id = @P1
        
        "#;

        conn.execute(query, &[&id]).await.map_err(|e| DatabaseError::QueryError((e)))
    }
}
