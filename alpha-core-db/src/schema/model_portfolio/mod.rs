use deadpool::managed::Object;
use tiberius::{time::chrono::NaiveDateTime, Row};

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Debug)]
pub struct ModelPortfolio {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub name: String,
    pub start_date: NaiveDateTime,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub market_value: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub annual_return_irr: f64,
    pub twrr_since_inception: f64,
    pub annual_performance_twrr: f64,
    pub current_cash_balance: f64,
    pub model_id: String,
}

impl ModelPortfolio {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            start_date: row.get::<NaiveDateTime, _>("StartDate").unwrap(),
            total_capital: row.get::<f64, _>("TotalCapital").unwrap(),
            invested_capital: row.get::<f64, _>("InvestedCapital").unwrap(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
            realised_gain_loss: row.get::<f64, _>("RealisedGainLoss").unwrap(),
            unrealised_gain_loss: row.get::<f64, _>("UnRealisedGainLoss").unwrap(),
            annual_return_irr: row.get::<f64, _>("AnnualReturnIrr").unwrap(),
            twrr_since_inception: row.get::<f64, _>("TwrrSinceInception").unwrap(),
            annual_performance_twrr: row.get::<f64, _>("AnnualPerformanceTwrr").unwrap(),
            current_cash_balance: row.get::<f64, _>("CurrentCashBalance").unwrap(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            ModelPortfolios
        WHERE
            ModelId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_by_model_id(conn: &mut Object<Manager>, id: &str) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            ModelPortfolios
        WHERE
            ModelId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
