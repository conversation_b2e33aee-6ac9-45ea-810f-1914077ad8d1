use crate::connection::pool::Manager;
use crate::error::DatabaseError;
use chrono::{DateTime, Utc, NaiveDateTime};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{ExecuteResult, Query};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SystematicDeployment {
    pub id: String,
    pub r#type: String,
    pub is_active: bool,
    pub status: String,
    pub pending_installments: i32,
    pub completed_installments: i32,
    pub redemption_date: NaiveDateTime,
    pub instalment_date: NaiveDateTime,
    pub instalment_amount: f64,
    pub frequency: String,
    pub no_of_installments: i32,
    pub deployment_type: String,
    pub sell_trigger_period: i32,
    pub last_updated_date: NaiveDateTime,
    pub is_auto_sell_trigger: bool,
    pub is_auto_buy_trigger: bool,
    pub last_buy_tracker_id: Option<String>,
    pub last_sell_tracker_id: Option<String>,
    pub source_portfolio_id: Option<String>,
    pub destination_portfolio_id: Option<String>,
}
impl SystematicDeployment {
    fn from_row(row: &tiberius::Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            r#type: row.get::<&str, _>("Type").unwrap().to_string(),
            is_active: row.get::<bool, _>("IsActive").unwrap(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            pending_installments: row.get::<i32, _>("PendingInstallments").unwrap(),
            completed_installments: row.get::<i32, _>("CompletedInstallments").unwrap(),
            redemption_date: row.get::<NaiveDateTime, _>("RedemptionDate").unwrap(),
            instalment_date: row.get::<NaiveDateTime, _>("InstalmentDate").unwrap(),
            instalment_amount: row.get::<f64, _>("InstallmentAmount").unwrap(),
            frequency: row.get::<&str, _>("Frequency").unwrap().to_string(),
            no_of_installments: row.get::<i32, _>("NoOfInstallments").unwrap(),
            deployment_type: row.get::<&str, _>("DeploymentType").unwrap().to_string(),
            sell_trigger_period: row.get::<i32, _>("SellTriggerPeriod").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            is_auto_sell_trigger: row.get::<bool, _>("IsAutoSellTrigger").unwrap(),
            is_auto_buy_trigger: row.get::<bool, _>("IsAutoBuyTrigger").unwrap(),
            last_buy_tracker_id: row.get::<&str, _>("LastBuyTrackerId").map(String::from),
            last_sell_tracker_id: row.get::<&str, _>("LastSellTrackerId").map(String::from),
            source_portfolio_id: row.get::<&str, _>("SourcePortfolioId").map(String::from),
            destination_portfolio_id: row.get::<&str, _>("DestinationPortfolioId").map(String::from),
        }
    }
    pub async fn get_all_active_redemptions_for_today(
        conn: &mut Object<Manager>,
    ) -> Result<Vec<SystematicDeployment>, DatabaseError> {
        let today = Utc::today().naive_utc();

        let query = "
        SELECT * 
        FROM 
            SystematicDeployments
        WHERE 
            IsActive = 1
        AND 
            PendingInstallments > 0
        AND 
            CAST(RedemptionDate AS DATE) = @p1
        AND 
            DeploymentType = 'Systematic'
        ";

        let row_iter = conn
            .query(query, &[&today])
            .await
            .map_err(|e| {
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                return DatabaseError::QueryError(e);
            })?;

        let active_redemptions = row_iter.iter().map(Self::from_row).collect();

        Ok(active_redemptions)
    }

    pub async fn get_all_active_installments_for_today(
        conn: &mut Object<Manager>,
    ) -> Result<Vec<SystematicDeployment>, DatabaseError> {
        let today = Utc::today().naive_local();

        let query = "
        SELECT * 
        FROM 
            SystematicDeployments
        WHERE 
            IsActive = 1
        AND 
            PendingInstallments > 0
        AND 
            CAST(InstalmentDate AS DATE) = @p1
        AND 
            DeploymentType = 'Systematic'
        ";

        let row_iter = conn
            .query(query, &[&today])
            .await
            .map_err(|e| {
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                return DatabaseError::QueryError(e);
            })?;

        let active_redemptions = row_iter.iter().map(Self::from_row).collect();

        Ok(active_redemptions)
    }

    pub async fn update(&mut self) {
        let update_date = Utc::today().naive_local();
        self.last_updated_date = update_date.into();
    }

    pub async fn commit(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE 
                SystematicDeployments
            SET
                Type = @P1,
                IsActive = @P2,
                Status = @P3,
                PendingInstallments = @P4,
                CompletedInstallments = @P5,
                RedemptionDate = @P6,
                InstalmentDate = @P7,
                InstallmentAmount = @P8,
                Frequency = @P9,
                NoOfInstallments = @P10,
                DeploymentType = @P11,
                SellTriggerPeriod = @P12,
                LastUpdatedDate = @P13,
                IsAutoSellTrigger = @P14,
                IsAutoBuyTrigger = @P15,
                LastBuyTrackerId = @P16,
                LastSellTrackerId = @P17,
                SourcePortfolioId = @P18,
                DestinationPortfolioId = @P19
            WHERE
                Id = @P20
        ",
        );

        query.bind(self.r#type.clone());
        query.bind(self.is_active);
        query.bind(self.status.clone());
        query.bind(self.pending_installments);
        query.bind(self.completed_installments);
        query.bind(self.redemption_date);
        query.bind(self.instalment_date);
        query.bind(self.instalment_amount);
        query.bind(self.frequency.clone());
        query.bind(self.no_of_installments);
        query.bind(self.deployment_type.clone());
        query.bind(self.sell_trigger_period);
        query.bind(self.last_updated_date);
        query.bind(self.is_auto_sell_trigger);
        query.bind(self.is_auto_buy_trigger);
        query.bind(self.last_buy_tracker_id.clone());
        query.bind(self.last_sell_tracker_id.clone());
        query.bind(self.source_portfolio_id.clone());
        query.bind(self.destination_portfolio_id.clone());
        query.bind(self.id.clone());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }
}
