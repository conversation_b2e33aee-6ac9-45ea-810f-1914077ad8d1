use crate::storage::table::AzureStorageTable;
use azure_core::error::Error as AzureError;
use azure_data_tables::prelude::*;
use chrono::{DateTime, NaiveDate, Utc};
use futures::StreamExt;
use serde::{Deserialize, Serialize};

// Define ExchangeHolidays struct
#[derive(Debug, Serialize, Deserialize)]
pub struct ExchangeHolidays {
    #[serde(rename = "PartitionKey")]
    partition_key: String,
    #[serde(rename = "RowKey")]
    row_key: String,
}

// NSEHolidays equivalent
pub struct NSEHolidays;

impl NSEHolidays {
    pub async fn get_nse_holidays_list() -> Result<Vec<NaiveDate>, String> {
        let service = AzureStorageTable::finflo_storage();
        let client = service.get_table_client("ExchangeHolidays".to_string()).await;

        // Generate filter conditions
        let partition_key_filter = format!("PartitionKey eq 'NSE'");

        let res = client.query().filter(partition_key_filter);
        let mut streams = res.into_stream::<ExchangeHolidays>();
        let mut holidays = Vec::new();

        while let Some(resp) = streams.next().await {
            holidays.append(
                &mut resp
                    .unwrap()
                    .entities
                    .iter()
                    .map(|h| { // example: 01-Aug-2020
                        NaiveDate::parse_from_str(&h.row_key, "%d-%b-%Y").unwrap()
                    })
                    .collect(),
            );
        }

        Ok(holidays)
    }
}
