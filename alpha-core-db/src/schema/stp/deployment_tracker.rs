use crate::{connection::pool::Manager, error::DatabaseError, schema::client_order_entry::OrderStatus};
use chrono::NaiveDateTime;
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use std::cmp::PartialEq;
use std::fmt;
use tiberius::{ExecuteResult, Query, Row};

#[derive(Debug, PartialEq, Clone)]
pub enum DeploymentTrackerStatus {
    Settled,
    PartiallySettled,
}

impl DeploymentTrackerStatus {
    fn from_row(row: &Row) -> Self {
        match row.get::<&str, _>("Status").unwrap() {
            "Settled" => DeploymentTrackerStatus::Settled,
            "PartiallySettled" => DeploymentTrackerStatus::PartiallySettled,
            _ => panic!("Invalid status"),
        }
    }
}

impl fmt::Display for DeploymentTrackerStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DeploymentTrackerStatus::Settled => write!(f, "Settled"),
            DeploymentTrackerStatus::PartiallySettled => write!(f, "PartiallySettled"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DeploymentTracker {
    // pub id: String,
    // pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub deployment_setup_id: String,
    pub installment_no: i32,
    pub status: String,
    pub setup_type: String,
    pub trigger_date: NaiveDateTime,
    pub triggered_by: String,
    pub rationale: String,
    pub previous_tracker_id: Option<String>,
    pub deployment_type: String,
    // pub is_triggered: bool,
}

impl DeploymentTracker {
    pub fn from_row(row: &Row) -> Self {
        Self {
            // id: row.get::<&str, _>("Id").unwrap().to_string(),
            // created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),7
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            deployment_setup_id: row.get::<&str, _>("DeploymentSetupId").unwrap().to_string(),
            setup_type: row.get::<&str, _>("SetupType").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            trigger_date: row.get::<NaiveDateTime, _>("TriggerDate").unwrap(),
            triggered_by: row.get::<&str, _>("TriggeredBy").unwrap().to_string(),
            installment_no: row.get::<i32, _>("InstallmentNo").unwrap(),
            rationale: row.get::<&str, _>("Rationale").unwrap().to_string(),
            deployment_type: row.get::<&str, _>("DeploymentType").unwrap().to_string(),
            previous_tracker_id: row.get::<&str, _>("PreviousTrackerId").map(String::from),
            // is_triggered: row.get::<bool, _>("IsTriggered").unwrap(),
        }
    }
}

impl DeploymentTracker {
    pub async fn get_by_id(conn: &mut Object<Manager>, id: &str) -> Result<Option<Self>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                DeploymentTrackers
            WHERE
                Id = @P1
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn change_tracker_status_by_id(
        conn: &mut Object<Manager>,
        id: &str,
        status: DeploymentTrackerStatus,
    ) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE
                DeploymentTrackers
            SET
                Status = @P1
            WHERE
                Id = @P2
            ",
        );

        query.bind(status.to_string());
        query.bind(id.to_string());

        Ok(query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))?)
    }

    pub async fn commit(self, conn: &mut Object<Manager>) -> Result<String, DatabaseError> {
        let mut query = Query::new(
            "
            INSERT INTO DeploymentTrackers (
                LastUpdatedDate,
                DeploymentSetupId,
                SetupType,
                Status,
                TriggerDate,
                TriggeredBy,
                InstallmentNo,
                Rationale,
                DeploymentType,
                PreviousTrackerId
            ) OUTPUT INSERTED.Id
            VALUES (@P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10)
            ",
        );
        query.bind(self.last_updated_date);
        query.bind(self.deployment_setup_id);
        query.bind(self.setup_type);
        query.bind(self.status.to_string());
        query.bind(self.trigger_date);
        query.bind(self.triggered_by);
        query.bind(self.installment_no);
        query.bind(self.rationale);
        query.bind(self.deployment_type);
        query.bind(self.previous_tracker_id);

        // Run the query and extract the inserted Id
        let row = query
            .query(conn)
            .await
            .map_err(DatabaseError::QueryError)?
            .into_row()
            .await
            .map_err(DatabaseError::QueryError)?;

        // Extract and return the ID as String
        if let Some(row) = row {
            let id: &str = row.get("Id").ok_or(DatabaseError::InvalidColumn("Id".into()))?;
            Ok(id.to_string())
        } else {
            Err(DatabaseError::InsertionFailed)
        }
    }
}
