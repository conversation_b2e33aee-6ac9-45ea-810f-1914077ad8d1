use deadpool::managed::Object;
use futures::TryStreamExt;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError, types::compute_order::StrategyForOrderComputation};

use super::{
    client::DomicileType,
    portfolio::{portfolio_custodian::PortfolioCustodianForOms, AccountStatus, PortfolioCashPosition, PortfolioType, TradingMode},
};

pub mod deviation;

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioForOms {
    pub id: String,
    pub client_strategy_code: String,
    pub account_status: AccountStatus,
    pub market_value: f64,
    pub trading_mode: TradingMode,
    pub portfolio_type : PortfolioType,

    /// This should always have value for NRI Client
    pub broker_trading_acc_number: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ClientForOms {
    pub id: String,
    pub domicile: DomicileType,
    pub bse_star_ucc: Option<String>,
    pub client_code: String,
    pub first_name: String,
    pub middle_name: Option<String>,
    pub last_name: String,
    pub bank_account_number: Option<String>,
}

impl ClientForOms {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            bse_star_ucc: row.get::<&str, _>("BseStarUcc").map(String::from),
            domicile: match row.get::<&str, _>("Domicile").unwrap().to_lowercase().as_ref() {
                "resident" => DomicileType::Resident,
                "nonresident" => DomicileType::NonResident,
                _ => panic!("Invalid domicile type"),
            },
            first_name: row.get::<&str, _>("FirstName").unwrap().to_string(),
            middle_name: row.get::<&str, _>("MiddleName").map(String::from),
            last_name: row.get::<&str, _>("LastName").unwrap().to_string(),
            bank_account_number: row.get::<&str, _>("AccountNumber").map(String::from),
        }
    }
}

impl PortfolioForOms {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            account_status: AccountStatus::from_str(row.get::<&str, _>("AccountStatus").unwrap()).unwrap(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
            broker_trading_acc_number: row.get::<&str, _>("TradingAccountNumber").map(String::from),
            trading_mode: TradingMode::from_str(row.get::<&str, _>("TradingMode").unwrap()).unwrap(),
            portfolio_type : PortfolioType::from_str(row.get::<&str, _>("PortfolioType").unwrap()).unwrap(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioDetailsForOms {
    /// Client Details of this Portfolio
    pub client: ClientForOms,

    /// Portfolio Details
    pub portfolio: PortfolioForOms,

    /// Strategy and Model Details of this Portfolio
    pub strategy: StrategyForOrderComputation,

    /// Custodian of this Portfolio
    pub custodian: PortfolioCustodianForOms,

    /// Cash Position Of Portfolio
    pub cash_position: PortfolioCashPosition,
}

impl PortfolioDetailsForOms {
    pub fn from_row(row: &Row) -> Self {
        Self {
            client: ClientForOms::from_row(row),
            custodian: PortfolioCustodianForOms::from_row(row),
            portfolio: PortfolioForOms::from_row(row),
            strategy: StrategyForOrderComputation::from_row(row),
            cash_position: PortfolioCashPosition::from_row(row),
        }
    }
}

impl PortfolioDetailsForOms {
    pub async fn get(conn: &mut Object<Manager>, id: &str) -> Result<Option<Self>, DatabaseError> {
        let query = "
            SELECT 
                -- Portfolio Information
                p.Id as PortfolioId,
                p.ClientStrategyCode,
                p.AccountStatus,
                p.MarketValue,
                p.TradingMode,
                p.PortfolioType,

                -- Client Information
                c.Id as ClientId,
                c.Domicile,
                c.ClientCode,
                c.FirstName,
                c.MiddleName,
                c.LastName,
                c.BseStarUcc,

                -- Client Bank
                cbks.AccountNumber,
        
                -- Strategy and Model Information
                s.Id AS StrategyId,
                s.Name AS StrategyName,
                s.IsOpen,
                s.StrategyCode,
                sm.Id AS ModelId,
                sm.Name AS ModelName,
                sm.ModelCode,

                -- Custodian Information
                cc.CustodianId,
                cc.CustodyAccountNumber,
                cc.DPType,
                sc.DpID,

                -- Portfolio Broker
                cb.TradingAccountNumber,

                -- Cash
                COALESCE(CashBalance.CurrentCashBalance, 0.00) as CurrentCashBalance,
                COALESCE(CashBalance.AvailableCashBalance, 0.00) as AvailableCashBalance,
                COALESCE(CashBalance.SubmittedBuyAmount, 0.00) as SubmittedBuyAmount,
                COALESCE(CashBalance.SubmittedSellAmount, 0.00) as SubmittedSellAmount,
                COALESCE(CashBalance.HeldBuyAmount, 0.00) as HeldBuyAmount,
                COALESCE(CashBalance.HeldSellAmount, 0.00) as HeldSellAmount

            FROM 
                Portfolios p
                INNER JOIN Clients c ON p.ClientId = c.Id
                LEFT JOIN ClientBanks cbks ON p.ClientId = cbks.ClientId
                INNER JOIN StrategyModels sm ON p.ModelId = sm.Id
                INNER JOIN Strategies s ON s.Id = sm.StrategyId
                INNER JOIN ClientCustodians cc ON p.Id = cc.PortfolioId
                INNER JOIN StrategyCustodians sc ON sc.CustodianId = cc.CustodianId 
                    AND sc.StrategyId = sm.StrategyId
                LEFT JOIN ClientBrokers cb ON cb.PortfolioId = p.Id
                OUTER APPLY dbo.GetPortfolioCashBalance(p.Id) CashBalance
            WHERE 
                p.Id = @P1
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?
            .into_row()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                DatabaseError::QueryError((e))
            })?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_bulk(conn: &mut Object<Manager>, portfolio_ids: &[String]) -> Result<Vec<Self>, DatabaseError> {
        if portfolio_ids.is_empty() {
            return Ok(Vec::new());
        }

        // Create a comma-separated list of quoted portfolio IDs
        let ids_list = portfolio_ids
            .iter()
            .map(|id| format!("'{}'", id))
            .collect::<Vec<_>>()
            .join(",");

        // Modify the query to use the IN clause with the list
        let query = format!(
            "
            SELECT 
                -- Portfolio Information
                p.Id as PortfolioId,
                p.ClientStrategyCode,
                p.AccountStatus,
                p.MarketValue,
                p.TradingMode,
                p.PortfolioType,

                -- Client Information
                c.Id as ClientId,
                c.Domicile,
                c.ClientCode,
                c.FirstName,
                c.MiddleName,
                c.LastName,
                c.BseStarUcc,

                -- Client Bank
                cbks.AccountNumber,
        
                -- Strategy and Model Information
                s.Id AS StrategyId,
                s.Name AS StrategyName,
                s.IsOpen,
                s.StrategyCode,
                sm.Id AS ModelId,
                sm.Name AS ModelName,
                sm.ModelCode,

                -- Custodian Information
                cc.CustodianId,
                cc.CustodyAccountNumber,
                cc.DPType,
                sc.DpID,

                -- Portfolio Broker
                cb.TradingAccountNumber,

                -- Cash
                COALESCE(CashBalance.CurrentCashBalance, 0.00) as CurrentCashBalance,
                COALESCE(CashBalance.AvailableCashBalance, 0.00) as AvailableCashBalance,
                COALESCE(CashBalance.SubmittedBuyAmount, 0.00) as SubmittedBuyAmount,
                COALESCE(CashBalance.SubmittedSellAmount, 0.00) as SubmittedSellAmount,
                COALESCE(CashBalance.HeldBuyAmount, 0.00) as HeldBuyAmount,
                COALESCE(CashBalance.HeldSellAmount, 0.00) as HeldSellAmount

            FROM 
                Portfolios p
                INNER JOIN Clients c ON p.ClientId = c.Id
                LEFT JOIN ClientBanks cbks ON p.ClientId = cbks.ClientId AND p.Id = cbks.PortfolioId
                INNER JOIN StrategyModels sm ON p.ModelId = sm.Id
                INNER JOIN Strategies s ON s.Id = sm.StrategyId
                INNER JOIN ClientCustodians cc ON p.Id = cc.PortfolioId
                INNER JOIN StrategyCustodians sc ON sc.CustodianId = cc.CustodianId 
                    AND sc.StrategyId = sm.StrategyId
                LEFT JOIN ClientBrokers cb ON cb.PortfolioId = p.Id
                OUTER APPLY dbo.GetPortfolioCashBalance(p.Id) CashBalance
            WHERE 
                p.Id IN ({})
        ",
            ids_list
        );

        // Execute the query without parameters since we've built the SQL directly
        // Execute the query and collect all rows
        let mut stream = conn.query(query, &[]).await.map_err(|e| {
            println!("Query error: {:?}", e);
            DatabaseError::QueryError((e))
        })?;

        let mut portfolios = Vec::new();

        // Process all rows from the result set
        while let Some(row) = stream.try_next().await.map_err(|e| {
            println!("Row fetch error: {:?}", e);
            DatabaseError::QueryError((e))
        })? {
            let row = &row.into_row();
            if let Some(row) = row {
                portfolios.push(Self::from_row(row));
            }
        }

        // Check if no results were returned
        if portfolios.is_empty() {
            return Err(DatabaseError::RowNotFound());
        }

        // Verify we have the same number of results as requested IDs
        if portfolios.len() != portfolio_ids.len() {
            return Err(DatabaseError::RowNotFound());
        }

        Ok(portfolios)
    }
}

#[cfg(test)]
mod tests {

    use crate::{
        connection::{connect_to_master_data, connect_to_mssql},
        schema::oms::PortfolioDetailsForOms,
    };

    #[tokio::test]
    async fn get_ptf_essentials() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let id = String::from("6dbd642bc96948e9b5bc750967495f95");
        let portfolio_with_cash = PortfolioDetailsForOms::get(&mut pool_conn, &id).await.unwrap();

        println!("{:?}", portfolio_with_cash);
    }
}
