use crate::{
    connection::pool::Manager,
    error::DatabaseError,
    schema::{
        investment::InvestmentsForDeviation,
        portfolio::{AccountStatus, PortfolioCashPosition},
    },
};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioDetailsForDeviations {
    pub client_id: String,
    pub client_name: String,
    pub client_code: String,
    pub portfolio_id: String,
    pub client_strategy_code: String,
    pub portfolio_name: String,
    pub current_cash_balance: f64,
    pub market_value: f64,
    pub investments: Vec<InvestmentsForDeviation>,
    pub cash_balance: PortfolioCashPosition,
    pub account_status: AccountStatus,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioDetailsForDeviationsForSingleSecurity {
    pub client_id: String,
    pub client_name: String,
    pub client_code: String,
    pub portfolio_id: String,
    pub client_strategy_code: String,
    pub portfolio_name: String,
    pub current_cash_balance: f64,
    pub market_value: f64,
    pub investments: Option<InvestmentsForDeviation>,
    pub cash_balance: PortfolioCashPosition,
    pub account_status: AccountStatus,
}

impl PortfolioDetailsForDeviations {
    pub async fn get_by_csc(
        conn: &mut Object<Manager>,
        client_strategy_codes: &[String],
        model_id: &str,
    ) -> Result<Vec<Self>, DatabaseError> {
        let formatted_codes = client_strategy_codes
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
SELECT
    (SELECT 
        CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS clientName,
        c.Id as clientId,
        c.ClientCode as clientCode,
        p.Id as portfolioId,
        p.ClientStrategyCode as clientStrategyCode,
        p.Name as portfolioName,
        p.CurrentCashBalance as currentCashBalance,
         p.AccountStatus as accountStatus,
        JSON_QUERY(ISNULL((
            SELECT 
                ROUND(CAST(ISNULL(CurrentCashBalance, 0.00) as decimal(18,2)), 2) as currentCashBalance,
                ROUND(CAST(ISNULL(AvailableCashBalance, 0.00) as decimal(18,2)), 2) as availableCashBalance,
                ROUND(CAST(ISNULL(SubmittedBuyAmount, 0.00) as decimal(18,2)), 2) as submittedBuyAmount,
                ROUND(CAST(ISNULL(SubmittedSellAmount, 0.00) as decimal(18,2)), 2) as submittedSellAmount,
                ROUND(CAST(ISNULL(HeldBuyAmount, 0.00) as decimal(18,2)), 2) as heldBuyAmount,
                ROUND(CAST(ISNULL(HeldSellAmount, 0.00) as decimal(18,2)), 2) as heldSellAmount
            FROM dbo.GetPortfolioCashBalance(p.Id)
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        ), 
        (SELECT 
            CAST(0.00 as decimal(18,2)) as currentCashBalance,
            CAST(0.00 as decimal(18,2)) as availableCashBalance,
            CAST(0.00 as decimal(18,2)) as submittedBuyAmount,
            CAST(0.00 as decimal(18,2)) as submittedSellAmount,
            CAST(0.00 as decimal(18,2)) as heldBuyAmount,
            CAST(0.00 as decimal(18,2)) as heldSellAmount
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        )) as cashBalance,
        ROUND(CAST(p.MarketValue as decimal(18,2)), 2) as marketValue,
        ISNULL((
            SELECT 
                i.Name as investmentMarketValue,
                i.Isin as isin,
                i.Symbol as symbol,
                i.Exchange as exchange,
                i.SecurityType as securityType,
                ROUND(CAST(i.CurrentHolding as decimal(18,2)), 2) as currentHolding,
                ROUND(CAST(i.MarketValue as decimal(18,2)), 2) as marketValue,
                0 as weight
            FROM Investments i
            WHERE i.PortfolioId = p.Id
            FOR JSON PATH
        ), '[]') as investments
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
FROM
    Clients c 
    JOIN Portfolios p ON c.Id = p.ClientId
WHERE
    p.ModelId = @P1
    AND p.ClientStrategyCode IN ({})
        ",
            formatted_codes
        );

        let rows_iter = conn
            .query(query, &[&model_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let portfolios = rows_iter
            .into_iter()
            .filter_map(|row| {
                let json_str: Option<String> = row.get::<&str, _>(0).map(String::from);
                json_str.and_then(|js| serde_json::from_str(&js).ok())
            })
            .collect::<Vec<Self>>();

        Ok(portfolios)
    }

    pub async fn get_by_csc_for_single_security(
        conn: &mut Object<Manager>,
        client_strategy_codes: &[String],
        model_id: &str,
        isin: &str,
    ) -> Result<Vec<PortfolioDetailsForDeviationsForSingleSecurity>, DatabaseError> {
        let formatted_codes = client_strategy_codes
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
SELECT
    (SELECT 
        CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS clientName,
        c.Id as clientId,
        c.ClientCode as clientCode,
        p.Id as portfolioId,
        p.ClientStrategyCode as clientStrategyCode,
        p.Name as portfolioName,
        p.CurrentCashBalance as currentCashBalance,
         p.AccountStatus as accountStatus,
        JSON_QUERY(ISNULL((
            SELECT 
                ROUND(CAST(ISNULL(CurrentCashBalance, 0.00) as decimal(18,2)), 2) as currentCashBalance,
                ROUND(CAST(ISNULL(AvailableCashBalance, 0.00) as decimal(18,2)), 2) as availableCashBalance,
                ROUND(CAST(ISNULL(SubmittedBuyAmount, 0.00) as decimal(18,2)), 2) as submittedBuyAmount,
                ROUND(CAST(ISNULL(SubmittedSellAmount, 0.00) as decimal(18,2)), 2) as submittedSellAmount,
                ROUND(CAST(ISNULL(HeldBuyAmount, 0.00) as decimal(18,2)), 2) as heldBuyAmount,
                ROUND(CAST(ISNULL(HeldSellAmount, 0.00) as decimal(18,2)), 2) as heldSellAmount
            FROM dbo.GetPortfolioCashBalance(p.Id)
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        ), 
        (SELECT 
            CAST(0.00 as decimal(18,2)) as currentCashBalance,
            CAST(0.00 as decimal(18,2)) as availableCashBalance,
            CAST(0.00 as decimal(18,2)) as submittedBuyAmount,
            CAST(0.00 as decimal(18,2)) as submittedSellAmount,
            CAST(0.00 as decimal(18,2)) as heldBuyAmount,
            CAST(0.00 as decimal(18,2)) as heldSellAmount
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
        )) as cashBalance,
        ROUND(CAST(p.MarketValue as decimal(18,2)), 2) as marketValue,
        JSON_QUERY((
                SELECT TOP 1
                    i.Name as investmentMarketValue,
                    i.Isin as isin,
                    i.Symbol as symbol,
                    i.Exchange as exchange,
                    i.SecurityType as securityType,
                    ROUND(CAST(i.CurrentHolding as decimal(18,2)), 2) as currentHolding,
                    ROUND(CAST(i.MarketValue as decimal(18,2)), 2) as marketValue,
                    CAST(0.00 as decimal(18,2)) as weight
                FROM Investments i
                WHERE 
                    i.PortfolioId = p.Id
                AND
                    i.Isin = @P2
                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
            )) as investments
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
FROM
    Clients c 
    JOIN Portfolios p ON c.Id = p.ClientId
WHERE
    p.ModelId = @P1
    AND p.ClientStrategyCode IN ({})
        ",
            formatted_codes
        );

        let rows_iter = conn
            .query(query, &[&model_id, &isin])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let portfolios = rows_iter
            .into_iter()
            .filter_map(|row| {
                let json_str: Option<String> = row.get::<&str, _>(0).map(String::from);
                json_str.and_then(|js| serde_json::from_str(&js).ok())
            })
            .collect::<Vec<PortfolioDetailsForDeviationsForSingleSecurity>>();

        Ok(portfolios)
    }

    /// Gets all the portfolios in a model with investments and cashBalance
    pub async fn get(conn: &mut Object<Manager>, model_id: &str) -> Result<Vec<Self>, DatabaseError> {
        let query = format!(
            "
            SELECT
                (SELECT 
                    CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS clientName,
                    c.Id as clientId,
                    c.ClientCode as clientCode,
                    p.Id as portfolioId,
                    p.ClientStrategyCode as clientStrategyCode,
                    p.Name as portfolioName,
                    p.CurrentCashBalance as currentCashBalance,
                    p.AccountStatus as accountStatus,
                    JSON_QUERY(ISNULL((
                        SELECT 
                            ROUND(CAST(ISNULL(CurrentCashBalance, 0.00) as decimal(18,2)), 2) as currentCashBalance,
                            ROUND(CAST(ISNULL(AvailableCashBalance, 0.00) as decimal(18,2)), 2) as availableCashBalance,
                            ROUND(CAST(ISNULL(SubmittedBuyAmount, 0.00) as decimal(18,2)), 2) as submittedBuyAmount,
                            ROUND(CAST(ISNULL(SubmittedSellAmount, 0.00) as decimal(18,2)), 2) as submittedSellAmount,
                            ROUND(CAST(ISNULL(HeldBuyAmount, 0.00) as decimal(18,2)), 2) as heldBuyAmount,
                            ROUND(CAST(ISNULL(HeldSellAmount, 0.00) as decimal(18,2)), 2) as heldSellAmount
                        FROM dbo.GetPortfolioCashBalance(p.Id)
                        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                    ), 
                    (SELECT 
                        CAST(0.00 as decimal(18,2)) as currentCashBalance,
                        CAST(0.00 as decimal(18,2)) as availableCashBalance,
                        CAST(0.00 as decimal(18,2)) as submittedBuyAmount,
                        CAST(0.00 as decimal(18,2)) as submittedSellAmount,
                        CAST(0.00 as decimal(18,2)) as heldBuyAmount,
                        CAST(0.00 as decimal(18,2)) as heldSellAmount
                    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
                    )) as cashBalance,
                    ROUND(CAST(p.MarketValue as decimal(18,2)), 2) as marketValue,
                    ISNULL((
                        SELECT 
                            i.Name as investmentMarketValue,
                            i.Isin as isin,
                            i.Symbol as symbol,
                            i.Exchange as exchange,
                            i.SecurityType as securityType,
                            ROUND(CAST(i.CurrentHolding as decimal(18,2)), 2) as currentHolding,
                            ROUND(CAST(i.MarketValue as decimal(18,2)), 2) as marketValue,
                            CAST(0.00 as decimal(18,2)) as weight
                        FROM Investments i
                        WHERE i.PortfolioId = p.Id
                        FOR JSON PATH
                    ), '[]') as investments
                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
            FROM
                Clients c 
                JOIN Portfolios p ON c.Id = p.ClientId
            WHERE
                            p.ModelId = @P1

        "
        );

        let rows_iter = conn
            .query(query, &[&model_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let portfolios = rows_iter
            .into_iter()
            .map(|row| {
                let json_str: String = row.get::<&str, _>(0).map(String::from).unwrap();
                let res: Self = serde_json::from_str(&json_str).unwrap();
                return res;
            })
            .collect::<Vec<Self>>();

        Ok(portfolios)
    }

    /// Gets all the portfolios in a model with investment for the isin and cashBalance
    pub async fn get_for_single_security(
        conn: &mut Object<Manager>,
        model_id: &str,
        isin: &str,
    ) -> Result<Vec<PortfolioDetailsForDeviationsForSingleSecurity>, DatabaseError> {
        let query = format!(
            "
            SELECT
                (SELECT 
                    CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS clientName,
                    c.Id as clientId,
                    c.ClientCode as clientCode,
                    p.Id as portfolioId,
                    p.ClientStrategyCode as clientStrategyCode,
                    p.Name as portfolioName,
                    p.CurrentCashBalance as currentCashBalance,
                    p.AccountStatus as accountStatus,
                    JSON_QUERY(ISNULL((
                        SELECT 
                            ROUND(CAST(ISNULL(CurrentCashBalance, 0.00) as decimal(18,2)), 2) as currentCashBalance,
                            ROUND(CAST(ISNULL(AvailableCashBalance, 0.00) as decimal(18,2)), 2) as availableCashBalance,
                            ROUND(CAST(ISNULL(SubmittedBuyAmount, 0.00) as decimal(18,2)), 2) as submittedBuyAmount,
                            ROUND(CAST(ISNULL(SubmittedSellAmount, 0.00) as decimal(18,2)), 2) as submittedSellAmount,
                            ROUND(CAST(ISNULL(HeldBuyAmount, 0.00) as decimal(18,2)), 2) as heldBuyAmount,
                            ROUND(CAST(ISNULL(HeldSellAmount, 0.00) as decimal(18,2)), 2) as heldSellAmount
                        FROM dbo.GetPortfolioCashBalance(p.Id)
                        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                    ), 
                    (SELECT 
                        CAST(0.00 as decimal(18,2)) as currentCashBalance,
                        CAST(0.00 as decimal(18,2)) as availableCashBalance,
                        CAST(0.00 as decimal(18,2)) as submittedBuyAmount,
                        CAST(0.00 as decimal(18,2)) as submittedSellAmount,
                        CAST(0.00 as decimal(18,2)) as heldBuyAmount,
                        CAST(0.00 as decimal(18,2)) as heldSellAmount
                    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
                    )) as cashBalance,
                    ROUND(CAST(p.MarketValue as decimal(18,2)), 2) as marketValue,
                    JSON_QUERY((
                            SELECT TOP 1
                                i.Name as investmentMarketValue,
                                i.Isin as isin,
                                i.Symbol as symbol,
                                i.Exchange as exchange,
                                i.SecurityType as securityType,
                                ROUND(CAST(i.CurrentHolding as decimal(18,2)), 2) as currentHolding,
                                ROUND(CAST(i.MarketValue as decimal(18,2)), 2) as marketValue,
                                CAST(0.00 as decimal(18,2)) as weight
                            FROM Investments i
                            WHERE 
                                i.PortfolioId = p.Id
                            AND
                                i.Isin = @P2
                            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                        )) as investments
                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER)
            FROM
                Clients c 
                JOIN Portfolios p ON c.Id = p.ClientId
            WHERE
                            p.ModelId = @P1

        "
        );

        let rows_iter = conn
            .query(query, &[&model_id, &isin])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let portfolios = rows_iter
            .into_iter()
            .map(|row| {
                let json_str: String = row.get::<&str, _>(0).map(String::from).unwrap();
                let res: PortfolioDetailsForDeviationsForSingleSecurity = serde_json::from_str(&json_str).unwrap();
                return res;
            })
            .collect::<Vec<PortfolioDetailsForDeviationsForSingleSecurity>>();

        Ok(portfolios)
    }
}
