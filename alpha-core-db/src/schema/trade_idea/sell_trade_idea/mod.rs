use chrono::Utc;
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{time::chrono::NaiveDateTime, Row};

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Serialize, Deserialize, Debug)]
pub struct SellTradeIdeas {
    pub id: String,
    pub security_name: String,
    pub symbol: String,
    pub isin: String,
    pub exchange: String,
    pub security_type: String,
    pub sell_net_change: f64,
    pub sell_price_from: f64,
    pub sell_price_to: f64,
    pub description: String,
    pub file_path: String,
    pub status: String,
    pub approved_by: Option<String>,
    pub strategy_id: String,
    pub model_id: String,
    pub change: f64,
    pub is_sell_all: bool,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
}

impl SellTradeIdeas {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            security_name: row.get::<&str, _>("SecurityName").unwrap().to_string(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            security_type: row.get::<&str, _>("SecurityType").unwrap().to_string(),
            sell_price_from: row.get::<f64, _>("SellPriceFrom").unwrap(),
            sell_price_to: row.get::<f64, _>("SellPriceTo").unwrap(),
            change: row.get::<f64, _>("Change").unwrap(),
            sell_net_change: row.get::<f64, _>("SellNetChange").unwrap(),
            description: row.get::<&str, _>("Description").unwrap().to_string(),
            file_path: row.get::<&str, _>("FilePath").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            approved_by: row.get::<&str, _>("ApprovedBy").map(String::from),
            strategy_id: row.get::<&str, _>("StrategyId").unwrap().to_string(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            is_sell_all: row.get::<bool, _>("IsSellAll").unwrap(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            SellTradeIdeas
        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn update_on_create_orders(
        conn: &mut Object<Manager>,
        id: String,
        executed_by: &str,
    ) -> Result<Option<Self>, DatabaseError> {
        let time = Utc::now().naive_utc();
        let query = r#"
            UPDATE
                SellTradeIdeas
            SET
                Status = 'Order-Drafted',
                ExecutedBy = @P1,
                ExecutedTime = @P2
            WHERE
                Id = @P3

        "#;

        let rows_iter = conn
            .query(query, &[&executed_by, &time, &id])
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            SellTradeIdeas    
        "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;
        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }
}

#[cfg(test)]
mod tests {

    use crate::{
        connection::{connect_to_master_data, connect_to_mssql},
        schema::{client::Client, trade_idea::sell_trade_idea::SellTradeIdeas},
    };

    #[tokio::test]
    async fn get_sell_trade_idea() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let master_data_pool = connect_to_master_data(1).await;
        let mut master_data_pool_conn = master_data_pool.get().await.unwrap();
        let id = String::from("0c7706cf278c4c07a8a31c12ba83ac8d");
        let tradeidea = SellTradeIdeas::get_all(&mut pool_conn).await.unwrap();
        println!("{:?}", tradeidea);
    }
}
