use crate::{
    connection::pool::Manager,
    error::DatabaseError,
    schema::{
        capital_register::PortfolioCapitalRegister,
        cash_ledger::PortfolioCashLedger,
        client_order_entry::{TransactionSubType, TransactionType},
    },
};
use chrono::NaiveDateTime;
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use tiberius::Row;

use super::Portfolio;

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioServiceSTP {
    pub id: String,
    pub client_id: String,
    pub model_id: String,
    pub market_value: f64,
}

impl PortfolioServiceSTP {
    fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
        }
    }

    pub async fn get_portfolio_by_id_for_stp(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<PortfolioServiceSTP, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            Portfolios
        WHERE
            Id = @P1
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        Ok(Self::from_row(&rows_iter.unwrap()))
    }

    pub async fn withdraw_capital_with_desc(
        conn: &mut Object<Manager>,
        portfolio_id: String,
        amount: f64,
        description: String,
        is_model_portfolio: bool,
    ) -> Result<(), DatabaseError> {
        let portfolio = Portfolio::get(conn, portfolio_id.clone()).await?;

        let portfolio_ledger_entry = PortfolioCashLedger {
            portfolio_id: Some(portfolio_id.clone()),
            transaction_date: chrono::Local::now().naive_local(),
            settlement_date: chrono::Local::now().naive_local(),
            transaction_type: TransactionType::Debit,
            transaction_sub_type: TransactionSubType::CapitalOut,
            amount,
            txn_ref_id: Some(String::new()),
            description: Some(description.to_string()),
            created_date: chrono::Local::now().naive_local(),
            last_updated_date: chrono::Local::now().naive_local(),
            is_model_portfolio,

            ..Default::default()
        };
        portfolio_ledger_entry.insert(conn).await?;

        let mut portfolio_register_entry = PortfolioCapitalRegister {
            portfolio_id: Some(portfolio_id.clone()),
            transaction_date: chrono::Local::now().naive_local(),
            settlement_date: chrono::Local::now().naive_local(),
            transaction_type: TransactionType::Outflow,
            transaction_sub_type: TransactionSubType::CapitalOut,
            amount,
            txn_ref_id: Some(String::new()),
            description: description,
            is_model_portfolio,
            ..Default::default()
        };
        // configure running_balance, modelportfolio_id
        if let Err(err) = portfolio_register_entry.entry(conn).await {
            return Err(err);
        }

        Ok(())
    }

    pub async fn add_capital_with_desc(
        conn: &mut Object<Manager>,
        portfolio_id: String,
        amount: f64,
        description: String,
        is_model_portfolio: bool,
    ) -> Result<(), DatabaseError> {
        let portfolio = Portfolio::get(conn, portfolio_id.clone()).await?.unwrap();
        let portfolio_name = portfolio.name.clone();

        let portfolio_ledger_entry = PortfolioCashLedger {
            portfolio_id: Some(portfolio_id.clone()),
            transaction_date: chrono::Local::now().naive_local(),
            settlement_date: chrono::Local::now().naive_local(),
            transaction_type: TransactionType::Debit,
            transaction_sub_type: TransactionSubType::CapitalOut,
            amount,
            txn_ref_id: Some(String::new()),
            description: Some(description.to_string()),
            created_date: chrono::Local::now().naive_local(),
            last_updated_date: chrono::Local::now().naive_local(),
            is_model_portfolio,

            ..Default::default()
        };
        portfolio_ledger_entry.insert(conn).await?;

        let portfolio_register_entry = PortfolioCapitalRegister {
            portfolio_id: Some(portfolio_id.clone()),
            transaction_date: chrono::Local::now().naive_local(),
            settlement_date: chrono::Local::now().naive_local(),
            transaction_type: TransactionType::Outflow,
            transaction_sub_type: TransactionSubType::CapitalOut,
            amount,
            txn_ref_id: Some(String::new()),
            description: description,
            is_model_portfolio,
            // todo: check if followings are correct
            id: String::from(""),
            running_balance: 0_f64,
            modelportfolio_id: None,
        };
        portfolio_register_entry.insert(conn).await?;

        Ok(())
    }
}
