use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

/// The Table Name in ClientBrokers
/// Client Can Have multiple brokers mapped
/// But one portfolio will only have one broker
pub struct PortfolioBroker {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub cp_code: Option<String>,
    pub trading_account_number: String,
    pub portfolio_id: String,
    pub broker_id: String,
    pub client_id: String,
    pub client_custodian_id: String,
}

impl PortfolioBroker {
    pub fn from_row(row: &Row) -> Self {
        Self {
            broker_id: row.get::<&str, _>("BrokerId").unwrap().to_string(),
            client_custodian_id: row.get::<&str, _>("ClientCustodianId").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            cp_code: row.get::<&str, _>("CPCode").map(String::from),
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            trading_account_number: row.get::<&str, _>("TradingAccountNumber").unwrap().to_string(),
        }
    }
}

impl PortfolioBroker {
    pub async fn get_by_portfolio_id(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            ClientBrokers
        WHERE
            PortfolioId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
