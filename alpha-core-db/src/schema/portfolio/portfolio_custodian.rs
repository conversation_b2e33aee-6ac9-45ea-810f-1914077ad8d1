use chrono::NaiveDateTime;
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tracing_subscriber::registry::Data;
use std::str::FromStr;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Debug, Serialize, Deserialize)]
pub enum DpType {
    NSDL,
    CDSL,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum ModeOfHolding {
    Single,
    Joint,
    AOS,
}

impl FromStr for DpType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "NSDL" => Ok(DpType::NSDL),
            "CDSL" => Ok(DpType::CDSL),
            _ => Err(()),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioCustodian {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub updated_date: NaiveDateTime,
    pub custodian_id: String,
    pub custody_account_number: String,
    pub dp_type: DpType,
    pub client_id: String,
    pub portfolio_id: String,
    pub mode_of_holding: ModeOfHolding,
    pub from_date: Option<NaiveDateTime>,
    pub to_date: Option<NaiveDateTime>,
    pub second_holder_name: Option<String>,
    pub third_holder_name: Option<String>,
    pub second_holder_pan: Option<String>,
    pub third_holder_pan: Option<String>,
    pub second_holder_phone: Option<String>,
    pub third_holder_phone: Option<String>,
    pub second_holder_email: Option<String>,
    pub third_holder_email: Option<String>,
    pub mfucc_demat: Option<String>,
    pub mfucc_physical: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioCustodianForOms {
    pub custodian_id: String,
    pub custody_account_number: String,
    pub dp_type: DpType,
    pub dp_id: String,
    pub client_id: String,
    pub portfolio_id: String,
}

impl PortfolioCustodianForOms {
    pub fn from_row(row: &Row) -> Self {
        Self {
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            custodian_id: row.get::<&str, _>("CustodianId").unwrap().to_string(),
            custody_account_number: row.get::<&str, _>("CustodyAccountNumber").unwrap().to_string(),
            dp_id: row.get::<&str, _>("DpID").unwrap().to_string(),
            dp_type: DpType::from_str(row.get::<&str, _>("DPType").unwrap()).unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
        }
    }
}

impl PortfolioCustodian {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            updated_date: row.get::<NaiveDateTime, _>("UpdatedDate").unwrap(),
            custodian_id: row.get::<&str, _>("CustodianId").unwrap().to_string(),
            custody_account_number: row.get::<&str, _>("CustodyAccountNumber").unwrap().to_string(),
            dp_type: DpType::from_str(row.get::<&str, _>("DPType").unwrap()).unwrap(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            mode_of_holding: ModeOfHolding::Single,
            from_date: row.get::<NaiveDateTime, _>("FromDate"),
            to_date: row.get::<NaiveDateTime, _>("ToDate"),
            second_holder_name: row.get::<&str, _>("SecondHolderName").map(String::from),
            third_holder_name: row.get::<&str, _>("ThirdHolderName").map(String::from),
            second_holder_pan: row.get::<&str, _>("SecondHolderPAN").map(String::from),
            third_holder_pan: row.get::<&str, _>("ThirdHolderPAN").map(String::from),
            second_holder_phone: row.get::<&str, _>("SecondHolderPhone").map(String::from),
            third_holder_phone: row.get::<&str, _>("ThirdHolderPhone").map(String::from),
            second_holder_email: row.get::<&str, _>("SecondHolderEmail").map(String::from),
            third_holder_email: row.get::<&str, _>("ThirdHolderEmail").map(String::from),
            mfucc_demat: row.get::<&str, _>("MFUCCDemat").map(String::from),
            mfucc_physical: row.get::<&str, _>("MFUCCTransfer").map(String::from),
        }
    }

    pub async fn get_by_portfolio_id_for_oms(
        conn: &mut Object<Manager>,
        portfolio_id: String,
    ) -> Result<Option<PortfolioCustodianForOms>, DatabaseError> {
        let query = r#"
            SELECT
            cc.PortfolioId,
            cc.ClientId,
            cc.CustodianId,
            cc.CustodyAccountNumber,
            cc.DPType,
            sc.DpID
        FROM
            ClientCustodians cc
        JOIN    
            Portfolios p ON p.Id = cc.PortfolioId
        JOIN
            StrategyModels sm ON p.ModelId = sm.Id
        JOIN
            StrategyCustodians sc ON sc.CustodianId = cc.CustodianId
        WHERE
            cc.PortfolioId = @P1
            and
            sc.StrategyId = sm.StrategyId
        "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(PortfolioCustodianForOms::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_portfolio_dp_id(
        conn: &mut Object<Manager>,
        portfolio_id: String,
    ) -> Result<Option<String>, DatabaseError> {
        let query = r#"
            SELECT
            sc.DpID
        FROM
            ClientCustodians cc
        JOIN    
            Portfolios p ON p.Id = cc.PortfolioId
        JOIN
            StrategyModels sm ON p.ModelId = sm.Id
        JOIN
            StrategyCustodians sc ON sc.CustodianId = cc.CustodianId
        WHERE
            cc.PortfolioId = @P1
            and
            sc.StrategyId = sm.StrategyId
        "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(rows_iter.unwrap().get::<&str, _>("DpID").unwrap().to_string()))
        }
    }
}
