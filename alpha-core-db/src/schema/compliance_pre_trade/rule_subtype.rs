use tiberius::Row;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ComplianceRuleSubType {
    pub rule_type_id: String,
    pub sub_type_name: String,
    pub is_active: bool,
}

impl ComplianceRuleSubType {
    pub fn from_row(row: &Row) -> ComplianceRuleSubType {
        ComplianceRuleSubType {
            rule_type_id: row.get::<&str, _>("RuleTypeId").unwrap().to_string(),
            sub_type_name: row.get::<&str, _>("SubTypeName").unwrap().to_string(),
            is_active: row.get::<bool, _>("IsActive").unwrap(),
        }
    }
}
