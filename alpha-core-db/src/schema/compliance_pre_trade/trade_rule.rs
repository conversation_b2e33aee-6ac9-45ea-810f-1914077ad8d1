use std::fmt::Display;

use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use super::{rule_subtype::ComplianceRuleSubType, rule_type::PreTradeRuleType, value_type::ValueType};
use crate::connection::pool::Manager;
use crate::error::DatabaseError;
use std::str::FromStr;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PreTradeLevel {
    Client,
    Strategy,
    Portfolio,
}

impl Display for PreTradeLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Client => writeln!(f, "Client"),
            Self::Strategy => writeln!(f, "Strategy"),
            Self::Portfolio => writeln!(f, "Portfolio"),
        }
    }
}

impl FromStr for PreTradeLevel {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Client" => Ok(Self::Client),
            "Strategy" => Ok(Self::Strategy),
            "Portfolio" => Ok(Self::Portfolio),
            _ => Err(()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompliancePreTradeRules {
    pub id: String,
    pub rule_type_id: String,
    pub rule_sub_type_id: Option<String>,
    pub level: PreTradeLevel,
    pub level_value: String,
    pub value_type: ValueType,
    pub value: String,
    pub rule_sub_type_value_explicit: String,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompliancePreTradeRuleWithRuleTypes {
    pub id: String,
    pub level: PreTradeLevel,
    pub level_value: String,
    pub value_type: ValueType,
    pub value: String,
    pub rule_sub_type_value_explicit: Option<String>,
    pub is_active: bool,
    pub rule_type_name: PreTradeRuleType,
    pub rule_sub_type: Option<ComplianceRuleSubType>,
}

impl CompliancePreTradeRuleWithRuleTypes {
    pub fn from_row(row: &Row) -> CompliancePreTradeRuleWithRuleTypes {
        CompliancePreTradeRuleWithRuleTypes {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            level: PreTradeLevel::from_str(row.get("Level").unwrap()).unwrap(),
            level_value: row.get::<&str, _>("LevelValue").unwrap().to_string(),
            value_type: ValueType::from_str(row.get::<&str, _>("ValueType").unwrap()).unwrap(),
            value: row.get::<&str, _>("Value").unwrap().to_string(),
            rule_sub_type_value_explicit: row.get::<&str, _>("RuleSubTypeValueExplicit").map(String::from),
            is_active: row.get::<bool, _>("IsActive").unwrap(),
            rule_type_name: PreTradeRuleType::from_str(row.get("RuleTypeName").unwrap()).unwrap(),
            rule_sub_type: if row.get::<&str, _>("RuleSubTypeId").is_some() {
                Some(ComplianceRuleSubType::from_row(row))
            } else {
                None
            },
        }
    }
}

impl CompliancePreTradeRules {
    pub fn from_row(row: &Row) -> CompliancePreTradeRules {
        CompliancePreTradeRules {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            rule_type_id: row.get::<&str, _>("RuleTypeId").unwrap().to_string(),
            rule_sub_type_id: row.get::<&str, _>("RuleSubTypeId").map(String::from),
            level: PreTradeLevel::from_str(row.get("Level").unwrap()).unwrap(),
            level_value: row.get::<&str, _>("LevelValue").unwrap().to_string(),
            value_type: ValueType::from_str(row.get::<&str, _>("ValueType").unwrap()).unwrap(),
            value: row.get::<&str, _>("Value").unwrap().to_string(),
            rule_sub_type_value_explicit: row.get::<&str, _>("RuleSubTypeValueExplicit").unwrap().to_string(),
            is_active: row.get::<bool, _>("IsActive").unwrap(),
        }
    }

    pub async fn get_for_portfolio(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
    ) -> Result<Vec<CompliancePreTradeRuleWithRuleTypes>, DatabaseError> {
        let query = r#"
                SELECT
                    *
                FROM
                    CompliancePreTradeRules cptr
                LEFT JOIN
                    ComplianceRuleTypes crt ON cptr.ruleTypeId = crt.Id

                LEFT JOIN
                    ComplianceRuleSubTypes crst ON cptr.ruleSubTypeId = crst.Id
                WHERE
                    cptr.Level = 'Portfolio'
                AND
                    cptr.LevelValue = @P1

                "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        let rows: Vec<CompliancePreTradeRuleWithRuleTypes> = rows_iter
            .iter()
            .map(CompliancePreTradeRuleWithRuleTypes::from_row)
            .collect();

        Ok(rows)
    }

    pub async fn get_active_rules_for_portfolio(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
    ) -> Result<Vec<CompliancePreTradeRuleWithRuleTypes>, DatabaseError> {
        let query = r#"
                SELECT
                    *
                FROM
                    CompliancePreTradeRules cptr
                LEFT JOIN
                    ComplianceRuleTypes crt ON cptr.ruleTypeId = crt.Id

                LEFT JOIN
                    ComplianceRuleSubTypes crst ON cptr.ruleSubTypeId = crst.Id
                WHERE
                    cptr.Level = 'Portfolio'
                AND
                    cptr.LevelValue = @P1
                AND
                    cptr.IsActive = 1

                "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        let rows: Vec<CompliancePreTradeRuleWithRuleTypes> = rows_iter
            .iter()
            .map(CompliancePreTradeRuleWithRuleTypes::from_row)
            .collect();

        Ok(rows)
    }
}

#[cfg(test)]
mod tests {
    use crate::{connection::connect_to_mssql, schema::compliance_pre_trade::trade_rule::CompliancePreTradeRules};

    #[tokio::test]
    async fn get_compliance_rule_for_portfolio() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();

        let c = CompliancePreTradeRules::get_for_portfolio(&mut pool_conn, "b9de414f6ea04d8a89d9879caaa25832").await;
        println!("{:?}", c);
    }
}
