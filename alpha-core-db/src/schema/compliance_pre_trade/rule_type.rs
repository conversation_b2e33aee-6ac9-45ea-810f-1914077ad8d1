use std::{fmt::Display, str::FromStr};

use serde::{Deserialize, Serialize};
use tiberius::Row;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash, Copy)]
pub enum PreTradeRuleType {
    AssetPreference,
    MarketCapMin,
    MarketCapMax,
    SectorMax,
    SectorMin,
    MinNumberOfStocks,
    MaxNumberOfStocks,
    CashWeightMin,
    CashWeightMax,
    SingleStockExposureMax,
    SingleStockExposureMin,
}

impl Display for PreTradeRuleType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::AssetPreference => write!(f, "AssetPreference"),
            Self::MarketCapMin => write!(f, "MarketCapMin"),
            Self::MarketCapMax => write!(f, "MarketCapMax"),
            Self::SectorMax => write!(f, "SectorMax"),
            Self::SectorMin => write!(f, "SectorMin"),
            Self::MinNumberOfStocks => write!(f, "NoOfStocksMin"),
            Self::MaxNumberOfStocks => write!(f, "NoOfStocksMax"),
            Self::CashWeightMin => write!(f, "CashWeightMin"),
            Self::CashWeightMax => write!(f, "CashWeightMax"),
            Self::SingleStockExposureMax => write!(f, "SingleStockExposureMax"),
            Self::SingleStockExposureMin => write!(f, "SingleStockExposureMin"),
        }
    }
}

impl FromStr for PreTradeRuleType {
    type Err = ();

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "assetpreference" => Ok(Self::AssetPreference),
            "marketcapmin" => Ok(Self::MarketCapMin),
            "marketcapmax" => Ok(Self::MarketCapMax),
            "sectormax" => Ok(Self::SectorMax),
            "sectormin" => Ok(Self::SectorMin),
            "noofstocksmin" => Ok(Self::MinNumberOfStocks),
            "noofstocksmax" => Ok(Self::MaxNumberOfStocks),
            "cashweightmin" => Ok(Self::CashWeightMin),
            "cashweightmax" => Ok(Self::CashWeightMax),
            "singlestockexposuremax" => Ok(Self::SingleStockExposureMax),
            "singlestockexposuremin" => Ok(Self::SingleStockExposureMin),
            _ => Err(()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceRuleType {
    pub rule_type_name: PreTradeRuleType,
    pub is_active: bool,
}

impl ComplianceRuleType {
    pub fn from_row(row: &Row) -> ComplianceRuleType {
        ComplianceRuleType {
            rule_type_name: PreTradeRuleType::from_str(row.get("RuleTypeName").unwrap()).unwrap(),
            is_active: row.get::<bool, _>("IsActive").unwrap(),
        }
    }
}


