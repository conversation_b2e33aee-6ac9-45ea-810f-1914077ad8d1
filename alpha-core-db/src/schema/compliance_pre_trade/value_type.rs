use std::{fmt::Display, str::FromStr};

use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize,Copy,PartialEq)]
pub enum ValueType {
    Absolute,
    Percentage,
}

impl Display for ValueType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ValueType::Absolute => write!(f, "Absolute"),
            ValueType::Percentage => write!(f, "Percentage"),
        }
    }
}

impl FromStr for ValueType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "absolute" => Ok(Self::Absolute),
            "percentage" => Ok(Self::Percentage),
            _ => Err(()),
        }
    }
}
