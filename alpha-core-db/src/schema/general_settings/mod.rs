use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Debug)]
pub struct GeneralSettings {
    pub key: String,
    pub value: String,
}

impl GeneralSettings {
    pub fn from_row(row: &Row) -> Self {
        Self {
            key: row.get::<&str, _>("Key").unwrap().to_string(),
            value: row.get::<&str, _>("Value").unwrap().to_string(),
        }
    }

    pub async fn get_by_key(conn: &mut Object<Manager>, key: &str) -> Result<Option<Self>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                GeneralSettings
            WHERE
                [Key] = @P1
        ";

        let rows_iter = conn
            .query(query, &[&key])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::{connection::connect_to_mssql, schema::general_settings::GeneralSettings};

    #[tokio::test]
    async fn get_all_clients() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();

        let general_settings = GeneralSettings::get_by_key(&mut pool_conn, "IsSttIncludedInCost").await;
        assert!(general_settings.is_ok());
    }
}
