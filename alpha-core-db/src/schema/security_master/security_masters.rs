use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

pub struct SecurityMasters {
    pub industry: Option<String>,
    pub bse_symbol: Option<String>,
    pub nse_symbol: Option<String>,
    pub nse_series: Option<String>,
    pub isin: String,
    pub status: String,
    pub sublisting: Option<String>,
    pub market_cap: Option<String>,
    pub sector: String,
    pub latest_price: f64,
}

impl SecurityMasters {
    pub fn from_row(row: &Row) -> Self {
        Self {
            bse_symbol: row.get::<&str, _>("BseSymbol").map(String::from),
            nse_symbol: row.get::<&str, _>("NseSymbol").map(String::from),
            nse_series: row.get::<&str, _>("NseSeries").map(String::from),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            sublisting: row.get::<&str, _>("Sublisting").map(String::from),
            industry: row.get::<&str, _>("Industry").map(String::from),
            market_cap: row.get::<&str, _>("MarketCap").map(String::from),
            sector: row.get::<&str, _>("Sector").unwrap().to_string(),
            latest_price: 0f64,
        }
    }

    pub async fn get_by_isin(conn: &mut Object<Manager>, isin: &str) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            sm.*,
        FROM
            SecurityMasters sm 
        WHERE
            sm.isin=@P1

    "#;

        let rows_iter = conn
            .query(query, &[&isin])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
