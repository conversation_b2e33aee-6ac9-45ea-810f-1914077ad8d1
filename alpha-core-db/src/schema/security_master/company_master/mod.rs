use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

pub struct CompanyMaster {}

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
pub struct EquityCompanyMaster {
    pub fincode: String,
    pub scripcode: Option<String>,
    pub scrip_group: Option<String>,
    pub comp_name: String,
    pub ind_code: Option<String>,
    pub hse_code: Option<String>,
    pub symbol: Option<String>,
    pub series: Option<String>,
    pub isin: String,
    pub s_name: Option<String>,
    pub status: String,
    pub sublisting: Option<String>,
    pub flag: String,
    pub industry: Option<String>,
    pub market_cap: Option<String>,
    pub sector: String,
    pub latest_price: f64,
}

impl EquityCompanyMaster {
    pub fn from_row(row: &Row) -> Self {
        Self {
            fincode: row.get::<&str, _>("Fincode").unwrap().to_string(),
            scripcode: row.get::<&str, _>("Scripcode").map(String::from),
            scrip_group: row.get::<&str, _>("Scrip_group").map(String::from),
            comp_name: row.get::<&str, _>("CompName").unwrap().to_string(),
            ind_code: row.get::<&str, _>("Ind_code").map(String::from),
            hse_code: row.get::<&str, _>("HSE_Code").map(String::from),
            symbol: row.get::<&str, _>("Symbol").map(String::from),
            series: row.get::<&str, _>("Series").map(String::from),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            s_name: row.get::<&str, _>("S_Name").map(String::from),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            sublisting: row.get::<&str, _>("Sublisting").map(String::from),
            flag: row.get::<&str, _>("Flag").unwrap().to_string(),
            industry: row.get::<&str, _>("Industry").map(String::from),
            market_cap: row.get::<&str, _>("MarketCap").map(String::from),
            sector: row.get::<&str, _>("Sector").unwrap().to_string(),
            latest_price: 0f64,
        }
    }
}

impl CompanyMaster {
    pub async fn get_by_isin(conn: &mut Object<Manager>, isin: String) -> Result<Option<EquityCompanyMaster>, DatabaseError> {
        let query = r#"
        SELECT
            cm.*,
            im.Sector,
            cmc.mode as MarketCap
        FROM
            CompanyMaster cm 
        JOIN IndustryMaster im on cm.Ind_code = im.Ind_code
        LEFT JOIN  CompanyMCap cmc on cm.Fincode = cmc.fincode
        WHERE
            cm.isin=@P1

    "#;

        let rows_iter = conn
            .query(query, &[&isin])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(EquityCompanyMaster::from_row(&rows_iter.unwrap())))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_company_master() {
        dotenv::dotenv().ok();
        let pool = crate::connection::connect_to_mssql(1).await;
        let matser_pool = crate::connection::connect_to_master_data(1).await;
        let mut conn = matser_pool.get().await.unwrap();
        let price = CompanyMaster::get_by_isin(&mut conn, String::from("INE0MLZ01019"))
            .await
            .unwrap();

        println!("{:?}", price);
    }
}
