use chrono::NaiveDateTime;
use deadpool::managed::Object;
use rust_decimal::Decimal;
use tiberius::Row;
use tracing_subscriber::registry::Data;
pub mod company_master;
use crate::{connection::pool::Manager, error::DatabaseError};
pub mod security_masters;
pub mod security_price;

pub struct SchemeMasterDetailsNew {}

#[derive(Debug, Clone)]
pub struct SchemeMasterDetails {
    pub isin: String,
    pub amfi_code: String,
    pub bse_star_scheme_code: String,
    pub settlement_type: String,
    pub scheme_type: String,
    pub scheme_name: String,
    pub purchase_allowed: String,
    pub minimum_purchase_amount: f32,
    pub additional_purchase_amount: f32,
    pub maximum_purchase_amount: f32,
    pub redemption_allowed: String,
    pub redemption_amount_minimum: f32,
    pub redemption_amount_maximum: f32,
    pub maximum_redemption_qty: f32,
    pub purchase_mode: String,
    pub distribution_status: String,
    pub investment_plan: String,
    pub fund_name: String,
    pub net_expense_ratio: Decimal,
    pub exchange_traded_share: String,
    pub fund_manager_name: String,
    pub fund_manager_start_date: Option<NaiveDateTime>,
    pub inception_date: NaiveDateTime,
    pub rta_code: String,
    pub fund_legal_name: String,
    pub asset_class: String,
    pub fund_class: String,
    pub category_name: String,
    pub amc: String,
    pub amc_code: String,
    pub benchmark: Option<String>,
    pub morningstar_benchmark_id: Option<String>,
    pub morningstar_benchmark_name: Option<String>,
    pub debt_allocation: Option<String>,
    pub equity_allocation: Option<String>,
    pub cash_allocation: Option<String>,
    pub fund_status: String,
    pub latest_price: f64,
}

impl SchemeMasterDetails {
    pub fn from_row(row: &Row) -> Self {
        Self {
            isin: row.get::<&str, _>("ISIN").unwrap().to_string(),
            amfi_code: row.get::<&str, _>("AmfiCode").unwrap().to_string(),
            bse_star_scheme_code: row.get::<&str, _>("BseStarSchemeCode").unwrap().to_string(),
            settlement_type: row.get::<&str, _>("SettlementType").unwrap().to_string(),
            scheme_type: row.get::<&str, _>("SchemeType").unwrap().to_string(),
            scheme_name: row.get::<&str, _>("SchemeName").unwrap().to_string(),
            purchase_allowed: row.get::<&str, _>("PurchaseAllowed").unwrap().to_string(),
            minimum_purchase_amount: row.get::<f32, _>("MinimumPurchaseAmount").unwrap(),
            additional_purchase_amount: row.get::<f32, _>("AdditionalPurchaseAmount").unwrap(),
            maximum_purchase_amount: row.get::<f32, _>("MaximumPurchaseAmount").unwrap(),
            redemption_allowed: row.get::<&str, _>("RedemptionAllowed").unwrap().to_string(),
            redemption_amount_minimum: row.get::<f32, _>("RedemptionAmountMinimum").unwrap(),
            redemption_amount_maximum: row.get::<f32, _>("RedemptionAmountMaximum").unwrap(),
            maximum_redemption_qty: row.get::<f32, _>("MaximumRedemptionQty").unwrap(),
            purchase_mode: row.get::<&str, _>("PurchaseMode").unwrap().to_string(),
            distribution_status: row.get::<&str, _>("DistributionStatus").unwrap().to_string(),
            investment_plan: row.get::<&str, _>("InvestmentPlan").unwrap().to_string(),
            fund_name: row.get::<&str, _>("FundName").unwrap().to_string(),
            net_expense_ratio: row.get::<Decimal, _>("NetExpenseRatio").unwrap(),
            exchange_traded_share: row.get::<&str, _>("ExchangeTradedShare").unwrap().to_string(),
            fund_manager_name: row.get::<&str, _>("FundManagername").unwrap().to_string(),
            fund_manager_start_date: row.get::<NaiveDateTime, _>("FundManagerStartDate"),
            inception_date: row.get::<NaiveDateTime, _>("InceptionDate").unwrap(),
            rta_code: row.get::<&str, _>("RTACode").unwrap().to_string(),
            fund_legal_name: row.get::<&str, _>("FundLegalName").unwrap().to_string(),
            asset_class: row.get::<&str, _>("AssetClass").unwrap().to_string(),
            fund_class: row.get::<&str, _>("FundClass").unwrap().to_string(),
            category_name: row.get::<&str, _>("CategoryName").unwrap().to_string(),
            amc: row.get::<&str, _>("Amc").unwrap().to_string(),
            amc_code: row.get::<&str, _>("AmcCode").unwrap().to_string(),
            benchmark: row.get::<&str, _>("Benchmark").map(String::from),
            morningstar_benchmark_id: row.get::<&str, _>("MorningStarBenchmarkId").map(String::from),
            morningstar_benchmark_name: row.get::<&str, _>("MorningStarBenchmarkName").map(String::from),
            debt_allocation: row.get::<&str, _>("DebtAllocation").map(String::from),
            equity_allocation: row.get::<&str, _>("EquityAllocation").map(String::from),
            cash_allocation: row.get::<&str, _>("CashAllocation").map(String::from),
            fund_status: row.get::<&str, _>("FundStatus").unwrap().to_string(),
            latest_price: 0f64,
        }
    }
}

impl SchemeMasterDetailsNew {
    pub async fn get_mf_details_by_isin(
        conn: &mut Object<Manager>,
        isin: String,
        purchase_allowed: bool,
        redemption_allowed: bool,
    ) -> Result<Option<SchemeMasterDetails>, DatabaseError> {
        let purchase_allowed_flag = if purchase_allowed { "Y" } else { "N" };
        let redemption_allowed_flag = if redemption_allowed { "Y" } else { "N" };

        let query = r#"
                SELECT 
                    bpsmd.SchemeCode as BseStarSchemeCode,
                    bpsmd.SettlementType,
                    bpsmd.SchemeType,
                    bpsmd.PurchaseAllowed,
                    bpsmd.MinimumPurchaseAmount,
                    bpsmd.AdditionalPurchaseAmount,
                    bpsmd.MaximumPurchaseAmount,
                    bpsmd.RedemptionAllowed,
                    bpsmd.RedemptionAmountMinimum,
                    bpsmd.RedemptionAmountMaximum,
                    bpsmd.MaximumRedemptionQty,
                    smdn.*
                FROM 
                    SchemeMasterDetailsNew smdn
                JOIN
                    BsePhysicalSchemeMasterDetails bpsmd ON smdn.ISIN = bpsmd.Isin
                WHERE
                    smdn.ISIN = @P1 AND PurchaseAllowed = @P2 AND RedemptionAllowed = @P3
        "#;

        let rows_iter = conn
            .query(query, &[&isin, &purchase_allowed_flag, &redemption_allowed_flag])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_row()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(SchemeMasterDetails::from_row(&rows_iter.unwrap())))
        }
    }
}
