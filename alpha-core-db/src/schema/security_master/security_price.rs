use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError};

pub struct TenantSecurityPrice {
    pub id: String,
    pub price_date: NaiveDateTime,
    pub price: f64,
    pub exchange: String,
    pub isin: String,
    pub security_master_id: String,
}

impl TenantSecurityPrice {
    fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            price_date: row.get::<NaiveDateTime, _>("PriceDate").unwrap(),
            price: row.get::<f64, _>("Price").unwrap(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            security_master_id: row.get::<&str, _>("SecurityMasterId").unwrap().to_string(),
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<Vec<Self>, DatabaseError> {
        let query = "SELECT * from SecurityPrices";

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| DatabaseError::QueryError(e))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError(e))?;

        let holdings: Vec<Self> = rows_iter.into_iter().map(|row| Self::from_row(&row)).collect();
        Ok(holdings)
    }
}

mod test {
    use chrono::NaiveDate;

    use crate::{
        connection::connect_to_mssql,
        schema::{investment::mis_holdings::RptHoldingDetailsForMis, security_master::security_price::TenantSecurityPrice},
    };

    #[tokio::test]
    async fn test_security_price_fetch_all() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let as_at_date = NaiveDate::from_ymd_opt(2025, 06, 01).unwrap();
        let price = TenantSecurityPrice::get_all(&mut pool_conn).await;
        assert!(price.is_ok());
    }
}
