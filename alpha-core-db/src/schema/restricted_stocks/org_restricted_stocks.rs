use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError, schema::client_order_entry::SecurityType};

pub struct RestrictedStockForOrganisation {
    pub restricted_security_identifier: String,

    pub alternative_security_identifier: Option<String>,

    pub rationale: String,

    pub exchange_restricted_security: String,

    pub exchange_alternative_security: Option<String>,

    pub isin_restricted_security: String,

    pub isin_alternative_security: Option<String>,

    pub status: String,
}

impl RestrictedStockForOrganisation {
    pub fn from_row(row: &Row) -> Self {
        Self {
            restricted_security_identifier: row.get::<&str, _>("RestrictedSecurityIdentifier").unwrap().to_string(),
            alternative_security_identifier: row.get::<&str, _>("AlternativeSecurityIdentifier").map(String::from),
            rationale: row.get::<&str, _>("Rationale").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            isin_alternative_security: row.get::<&str, _>("IsinAlternativeSecurity").map(String::from),
            isin_restricted_security: row.get::<&str, _>("IsinRestrictedSecurity").unwrap().to_string(),
            exchange_alternative_security: row.get::<&str, _>("ExchangeAlternativeSecurity").map(String::from),
            exchange_restricted_security: row.get::<&str, _>("ExchangeRestrictedSecurity").unwrap().to_string(),
        }
    }
}

impl RestrictedStockForOrganisation {
    pub async fn get_by_and_isin(conn: &mut Object<Manager>, isin: &str) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            RestrictedStockForOrganisation
        WHERE
            IsinRestrictedSecurity = @P1
        AND
            Status = 'Active'

        "#;

        let rows_iter = conn
            .query(query, &[&isin])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<Vec<Self>, DatabaseError> {
        let query = format!(
            "
            SELECT
                *
            FROM
                RestrictedStockForOrganisation
            WHERE
                Status = 'Active'

            ",
        );

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap()
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap();

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }
}
