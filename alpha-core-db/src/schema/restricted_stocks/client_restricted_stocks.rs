use std::str::FromStr;

use deadpool::managed::Object;
use tiberius::Row;

use crate::{connection::pool::Manager, error::DatabaseError, schema::client_order_entry::SecurityType};

#[derive(Clone)]
pub struct RestrictedStockForClients {
    pub restricted_security_identifier: String,

    pub alternative_security_identifier: Option<String>,

    pub rationale: String,

    pub exchange_restricted_security: String,

    pub exchange_alternative_security: Option<String>,

    pub client_id: String,

    pub isin_restricted_security: String,

    pub isin_alternative_security: Option<String>,

    pub status: Option<String>,
}

impl RestrictedStockForClients {
    pub fn from_row(row: &Row) -> Self {
        Self {
            restricted_security_identifier: row.get::<&str, _>("RestrictedSecurityIdentifier").unwrap().to_string(),
            alternative_security_identifier: row.get::<&str, _>("AlternativeSecurityIdentifier").map(String::from),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            rationale: row.get::<&str, _>("Rationale").unwrap().to_string(),
            status: row.get::<&str, _>("Status").map(String::from),
            isin_alternative_security: row.get::<&str, _>("IsinAlternativeSecurity").map(String::from),
            isin_restricted_security: row.get::<&str, _>("IsinRestrictedSecurity").unwrap().to_string(),
            exchange_alternative_security: row.get::<&str, _>("ExchangeAlternativeSecurity").map(String::from),
            exchange_restricted_security: row.get::<&str, _>("ExchangeRestrictedSecurity").unwrap().to_string(),
        }
    }
}

impl RestrictedStockForClients {
    pub async fn get_by_client_id_and_isin(
        conn: &mut Object<Manager>,
        client_id: String,
        isin: String,
    ) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            RestrictedStocksForClients
        WHERE
            ClientId = @P1 
        AND
            IsinRestrictedSecurity = @P2
        AND
            Status = 'Active'
    "#;

        let rows_iter = conn
            .query(query, &[&client_id, &isin])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap()
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))
            .unwrap();

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_by_client_ids_and_isin(
        conn: &mut Object<Manager>,
        client_ids: &[String],
        isin: String,
    ) -> Result<Vec<Self>, DatabaseError> {
        let formatted_ids = client_ids
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
            SELECT
                *
            FROM
                RestrictedStocksForClients
            WHERE
                ClientId in ({})
            AND
                IsinRestrictedSecurity = @P1
             ",
            formatted_ids
        );

        let rows_iter = conn
            .query(query, &[&isin])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }


    pub async fn get_all_by_client_ids(
        conn: &mut Object<Manager>,
        client_ids: &[String],
    ) -> Result<Vec<Self>, DatabaseError> {
        let formatted_ids = client_ids
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
            SELECT
                *
            FROM
                RestrictedStocksForClients
            WHERE
                ClientId in ({})
            AND 
                Status = 'Active'
             ",
            formatted_ids
        );

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }
}
