use std::str::FromStr;

use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{error::Error, ColumnData, ExecuteResult, Query, Row, TokenRow};
use tracing::error;

use crate::{connection::pool::Manager, error::DatabaseError};

use super::client_order_entry::{SecuritySubType, SecurityType, TransactionSubType, TransactionType};

#[derive(Debug, Serialize, Deserialize, Default, Clone)]
pub struct InvestmentTransaction {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub investment_id: String,
    pub portfolio_id: Option<String>,
    pub client_id: Option<String>,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub cgt_date: NaiveDateTime,
    pub quantity: f64,
    pub unrealised_holding: f64,
    pub current_holding: f64,
    pub price: f64,
    pub market_rate: f64,
    pub amount: f64,
    pub brokerage: f64,
    pub service_tax: f64,
    pub stt_amount: f64,
    pub turn_tax: f64,
    pub other_tax: f64,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub symbol: String,
    pub exchange: String,
    pub isin: String,
    pub name: String,
    pub mf_folio: Option<String>,
    pub currency: Option<String>,
    pub currency_conversion_rate: f64,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct TransactionsForPerformanceEngine {
    pub id: String,
    pub investment_id: String,
    pub isin: String,
    pub client_id: String,
    pub portfolio_id: String,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub transaction_date: NaiveDateTime,
    pub cgt_date: NaiveDateTime,
    pub quantity: f64,
    pub unrealised_holding: f64,
    pub current_holding: f64,
    pub amount: f64,
    pub price: f64,
}

impl TransactionsForPerformanceEngine {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            cgt_date: row.get::<NaiveDateTime, _>("CGTDate").unwrap(),
            quantity: row.get::<f64, _>("Quantity").unwrap(),
            unrealised_holding: row.get::<f64, _>("UnrealisedHolding").unwrap(),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("Type").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("SubType").unwrap()).unwrap(),
            amount: row.get::<f64, _>("Amount").unwrap(),
            price: row.get::<f64, _>("Price").unwrap(),
            investment_id: row.get::<&str, _>("InvestmentId").unwrap().to_string(),
        }
    }
}

impl InvestmentTransaction {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            investment_id: row.get::<&str, _>("InvestmentId").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").map(String::from),
            client_id: row.get::<&str, _>("ClientId").map(String::from),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            cgt_date: row.get::<NaiveDateTime, _>("CGTDate").unwrap(),
            quantity: row.get::<f64, _>("Quantity").unwrap(),
            unrealised_holding: row.get::<f64, _>("UnrealisedHolding").unwrap(),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap(),
            price: row.get::<f64, _>("Price").unwrap(),
            market_rate: row.get::<f64, _>("MarketRate").unwrap(),
            amount: row.get::<f64, _>("Amount").unwrap(),
            brokerage: row.get::<f64, _>("Brokerage").unwrap(),
            service_tax: row.get::<f64, _>("ServiceTax").unwrap(),
            stt_amount: row.get::<f64, _>("SttAmount").unwrap(),
            turn_tax: row.get::<f64, _>("TurnTax").unwrap(),
            other_tax: row.get::<f64, _>("OtherTax").unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("Type").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("SubType").unwrap()).unwrap(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            mf_folio: row.get::<&str, _>("MFFolio").map(String::from),
            currency: row.get::<&str, _>("Currency").map(String::from),
            currency_conversion_rate: row.get::<f64, _>("CurrencyConversionRate").unwrap(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            InvestmentTransactions
        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError(e))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError(e))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_by_investment_id(conn: &mut Object<Manager>, id: String) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            InvestmentTransactions
        WHERE
            InvestmentId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(res)
    }

    pub async fn get_by_portfolio_id_for_performance_engine(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<TransactionsForPerformanceEngine>, DatabaseError> {
        let query = r#"
        SELECT
            Id,
            ClientId,
            PortfolioId,
            Isin,
            Type,
            SubType,
            UnrealisedHolding,
            Price,
            Amount,
            Quantity,
            TransactionDate,
            CGTDate,
            CurrentHolding,
            InvestmentId
        FROM
            InvestmentTransactions
        WHERE
            PortfolioId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from InvestmentTransactions From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from InvestmentTransactions From Database ");
                return DatabaseError::QueryError(e);
            })?;

        let res: Vec<TransactionsForPerformanceEngine> = rows_iter
            .iter()
            .map(TransactionsForPerformanceEngine::from_row)
            .collect();

        Ok(res)
    }

    pub async fn bulk_insert(
        conn: &mut Object<Manager>,
        transactions: &Vec<Self>,
    ) -> Result<ExecuteResult, DatabaseError> {
        let mut req = conn.bulk_insert("InvestmentTransactions").await.unwrap();

        for transaction in transactions {
            let mut token_row = TokenRow::new();
            let amount = ColumnData::F64(Some(transaction.amount));
            token_row.push(amount);

            req.send(token_row).await.unwrap();
        }

        req.finalize().await.map_err(|e| DatabaseError::QueryError(e))
    }

    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
                INSERT INTO InvestmentTransactions (
                    InvestmentId, PortfolioId, ClientId, TransactionDate, SettlementDate,
                    Quantity, Price, Amount, Brokerage, ServiceTax,
                    SttAmount, TurnTax, OtherTax, Type, SubType,
                    Symbol, Name, Exchange, Isin, CurrentHolding,
                    UnrealisedHolding, MarketRate, MFFolio, CGTDate, Currency,
                    CurrencyConversionRate
                ) VALUES (
                    @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                    @P11, @P12, @P13, @P14, @P15, @P16, @P17, @P18, @P19, @P20,
                    @P21, @P22, @P23, @P24, @P25, @P26
                )
            ",
        );

        query.bind(&self.investment_id);
        query.bind(self.portfolio_id.to_owned());
        query.bind(self.client_id.to_owned());
        query.bind(self.transaction_date);
        query.bind(self.settlement_date);
        query.bind(self.quantity);
        query.bind(self.price);
        query.bind(self.amount);
        query.bind(self.brokerage);
        query.bind(self.service_tax);
        query.bind(self.stt_amount);
        query.bind(self.turn_tax);
        query.bind(self.other_tax);
        query.bind(self.transaction_type.to_string());
        query.bind(self.transaction_sub_type.to_string());
        query.bind(&self.symbol);
        query.bind(&self.name);
        query.bind(&self.exchange);
        query.bind(&self.isin);
        query.bind(self.current_holding);
        query.bind(self.unrealised_holding);
        query.bind(self.market_rate);
        query.bind(self.mf_folio.to_owned());
        query.bind(self.cgt_date);
        query.bind(self.currency.to_owned());
        query.bind(self.currency_conversion_rate);

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError(e))
    }

    pub async fn get_by_portfolio_id_and_transaction_date(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
        txn_date: NaiveDate,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            InvestmentTransactions
        WHERE
            PortfolioId = @P1
            AND
         CAST(TransactionDate AS DATE) = CAST(@P2 AS DATE)
        "#;

        let rows_iter = conn
            .query(query, &[&portfolio_id, &txn_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        Ok(rows_iter)
    }

    pub async fn get_mf_folio(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
        isin: &str,
    ) -> Result<Option<String>, DatabaseError> {
        let query = "
            SELECT 
                Top(1)
                *
            FROM 
                InvestmentTransactions 
            WHERE 
                PortfolioId=@P1
            AND
                 Isin=@P2 
            AND 
                MFFolio IS NOT NULL
            Order By
                TransactionDate
        ";

        let rows_iter = conn
            .query(query, &[&portfolio_id, &isin])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?
            .into_row()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError(e);
            })?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(rows_iter.unwrap().get::<&str, _>("MFFolio").unwrap().to_string()))
        }
    }

    pub async fn get_transactions_data(
        conn: &mut Object<Manager>,
        portfolio_type: String,
        txn_type: &str,
        from_date: NaiveDateTime,
        to_date: NaiveDateTime,
    ) -> Result<i32, DatabaseError> {
        let query = r#"
                        SELECT SUM(it.MarketValue) as Count from InvestmentTransactions it 
                        join portfolios p on p.Id = it.PortfolioId where p.PortfolioType = @P1
                        and it.Type = @P2 or it.SubType = @P2 and it.TransactionDate BETWEEN @P3 and @P4
        "#;

        let row = conn
            .query(query, &[&portfolio_type, &txn_type, &from_date, &to_date])
            .await?
            .into_row()
            .await?
            .ok_or_else(|| {
                return DatabaseError::RowNotFound();
            })?;

        let count = row.get("Count").unwrap();
        Ok(count)
    }
}
