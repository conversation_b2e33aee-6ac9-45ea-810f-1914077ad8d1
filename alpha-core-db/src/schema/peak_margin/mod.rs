use chrono::NaiveDateTime;
use deadpool::managed::Object;
use tiberius::Row;

use crate::connection::pool::Manager;
use crate::error::DatabaseError;

pub struct PeakMarginLogs {
    pub portfolio_id: String,
    pub peak_margin_pct: f64,
    pub amount: f64,
    pub status: String,
    pub date: NaiveDateTime,
    pub type_name: String,
}

impl PeakMarginLogs {
    pub fn from_row(row: &Row) -> Self {
        Self {
            amount: row.get::<f64, _>("Amount").unwrap(),
            date: row.get::<NaiveDateTime, _>("Date").unwrap(),
            peak_margin_pct: row.get::<f64, _>("PeakMarginpct").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            type_name: row.get::<&str, _>("Type").unwrap().to_string(),
        }
    }
}

impl PeakMarginLogs {
    pub async fn get_by_portfolio_id_and_date(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
        date: NaiveDateTime,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                PeakMarginLogs
            Where
                PortfolioId = @P1
                AND
            CAST(Date AS DATE) = CAST(@P2 AS DATE)
        ";

        let rows_iter = conn
            .query(query, &[&portfolio_id, &date])
            .await?
            .into_first_result()
            .await?;
        Ok(rows_iter)
    }
}
