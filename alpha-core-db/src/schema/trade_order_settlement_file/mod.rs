use std::{fmt, str::FromStr};

use chrono::NaiveDateTime;
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{Query, Row};

use crate::{connection::pool::Manager, error::DatabaseError};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TradeOrderSettlementFileStatus {
    Submitted,
    Processing,
    Processed,
    Settling,
    Settled,
    Failed,
    Abandoned,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TradeOrderSettlementFile {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub placed_by: String,
    pub file_path: String,
    pub alpha_transformer_integration_id: String,
    pub alpha_file_path: String,
    pub processing_status: TradeOrderSettlementFileStatus,
    pub failure_message: Option<String>,
    pub failure_description: Option<String>,
}

impl std::str::FromStr for TradeOrderSettlementFileStatus {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Processed" => Ok(TradeOrderSettlementFileStatus::Processed),
            "Failed" => Ok(TradeOrderSettlementFileStatus::Failed),
            "Processing" => Ok(TradeOrderSettlementFileStatus::Processing),
            "Settled" => Ok(TradeOrderSettlementFileStatus::Settled),
            "Settling" => Ok(TradeOrderSettlementFileStatus::Settling),
            "Submitted" => Ok(TradeOrderSettlementFileStatus::Submitted),
            _ => Err(()),
        }
    }
}

impl fmt::Display for TradeOrderSettlementFileStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TradeOrderSettlementFileStatus::Processed => write!(f, "Processed"),
            TradeOrderSettlementFileStatus::Failed => write!(f, "Failed"),
            TradeOrderSettlementFileStatus::Processing => write!(f, "Processing"),
            TradeOrderSettlementFileStatus::Settled => write!(f, "Settled"),
            TradeOrderSettlementFileStatus::Settling => write!(f, "Settling"),
            TradeOrderSettlementFileStatus::Submitted => write!(f, "Submitted"),
            TradeOrderSettlementFileStatus::Abandoned => write!(f, "Abandoned"),
        }
    }
}

impl TradeOrderSettlementFile {
    pub fn from_row(row: &Row) -> TradeOrderSettlementFile {
        TradeOrderSettlementFile {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            placed_by: row.get::<&str, _>("PlacedBy").unwrap().to_string(),
            file_path: row.get::<&str, _>("FilePath").unwrap().to_string(),
            alpha_transformer_integration_id: row.get::<&str, _>("AlphaTransformerIntegrationId").unwrap().to_string(),
            alpha_file_path: row.get::<&str, _>("AlphaFilePath").unwrap().to_string(),
            processing_status: TradeOrderSettlementFileStatus::from_str(
                row.get::<&str, _>("ProcessingStatus").unwrap(),
            )
            .unwrap(),
            failure_message: row.get::<&str, _>("FailureMessage").map(String::from),
            failure_description: row.get::<&str, _>("FailureDescription").map(String::from),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<TradeOrderSettlementFile, DatabaseError> {
        let query = r#"
            SELECT
                *
            FROM
                TradeOrderSettlementFiles
            WITH (XLOCK,NOWAIT)
            WHERE
                Id = @P1
        
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Err(DatabaseError::RowNotFound())
        } else {
            Ok(Self::from_row(&rows_iter.unwrap()))
        }
    }

    pub async fn update_status(
        &self,
        conn: &mut Object<Manager>,
        status: TradeOrderSettlementFileStatus,
    ) -> Result<tiberius::ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE 
                TradeOrderSettlementFiles
            SET
                ProcessingStatus = @P1
            WHERE
                Id = @P2
        ",
        );

        query.bind(status.to_string());
        query.bind(self.id.to_string());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn update_status_with_remarks(
        &self,
        conn: &mut Object<Manager>,
        status: TradeOrderSettlementFileStatus,
        remarks: String,
    ) -> Result<tiberius::ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE 
                TradeOrderSettlementFiles
            SET
                ProcessingStatus = @P1,
                FailureMessage = @P2
            WHERE
                Id = @P3
        ",
        );

        query.bind(status.to_string());
        query.bind(remarks.to_string());
        query.bind(self.id.to_string());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }
}
