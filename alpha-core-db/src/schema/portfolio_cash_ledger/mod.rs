use deadpool::managed::Object;
use tiberius::time::time::{Date, Month, PrimitiveDateTime, Time};

use crate::{connection::pool::Manager, error::DatabaseError};

pub async fn get_income_expense_details_for_mis(conn: &mut Object<Manager>) -> Result<Vec<tiberius::Row>, DatabaseError> {
    let date = Date::from_calendar_date(2023, Month::June, 15).unwrap();
    let time = Time::from_hms(12, 0, 0).unwrap();
    let as_at_date = PrimitiveDateTime::new(date, time);
    let query = r#"
    SELECT
        c.ClientCode,
        c.FirstName + ' ' + c.LastName as ClientName, 
        p.Name as StrategyName,
        p.ClientStrategyCode,
        p.CustodianPortfolioCode,
        TransactionDate,
        TransactionType,
        TransactionSubType,
        Amount,
        @P1 as AsAtDate,
        Description,
        pcl.Id as PortfolioCashLedgerId
    FROM
        PortfolioCashLedgers  pcl join Portfolios p on pcl.PortfolioId = p.id
        join clients c
                    on p.ClientId = c.id
    WHERE
        TransactionSubType not in ('buy','Dividend','sell','FractionPayment','MergerPartial','BonusPartial','SplitPartial','CapitalOut','CapitalIn','Tds','PeakMargin','Receipt')
        and TransactionDate <= @P1
    Order by
        p.Name,TransactionDate, TxnSequenceId
    
    "#;

    let rows_iter = conn
        .query(query, &[&as_at_date])
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?
        .into_first_result()
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?;

    Ok(rows_iter)
}

pub async fn get_dividends_details_for_mis(conn: &mut Object<Manager>) -> Result<Vec<tiberius::Row>, DatabaseError> {
    let date = Date::from_calendar_date(2023, Month::June, 15).unwrap();
    let time = Time::from_hms(12, 0, 0).unwrap();
    let as_at_date = PrimitiveDateTime::new(date, time);

    let query = r#"
         SELECT
            c.ClientCode,
            c.FirstName + ' ' + c.LastName as ClientName, 
            p.Name as StrategyName,
            p.ClientStrategyCode,
            p.CustodianPortfolioCode,
            pcl.TransactionDate,
            pcl.TransactionType,
            pcl.TransactionSubType,
            pcl.Amount,
            @P1 as AsAtDate,
            pcl.Description
        FROM
            PortfolioCashLedgers pcl 
            JOIN Portfolios p ON pcl.PortfolioId = p.id
            JOIN Clients c ON p.ClientId = c.id
        WHERE
            pcl.TransactionSubType = 'Dividend'
            AND pcl.TransactionDate <= @P1
        ORDER BY
            c.FirstName + ' ' + c.LastName,
            p.Name,
            pcl.TransactionDate, 
            pcl.TxnSequenceId
    "#;

    let rows_iter = conn
        .query(query, &[&as_at_date])
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?
        .into_first_result()
        .await
        .map_err(|e| {
            println!("{:?}", e);
            return DatabaseError::QueryError((e));
        })?;

    Ok(rows_iter)
}
