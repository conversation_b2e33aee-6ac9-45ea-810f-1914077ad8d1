use std::str::FromStr;

use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{error::Error, ExecuteResult, Query, Row};
use tracing::error;

use crate::{connection::pool::Manager, error::DatabaseError};

use super::client_order_entry::{TransactionSubType, TransactionType};

#[derive(Serialize, Deserialize, Default, Debug)]
#[serde(rename_all = "PascalCase")]
pub struct PortfolioCashLedger {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub amount: f64,
    pub running_balance: f64,
    pub description: Option<String>,
    pub txn_ref_id: Option<String>,
    pub txn_sequence_id: i32,
    pub is_model_portfolio: bool,
    pub portfolio_id: Option<String>,
    pub model_portfolio_id: Option<String>,
}

impl PortfolioCashLedger {
    fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("TransactionSubType").unwrap())
                .unwrap(),
            amount: row.get::<f64, _>("Amount").unwrap(),
            running_balance: row.get::<f64, _>("RunningBalance").unwrap(),
            description: row.get::<&str, _>("Description").map(String::from),
            txn_ref_id: row.get::<&str, _>("TxnRefId").map(String::from),
            txn_sequence_id: row.get::<i32, _>("TxnSequenceId").unwrap(),
            is_model_portfolio: row.get::<bool, _>("IsModelPortfolio").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").map(String::from),
            model_portfolio_id: row.get::<&str, _>("ModelportfolioId").map(String::from),
        }
    }

    pub async fn get_by_portfolio_id(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<PortfolioCashLedger>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            PortfolioCashLedgers
        WHERE
            PortfolioId = @P1
    
    "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(res)
    }

    pub async fn get_by_portfolio_id_for_performance_engine(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<PortfolioCashLedger>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            PortfolioCashLedgers
        WHERE
            PortfolioId = @P1
    
    "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(res)
    }

    pub async fn get_by_portfolio_id_and_date_for_performance_engine(
        conn: &mut Object<Manager>,
        id: String,
        date: NaiveDate,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            PortfolioCashLedgers
        WHERE
            PortfolioId = @P1
            And
            TransactionDate = @P2
            AND
            TransactionType In ('Credit','Debit')

    
    "#;

        let rows_iter = conn
            .query(query, &[&id, &date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        Ok(rows_iter)
    }

    pub async fn get_by_portfolio_id_and_date(
        conn: &mut Object<Manager>,
        id: String,
        date: NaiveDate,
    ) -> Result<Vec<PortfolioCashLedger>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            PortfolioCashLedgers
        WHERE
            PortfolioId = @P1
            And
            TransactionDate = @P2
    "#;

        let rows_iter = conn
            .query(query, &[&id, &date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(res)
    }

    pub async fn get_running_balance_of_a_portfolio(
        conn: &mut Object<Manager>,
        id: String,
        lock_row: bool,
    ) -> Result<Option<f64>, DatabaseError> {
        let query = r#"
                SELECT TOP 1 
                    runningBalance 
                FROM 
                    PortfolioCashLedgers 
                WITH 
                    (UPDLOCK, ROWLOCK)
                WHERE 
                    portfolioId = @P1 
                ORDER BY 
                    TxnSequenceId 
                DESC
                
         "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_row()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCashLedger From Database ");
                return DatabaseError::QueryError(e);
            })?;

        if let Some(row) = rows_iter {
            if let Some(running_balance) = row.get(0) {
                return Ok(Some(running_balance));
            } else {
                error!("Couldn't Find Any row with portfolioId = {} in CashLedger", id);
                return Err(DatabaseError::RowNotFound());
            }
        } else {
            Ok(None)
        }
    }

    pub async fn get_running_balance_of_a_model(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Option<f64>, DatabaseError> {
        let query = r#"
                SELECT TOP 1 
                    runningBalance 
                FROM 
                    PortfolioCashLedgers 
                WHERE 
                    ModelPortfolioId = @P1 
                ORDER BY 
                    TxnSequenceId 
                DESC
                
         "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_row()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCashLedger From Database ");
                return DatabaseError::QueryError(e);
            })?;

        if let Some(row) = rows_iter {
            if let Some(running_balance) = row.get(0) {
                return Ok(Some(running_balance));
            } else {
                error!("Couldn't Find Any row with portfolioId = {} in CashLedger", id);
                return Err(DatabaseError::RowNotFound());
            }
        } else {
            Ok(None)
        }
    }

    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, Error> {
        let mut query = Query::new(
            "INSERT INTO PortfolioCashLedgers (
                TransactionDate, SettlementDate,
                TransactionType, TransactionSubType, Amount, RunningBalance, Description,
                TxnRefId, IsModelPortfolio, PortfolioId, ModelPortfolioId
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                @P11
            )",
        );

        query.bind(self.transaction_date);
        query.bind(self.settlement_date);
        query.bind(self.transaction_type.to_string());
        query.bind(self.transaction_sub_type.to_string());
        query.bind(self.amount);
        query.bind(self.running_balance);
        query.bind(self.description.to_owned());
        query.bind(self.txn_ref_id.to_owned());
        query.bind(self.is_model_portfolio);
        query.bind(self.portfolio_id.to_owned());
        query.bind(self.model_portfolio_id.to_owned());

        query.execute(conn).await
    }
}
