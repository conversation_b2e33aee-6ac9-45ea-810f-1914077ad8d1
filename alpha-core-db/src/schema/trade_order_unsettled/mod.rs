use chrono::{NaiveDateTime, Utc};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{ExecuteResult, Query};

use crate::{connection::pool::Manager, error::DatabaseError};

use super::{client_order_entry::TransactionType};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TradeOrderUnsettledAmounts {
    pub id: String,
    pub trade_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub r#type: String, // Using 'r#type' as 'type' is a reserved keyword in Rust
    pub amount: f64,
    pub quantity: f64,
    pub status: String,
    pub settlement_calendar_id: String,
    pub client_order_entry_id: String,
    pub client_id: String,
    pub strategy_model_id: String,
    pub portfolio_id: String,
}

impl TradeOrderUnsettledAmounts {
    fn from_row(row: &tiberius::Row) -> Self {
        TradeOrderUnsettledAmounts {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            trade_date: row.get::<NaiveDateTime, _>("TradeDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            r#type: row.get::<&str, _>("Type").unwrap().to_string(),
            amount: row.get::<f64, _>("Amount").unwrap(),
            quantity: row.get::<f64, _>("Quantity").unwrap(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            settlement_calendar_id: row.get::<&str, _>("SettlementCalendarId").unwrap().to_string(),
            client_order_entry_id: row.get::<&str, _>("ClientOrderEntryId").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            strategy_model_id: row.get::<&str, _>("StrategyModelId").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
        }
    }

    pub async fn get_for_settlement_by_coe_ids(
        conn: &mut Object<Manager>,
        ids: &Vec<String>,
    ) -> Result<Vec<Self>, DatabaseError> {
        let formatted_ids = ids
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
        SELECT
            *
        FROM
            TradeOrderUnsettledAmounts
        WHERE
            ClientOrderEntryId  in ({})
    
        ",
            formatted_ids
        );

        let rows_iter = conn.query(query, &[]).await?.into_first_result().await?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }

    pub async fn get_by_coe_id(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            TradeOrderUnsettledAmounts
        WHERE
            ClientOrderEntryId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn bulk_update_on_settlement(
        conn: &mut Object<Manager>,
        toeuas: &Vec<Self>,
    ) -> Result<ExecuteResult, DatabaseError> {
        let mut query = String::from("");

        for toeua in toeuas {
            let q = format!(
                "UPDATE TradeOrderUnsettledAmounts SET \
                Status = '{}', \
                Amount = {}, \
                LastUpdatedDate = '{}' \
                WHERE Id = '{}';",
                &toeua.status,
                toeua.amount,
                Utc::now().naive_utc(),
                &toeua.id,
            );

            query += &q;
        }

        conn.execute(query, &[]).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn update_status_and_amount(
        &self,
        conn: &mut Object<Manager>,
    ) -> Result<tiberius::ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
                UPDATE
                    TradeOrderUnsettledAmounts
                SET
                    Status = @P1,
                    Amount = @P2
                WHERE 
                    Id = @P3
            ",
        );

        query.bind(&self.status);
        query.bind(self.amount);
        query.bind(&self.id);

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn get_trade_orders_portfolio_id(
        conn: &mut Object<Manager>,
        status: String,
        portfolio_id: String,
        transaction_type: TransactionType,
    ) -> Result<Vec<Self>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                TradeOrderUnsettledAmounts
            WHERE
                PortfolioId = @P1
                AND
                Status = @P2
                AND
                Type = @P3
        ";

        let rows_iter = conn
            .query(query, &[&portfolio_id, &status, &transaction_type.to_string()])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?;

        let res: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(res)
    }
}
