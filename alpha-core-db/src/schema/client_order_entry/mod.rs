use std::{
    fmt::{self},
    str::FromStr,
};

use chrono::{NaiveDateTime, Utc};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use tiberius::{ExecuteResult, IntoSql, Query, Row, TokenRow};

use crate::{
    connection::pool::Manager,
    error::DatabaseError,
    types::{OrderSourceType, OrderType},
};

use super::{
    client::{Client, DomicileType},
    trade_order_unsettled::TradeOrderUnsettledAmounts,
};

#[derive(Debug, Serialize, Deserialize, Clone, Default, PartialEq, Eq, Hash)]
pub enum SecurityType {
    #[default]
    Stocks,
    ETF,
    MutualFund,
    FixedIncome,
    Commodity,
    EquityDerivatives,
    PlainDebt,
    StructuredDebt,
    Others,
    CommodityDerivatives,
    Goods,
    CashEquivalent,
    Cash,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default, PartialEq, Eq)]
pub enum SecuritySubType {
    Listed,
    Unlisted,
    ETF,
    Ipo,
    MutualFund,
    Bond,
    Debenture,
    TreasuryBill,
    SoverignBond,
    Gold,
    Futures,
    Options,
    Swaps,
    Commodity,
    Equity,
    Cash,
    #[default]
    Others,
}

#[derive(Debug, Serialize, Deserialize, Default, Clone, PartialEq, Copy)]
pub enum TransactionType {
    #[default]
    Buy,
    Sell,
    DividendReinvestment,
    DividendPaid,
    Inflow,
    Outflow,
    Debit,
    Credit,
    SecurityIn,
    SecurityOut,
}

impl fmt::Display for TransactionType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            TransactionType::Buy => write!(f, "Buy"),
            TransactionType::Sell => write!(f, "Sell"),
            TransactionType::DividendReinvestment => write!(f, "DividendReinvestment"),
            TransactionType::DividendPaid => write!(f, "DividendPaid"),
            TransactionType::Inflow => write!(f, "Inflow"),
            TransactionType::Outflow => write!(f, "Outflow"),
            TransactionType::Debit => write!(f, "Debit"),
            TransactionType::Credit => write!(f, "Credit"),
            TransactionType::SecurityIn => write!(f, "SecurityIn"),
            TransactionType::SecurityOut => write!(f, "SecurityOut"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Default, Clone, PartialEq)]
pub enum TransactionSubType {
    #[default]
    Buy,
    BuyAdjustment,
    Sell,
    SellAdjustment,
    Purchase,
    Redemption,
    DividendReinvestment,
    Dividend,
    SwitchIn,
    SwitchOut,
    TransferIn,
    TransferOut,
    Split,
    BuySplitAdjustment,
    Bonus,
    BuyBonusAdjustment,
    SecurityIn,
    SecurityOut,
    CapitalIn,
    CapitalOut,
    Fees,
    PerformanceFees,
    Charges,
    Tds,
    Other,
    Receipt,
    Payment,
    Interest,
    Tax,
    FractionPayment,
    SplitPartial,
    BonusPartial,
    ReturnOnCapital,
    IncomeSurplus,
    Merger,
    Demerger,
    Amalgamation,
    MergerPartial,
    DemergerPartial,
    AmalgamationPartial,
    SpinOff,
    SpinOffPartial,
    CashBalance,
    PeakMargin,
    Stt,
    AccoutOpeningCharges,
}

impl fmt::Display for TransactionSubType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TransactionSubType::Buy => write!(f, "Buy"),
            TransactionSubType::BuyAdjustment => write!(f, "BuyAdjustment"),
            TransactionSubType::Sell => write!(f, "Sell"),
            TransactionSubType::SellAdjustment => write!(f, "SellAdjustment"),
            TransactionSubType::Purchase => write!(f, "Purchase"),
            TransactionSubType::Redemption => write!(f, "Redemption"),
            TransactionSubType::DividendReinvestment => write!(f, "DividendReinvestment"),
            TransactionSubType::Dividend => write!(f, "Dividend"),
            TransactionSubType::SwitchIn => write!(f, "SwitchIn"),
            TransactionSubType::SwitchOut => write!(f, "SwitchOut"),
            TransactionSubType::TransferIn => write!(f, "TransferIn"),
            TransactionSubType::TransferOut => write!(f, "TransferOut"),
            TransactionSubType::Split => write!(f, "Split"),
            TransactionSubType::BuySplitAdjustment => write!(f, "BuySplitAdjustment"),
            TransactionSubType::Bonus => write!(f, "Bonus"),
            TransactionSubType::BuyBonusAdjustment => write!(f, "BuyBonusAdjustment"),
            TransactionSubType::SecurityIn => write!(f, "SecurityIn"),
            TransactionSubType::SecurityOut => write!(f, "SecurityOut"),
            TransactionSubType::CapitalIn => write!(f, "CapitalIn"),
            TransactionSubType::CapitalOut => write!(f, "CapitalOut"),
            TransactionSubType::Fees => write!(f, "Fees"),
            TransactionSubType::PerformanceFees => write!(f, "PerformanceFees"),
            TransactionSubType::Charges => write!(f, "Charges"),
            TransactionSubType::Tds => write!(f, "Tds"),
            TransactionSubType::Other => write!(f, "Other"),
            TransactionSubType::Receipt => write!(f, "Receipt"),
            TransactionSubType::Payment => write!(f, "Payment"),
            TransactionSubType::Interest => write!(f, "Interest"),
            TransactionSubType::Tax => write!(f, "Tax"),
            TransactionSubType::FractionPayment => write!(f, "FractionPayment"),
            TransactionSubType::SplitPartial => write!(f, "SplitPartial"),
            TransactionSubType::BonusPartial => write!(f, "BonusPartial"),
            TransactionSubType::ReturnOnCapital => write!(f, "ReturnOnCapital"),
            TransactionSubType::IncomeSurplus => write!(f, "IncomeSurplus"),
            TransactionSubType::Merger => write!(f, "Merger"),
            TransactionSubType::Demerger => write!(f, "Demerger"),
            TransactionSubType::Amalgamation => write!(f, "Amalgamation"),
            TransactionSubType::MergerPartial => write!(f, "MergerPartial"),
            TransactionSubType::DemergerPartial => write!(f, "DemergerPartial"),
            TransactionSubType::AmalgamationPartial => write!(f, "AmalgamationPartial"),
            TransactionSubType::SpinOff => write!(f, "SpinOff"),
            TransactionSubType::SpinOffPartial => write!(f, "SpinOffPartial"),
            TransactionSubType::CashBalance => write!(f, "CashBalance"),
            TransactionSubType::PeakMargin => write!(f, "PeakMargin"),
            TransactionSubType::Stt => write!(f, "Stt"),
            TransactionSubType::AccoutOpeningCharges => write!(f, "AccoutOpeningCharges"),
        }
    }
}

impl std::str::FromStr for SecurityType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Stocks" => Ok(SecurityType::Stocks),
            "ETF" => Ok(SecurityType::ETF),
            "MutualFund" => Ok(SecurityType::MutualFund),
            "FixedIncome" => Ok(SecurityType::FixedIncome),
            "Commodity" => Ok(SecurityType::Commodity),
            "EquityDerivatives" => Ok(SecurityType::EquityDerivatives),
            "PlainDebt" => Ok(SecurityType::PlainDebt),
            "StructuredDebt" => Ok(SecurityType::StructuredDebt),
            "Others" => Ok(SecurityType::Others),
            "CommodityDerivatives" => Ok(SecurityType::CommodityDerivatives),
            "Goods" => Ok(SecurityType::Goods),
            "CashEquivalent" => Ok(SecurityType::CashEquivalent),
            "Cash" => Ok(SecurityType::Cash),
            _ => Err(()),
        }
    }
}

impl FromStr for SecuritySubType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "listed" => Ok(SecuritySubType::Listed),
            "unlisted" => Ok(SecuritySubType::Unlisted),
            "etf" => Ok(SecuritySubType::ETF),
            "ipo" => Ok(SecuritySubType::Ipo),
            "mutualfund" | "mutual fund" => Ok(SecuritySubType::MutualFund),
            "bond" => Ok(SecuritySubType::Bond),
            "debenture" => Ok(SecuritySubType::Debenture),
            "treasurybill" | "treasury bill" => Ok(SecuritySubType::TreasuryBill),
            "sovereignbond" | "sovereign bond" => Ok(SecuritySubType::SoverignBond),
            "gold" => Ok(SecuritySubType::Gold),
            "futures" => Ok(SecuritySubType::Futures),
            "options" => Ok(SecuritySubType::Options),
            "swaps" => Ok(SecuritySubType::Swaps),
            "commodity" => Ok(SecuritySubType::Commodity),
            "equity" => Ok(SecuritySubType::Equity),
            "others" => Ok(SecuritySubType::Others),
            "cash" => Ok(SecuritySubType::Cash),
            _ => Ok(SecuritySubType::Others),
        }
    }
}

impl FromStr for TransactionType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Buy" => Ok(TransactionType::Buy),
            "Sell" => Ok(TransactionType::Sell),
            "DividendReinvestment" => Ok(TransactionType::DividendReinvestment),
            "DividendPaid" => Ok(TransactionType::DividendPaid),
            "Inflow" => Ok(TransactionType::Inflow),
            "Outflow" => Ok(TransactionType::Outflow),
            "Debit" => Ok(TransactionType::Debit),
            "Credit" => Ok(TransactionType::Credit),
            _ => Err(()),
        }
    }
}

impl FromStr for TransactionSubType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Buy" => Ok(TransactionSubType::Buy),
            "BuyAdjustment" => Ok(TransactionSubType::BuyAdjustment),
            "Sell" => Ok(TransactionSubType::Sell),
            "SellAdjustment" => Ok(TransactionSubType::SellAdjustment),
            "Purchase" => Ok(TransactionSubType::Purchase),
            "Redemption" => Ok(TransactionSubType::Redemption),
            "DividendReinvestment" => Ok(TransactionSubType::DividendReinvestment),
            "Dividend" => Ok(TransactionSubType::Dividend),
            "SwitchIn" => Ok(TransactionSubType::SwitchIn),
            "SwitchOut" => Ok(TransactionSubType::SwitchOut),
            "TransferIn" => Ok(TransactionSubType::TransferIn),
            "TransferOut" => Ok(TransactionSubType::TransferOut),
            "Split" => Ok(TransactionSubType::Split),
            "BuySplitAdjustment" => Ok(TransactionSubType::BuySplitAdjustment),
            "Bonus" => Ok(TransactionSubType::Bonus),
            "BuyBonusAdjustment" => Ok(TransactionSubType::BuyBonusAdjustment),
            "SecurityIn" => Ok(TransactionSubType::SecurityIn),
            "SecurityOut" => Ok(TransactionSubType::SecurityOut),
            "CapitalIn" => Ok(TransactionSubType::CapitalIn),
            "CapitalOut" => Ok(TransactionSubType::CapitalOut),
            "Fees" => Ok(TransactionSubType::Fees),
            "PerformanceFees" => Ok(TransactionSubType::PerformanceFees),
            "Charges" => Ok(TransactionSubType::Charges),
            "Tds" => Ok(TransactionSubType::Tds),
            "Other" => Ok(TransactionSubType::Other),
            "Receipt" => Ok(TransactionSubType::Receipt),
            "Payment" => Ok(TransactionSubType::Payment),
            "Interest" => Ok(TransactionSubType::Interest),
            "Tax" => Ok(TransactionSubType::Tax),
            "FractionPayment" => Ok(TransactionSubType::FractionPayment),
            "SplitPartial" => Ok(TransactionSubType::SplitPartial),
            "BonusPartial" => Ok(TransactionSubType::BonusPartial),
            "ReturnOnCapital" => Ok(TransactionSubType::ReturnOnCapital),
            "IncomeSurplus" => Ok(TransactionSubType::IncomeSurplus),
            "Merger" => Ok(TransactionSubType::Merger),
            "Demerger" => Ok(TransactionSubType::Demerger),
            "Amalgamation" => Ok(TransactionSubType::Amalgamation),
            "MergerPartial" => Ok(TransactionSubType::MergerPartial),
            "DemergerPartial" => Ok(TransactionSubType::DemergerPartial),
            "AmalgamationPartial" => Ok(TransactionSubType::AmalgamationPartial),
            "SpinOff" => Ok(TransactionSubType::SpinOff),
            "SpinOffPartial" => Ok(TransactionSubType::SpinOffPartial),
            "CashBalance" => Ok(TransactionSubType::CashBalance),
            "PeakMargin" => Ok(TransactionSubType::PeakMargin),
            _ => Err(()),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Default, PartialEq)]
pub enum OrderStatus {
    #[default]
    Draft,
    AwaitingManagerApproval,
    AwaitingClientApproval,
    SentToExchange,
    SentToBroker,
    PartiallySettled,
    Settled,
    Failed,
    Cancelled,
    Abandoned,
}

impl fmt::Display for OrderStatus {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            OrderStatus::Draft => write!(f, "Draft"),
            OrderStatus::AwaitingManagerApproval => write!(f, "AwaitingManagerApproval"),
            OrderStatus::AwaitingClientApproval => write!(f, "AwaitingClientApproval"),
            OrderStatus::SentToExchange => write!(f, "SentToExchange"),
            OrderStatus::SentToBroker => write!(f, "SentToBroker"),
            OrderStatus::PartiallySettled => write!(f, "PartiallySettled"),
            OrderStatus::Settled => write!(f, "Settled"),
            OrderStatus::Failed => write!(f, "Failed"),
            OrderStatus::Cancelled => write!(f, "Cancelled"),
            OrderStatus::Abandoned => write!(f, "Abandoned"),
        }
    }
}

impl fmt::Display for SecuritySubType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SecuritySubType::Listed => write!(f, "Listed"),
            SecuritySubType::Unlisted => write!(f, "Unlisted"),
            SecuritySubType::ETF => write!(f, "ETF"),
            SecuritySubType::Ipo => write!(f, "Ipo"),
            SecuritySubType::MutualFund => write!(f, "MutualFund"),
            SecuritySubType::Bond => write!(f, "Bond"),
            SecuritySubType::Debenture => write!(f, "Debenture"),
            SecuritySubType::TreasuryBill => write!(f, "TreasuryBill"),
            SecuritySubType::SoverignBond => write!(f, "SoverignBond"),
            SecuritySubType::Gold => write!(f, "Gold"),
            SecuritySubType::Futures => write!(f, "Futures"),
            SecuritySubType::Options => write!(f, "Options"),
            SecuritySubType::Swaps => write!(f, "Swaps"),
            SecuritySubType::Commodity => write!(f, "Commodity"),
            SecuritySubType::Equity => write!(f, "Equity"),
            SecuritySubType::Others => write!(f, "Others"),
            SecuritySubType::Cash => write!(f, "Cash"),
        }
    }
}

impl fmt::Display for SecurityType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SecurityType::Stocks => write!(f, "Stocks"),
            SecurityType::ETF => write!(f, "ETF"),
            SecurityType::MutualFund => write!(f, "MutualFund"),
            SecurityType::FixedIncome => write!(f, "FixedIncome"),
            SecurityType::Commodity => write!(f, "Commodity"),
            SecurityType::EquityDerivatives => write!(f, "EquityDerivatives"),
            SecurityType::PlainDebt => write!(f, "PlainDebt"),
            SecurityType::StructuredDebt => write!(f, "StructuredDebt"),
            SecurityType::Others => write!(f, "Others"),
            SecurityType::CommodityDerivatives => write!(f, "CommodityDerivatives"),
            SecurityType::Goods => write!(f, "Goods"),
            SecurityType::CashEquivalent => write!(f, "CashEquivalent"),
            SecurityType::Cash => write!(f, "Cash"),
        }
    }
}

#[derive(Debug, Clone, Default, PartialEq)]
pub struct ClientOrderEntry {
    pub id: String,
    pub created_date: NaiveDateTime,
    pub last_updated_date: NaiveDateTime,
    pub client_id: String,
    pub client_code: String,
    pub strategy_code: String,
    pub strategy_name: String,
    pub strategy_model_name: String,
    pub strategy_custody_code: String,
    pub strategy_trading_account_number: Option<String>,
    pub strategy_model_id: String,
    pub identifier: String,
    pub isin: String,
    pub exchange: String,
    pub scrip_name: String,
    pub investment_type: SecurityType,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub quantity: f64,
    pub currency: Option<String>,
    pub currency_conversion_rate: Option<f64>,
    pub pending_quantity: f64,
    pub price: f64,
    pub order_type: OrderType,
    pub order_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub order_status: OrderStatus,
    pub transaction_amount: f64,
    pub settlement_quantity: f64,
    pub settlement_price: f64,
    pub settlement_market_amount: f64,
    pub settlement_brokerage_amount: f64,
    pub settlement_service_tax: f64,
    pub settlement_stt_amount: f64,
    pub settlement_net_rate: f64,
    pub settlement_turn_tax: f64,
    pub settlement_other_tax: f64,
    pub settlement_net_amount: f64,
    pub source_type: String,
    pub source_reference: String,
    pub mf_folio_number: Option<String>,
    pub mf_buy_sell_type: Option<String>,
    pub mf_all_units_redemption_flag: Option<String>,
    pub mf_client_bank: Option<String>,
    pub mf_oms_client_code: Option<String>,
    pub euin_number: Option<String>,
    pub client_domicile: DomicileType,
    pub client_trading_account: String,
    pub remarks: String,
    pub order_rationale: String,
    pub peak_margin: Option<String>,
    pub peak_margin_ref: Option<String>,
    pub portfolio_id: String,
    pub created_by: Option<String>,
    pub series: Option<String>,
}

impl ClientOrderEntry {
    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<tiberius::ExecuteResult, tiberius::error::Error> {
        let mut query = Query::new(
            "INSERT INTO ClientOrderEntries (
                Identifier, Isin, Exchange, ScripName, InvestmentType,
                TransactionType, TransactionSubType, Quantity, PendingQuantity, Price,
                OrderType, OrderDate, SettlementDate, StrategyCode, StrategyName,
                StrategyModelName, StrategyTradingAccountNumber, StrategyCustodyCode, StrategyModelId, OrderStatus,
                TransactionAmount, SettlementQuantity, SettlementPrice, SettlementMarketAmount, SettlementBrokerageAmount,
                SettlementServiceTax, SettlementSttAmount, SettlementNetRate, SettlementTurnTax, SettlementOtherTax,
                SettlementNetAmount, ClientCode, ClientId, SourceReference, SourceType,
                MfFolioNumber, MfAllUnitsRedemptionFlag, MfBuySellType, MfClientBank, EUINNumber,
                MfOmsClientCode, ClientDomicile, ClientTradingAccount, OrderRationale, Remarks,
                PortfolioId, PeakMargin, Currency, PeakMarginRef, CurrencyConversionRate,
                CreatedBy, Series
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                @P11, @P12, @P13, @P14, @P15, @P16, @P17, @P18, @P19, @P20,
                @P21, @P22, @P23, @P24, @P25, @P26, @P27, @P28, @P29, @P30,
                @P31, @P32, @P33, @P34, @P35, @P36, @P37, @P38, @P39, @P40,
                @P41, @P42, @P43, @P44, @P45, @P46, @P47, @P48, @P49, @P50,
                @P51, @P52
            )"
        );

        query.bind(&self.identifier);
        query.bind(&self.isin);
        query.bind(&self.exchange);
        query.bind(&self.scrip_name);
        query.bind(self.investment_type.to_string());
        query.bind(self.transaction_type.to_string());
        query.bind(self.transaction_sub_type.to_string());
        query.bind(self.quantity);
        query.bind(self.pending_quantity);
        query.bind(self.price);
        query.bind(self.order_type.to_string());
        query.bind(self.order_date);
        query.bind(self.settlement_date);
        query.bind(&self.strategy_code);
        query.bind(&self.strategy_name);
        query.bind(&self.strategy_model_name);
        query.bind(self.strategy_trading_account_number.to_owned());
        query.bind(&self.strategy_custody_code);
        query.bind(&self.strategy_model_id);
        query.bind(self.order_status.to_string());
        query.bind(self.transaction_amount);
        query.bind(self.settlement_quantity);
        query.bind(self.settlement_price);
        query.bind(self.settlement_market_amount);
        query.bind(self.settlement_brokerage_amount);
        query.bind(self.settlement_service_tax);
        query.bind(self.settlement_stt_amount);
        query.bind(self.settlement_net_rate);
        query.bind(self.settlement_turn_tax);
        query.bind(self.settlement_other_tax);
        query.bind(self.settlement_net_amount);
        query.bind(&self.client_code);
        query.bind(&self.client_id);
        query.bind(&self.source_reference);
        query.bind(self.source_type.to_string());
        query.bind(self.mf_folio_number.to_owned());
        query.bind(self.mf_all_units_redemption_flag.to_owned());
        query.bind(self.mf_buy_sell_type.to_owned());
        query.bind(self.mf_client_bank.to_owned());
        query.bind(self.euin_number.to_owned());
        query.bind(self.mf_oms_client_code.to_owned());
        query.bind(self.client_domicile.to_string());
        query.bind(&self.client_trading_account);
        query.bind(&self.order_rationale);
        query.bind(&self.remarks);
        query.bind(&self.portfolio_id);
        query.bind(self.peak_margin.to_owned());
        query.bind(self.currency.to_owned());
        query.bind(self.peak_margin_ref.to_owned());
        query.bind(self.currency_conversion_rate);
        query.bind(self.created_by.to_owned());
        query.bind(self.series.to_owned());

        query.execute(conn).await
    }

    pub async fn bulk_insert(client: &mut Object<Manager>, records: Vec<Self>) -> Result<(), DatabaseError> {
        let temp_table = "CREATE TABLE ##TempTable (
                    Identifier varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Isin varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Exchange varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    ScripName varchar(1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    InvestmentType varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    TransactionType varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    TransactionSubType varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Quantity float NOT NULL,
                    PendingQuantity float NOT NULL,
                    Price float NOT NULL,
                    OrderType varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    OrderDate datetime2 NOT NULL,
                    SettlementDate datetime2 NOT NULL,
                    StrategyCode varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    StrategyName varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    StrategyModelName varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    StrategyTradingAccountNumber varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    StrategyCustodyCode varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    StrategyModelId varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    OrderStatus varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    TransactionAmount float NOT NULL,
                    SettlementQuantity float NOT NULL,
                    SettlementPrice float NOT NULL,
                    SettlementMarketAmount float NOT NULL,
                    SettlementBrokerageAmount float NOT NULL,
                    SettlementServiceTax float NOT NULL,
                    SettlementSttAmount float NOT NULL,
                    SettlementNetRate float NOT NULL,
                    SettlementTurnTax float NOT NULL,
                    SettlementOtherTax float NOT NULL,
                    SettlementNetAmount float NOT NULL,
                    ClientCode varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    ClientId varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    SourceReference varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    SourceType nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    MfFolioNumber varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    MfAllUnitsRedemptionFlag varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    MfBuySellType varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    MfClientBank varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    EUINNumber varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    MfOmsClientCode varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    ClientDomicile varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT '' NOT NULL,
                    ClientTradingAccount varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    OrderRationale varchar(500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Remarks nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    PortfolioId varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    PeakMargin varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Currency varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    PeakMarginRef varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    CurrencyConversionRate float DEFAULT 0.0000000000000000e+000 NOT NULL,
                    CreatedBy varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                    Series varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
                )";

        client.simple_query(temp_table).await?;
        // Start bulk insert
        let mut bulk_insert = client.bulk_insert("##TempTable").await?;

        let date = Utc::now();

        // Send each record
        for record in records {
            let mut token = TokenRow::new();
            token.push(record.identifier.clone().into_sql());
            token.push(record.isin.clone().into_sql());
            token.push(record.exchange.clone().into_sql());
            token.push(record.scrip_name.clone().into_sql());
            token.push(record.investment_type.to_string().into_sql());
            token.push(record.transaction_type.to_string().into_sql());
            token.push(record.transaction_sub_type.to_string().into_sql());
            token.push(record.quantity.into_sql());
            token.push(record.pending_quantity.into_sql());
            token.push(record.price.into_sql());
            token.push(record.order_type.to_string().into_sql());
            token.push(record.order_date.into_sql());
            token.push(record.settlement_date.into_sql());
            token.push(record.strategy_code.clone().into_sql());
            token.push(record.strategy_name.clone().into_sql());
            token.push(record.strategy_model_name.clone().into_sql());
            token.push(record.strategy_trading_account_number.clone().into_sql());
            token.push(record.strategy_custody_code.clone().into_sql());
            token.push(record.strategy_model_id.clone().into_sql());
            token.push(record.order_status.to_string().into_sql());
            token.push(record.transaction_amount.into_sql());
            token.push(record.settlement_quantity.into_sql());
            token.push(record.settlement_price.into_sql());
            token.push(record.settlement_market_amount.into_sql());
            token.push(record.settlement_brokerage_amount.into_sql());
            token.push(record.settlement_service_tax.into_sql());
            token.push(record.settlement_stt_amount.into_sql());
            token.push(record.settlement_net_rate.into_sql());
            token.push(record.settlement_turn_tax.into_sql());
            token.push(record.settlement_other_tax.into_sql());
            token.push(record.settlement_net_amount.into_sql());
            token.push(record.client_code.clone().into_sql());
            token.push(record.client_id.clone().into_sql());
            token.push(record.source_reference.clone().into_sql());
            token.push(record.source_type.to_string().into_sql());
            token.push(record.mf_folio_number.clone().into_sql());
            token.push(record.mf_all_units_redemption_flag.clone().into_sql());
            token.push(record.mf_buy_sell_type.clone().into_sql());
            token.push(record.mf_client_bank.clone().into_sql());
            token.push(record.euin_number.clone().into_sql());
            token.push(record.mf_oms_client_code.clone().into_sql());
            token.push(record.client_domicile.to_string().into_sql());
            token.push(record.client_trading_account.clone().into_sql());
            token.push(record.order_rationale.clone().into_sql());
            token.push(record.remarks.clone().into_sql());
            token.push(record.portfolio_id.clone().into_sql());
            token.push(record.peak_margin.clone().into_sql());
            token.push(record.currency.clone().into_sql());
            token.push(record.peak_margin_ref.clone().into_sql());
            token.push(record.currency_conversion_rate.into_sql());
            token.push(record.created_by.clone().into_sql());
            token.push(record.series.clone().into_sql());

            bulk_insert.send(token).await?;
        }

        let res = bulk_insert.finalize().await?;
        println!("{:?}", res);

        // Insert from temp table to main table, letting SQL Server generate IDs
        client
            .execute(
                r#"
            INSERT INTO ClientOrderEntries (
            Identifier, Isin, Exchange, ScripName, InvestmentType, TransactionType, TransactionSubType, Quantity, PendingQuantity,
            Price, OrderType, OrderDate, SettlementDate, StrategyCode, StrategyName, StrategyModelName, 
            StrategyTradingAccountNumber, StrategyCustodyCode, StrategyModelId, OrderStatus, TransactionAmount,
            SettlementQuantity, SettlementPrice, SettlementMarketAmount, SettlementBrokerageAmount, 
            SettlementServiceTax, SettlementSttAmount, SettlementNetRate, SettlementTurnTax, SettlementOtherTax, 
            SettlementNetAmount, ClientCode, ClientId, SourceReference, SourceType, MfFolioNumber, MfAllUnitsRedemptionFlag,
            MfBuySellType, MfClientBank, EUINNumber, MfOmsClientCode, ClientDomicile, ClientTradingAccount, OrderRationale, 
            Remarks, PortfolioId, PeakMargin, Currency, PeakMarginRef, CurrencyConversionRate, CreatedBy, Series
            )
            SELECT * FROM ##TempTable;
            DROP TABLE ##TempTable;
            "#,
                &[],
            )
            .await?;

        Ok(())
    }

    pub async fn update_bulk_on_settlement(
        conn: &mut Object<Manager>,
        coes: &Vec<Self>,
    ) -> Result<ExecuteResult, tiberius::error::Error> {
        let mut query = String::from("");

        for coe in coes {
            let q = format!(
                "UPDATE ClientOrderEntries SET \
                 SettlementDate = '{}', \
                 SettlementQuantity = {}, \
                 SettlementPrice = {}, \
                 SettlementMarketAmount = {}, \
                 SettlementBrokerageAmount = {}, \
                 SettlementServiceTax = {}, \
                 SettlementNetRate = {}, \
                 SettlementSttAmount = {}, \
                 SettlementTurnTax = {}, \
                 SettlementOtherTax = {}, \
                 SettlementNetAmount = {}, \
                 PendingQuantity = {}, \
                 OrderStatus = '{}', \
                 LastUpdatedDate = '{}' \
                 WHERE id = '{}';",
                coe.settlement_date,
                coe.settlement_quantity,
                coe.settlement_price,
                coe.settlement_market_amount,
                coe.settlement_brokerage_amount,
                coe.settlement_service_tax,
                coe.settlement_net_rate,
                coe.settlement_stt_amount,
                coe.settlement_turn_tax,
                coe.settlement_other_tax,
                coe.settlement_net_amount,
                coe.pending_quantity,
                coe.order_status.to_string(),
                Utc::now().naive_utc(),
                coe.id
            );
            query += &q;
        }

        conn.execute(query, &[]).await
    }

    pub async fn update_on_settlement(&self, conn: &mut Object<Manager>) {
        let mut query = Query::new(
            "UPDATE ClientOrderEntries SET 
            SettlementDate = @p1,
            SettlementQuantity = @p2,
            SettlementPrice = @p3,
            SettlementMarketAmount = @p4,
            SettlementBrokerageAmount = @p5,
            SettlementServiceTax = @p6,
            SettlementNetRate = @p7,
            SettlementSttAmount = @p8,
            SettlementTurnTax = @p9,
            SettlementOtherTax = @p10,
            SettlementNetAmount = @p11,
            PendingQuantity = @p12,
            OrderStatus = @p13,
            LastUpdatedDate = @p14
            WHERE id = @p15",
        );

        query.bind(self.settlement_date);
        query.bind(self.settlement_quantity);
        query.bind(self.settlement_price);
        query.bind(self.settlement_market_amount);
        query.bind(self.settlement_brokerage_amount);
        query.bind(self.settlement_service_tax);
        query.bind(self.settlement_net_rate);
        query.bind(self.settlement_stt_amount);
        query.bind(self.settlement_turn_tax);
        query.bind(self.settlement_other_tax);
        query.bind(self.settlement_net_amount);
        query.bind(self.pending_quantity);
        query.bind(self.order_status.to_string());
        query.bind(Utc::now().naive_utc());
        query.bind(&self.id);

        query.execute(conn).await.unwrap();
    }

    pub async fn get_total_orders_settled_orders_by_source_reference(
        conn: &mut Object<Manager>,
        source_reference: &str,
    ) -> Result<(i32, i32, i32), DatabaseError> {
        let query = "
            SELECT 
                COUNT(*) as Count,
                COUNT(CASE WHEN OrderStatus = 'Settled' THEN 1 END) as SettledCount,
                COUNT(CASE WHEN OrderStatus = 'PartiallySettled' THEN 1 END) as PartiallySettledCount
            FROM ClientOrderEntries 
            WHERE SourceReference = @P1 
            AND Quantity > 0.00
            ";

        let row = conn
            .query(query, &[&source_reference])
            .await?
            .into_row()
            .await?
            .ok_or_else(|| {
                return DatabaseError::RowNotFound();
            })?;

        let total_orders: i32 = row.get("Count").unwrap();
        let total_settled_orders: i32 = row.get("SettledCount").unwrap();
        let total_partially_settled_orders: i32 = row.get("PartiallySettledCount").unwrap();

        Ok((total_orders, total_settled_orders, total_partially_settled_orders))
    }
}

impl ClientOrderEntry {
    fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            created_date: row.get::<NaiveDateTime, _>("CreatedDate").unwrap(),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            strategy_code: row.get::<&str, _>("StrategyCode").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
            strategy_model_name: row.get::<&str, _>("StrategyModelName").unwrap().to_string(),
            strategy_custody_code: row.get::<&str, _>("StrategyCustodyCode").unwrap().to_string(),
            strategy_trading_account_number: row.get::<&str, _>("StrategyTradingAccountNumber").map(String::from),
            strategy_model_id: row.get::<&str, _>("StrategyModelId").unwrap().to_string(),
            identifier: row.get::<&str, _>("Identifier").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            scrip_name: row.get::<&str, _>("ScripName").unwrap().to_string(),
            investment_type: match row.get::<&str, _>("InvestmentType").unwrap() {
                "Stocks" => SecurityType::Stocks,
                "ETF" => SecurityType::ETF,
                "MutualFund" => SecurityType::MutualFund,
                "FixedIncome" => SecurityType::FixedIncome,
                "Commodity" => SecurityType::Commodity,
                "EquityDerivatives" => SecurityType::EquityDerivatives,
                "PlainDebt" => SecurityType::PlainDebt,
                "StructuredDebt" => SecurityType::StructuredDebt,
                "Others" => SecurityType::Others,
                "CommodityDerivatives" => SecurityType::CommodityDerivatives,
                "Goods" => SecurityType::Goods,
                "CashEquivalent" => SecurityType::CashEquivalent,
                "Cash" => SecurityType::Cash,
                _ => panic!("Invalid SecurityType"),
            },
            transaction_type: match row.get::<&str, _>("TransactionType").unwrap() {
                "Buy" => TransactionType::Buy,
                "Sell" => TransactionType::Sell,
                "DividendReinvestment" => TransactionType::DividendReinvestment,
                "DividendPaid" => TransactionType::DividendPaid,
                "Inflow" => TransactionType::Inflow,
                "Outflow" => TransactionType::Outflow,
                "Debit" => TransactionType::Debit,
                "Credit" => TransactionType::Credit,
                _ => panic!("Invalid TransactionType"),
            },
            transaction_sub_type: match row.get::<&str, _>("TransactionSubType").unwrap() {
                "Buy" => TransactionSubType::Buy,
                "BuyAdjustment" => TransactionSubType::BuyAdjustment,
                "Sell" => TransactionSubType::Sell,
                "SellAdjustment" => TransactionSubType::SellAdjustment,
                "Purchase" => TransactionSubType::Purchase,
                "Redemption" => TransactionSubType::Redemption,
                "DividendReinvestment" => TransactionSubType::DividendReinvestment,
                "Dividend" => TransactionSubType::Dividend,
                "SwitchIn" => TransactionSubType::SwitchIn,
                "SwitchOut" => TransactionSubType::SwitchOut,
                "TransferIn" => TransactionSubType::TransferIn,
                "TransferOut" => TransactionSubType::TransferOut,
                "Split" => TransactionSubType::Split,
                "BuySplitAdjustment" => TransactionSubType::BuySplitAdjustment,
                "Bonus" => TransactionSubType::Bonus,
                "BuyBonusAdjustment" => TransactionSubType::BuyBonusAdjustment,
                "SecurityIn" => TransactionSubType::SecurityIn,
                "SecurityOut" => TransactionSubType::SecurityOut,
                "CapitalIn" => TransactionSubType::CapitalIn,
                "CapitalOut" => TransactionSubType::CapitalOut,
                "Fees" => TransactionSubType::Fees,
                "PerformanceFees" => TransactionSubType::PerformanceFees,
                "Charges" => TransactionSubType::Charges,
                "Tds" => TransactionSubType::Tds,
                "Other" => TransactionSubType::Other,
                "Receipt" => TransactionSubType::Receipt,
                "Payment" => TransactionSubType::Payment,
                "Interest" => TransactionSubType::Interest,
                "Tax" => TransactionSubType::Tax,
                "FractionPayment" => TransactionSubType::FractionPayment,
                "SplitPartial" => TransactionSubType::SplitPartial,
                "BonusPartial" => TransactionSubType::BonusPartial,
                "ReturnOnCapital" => TransactionSubType::ReturnOnCapital,
                "IncomeSurplus" => TransactionSubType::IncomeSurplus,
                "Merger" => TransactionSubType::Merger,
                "Demerger" => TransactionSubType::Demerger,
                "Amalgamation" => TransactionSubType::Amalgamation,
                "MergerPartial" => TransactionSubType::MergerPartial,
                "DemergerPartial" => TransactionSubType::DemergerPartial,
                "AmalgamationPartial" => TransactionSubType::AmalgamationPartial,
                "SpinOff" => TransactionSubType::SpinOff,
                "SpinOffPartial" => TransactionSubType::SpinOffPartial,
                "CashBalance" => TransactionSubType::CashBalance,
                "PeakMargin" => TransactionSubType::PeakMargin,
                _ => panic!("Invalid TransactionSubType"),
            },
            quantity: row.get::<f64, _>("Quantity").unwrap(),
            currency: row.get::<&str, _>("Currency").map(String::from),
            currency_conversion_rate: row.get::<f64, _>("CurrencyConversionRate"),
            pending_quantity: row.get::<f64, _>("PendingQuantity").unwrap(),
            price: row.get::<f64, _>("Price").unwrap(),
            order_type: OrderType::from_str(row.get::<&str, _>("OrderType").unwrap()).unwrap(),
            order_date: row.get::<NaiveDateTime, _>("OrderDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            order_status: match row.get::<&str, _>("OrderStatus").unwrap() {
                "Draft" => OrderStatus::Draft,
                "AwaitingManagerApproval" => OrderStatus::AwaitingManagerApproval,
                "AwaitingClientApproval" => OrderStatus::AwaitingClientApproval,
                "SentToExchange" => OrderStatus::SentToExchange,
                "SentToBroker" => OrderStatus::SentToBroker,
                "PartiallySettled" => OrderStatus::PartiallySettled,
                "Settled" => OrderStatus::Settled,
                "Failed" => OrderStatus::Failed,
                "Cancelled" => OrderStatus::Cancelled,
                "Abandoned" => OrderStatus::Abandoned,
                _ => panic!("Invalid OrderStatus"),
            },
            transaction_amount: row.get::<f64, _>("TransactionAmount").unwrap(),
            settlement_quantity: row.get::<f64, _>("SettlementQuantity").unwrap(),
            settlement_price: row.get::<f64, _>("SettlementPrice").unwrap(),
            settlement_market_amount: row.get::<f64, _>("SettlementMarketAmount").unwrap(),
            settlement_brokerage_amount: row.get::<f64, _>("SettlementBrokerageAmount").unwrap(),
            settlement_service_tax: row.get::<f64, _>("SettlementServiceTax").unwrap(),
            settlement_stt_amount: row.get::<f64, _>("SettlementSttAmount").unwrap(),
            settlement_net_rate: row.get::<f64, _>("SettlementNetRate").unwrap(),
            settlement_turn_tax: row.get::<f64, _>("SettlementTurnTax").unwrap(),
            settlement_other_tax: row.get::<f64, _>("SettlementOtherTax").unwrap(),
            settlement_net_amount: row.get::<f64, _>("SettlementNetAmount").unwrap(),
            source_type: row.get::<&str, _>("SourceType").unwrap().to_string(),
            source_reference: row.get::<&str, _>("SourceReference").unwrap().to_string(),
            mf_folio_number: row.get::<&str, _>("MfFolioNumber").map(String::from),
            mf_buy_sell_type: row.get::<&str, _>("MfBuySellType").map(String::from),
            mf_all_units_redemption_flag: row.get::<&str, _>("MfAllUnitsRedemptionFlag").map(String::from),
            mf_client_bank: row.get::<&str, _>("MfClientBank").map(String::from),
            mf_oms_client_code: row.get::<&str, _>("MfOmsClientCode").map(String::from),
            euin_number: row.get::<&str, _>("EUINNumber").map(String::from),
            client_domicile: match row.get::<&str, _>("ClientDomicile").unwrap() {
                "Resident" => DomicileType::Resident,
                "NonResident" => DomicileType::NonResident,
                _ => panic!("Invalid DomicileType"),
            },
            client_trading_account: row.get::<&str, _>("ClientTradingAccount").unwrap().to_string(),
            remarks: row.get::<&str, _>("Remarks").unwrap().to_string(),
            order_rationale: row
                .get::<&str, _>("OrderRationale")
                .unwrap_or_else(|| "Not Mentioned")
                .to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            created_by: row.get::<&str, _>("CreatedBy").map(String::from),
            series: row.get::<&str, _>("Series").map(String::from),
            peak_margin: row.get::<&str, _>("PeakMargin").map(String::from),
            peak_margin_ref: row.get::<&str, _>("PeakMarginRef").map(String::from),
        }
    }

    pub async fn get_for_settlement(
        conn: &mut Object<Manager>,
        coes: &Vec<String>,
    ) -> Result<Vec<Self>, DatabaseError> {
        let formatted_ids = coes
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
                SELECT
                    *
                FROM
                    ClientOrderEntries
                WHERE
                    Id in ({})
                AND
                    OrderStatus = 'SentToExchange'
                ",
            formatted_ids
        );

        let rows_iter = conn.query(query, &[]).await?.into_first_result().await?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            ClientOrderEntries
        WHERE
            Id = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }
}
