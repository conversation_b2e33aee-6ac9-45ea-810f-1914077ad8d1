#![allow(unused)]
use chrono::{NaiveDate, NaiveDateTime};
use deadpool::managed::Object;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use tiberius::{ExecuteResult, Query, Row};
use tracing::error;

use crate::{connection::pool::Manager, error::DatabaseError};

use super::{
    cash_ledger::PortfolioCashLedger,
    client_order_entry::{TransactionSubType, TransactionType},
};

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct PortfolioCapitalRegister {
    pub id: String,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub amount: f64,
    pub running_balance: f64,
    pub description: String,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub txn_ref_id: Option<String>,
    pub is_model_portfolio: bool,
    pub portfolio_id: Option<String>,
    pub modelportfolio_id: Option<String>,
}

#[derive(Debug, Clone)]
pub struct InflowOutflow {
    pub name: String,
    pub inflow_month: String,
    pub outflow_month: String,
    pub netflow_month: String,
    pub inflow_fy: String,
    pub outflow_fy: String,
    pub netflow_fy: String,
}

impl InflowOutflow {
    fn from_row(row: &Row) -> Self {
        Self {
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            inflow_month: row.get::<f64, _>("Inflow_Amount_Month").unwrap().to_string(),
            outflow_month: row.get::<f64, _>("Outflow_Amount_Month").unwrap().to_string(),
            netflow_month: row.get::<f64, _>("Net_Inflow_Outflow_Month").unwrap().to_string(),
            inflow_fy: row.get::<f64, _>("Inflow_Amount_Year").unwrap().to_string(),
            outflow_fy: row.get::<f64, _>("Outflow_Amount_Year").unwrap().to_string(),
            netflow_fy: row.get::<f64, _>("Net_Inflow_Outflow_Year").unwrap().to_string(),
        }
    }
}

#[derive(Debug)]
pub struct PortfolioCapitalRegisterForPerformanceEngine {
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub amount: f64,
}

impl PortfolioCapitalRegisterForPerformanceEngine {
    fn from_row(row: &Row) -> Self {
        Self {
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("TransactionSubType").unwrap())
                .unwrap(),
            amount: row.get::<f64, _>("Amount").unwrap(),
        }
    }
}

impl PortfolioCapitalRegister {
    fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("TransactionSubType").unwrap())
                .unwrap(),
            amount: row.get::<f64, _>("Amount").unwrap(),
            running_balance: row.get::<f64, _>("Amount").unwrap(),
            description: row.get::<&str, _>("Description").unwrap().to_string(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            txn_ref_id: row.get::<&str, _>("TxnRefId").map(String::from),
            is_model_portfolio: row.get::<bool, _>("IsModelPortfolio").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").map(String::from),
            modelportfolio_id: row.get::<&str, _>("ModelportfolioId").map(String::from),
        }
    }

    pub async fn insert(&self, conn: &mut Object<Manager>) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            INSERT INTO PortfolioCapitalRegisters (
                TransactionDate, SettlementDate,
                TransactionType, TransactionSubType, Amount, RunningBalance, Description,
                TxnRefId, IsModelPortfolio, PortfolioId, ModelPortfolioId
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                @P11
            )",
        );

        query.bind(self.transaction_date);
        query.bind(self.settlement_date);
        query.bind(self.transaction_type.to_string());
        query.bind(self.transaction_sub_type.to_string());
        query.bind(self.amount);
        query.bind(self.running_balance);
        query.bind(self.description.to_owned());
        query.bind(self.txn_ref_id.to_owned());
        query.bind(self.is_model_portfolio);
        query.bind(self.portfolio_id.to_owned());
        query.bind(self.modelportfolio_id.to_owned());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn entry(&mut self, conn: &mut Object<Manager>) -> Result<(), DatabaseError> {
        let cash_ledger_entries = PortfolioCashLedger::get_by_portfolio_id(conn, self.portfolio_id.clone().unwrap())
            .await
            .unwrap();

        if cash_ledger_entries.is_empty() {
            // TODO: FIXME: These seems wrong....
            match self.transaction_type {
                TransactionType::Debit => self.running_balance = self.amount,
                TransactionType::Credit => self.running_balance = self.amount,
                _ => {}
            }
        } else {
            let latest_pool_balance = cash_ledger_entries
                .iter()
                .max_by_key(|entry| entry.settlement_date)
                .unwrap();

            match self.transaction_type {
                TransactionType::Debit => self.running_balance = latest_pool_balance.running_balance - self.amount,
                TransactionType::Credit => self.running_balance = latest_pool_balance.running_balance + self.amount,
                _ => {}
            }
        }

        self.insert(conn).await?;
        Ok(())
    }

    pub async fn get_the_lowest_date_entries(
        conn: &mut Object<Manager>,
        portfolio_id: &str,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = "
        SELECT *
        FROM PortfolioCapitalRegisters
        WHERE transactiondate = (
            SELECT MIN(transactiondate)
            FROM PortfolioCapitalRegisters
            where PortfolioId=@P1
        )           
        And PortfolioId=@P1
        ";

        let rows_iter = conn
            .query(query, &[&portfolio_id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?;

        Ok(rows_iter)
    }

    pub async fn get_by_portfolio_id_and_date(
        conn: &mut Object<Manager>,
        id: String,
        date: NaiveDate,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            PortfolioCapitalRegisters
        WHERE
            PortfolioId = @P1
            And
        CAST(TransactionDate AS DATE) = CAST(@P2 AS DATE)
    
        "#;

        let rows_iter = conn
            .query(query, &[&id, &date])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?;

        Ok(rows_iter)
    }

    pub async fn get_by_portfolio_id_for_performance_engine(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = r#"
        SELECT
            TransactionType,
            TransactionSubType,
            Amount
        FROM
            PortfolioCapitalRegisters
        WHERE
            PortfolioId = @P1
    
        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?;
        Ok(rows_iter)
    }

    pub async fn get_entries_for_sebi_monthly(
        conn: &mut Object<Manager>,
        from_date: NaiveDateTime,
        to_date: NaiveDateTime,
        fy_from: NaiveDate,
        fy_to: NaiveDate,
        portfolio_type: String,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query = r#"
                        SELECT 
                    s.Name,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P1 AS DATE) AND CAST(@P2 AS DATE) 
                            AND pc.transactiontype IN ('inflow', 'receipt') 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Inflow_Amount_Month,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P1 AS DATE) AND CAST(@P2 AS DATE)
                            AND pc.transactiontype IN ('outflow', 'payment') 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Outflow_Amount_Month,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P1 AS DATE) AND CAST(@P2 AS DATE) 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Net_Inflow_Outflow_Month,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P3 AS DATE) AND CAST(@P4 AS DATE)
                            AND pc.transactiontype IN ('inflow', 'receipt') 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Inflow_Amount_Year,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P3 AS DATE) AND CAST(@P4 AS DATE)
                            AND pc.transactiontype IN ('outflow', 'payment') 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Outflow_Amount_Year,

                    SUM(CASE 
                            WHEN pc.transactionDate BETWEEN CAST(@P3 AS DATE) AND CAST(@P4 AS DATE) 
                            THEN pc.Amount 
                            ELSE 0 
                        END) AS Net_Inflow_Outflow_Year

                FROM PortfolioCapitalRegisters pc
                JOIN portfolios p ON p.id = pc.portfolioId
                JOIN strategymodels sm ON sm.id = p.modelId
                JOIN strategies s ON s.Id = sm.strategyId
                where p.portfolioType = @P5
                GROUP BY s.Name;

        "#;

        let rows_iter = conn
            .query(query, &[&from_date, &to_date, &fy_from, &fy_to, &portfolio_type])
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                error!("Failed On Reading Entries from PortfolioCapitalRegiters From Database ");
                return DatabaseError::QueryError(e);
            })?;
        // let rows: Vec<InflowOutflow> = rows_iter.iter().map(InflowOutflow::from_row).collect();

        Ok(rows_iter)
    }
}
