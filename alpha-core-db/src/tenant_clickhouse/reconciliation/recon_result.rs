use chrono::NaiveDate;
use clickhouse::{Client, Row};
use uuid::Uuid;

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct ReconResultBreakCategories {
    #[serde(rename = "ReconResultId")]
    pub recon_result_id: String,
    #[serde(rename = "RunId")]
    pub run_id: String,
    #[serde(rename = "ProjectId")]
    pub project_id: String,
    #[serde(rename = "Category")]
    pub category: String,
}

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct ReconResult {
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "RunId")]
    pub run_id: String,
    #[serde(rename = "HoldingDate", with = "clickhouse::serde::chrono::date")]
    pub holding_date: NaiveDate,
    #[serde(rename = "ClientCode")]
    pub client_code: String,
    #[serde(rename = "Name")]
    pub name: String,
    #[serde(rename = "Symbol")]
    pub symbol: String,
    #[serde(rename = "SecurityName")]
    pub security_name: String,
    #[serde(rename = "Isin")]
    pub isin: String,
    #[serde(rename = "ClientHoldings")]
    pub client_holdings: f64,
    #[serde(rename = "AlphaHoldings")]
    pub alpha_holdings: f64,
    #[serde(rename = "HoldingDiff")]
    pub holding_diff: f64,
    #[serde(rename = "ClientUnitCost")]
    pub client_unit_cost: f64,
    #[serde(rename = "AlphaUnitCost")]
    pub alpha_unit_cost: f64,
    #[serde(rename = "UnitCostDiff")]
    pub unit_cost_diff: f64,
    #[serde(rename = "ClientTotalCost")]
    pub client_total_cost: f64,
    #[serde(rename = "AlphaTotalCost")]
    pub alpha_total_cost: f64,
    #[serde(rename = "TotalCostDiff")]
    pub total_cost_diff: f64,
    #[serde(rename = "ClientMarketValue")]
    pub client_market_value: f64,
    #[serde(rename = "AlphaMarketValue")]
    pub alpha_market_value: f64,
    #[serde(rename = "MarketValueDiff")]
    pub market_value_diff: f64,
    #[serde(rename = "ClientAccruedIncome")]
    pub client_accrued_income: f64,
    #[serde(rename = "AlphaAccruedIncome")]
    pub alpha_accrued_income: f64,
    #[serde(rename = "AccruedIncomeDiff")]
    pub accrued_income_diff: f64,
    #[serde(rename = "MatchStatus")]
    pub match_status: String,
    #[serde(rename = "UnmatchedCount")]
    pub unmatched_count: i32,
}

impl ReconResult {
    pub async fn insert(conn: &Client, recon_results: &Vec<ReconResult>) -> Result<(), clickhouse::error::Error> {
        const CHUNK_SIZE: usize = 1000;

        for chunk in recon_results.chunks(CHUNK_SIZE) {
            println!("Inserting chunk of {} recon results", chunk.len());
            let mut inserter = conn.inserter::<ReconResult>("ReconResult")?;
            for row in chunk {
                inserter.write(row)?;
            }
            inserter.end().await?;
        }
        Ok(())
    }
}

impl ReconResultBreakCategories {
    pub async fn insert(
        conn: &Client,
        recon_result_break_categories: &Vec<ReconResultBreakCategories>,
    ) -> Result<(), clickhouse::error::Error> {
        const CHUNK_SIZE: usize = 1000;

        for chunk in recon_result_break_categories.chunks(CHUNK_SIZE) {
            println!("Inserting chunk of {} recon result break categories", chunk.len());
            let mut inserter = conn.inserter::<ReconResultBreakCategories>("ReconResultBreakCategories")?;
            for row in chunk {
                inserter.write(row)?;
            }
            inserter.end().await?;
        }
        Ok(())
    }
}
