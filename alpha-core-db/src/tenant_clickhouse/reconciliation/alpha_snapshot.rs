use chrono::NaiveDate;
use clickhouse::{Client, Row};
use serde::{Deserialize, Serialize};

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct AlphaHolding {
    pub holding_date: NaiveDate,
    pub client_code: String,
    pub name: String,
    pub asset_class: String,
    pub symbol: String,
    pub security_name: String,
    pub isin: String,
    pub holdings: f64,
    pub unit_cost: f64,
    pub total_cost: f64,
    pub unit_price: f64,
    pub accrued_income: f64,
    pub market_value: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct AlphaSnapshot {
    #[serde(rename = "RunId")]
    pub run_id: String,

    #[serde(rename = "HoldingDate", with = "clickhouse::serde::chrono::date")]
    pub holding_date: NaiveDate,

    #[serde(rename = "ClientCode")]
    pub client_code: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Symbol")]
    pub symbol: String,

    #[serde(rename = "SecurityName")]
    pub security_name: String,

    #[serde(rename = "Isin")]
    pub isin: String,

    #[serde(rename = "Holdings")]
    pub holdings: f64,

    #[serde(rename = "UnitCost")]
    pub unit_cost: f64,

    #[serde(rename = "TotalCost")]
    pub total_cost: f64,

    #[serde(rename = "AccruedIncome")]
    pub accrued_income: f64,

    #[serde(rename = "MarketValue")]
    pub market_value: f64,
}

impl AlphaSnapshot {
    pub async fn insert(conn: &Client, row: AlphaSnapshot) -> Result<(), clickhouse::error::Error> {
        let mut insert = conn.insert::<Self>("AlphaSnapshot")?;
        insert.write(&row).await?;
        insert.end().await?;
        Ok(())
    }

    pub async fn insert_batch(conn: &Client, rows: Vec<AlphaSnapshot>) -> Result<(), clickhouse::error::Error> {
        let mut insert = conn.insert::<Self>("AlphaSnapshot")?;
        for row in rows {
            insert.write(&row).await?;
        }
        insert.end().await?;
        Ok(())
    }

    // This is in tenant_clickhouse_client
    pub async fn snapshot_holdings(
        conn: &Client,
        holdings: Vec<AlphaHolding>,
        run_id: &str,
    ) -> Result<Vec<AlphaSnapshot>, clickhouse::error::Error> {
        let alpha_snapshots: Vec<AlphaSnapshot> = holdings
            .into_iter()
            .map(|holding| AlphaSnapshot {
                run_id: run_id.to_string(),
                holding_date: holding.holding_date,
                client_code: holding.client_code,
                name: holding.name,
                symbol: holding.symbol,
                security_name: holding.security_name,
                isin: holding.isin,
                holdings: holding.holdings,
                unit_cost: holding.unit_cost,
                total_cost: holding.total_cost,
                accrued_income: holding.accrued_income,
                market_value: holding.market_value,
            })
            .collect();
        
        for (chunk_index, chunk) in alpha_snapshots.chunks(1000).enumerate() {
            println!("Inserting chunk {} ({} alpha snapshots)", chunk_index + 1, chunk.len());

            let mut inserter = conn.inserter::<AlphaSnapshot>("AlphaSnapshot")?;

            for (i, row) in chunk.iter().enumerate() {
                inserter.write(row)?;
            }

            inserter.end().await?;
        }
        println!("✅ Inserted {} alpha snapshots", alpha_snapshots.len());
        Ok(alpha_snapshots)
    }
}
