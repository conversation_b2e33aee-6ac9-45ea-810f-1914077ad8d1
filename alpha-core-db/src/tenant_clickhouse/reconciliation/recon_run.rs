use clickhouse::Client;
use clickhouse::Row;
use serde::{Deserialize, Serialize};

#[derive(De<PERSON>ult, Ser<PERSON><PERSON>, Deserialize, Debug, <PERSON>lone, Row)]
pub struct ReconProjectRuns {
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "ProjectId")]
    pub project_id: String,
    #[serde(rename = "SerialNo")]
    pub serial_no: i32,
    #[serde(rename = "MatchedRowCount")]
    pub matched_row_count: i32,
    #[serde(rename = "UnmatchedRowCount")]
    pub unmatched_row_count: i32,
    #[serde(rename = "PartiallyMatchedRowCount")]
    pub partially_matched_row_count: i32,
    #[serde(rename = "NotFoundInSource")]
    pub not_found_in_client_row_count: i32,
    #[serde(rename = "NotFoundInAlpha")]
    pub not_found_in_alpha_row_count: i32,
}

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Row)]
pub struct GetAllRunsResponse {
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "ProjectId")]
    pub project_id: String,
    #[serde(rename = "SerialNo")]
    pub serial_no: i32
}

impl ReconProjectRuns {
    pub async fn insert(conn: &Client, recon_project_run: &ReconProjectRuns) -> Result<(), clickhouse::error::Error> {
        let mut client = conn.insert::<ReconProjectRuns>("ReconProjectRuns")?;
        client.write(&recon_project_run).await.unwrap();
        client.end().await.unwrap();
        Ok(())
    }

    pub async fn get_all(conn: &Client, project_id: &str) -> Result<Vec<GetAllRunsResponse>, clickhouse::error::Error> {
        let query = format!("SELECT Id, ProjectId, SerialNo FROM ReconProjectRuns WHERE ProjectId = '{}'", project_id);
        let result = conn.query(&query).fetch_all::<GetAllRunsResponse>().await;
        match result {
            Ok(runs) => Ok(runs),
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(conn: &Client, run_id: &str) -> Result<ReconProjectRuns, clickhouse::error::Error> {
        let query = format!("SELECT * FROM ReconProjectRuns WHERE Id = '{}'", run_id);
        let result = conn.query(&query).fetch_one::<ReconProjectRuns>().await;
        match result {
            Ok(run) => Ok(run),
            Err(e) => Err(e),
        }
    }
    pub async fn get_last_serial_no(conn: &Client, project_id: &str) -> Result<i32, clickhouse::error::Error> {
        let query = format!(
            "SELECT MAX(SerialNo) FROM ReconProjectRuns WHERE ProjectId = '{}'",
            project_id
        );
        match conn.query(&query).fetch_one::<i32>().await {
            Ok(serial_no) => Ok(serial_no),
            Err(e) => Err(e),
        }
    }
}
