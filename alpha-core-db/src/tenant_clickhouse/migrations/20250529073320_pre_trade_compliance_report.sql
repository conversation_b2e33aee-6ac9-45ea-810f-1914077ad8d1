CREATE TABLE pre_trade_compliance_report
(
    id UInt64,
    session_id UUID,
    isin String,
    security_name String,
    market_cap String,
    sector String,
    portfolio_name String,
    portfolio_id String,
    symbol String,
    amount Float64,
    transaction_type String,
    quantity Float64,
    rule String,
    violate Boolean
)
ENGINE = MergeTree
ORDER BY (session_id, id, rule)
SETTINGS index_granularity = 8192;
