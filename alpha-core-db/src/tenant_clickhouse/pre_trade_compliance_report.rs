use clickhouse::{Client, Row};
use futures_util::stream::{self, StreamExt};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::client_order_entry::TransactionType;

#[derive(Debug, Clone, Serialize, Deserialize, Row)]
pub struct PreTradeComplianceReportDb {
    pub id: u64,
    #[serde(with = "clickhouse::serde::uuid")]
    pub session_id: Uuid,
    pub isin: String,
    pub security_name: String,
    pub market_cap: String,
    pub sector: String,
    pub portfolio_name: String,
    pub portfolio_id: String,
    pub symbol: String,
    pub amount: f64,
    pub transaction_type: String,
    pub quantity: f64,
    pub rule: String,
    pub violate: bool,
}

impl PreTradeComplianceReportDb {
    pub async fn bulk_insert(
        client: &Client,
        rows: Vec<PreTradeComplianceReportDb>,
    ) -> Result<(), clickhouse::error::Error> {
        let mut insert = client.insert::<Self>("pre_trade_compliance_report").unwrap();
        for row in rows {
            insert.write(&row).await.unwrap();
        }

        insert.end().await.unwrap();

        Ok(())
    }
}
