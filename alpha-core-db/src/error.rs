use thiserror::Error;
use tracing::field::Value;

#[derive(E<PERSON><PERSON>, Debug)]
pub enum DatabaseError {
    #[error("Database query failed: {0}")]
    QueryError(#[from] tiberius::error::Error),
    #[error("row not found")]
    RowNotFound(),
    #[error("IO error")]
    Io,
    #[error("Insertion failed")]
    InsertionFailed,
    #[error("Invalid column: {0}")]
    InvalidColumn(String),
}

/// Implements conversion from DatabaseError to String for functions that return Result<T, String>.
/// This allows automatic conversion of database errors into string error messages.
impl From<DatabaseError> for String {
    fn from(err: DatabaseError) -> Self {
        err.to_string()
    }
}