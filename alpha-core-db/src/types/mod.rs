use std::{fmt, str::FromStr};

use serde::{Deserialize, Serialize};

use redis::{FromRedisValue, RedisError, RedisResult, Value};

pub mod compute_order;
pub mod storage_engine;
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, Default, PartialEq, Eq, Hash)]
pub enum OrderType {
    MarketOrder,
    #[default]
    LimitOrder,
}

impl fmt::Display for OrderType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            OrderType::MarketOrder => write!(f, ""),
            OrderType::LimitOrder => write!(f, "LimitOrder"),
        }
    }
}

impl FromStr for OrderType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "marketorder" => Ok(OrderType::MarketOrder),
            "limitorder" => Ok(OrderType::LimitOrder),
            _ => panic!("Invalid Order Type"),
        }
    }
}

#[derive(Serialize, Deserialize, Clone, Debug, Default, PartialEq)]
pub enum OrderSourceType {
    #[default]
    NewCapitalInPortfolio,
    AdditionalCapitalInPortfolio,
    CapitalWithdrawalInPortfolio,
    BuyTradeIdea,
    SellTradeIdea,
    RestrictedBuyTradeIdea,
    RestrictedSellTradeIdea,
    CustomClientOrder,
    SystematicDeploymentRedemption,
    SystematicDeploymentInstallment,
    StrategicDeploymentInstallment,
    StrategicDeploymentRedemption,
}

impl FromRedisValue for OrderSourceType {
    fn from_redis_value(v: &Value) -> RedisResult<Self> {
        let s: String = FromRedisValue::from_redis_value(v)?;
        match s.as_str() {
            "BUYTRADEIDEA" => Ok(OrderSourceType::BuyTradeIdea),
            "SELLTRADEIDEA" => Ok(OrderSourceType::SellTradeIdea),
            "RESTRICTED-SELLTRADEIDEA" => Ok(OrderSourceType::RestrictedSellTradeIdea),
            "RESTRICTED-BUYTRADEIDEA" => Ok(OrderSourceType::RestrictedBuyTradeIdea),
            "ADDITIONALCAPITALINPORTFOLIO" => Ok(OrderSourceType::AdditionalCapitalInPortfolio),
            "CUSTOMCLIENTORDER" => Ok(OrderSourceType::CustomClientOrder),
            "CAPITALWITHDRAWALINPORTFOLIO" => Ok(OrderSourceType::CapitalWithdrawalInPortfolio),
            "STRATEGICDEPLOYMENTINSTALLMENT" => Ok(OrderSourceType::StrategicDeploymentInstallment),
            "STRATEGICDEPLOYMENTREDEMPTION" => Ok(OrderSourceType::StrategicDeploymentRedemption),
            "SYSTEMATICDEPLOYMENTINSTALLMENT" => Ok(OrderSourceType::SystematicDeploymentInstallment),
            "SYSTEMATICDEPLOYMENTREDEMPTION" => Ok(OrderSourceType::SystematicDeploymentRedemption),
            "NEWCAPITALINPORTFOLIO" => Ok(OrderSourceType::NewCapitalInPortfolio),
            _ => Err(RedisError::from((
                redis::ErrorKind::TypeError,
                "Unknown OrderSourceType variant",
            ))),
        }
    }
}

impl fmt::Display for OrderSourceType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            OrderSourceType::BuyTradeIdea => write!(f, "BUYTRADEIDEA"),
            OrderSourceType::SellTradeIdea => write!(f, "SELLTRADEIDEA"),
            OrderSourceType::RestrictedSellTradeIdea => write!(f, "RESTRICTED-SELLTRADEIDEA"),
            OrderSourceType::RestrictedBuyTradeIdea => write!(f, "RESTRICTED-BUYTRADEIDEA"),
            OrderSourceType::AdditionalCapitalInPortfolio => {
                write!(f, "ADDITIONALCAPITALINPORTFOLIO")
            }
            OrderSourceType::CustomClientOrder => write!(f, "CUSTOMCLIENTORDER"),
            OrderSourceType::CapitalWithdrawalInPortfolio => {
                write!(f, "CAPITALWITHDRAWALINPORTFOLIO")
            }
            OrderSourceType::StrategicDeploymentInstallment => {
                write!(f, "STRATEGICDEPLOYMENTINSTALLMENT")
            }
            OrderSourceType::StrategicDeploymentRedemption => {
                write!(f, "STRATEGICDEPLOYMENTREDEMPTION")
            }
            OrderSourceType::SystematicDeploymentInstallment => {
                write!(f, "SYSTEMATICDEPLOYMENTINSTALLMENT")
            }
            OrderSourceType::SystematicDeploymentRedemption => {
                write!(f, "SYSTEMATICDEPLOYMENTREDEMPTION")
            }
            OrderSourceType::NewCapitalInPortfolio => write!(f, "NEWCAPITALINPORTFOLIO"),
        }
    }
}

impl FromStr for OrderSourceType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_uppercase().as_str() {
            "BUYTRADEIDEA" => Ok(OrderSourceType::BuyTradeIdea),
            "SELLTRADEIDEA" => Ok(OrderSourceType::SellTradeIdea),
            "ADDITIONALCAPITALINPORTFOLIO" => Ok(OrderSourceType::AdditionalCapitalInPortfolio),
            "CUSTOMCLIENTORDER" => Ok(OrderSourceType::CustomClientOrder),
            "NEWCAPITALINPORTFOLIO" => Ok(OrderSourceType::NewCapitalInPortfolio),
            "CAPITALWITHDRAWALINPORTFOLIO" => Ok(OrderSourceType::CapitalWithdrawalInPortfolio),
            "STRATEGICDEPLOYMENTINSTALLMENT" => Ok(OrderSourceType::StrategicDeploymentInstallment),
            "STRATEGICDEPLOYMENTREDEMPTION" => Ok(OrderSourceType::StrategicDeploymentInstallment),
            "SYSTEMATICDEPLOYMENTINSTALLMENT" => Ok(OrderSourceType::SystematicDeploymentInstallment),
            "SYSTEMATICDEPLOYMENTREDEMPTION" => Ok(OrderSourceType::SystematicDeploymentRedemption),
            _ => panic!("Invalid Source Type"),
        }
    }
}
