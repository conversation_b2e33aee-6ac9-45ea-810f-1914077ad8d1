use tiberius::Row;
use serde::{Deserialize,Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct StrategyForOrderComputation {
    pub model_id: String,
    pub strategy_name: String,
    pub model_name: String,
    pub is_open: bool,
    pub strategy_id: String,
    pub model_code: Option<String>,
    pub strategy_code: String,
}

impl StrategyForOrderComputation {
    pub fn from_row(row: &Row) -> Self {
        Self {
            model_code: row.get::<&str, _>("ModelCode").map(String::from),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            model_name: row.get::<&str, _>("ModelName").unwrap().to_string(),
            is_open: row.get::<bool, _>("IsOpen").unwrap(),
            strategy_code: row.get::<&str, _>("StrategyCode").unwrap().to_string(),
            strategy_id: row.get::<&str, _>("StrategyId").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
        }
    }
}
