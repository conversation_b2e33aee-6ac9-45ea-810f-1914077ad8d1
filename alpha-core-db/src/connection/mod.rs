pub mod error;
pub mod pool;
use pool as deadpool_tiberius;
use tokio::time::Duration;
pub async fn connect_to_mssql(pool_size: u8) -> deadpool::managed::Pool<pool::Manager> {
    let db_host = std::env::var("DATABASE_HOST").expect("Failed to load database host from env");
    let db_user = std::env::var("DATABASE_USER").expect("Failed to load database user from env");
    let db_password = std::env::var("DATABASE_PASSWORD").expect("Failed to load database password from env");
    let db_port = std::env::var("DATABASE_PORT")
        .expect("Failed to load database port from env")
        .parse()
        .expect("Datbase Port should be an Integer");
    let db_name = std::env::var("DATABASE_NAME").expect("Failed to load database name from env");

    let pool: deadpool::managed::Pool<pool::Manager> = deadpool_tiberius::Manager::new()
        .host(db_host) // default to localhost
        .port(db_port) // default to
        .basic_authentication(db_user, db_password)
        //  or .authentication(tiberius::AuthMethod)
        .database(db_name)
        .max_size(pool_size as usize)
        .pre_recycle_sync(|_client, _metrics| {
            // do sth with client object and pool metrics
            Ok(())
        })
        .post_recycle_sync(|_c, _p| Ok(()))
        .encryption(tiberius::EncryptionLevel::Required)
        .trust_cert()
        .create_pool()
        .unwrap();

    pool
}

pub async fn connect_to_master_data(pool_size: u8) -> deadpool::managed::Pool<pool::Manager> {
    let db_host = std::env::var("MASTER_DATABASE_HOST").expect("Failed to load database host from env");
    let db_user = std::env::var("MASTER_DATABASE_USER").expect("Failed to load database user from env");
    let db_password = std::env::var("MASTER_DATABASE_PASSWORD").expect("Failed to load database password from env");
    let db_port = std::env::var("MASTER_DATABASE_PORT")
        .expect("Failed to load database port from env")
        .parse()
        .expect("Datbase Port should be an Integer");
    let db_name = std::env::var("MASTER_DATABASE_NAME").expect("Failed to load database name from env");

    let pool: deadpool::managed::Pool<pool::Manager> = deadpool_tiberius::Manager::new()
        .host(db_host) // default to localhost
        .port(db_port) // default to
        .basic_authentication(db_user, db_password)
        //  or .authentication(tiberius::AuthMethod)
        .database(db_name)
        .max_size(pool_size as usize)
        .pre_recycle_sync(|_client, _metrics| {
            // do sth with client object and pool metrics
            Ok(())
        })
        .encryption(tiberius::EncryptionLevel::Required)
        .trust_cert()
        .create_pool()
        .unwrap();

    let pool_for_watch = pool.clone();

    let interval = Duration::from_secs(30);
    let max_age = Duration::from_secs(60);
    tokio::spawn(async move {
        loop {
            tokio::time::sleep(interval).await;
            pool_for_watch.retain(|_, metrics| metrics.last_used() < max_age);
        }
    });

    pool
}
