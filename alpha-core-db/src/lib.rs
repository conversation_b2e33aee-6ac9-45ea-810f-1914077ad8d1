use ::redis::aio::MultiplexedConnection;
use connection::pool::Manager;
use deadpool::managed::{Object, Pool};

pub mod clickhouse;
pub mod connection;
pub mod redis;
pub mod schema;
pub mod storage;
pub mod types;
pub mod error;
pub mod tenant_clickhouse;
pub mod transactions;
pub mod minio;

pub struct AlphaPools {
    pub redis_pool: MultiplexedConnection,
    pub master_pool: Pool<Manager>,
    pub db_pool: Pool<Manager>,
}

pub struct AlphaConnections<'a> {
    pub redis_conn: MultiplexedConnection,
    pub master_conn: &'a mut Object<Manager>,
    pub db_conn: &'a mut Object<Manager>,
}
