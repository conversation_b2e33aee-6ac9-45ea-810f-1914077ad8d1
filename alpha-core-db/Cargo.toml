[package]
name = "alpha-core-db"
version = "0.1.0"
edition = "2021"

[dependencies]
deadpool = { workspace = true }
tiberius = { workspace = true }
tokio-util = { version = "0.7.11", features = ["compat"] }
tokio = { version = "1.33.0", features = ["full"] }
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.122"
rust_decimal = "1.36.0"
azure_storage_blobs = "0.20.0"
azure_storage = "0.20.0"
azure_core = "0.20.0"
azure_data_tables = "0.20.0"
chrono = { workspace = true }
futures = "0.3.30"
futures-util = "0.3.30"
clickhouse = { workspace = true, features = ["inserter","uuid", "chrono"] }
thiserror = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
redis = { workspace = true }
dotenv = { workspace = true }
time = { workspace = true }
bb8-redis = { workspace = true }
bb8 = { workspace = true }
rkyv = { workspace = true }
actlogica_logs = { workspace = true }
uuid = { workspace = true }
minio = "0.3.0"