{
  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixpkgs-unstable";
    crane.url = "github:ipetkov/crane";
    flake-utils.url = "github:numtide/flake-utils";
  };
  outputs = { self, nixpkgs, crane, flake-utils, ... }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
        craneLib = crane.mkLib pkgs;

        commonArgs = {
           src = ./.;
          
            doCheck = false;
          buildInputs = [
            pkgs.openssl
            pkgs.xz
            pkgs.llvmPackages_19.llvm
          ];

          nativeBuildInputs = [
            pkgs.pkg-config
            pkgs.gnumake
            pkgs.gcc
            pkgs.perl
            pkgs.cmake
            pkgs.protobuf
            pkgs.xz
            pkgs.llvmPackages_19.llvm
          ];
          
        };

        cargoArtifacts = craneLib.buildDepsOnly commonArgs;
        
        # Build the binary
        alphaBinary = craneLib.buildPackage (commonArgs // {
          inherit cargoArtifacts;
        });

        # Docker image configuration
        dockerImage = pkgs.dockerTools.buildLayeredImage {
          name = "alpha";
          tag = "latest";
          contents = [
            pkgs.openssl
            pkgs.perl
            pkgs.zstd
            alphaBinary
          ];

        };

      in
      {
        packages = {
          default = dockerImage;
          docker = dockerImage;
          indexer = alphaBinary;
        };

        devShells.default = pkgs.mkShell {
          inputsFrom = [ self.packages.${system}.default ];
          buildInputs = [
            pkgs.openssl
            pkgs.pkg-config
            pkgs.protobuf
            pkgs.perl
            pkgs.gnumake
            pkgs.gcc
            pkgs.cmake
            pkgs.xz
            pkgs.llvmPackages_19.llvm
          ];
        };
      });
}