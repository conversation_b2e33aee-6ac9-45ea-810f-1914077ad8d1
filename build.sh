docker build -t alpha-core .
docker tag alpha-core alphapregistry.azurecr.io/alpha-core:latest
docker push alphapregistry.azurecr.io/alpha-core:latest  


#Kubernetes deployment
kubectl apply -f env-configmap.yaml
kubectl apply -f alpha-oms-deployment.yaml
kubectl apply -f alpha-oms-service.yaml

#Remember Dont put anything in backend namespace
#SSL Config
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm install nginx-ingress ingress-nginx/ingress-nginx --create-namespace --namespace ingress-nginx

helm repo add jetstack https://charts.jetstack.io
helm repo update
helm install cert-manager jetstack/cert-manager --namespace cert-manager --create-namespace --version v1.8.0 --set installCRDs=true


kubectl apply -f backend.yaml
kubectl apply -f cert-manager-issuer.yaml
kubectl apply -f alpha-oms-ingress.yaml