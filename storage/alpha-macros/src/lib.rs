extern crate proc_macro;
use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, DeriveInput};

#[proc_macro_derive(ReflectFields)]
pub fn derive_reflect_fields(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let struct_name = &input.ident;

    let fields = match &input.data {
        syn::Data::Struct(s) => &s.fields,
        _ => panic!("ReflectFields only works on structs"),
    };

    let field_entries = fields.iter().map(|f| {
        let name = f.ident.as_ref().unwrap().to_string();
        let ty = f.ty.clone();
        let ty_string = quote!(#ty).to_string();
        quote! {
            (#name, #ty_string)
        }
    });

    let field_matches = fields.iter().map(|f| {
        let field_name = f.ident.as_ref().unwrap();
        let field_str = field_name.to_string();
        quote! {
            #field_str => Some(Box::new(self.#field_name.clone())),
        }
    });

    let expanded = quote! {
        impl ReflectFieldAccess for  #struct_name {
            fn reflect_fields() -> Vec<(&'static str, &'static str)> {
                vec![#(#field_entries),*]
            }

            fn reflect_get(&self, field: &str) -> Option<Box<dyn std::any::Any>> {
                match field {
                    #(#field_matches)*
                    _ => None,
                }
            }
        }
    };

    TokenStream::from(expanded)
}
