[package]
name = "alpha-perf-api"
version = "0.1.0"
edition = "2021"

[dependencies]
alpha-utils = { path = "../../alpha-utils" }
alpha-core-db = { path = "../../alpha-core-db" }
alpha-storage-engine = { path = "../alpha-storage-engine" }
tokio = { version = "1.40.0", features = ["full"] }
dotenv = "0.15.0"
native-tls = "0.2.12"
futures = "0.3.30"
futures-util = "0.3.30"
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
rocksdb = { version = "0.22.0", features = ["multi-threaded-cf"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
axum = { version = "0.8.3", features = ["macros"] }
axum-extra = { version = "0.9.4", features = ["cookie"] }
tower-http = { version = "0.6.1", features = ["cors"] }
clickhouse = { workspace = true }
bb8-redis = { workspace = true }
csv = "1.3.0"
rust_decimal = { workspace = true, features = ["serde-float"] }
rust_decimal_macros = { workspace = true }
rkyv = { workspace = true }
tokio-cron-scheduler = "0.13.0"
job_scheduler = "1.2.1"
deadpool-redis = { workspace = true }
deadpool = { workspace = true }
utoipa = { version = "5.3.1", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "9.0.1", features = ["axum"] }
actlogica_logs = { workspace = true }
