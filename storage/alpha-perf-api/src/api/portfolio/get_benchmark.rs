use alpha_storage_engine::{storage::StorageWriter, utils::benchmark::BenchmarkIndices};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate, Utc};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::sync::Arc;
use tracing::info;
use utoipa::ToSchema;

use crate::{types::XirrResults, AppState};

#[derive(Serialize, Deserialize, Clone, Debug, ToSchema)]
#[repr(i32)]
#[schema(title = "Benchmark Type")]
pub enum BenchmarkIndicesPayload {
    SpBseSensex = 16,

    SpBse500 = 22,

    Nifty50 = 62,

    Index379 = 379,

    Index450 = 450,

    Index60 = 60,

    Index437 = 437,
}

#[derive(Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioMetrics {
    pub benchmark_index: BenchmarkIndicesPayload,
    #[schema(value_type = String, example = "2022-01-22")]
    pub from_date: NaiveDate,
    #[schema(value_type = String, example = "2022-01-22")]
    pub to_date: NaiveDate,
}

#[derive(Serialize, Deserialize, ToSchema)]
pub struct Metrics {
    #[schema(value_type = String, example = "2022-01-22")]
    date: NaiveDate,
    xirr: XirrResults,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/benchmark/{portfolio_id}",
    responses(
        (status = 200, description = "Benchmark", body = Metrics),
        (status = NOT_FOUND, description = "Benchmark was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Benchmark query"),
        ("toDate" = String, Query, description = "To Date filter for Benchmark query"),
        ("benchmarkIndex" = String, Query, description = "Benchmark Index")
    )
)]
pub async fn get_portfolio_benchmark(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<PortfolioMetrics>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());

    let mut from_date = payload.from_date;
    let to_date = payload.to_date;

    let mut xi = Vec::new();

    let benchmark_index = match payload.benchmark_index {
        BenchmarkIndicesPayload::Index379 => BenchmarkIndices::Index379,
        BenchmarkIndicesPayload::Index437 => BenchmarkIndices::Index437,
        BenchmarkIndicesPayload::Index450 => BenchmarkIndices::Index450,
        BenchmarkIndicesPayload::Index60 => BenchmarkIndices::Index60,
        BenchmarkIndicesPayload::Nifty50 => BenchmarkIndices::Nifty50,
        BenchmarkIndicesPayload::SpBse500 => BenchmarkIndices::SpBse500,
        BenchmarkIndicesPayload::SpBseSensex => BenchmarkIndices::SpBseSensex,
    };

    while from_date <= to_date {
        let xirr = storage_rw
            .get_benchmark_xirr_metrics(&portfolio_id, from_date, &benchmark_index)
            .map_err(|e| {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get Xirr From Data")
                });

                return (StatusCode::BAD_REQUEST, Json(res));
            })?;

        if let Some(xirr) = xirr {
            let metrics = Metrics {
                date: from_date,
                xirr: XirrResults {
                    fytd: Decimal::from_f64(xirr.fytd.unwrap_or_default()).unwrap_or_default(),
                    one_month: Decimal::from_f64(xirr.one_month.unwrap_or_default()).unwrap_or_default(),
                    one_year: Decimal::from_f64(xirr.one_year.unwrap_or_default()).unwrap_or_default(),
                    seven_year: Decimal::from_f64(xirr.seven_year.unwrap_or_default()).unwrap_or_default(),
                    since_inception: Decimal::from_f64(xirr.since_inception.unwrap_or_default()).unwrap_or_default(),
                    six_month: Decimal::from_f64(xirr.six_month.unwrap_or_default()).unwrap_or_default(),
                    ten_year: Decimal::from_f64(xirr.ten_year.unwrap_or_default()).unwrap_or_default(),
                    three_month: Decimal::from_f64(xirr.three_month.unwrap_or_default()).unwrap_or_default(),
                    three_year: Decimal::from_f64(xirr.three_year.unwrap_or_default()).unwrap_or_default(),
                    two_year: Decimal::from_f64(xirr.two_year.unwrap_or_default()).unwrap_or_default(),
                    ytd: Decimal::from_f64(xirr.ytd.unwrap_or_default()).unwrap_or_default(),
                },
            };

            xi.push(metrics);
        }

        from_date = from_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<Vec<Metrics>>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(xi)))
}
