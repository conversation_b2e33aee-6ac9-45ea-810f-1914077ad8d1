use alpha_storage_engine::storage::StorageWriter;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate, Utc};
use csv::Writer;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::{fs, sync::Arc};
use tracing_subscriber::fmt::format;

use crate::{
    types::{Metrics, TwrrResults, XirrResults},
    AppState,
};

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioMetrics {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/metrics/{portfolio_id}",
    responses(
        (status = 200, description = "Metrics", body = Metrics),
        (status = NOT_FOUND, description = "metrics was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Metrics query"),
        ("toDate" = String, Query, description = "To Date filter for Metrics query")
    )
)]
pub async fn get_portfolio_metrics(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<PortfolioMetrics>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());
    let mut from_date = payload.from_date;
    let to_date = payload.to_date;
    let mut xi = Vec::new();

    while from_date <= to_date {
        let xirr = storage_rw.get_xirr_metrics(&portfolio_id, from_date).map_err(|e| {
            let res = json!({
                "status": "error",
                "message": format!("Failed to Get Xirr From Data")
            });

            return (StatusCode::BAD_REQUEST, Json(res));
        })?;

        let twrr = storage_rw
            .get_portfolio_twrr_metrics(&portfolio_id, from_date)
            .map_err(|e| {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get Xirr From Data")
                });

                return (StatusCode::BAD_REQUEST, Json(res));
            })?;

        if let (Some(xirr), Some(twrr)) = (xirr, twrr) {
            let metrics = Metrics {
                date: from_date,
                xirr: XirrResults {
                    fytd: Decimal::from_f64(xirr.fytd.unwrap_or_default()).unwrap_or_default(),
                    one_month: Decimal::from_f64(xirr.one_month.unwrap_or_default()).unwrap_or_default(),
                    one_year: Decimal::from_f64(xirr.one_year.unwrap_or_default()).unwrap_or_default(),
                    seven_year: Decimal::from_f64(xirr.seven_year.unwrap_or_default()).unwrap_or_default(),
                    since_inception: Decimal::from_f64(xirr.since_inception.unwrap_or_default()).unwrap_or_default(),
                    six_month: Decimal::from_f64(xirr.six_month.unwrap_or_default()).unwrap_or_default(),
                    ten_year: Decimal::from_f64(xirr.ten_year.unwrap_or_default()).unwrap_or_default(),
                    three_month: Decimal::from_f64(xirr.three_month.unwrap_or_default()).unwrap_or_default(),
                    three_year: Decimal::from_f64(xirr.three_year.unwrap_or_default()).unwrap_or_default(),
                    two_year: Decimal::from_f64(xirr.two_year.unwrap_or_default()).unwrap_or_default(),
                    ytd: Decimal::from_f64(xirr.ytd.unwrap_or_default()).unwrap_or_default(),
                },
                twrr: TwrrResults {
                    fytd: twrr.fytd.unwrap_or_default(),
                    one_month: twrr.one_month.unwrap_or_default(),
                    one_year: twrr.one_year.unwrap_or_default(),
                    seven_year: twrr.seven_year.unwrap_or_default(),
                    since_inception: twrr.since_inception.unwrap_or_default(),
                    six_month: twrr.six_month.unwrap_or_default(),
                    ten_year: twrr.ten_year.unwrap_or_default(),
                    three_month: twrr.three_month.unwrap_or_default(),
                    three_year: twrr.three_year.unwrap_or_default(),
                    two_year: twrr.two_year.unwrap_or_default(),
                    ytd: twrr.ytd.unwrap_or_default(),
                },
            };

            xi.push(metrics);
        }

        from_date = from_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<Vec<Metrics>>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(xi)))
}
