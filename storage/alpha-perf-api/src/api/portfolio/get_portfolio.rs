use alpha_core_db::schema::strategy::Strategies;
use alpha_storage_engine::{states::aum::Aum, storage::StorageWriter, utils::benchmark::BenchmarkIndices};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate};
use csv::Writer;
use rkyv::Deserialize;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::Serialize;
use serde_json::{json, Value};
use std::{collections::HashSet, fs, sync::Arc};
use utoipa::ToSchema;

use crate::{api::trigger::portfolio, AppState};

#[derive(serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioDetailsQuery {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(Serialize, ToSchema)]
pub struct Portfolio {
    #[schema(value_type = String, example = "0")]
    pub realised: Decimal,

    #[schema(value_type = String, example = "0")]
    pub unrealised: Decimal,

    #[schema(value_type = String, example = "0")]
    pub market_value: Decimal,

    #[schema(value_type = String, example = "0")]
    pub invested_capital: Decimal,

    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,

    #[schema(value_type = String, example = "0")]
    pub cash: Decimal,

    #[schema(value_type = String, example = "0")]
    pub payables: Decimal,

    #[schema(value_type = String, example = "0")]
    pub receivables: Decimal,

    #[schema(value_type = String, example = "0")]
    pub dividends_paid: Decimal,

    #[schema(value_type = String, example = "0")]
    pub protfolio_xirr_since_inception: Decimal,

    #[schema(value_type = String, example = "0")]
    pub twrr: Decimal,

    #[schema(value_type = String, example = "0")]
    pub assets_and_liabilities: Decimal,

    #[schema(value_type = String, example = "0")]
    pub benchmark_nifty_50_xirr: Decimal,

    #[schema(value_type = String, example = "0")]
    pub benchmark_s_and_p_500_xirr: Decimal,

    #[schema(value_type = String, example = "0")]
    pub portfolio_xirr_1y: Decimal,

    #[schema(value_type = String, example = "0")]
    pub benchmark_nifty_50_xirr_1y: Decimal,

    #[schema(value_type = String, example = "0")]
    pub benchmark_s_and_p_500_xirr_1y: Decimal,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/get_portfolio/{portfolio_id}",
    responses(
        (status = 200, description = "Portfolio Details", body = Portfolio),
        (status = NOT_FOUND, description = "Portfolio was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Portfolio query"),
        ("toDate" = String, Query, description = "To Date filter for Portfolio query")
    )
)]

pub async fn get_portfolio_details(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<PortfolioDetailsQuery>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut portfolios: Vec<Portfolio> = Vec::new();

    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());
    while start_date <= end_date {
        let portfolio = storage_rw.get_portfolio(&portfolio_id, start_date);

        if let Some(portfolio) = portfolio {
            let nifty_benchmark_xirr = storage_rw
                .get_benchmark_xirr_metrics(&portfolio_id, start_date, &BenchmarkIndices::Nifty50)
                .map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Nifty 50 Benchmark Xirr")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?
                .ok_or_else(|| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Nifty Benchmark Xirr not found on {}",start_date)
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

            let sp_benchmark_xirr = storage_rw
                .get_benchmark_xirr_metrics(&portfolio_id, start_date, &BenchmarkIndices::SpBse500)
                .map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get S&P BSE 500 Benchmark Xirr")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?
                .ok_or_else(|| {
                    let res = json!({
                        "status": "error",
                        "message": format!("S&P Bse 500 Benchmark Xirr not found on {}",start_date)
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

            let portfolio_xirr = storage_rw
                .get_xirr_metrics(&portfolio_id, start_date)
                .map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Portfolio Xirr On {}",start_date)
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?
                .ok_or_else(|| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Portfolio Xirr not found on {}",start_date)
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

            let portfolio = Portfolio {
                realised: portfolio.realised_gain_loss,
                unrealised: portfolio.unrealised_gain_loss,
                market_value: portfolio.market_value,
                invested_capital: portfolio.invested_capital,
                date: start_date,
                cash: portfolio.balance,
                payables: portfolio.payables,
                receivables: portfolio.receivables,
                dividends_paid: portfolio.dividends_paid,
                twrr: portfolio.twrr,
                protfolio_xirr_since_inception: portfolio.xirr,
                assets_and_liabilities: portfolio.assets_and_liabilities,
                benchmark_nifty_50_xirr: Decimal::from_f64(nifty_benchmark_xirr.since_inception.unwrap_or_default())
                    .unwrap_or_default(),
                benchmark_s_and_p_500_xirr: Decimal::from_f64(sp_benchmark_xirr.since_inception.unwrap_or_default())
                    .unwrap_or_default(),
                benchmark_nifty_50_xirr_1y: Decimal::from_f64(nifty_benchmark_xirr.one_year.unwrap_or_default())
                    .unwrap_or_default(),
                benchmark_s_and_p_500_xirr_1y: Decimal::from_f64(sp_benchmark_xirr.one_year.unwrap_or_default())
                    .unwrap_or_default(),

                portfolio_xirr_1y: Decimal::from_f64(portfolio_xirr.one_year.unwrap_or_default()).unwrap_or_default(),
            };
            portfolios.push(portfolio);
        }
        start_date = start_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<Vec<Portfolio>>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(portfolios)))
}
