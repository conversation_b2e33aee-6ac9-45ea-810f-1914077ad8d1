use std::sync::Arc;

use axum::{
    routing::{get, post},
    Router,
};

use crate::AppState;

pub mod get_analytics;
pub mod get_aum;
pub mod get_aum_units;
pub mod get_benchmark;
pub mod get_holdings;
pub mod get_metrics;
pub mod get_performance;
pub mod get_portfolio;
pub mod get_portfolio_in_strategy_aum;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route(
            "/analytics/{portfolio_id}",
            get(get_analytics::get_analytics_for_portfolio),
        )
        .route("/get_aum/{portfolio_id}", get(get_aum::get_aum_range))
        .route("/get_all_portfolios_aum", get(get_aum::get_aum_for_all_portfolios))
        .route("/get_aum_units/{portfolio_id}", get(get_aum_units::get_aum_units))
        .route(
            "/get_portfolio/{portfolio_id}",
            get(get_portfolio::get_portfolio_details),
        )
        .route(
            "/get_portfolios_in_strategy_aum/{strategy_id}",
            get(get_portfolio_in_strategy_aum::get_portfolios_in_strategy_aum_range),
        )
        .route(
            "/get_portfolios_in_strategy_holings/{strategy_id}",
            get(get_portfolio_in_strategy_aum::get_portfolios_in_strategy_holdings),
        )
        .route("/holdings/{portfolio_id}", get(get_holdings::get_holdings))
        .route("/all_holdings", get(get_holdings::get_all_portfolio_holdings))
        .route("/benchmark/{portfolio_id}", get(get_benchmark::get_portfolio_benchmark))
        .route("/metrics/{portfolio_id}", get(get_metrics::get_portfolio_metrics))
        .route(
            "/performance_for_periods/{portfolio_id}",
            get(get_performance::get_performance_for_periods),
        )
}
