use std::{fs, sync::Arc};

use alpha_storage_engine::{states::aum::Aum, storage::StorageWriter};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate, Utc};
use csv::Writer;
use rkyv::Deserialize;
use rust_decimal::Decimal;
use serde_json::{json, Value};
use utoipa::ToSchema;

use crate::AppState;

#[derive(serde::Serialize, serde::Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct GetAum {
    #[schema(value_type = String, example = "2022-01-22")]
    from_date: NaiveDate,
    #[schema(value_type = String, example = "2022-01-22")]
    to_date: NaiveDate,
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct AumRes {
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    #[schema(value_type = String, example = 0)]
    pub cash: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub nav: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub change: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub total_aum: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub units: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub net_cash_flows: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub payables: Decimal,
    #[schema(value_type = f64, example = 0)]
    pub receivables: Decimal,
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct PortfolioAumResponse {
    aums: Vec<AumRes>,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/get_aum/{portfolio_id}",
    responses(
        (status = 200, description = "Aum Return", body = PortfolioAumResponse),
        (status = NOT_FOUND, description = "Aum was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for AUM query"),
        ("toDate" = String, Query, description = "To Date filter for AUM query")
    )
)]

pub async fn get_aum_range(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetAum>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let mut aums: Vec<AumRes> = Vec::new();

    while start_date <= end_date {
        let mut prefix: String = format!(
            "{}-{}-",
            alpha_storage_engine::storage::DbKeyPrefix::PortfolioAum,
            portfolio_id
        );
        prefix.push_str(&start_date.to_string());

        let aum = state.storage_engine.db.get(prefix);
        match aum {
            Ok(aum) => {
                if let Some(aum) = aum {
                    let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                    let aum_des: Result<Aum, std::convert::Infallible> = archived.deserialize(&mut rkyv::Infallible);
                    if let Ok(aum) = aum_des {
                        aums.push(AumRes {
                            cash: aum.cash,
                            change: aum.change,
                            date: aum.date,
                            market_value: aum.market_value,
                            nav: aum.nav,
                            net_cash_flows: aum.net_cash_flows,
                            payables: aum.payables,
                            receivables: aum.receivables,
                            total_aum: aum.total_aum,
                            units: aum.units,
                        });
                    }
                }
            }
            Err(aum) => {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get AUM From Data")
                });

                return Err((StatusCode::BAD_REQUEST, Json(res)));
            }
        }

        start_date = start_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<PortfolioAumResponse>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(PortfolioAumResponse { aums }),
    ))
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct AumAll {
    pub portfolio_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    #[schema(value_type = String, example = "0")]
    pub cash: Decimal,
    #[schema(value_type = String, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = String, example = "0")]
    pub nav: Decimal,
    #[schema(value_type = String, example = "0")]
    pub change: Decimal,
    #[schema(value_type = String, example = "0")]
    pub total_aum: Decimal,
    #[schema(value_type = String, example = "0")]
    pub units: Decimal,
    #[schema(value_type = String, example = "0")]
    pub net_cash_flows: Decimal,
    #[schema(value_type = String, example = "0")]
    pub payables: Decimal,
    #[schema(value_type = String, example = "0")]
    pub receivables: Decimal,
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct PortfolioAumAllResponse {
    aums: Vec<AumAll>,
}

pub async fn get_aum_for_all_portfolios(
    State(state): State<Arc<AppState>>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut aums: Vec<AumAll> = Vec::new();
    let storage = StorageWriter::new(state.storage_engine.db.clone());
    let portfolios = storage.get_all_portfolio_with_start_date();

    for portfolio in portfolios {
        let mut start_date = portfolio.start_date;
        let end_date = Utc::now().date_naive();

        while start_date <= end_date {
            let mut prefix: String = format!(
                "{}-{}-",
                alpha_storage_engine::storage::DbKeyPrefix::PortfolioAum,
                portfolio.portfolio_id
            );
            prefix.push_str(&start_date.to_string());

            let aum = state.storage_engine.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des: Result<Aum, std::convert::Infallible> =
                            archived.deserialize(&mut rkyv::Infallible);
                        if let Ok(aum) = aum_des {
                            aums.push(AumAll {
                                portfolio_id: portfolio.portfolio_id.clone(),
                                cash: aum.cash,
                                change: aum.change,
                                date: aum.date,
                                market_value: aum.market_value,
                                nav: aum.nav,
                                net_cash_flows: aum.net_cash_flows,
                                payables: aum.payables,
                                receivables: aum.receivables,
                                total_aum: aum.total_aum,
                                units: aum.units,
                            });
                        }
                    }
                }
                Err(aum) => {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get AUM From Data")
                    });

                    return Err((StatusCode::BAD_REQUEST, Json(res)));
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in aums.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./aum.csv", buff).unwrap();

    Ok::<(StatusCode, Json<PortfolioAumAllResponse>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(PortfolioAumAllResponse { aums }),
    ))
}
