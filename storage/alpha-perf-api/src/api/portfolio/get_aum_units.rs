use std::sync::Arc;

use alpha_storage_engine::states::aum::Aum;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate};
use rkyv::Deserialize as RkyvDeserialize;
use rust_decimal::Decimal;
use utoipa::{ToResponse, ToSchema};

use crate::AppState;
use serde::{Deserialize, Serialize};
use serde_json::json;

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetAumUnits {
    from_date: NaiveDate,
    to_date: NaiveDate,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct AumUnits {
    #[schema(value_type = f64, example = "0")]
    pub units: Decimal,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/get_aum_units/{portfolio_id}",
    responses(
        (status = 200, description = "Aum Units", body = AumUnits),
        (status = NOT_FOUND, description = "Aum Units was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for AUM Units query"),
        ("toDate" = String, Query, description = "To Date filter for AUM Units query")
    )
)]

pub async fn get_aum_units(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetAumUnits>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let mut aums: Vec<Aum> = Vec::new();

    while start_date <= end_date {
        let mut prefix: String = format!(
            "{}-{}-",
            alpha_storage_engine::storage::DbKeyPrefix::PortfolioAum,
            portfolio_id
        );
        prefix.push_str(&start_date.to_string());

        let aum = state.storage_engine.db.get(prefix);
        match aum {
            Ok(aum) => {
                if let Some(aum) = aum {
                    let archived = unsafe { rkyv::archived_root::<Aum>(&aum) };
                    let deserialise_aum = archived.deserialize(&mut rkyv::Infallible);
                    if let Ok(aum) = deserialise_aum {
                        aums.push(aum);
                    }
                }
            }
            Err(aum) => {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get AUM From Data")
                });

                return Err((StatusCode::BAD_REQUEST, Json(res)));
            }
        }

        start_date = start_date.checked_add_days(Days::new(1)).unwrap();
    }

    let total_units: Decimal = aums.iter().map(|a| a.units).sum();
    let units = AumUnits { units: total_units };
    Ok((StatusCode::ACCEPTED, Json(units)))
}
