use std::sync::Arc;

use alpha_storage_engine::{states::investment::Investment, storage::StorageWriter};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::NaiveDate;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use utoipa::ToSchema;

use crate::AppState;

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ComputeFromLatest {
    pub date: NaiveDate,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
struct PortfolioAnalytics {
    pub industries_allocations: Vec<IndustriesAllocationRes>,
    pub market_cap_allocation: MarketCapAllocationRes,
    pub asset_class_allocation: AssetClassAllocationRes,
    pub scoreboard: ScoreBoard,
    pub top_five_performers: Vec<TopFivePerformers>,
    pub top_ten_holdings: Vec<TopTenHoldings>,
    pub aum: Vec<AumRes>,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct TopFivePerformers {
    pub name: String,
    #[schema(value_type = f64, example = "0")]
    pub weight: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub xirr: Decimal,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ScoreBoard {
    pub gainers: Vec<ScoreBoardElements>,
    pub losers: Vec<ScoreBoardElements>,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ScoreBoardElements {
    pub name: String,
    #[schema(value_type = f64, example = "0")]
    pub change: Decimal,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct TopTenHoldings {
    pub name: String,
    pub industry: String,
    pub market_cap: String,
    #[schema(value_type = f64, example = "0")]
    pub weight: Decimal,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct MarketCapAllocationRes {
    #[schema(value_type = f64, example = "0")]
    pub large_cap: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub mid_cap: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub small_cap: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub others: Decimal,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct AssetClassAllocationRes {
    #[schema(value_type = f64, example = "0")]
    pub equity: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub cash: Decimal,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct IndustriesAllocationRes {
    pub name: String,
    #[schema(value_type = f64, example = "0")]
    pub weight: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub xirr: Decimal,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct AumRes {
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    #[schema(value_type = f64, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub invested_capital: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub change: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub total_aum: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub net_cash_flow: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub cash_balance: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub nav: Decimal,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/analytics/{portfolio_id}",
    responses(
        (status = 200, description = "Portfolio Analytics Return", body = PortfolioAnalytics),
        (status = NOT_FOUND, description = "Portfolio Analytics was not found")
    ),
    params(
        ("date" = String, Query, description = "As At Date filter for Portfolio Analytics query"),
    )
)]

pub async fn get_analytics_for_portfolio(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<ComputeFromLatest>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let portfolio_id = &portfolio_id;
    let date = payload.date;

    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());

    let portfolio_start_date = storage_rw.get_portfolio_start_date(portfolio_id).ok_or_else(|| {
        let res = json!({
            "status": "error",
            "message": format!("Portfolio Doesn't exist")
        });

        return (StatusCode::BAD_REQUEST, Json(res));
    })?;

    let asset_class_allocation = storage_rw
        .get_asset_class_allocation(portfolio_id, date)
        .ok_or_else(|| {
            let res = json!({
                "status": "error",
                "message": format!("Failed to Get AUM From Data")
            });

            return (StatusCode::BAD_REQUEST, Json(res));
        })?;

    let asset_class_allocation = AssetClassAllocationRes {
        cash: asset_class_allocation.cash,
        equity: asset_class_allocation.equity,
    };

    let market_cap_allocation = storage_rw
        .get_market_cap_allocation(portfolio_id, date)
        .ok_or_else(|| {
            let res = json!({
                "status": "error",
                "message": format!("Failed to Get AUM From Data")
            });

            return (StatusCode::BAD_REQUEST, Json(res));
        })?;

    let market_cap_allocation = MarketCapAllocationRes {
        large_cap: market_cap_allocation.large_cap,
        mid_cap: market_cap_allocation.mid_cap,
        others: market_cap_allocation.others,
        small_cap: market_cap_allocation.small_cap,
    };

    let industries_allocation = storage_rw
        .get_industries_allocation(portfolio_id, date)
        .ok_or_else(|| {
            let res = json!({
                "status": "error",
                "message": format!("Failed to Get AUM From Data")
            });

            return (StatusCode::BAD_REQUEST, Json(res));
        })?;

    let industries_allocations: Vec<IndustriesAllocationRes> = industries_allocation
        .iter()
        .map(|industries_allocation| IndustriesAllocationRes {
            name: industries_allocation.name.clone(),
            weight: industries_allocation.weight,
            xirr: industries_allocation.xirr,
        })
        .collect();

    let mut investments = storage_rw.get_portfolio_investment(portfolio_id, date).ok_or_else(|| {
        let res = json!({
            "status": "error",
            "message": format!("Failed to Get AUM From Data")
        });

        return (StatusCode::BAD_REQUEST, Json(res));
    })?;

    investments.sort_by(|a, b| b.xirr.partial_cmp(&a.xirr).unwrap());

    let top_five_performers: Vec<TopFivePerformers> = investments
        .iter()
        .take(5)
        .map(|inv| {
            return TopFivePerformers {
                name: inv.name.clone(),
                xirr: inv.xirr,
                weight: inv.weight,
            };
        })
        .collect();

    let (gainers, losers) = build_scoreboard(&investments);

    investments.sort_by(|a, b| b.weight.partial_cmp(&a.weight).unwrap());

    let top_ten_holdings = investments
        .iter()
        .take(10)
        .map(|i| {
            return TopTenHoldings {
                industry: i.industry.clone(),
                market_cap: i.market_cap.clone(),
                name: i.name.clone(),
                weight: i.weight,
            };
        })
        .collect();

    let aum = storage_rw
        .get_aum_for_period(portfolio_id, portfolio_start_date, date)
        .iter()
        .map(|f| {
            let portfolio = storage_rw.get_portfolio(portfolio_id, date).unwrap();
            return AumRes {
                date: f.date,
                total_aum: f.total_aum,
                change: f.change,
                invested_capital: portfolio.invested_capital,
                market_value: f.market_value,
                cash_balance: f.cash,
                net_cash_flow: f.net_cash_flows,
                nav: f.nav,
            };
        })
        .collect::<Vec<AumRes>>();

    let analytics = PortfolioAnalytics {
        asset_class_allocation,
        aum,
        industries_allocations,
        market_cap_allocation,
        scoreboard: ScoreBoard { gainers, losers },
        top_five_performers,
        top_ten_holdings,
    };
    Ok::<(StatusCode, Json<PortfolioAnalytics>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(analytics)))
}

fn build_scoreboard(investments: &[Investment]) -> (Vec<ScoreBoardElements>, Vec<ScoreBoardElements>) {
    let mut positive: Vec<_> = investments
        .iter()
        .filter(|inv| inv.close_price_change > dec!(0))
        .map(|inv| ScoreBoardElements {
            name: inv.name.clone(),
            change: inv.close_price_change, // Convert f64 to Decimal
        })
        .collect();

    let mut negative: Vec<_> = investments
        .iter()
        .filter(|inv| inv.close_price_change < dec!(0))
        .map(|inv| ScoreBoardElements {
            name: inv.name.clone(),
            change: inv.close_price_change,
        })
        .collect();

    // Sort positive in descending order (highest first)
    positive.sort_by(|a, b| b.change.partial_cmp(&a.change).unwrap());

    // Sort negative in ascending order (lowest first)
    negative.sort_by(|a, b| a.change.partial_cmp(&b.change).unwrap());

    // Take top 5 from each
    (
        positive.into_iter().take(5).collect(),
        negative.into_iter().take(5).collect(),
    )
}
