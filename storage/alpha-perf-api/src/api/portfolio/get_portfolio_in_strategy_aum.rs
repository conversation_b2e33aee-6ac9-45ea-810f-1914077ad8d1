use std::{collections::HashSet, fs, sync::Arc};

use alpha_core_db::schema::strategy::Strategies;
use alpha_storage_engine::{states::aum::Aum, storage::StorageWriter};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate};
use csv::Writer;
use rkyv::Deserialize;
use rust_decimal::Decimal;
use serde::Serialize;
use serde_json::{json, Value};
use utoipa::{IntoParams, ToSchema};

use crate::AppState;

use super::get_holdings::GetHoldings;

#[derive(serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioAumQuery {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(serde::Serialize, serde::Deserialize, <PERSON><PERSON>, ToSchema)]
pub struct AumRes {
    pub portfolio_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    #[schema(value_type = String, example = "0")]
    pub cash: Decimal,
    #[schema(value_type = String, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = String, example = "0")]
    pub nav: Decimal,
    #[schema(value_type = String, example = "0")]
    pub change: Decimal,
    #[schema(value_type = String, example = "0")]
    pub total_aum: Decimal,
    #[schema(value_type = String, example = "0")]
    pub units: Decimal,
    #[schema(value_type = String, example = "0")]
    pub net_cash_flows: Decimal,
    #[schema(value_type = String, example = "0")]
    pub payables: Decimal,
    #[schema(value_type = String, example = "0")]
    pub receivables: Decimal,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/get_portfolios_in_strategy_aum/{portfolio_id}",
    responses(
        (status = 200, description = "Benchmark", body = AumRes),
        (status = NOT_FOUND, description = "Benchmark was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Aum query"),
        ("toDate" = String, Query, description = "To Date filter for Aum query")
    )
)]

pub async fn get_portfolios_in_strategy_aum_range(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<PortfolioAumQuery>,
    Path(strategy_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;
    let mut conn = state.db.get().await.unwrap();
    let mut aums: Vec<AumRes> = Vec::new();
    let storage = StorageWriter::new(state.storage_engine.db.clone());

    let portfolios_in_models = Strategies::get_portfolios_in_strategy(&mut conn, &strategy_id)
        .await
        .map_err(|e| format!("Error"))
        .unwrap();

    let portfolio_ids: Vec<_> = portfolios_in_models
        .iter()
        .map(|p| p.portfolio_id.clone())
        .collect::<HashSet<_>>()
        .into_iter()
        .collect();

    for portfolio_id in portfolio_ids {
        while start_date <= end_date {
            let mut prefix: String = format!(
                "{}-{}-",
                alpha_storage_engine::storage::DbKeyPrefix::PortfolioAum,
                portfolio_id
            );

            prefix.push_str(&start_date.to_string());

            let aum = state.storage_engine.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des: Result<Aum, std::convert::Infallible> =
                            archived.deserialize(&mut rkyv::Infallible);
                        if let Ok(aum) = aum_des {
                            aums.push(AumRes {
                                portfolio_id: portfolio_id.clone(),
                                cash: aum.cash,
                                change: aum.change,
                                date: aum.date,
                                market_value: aum.market_value,
                                nav: aum.nav,
                                net_cash_flows: aum.net_cash_flows,
                                payables: aum.payables,
                                receivables: aum.receivables,
                                total_aum: aum.total_aum,
                                units: aum.units,
                            });
                        }
                    }
                }
                Err(aum) => {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get AUM From Data")
                    });

                    return Err((StatusCode::BAD_REQUEST, Json(res)));
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }

        start_date = payload.from_date
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in aums.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./aum.csv", buff).unwrap();

    Ok::<(StatusCode, Json<Vec<AumRes>>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(aums)))
}

#[derive(Serialize, Clone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioHoldings {
    holdings: Vec<Investment>,
}

#[derive(Serialize, Clone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct Investment {
    portfolio_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    pub name: String,
    pub isin: String,
    #[schema(value_type = f64, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub holdings: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub average_price: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub current_price: Decimal,
}

pub async fn get_portfolios_in_strategy_holdings(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetHoldings>,
    Path(strategy_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let mut conn = state.db.get().await.unwrap();

    let mut holdings: Vec<Investment> = Vec::new();

    let portfolios_in_models = Strategies::get_portfolios_in_strategy(&mut conn, &strategy_id)
        .await
        .map_err(|e| format!("Error"))
        .unwrap();

    let portfolio_ids: Vec<_> = portfolios_in_models
        .iter()
        .map(|p| p.portfolio_id.clone())
        .collect::<HashSet<_>>()
        .into_iter()
        .collect();

    for portfolio_id in portfolio_ids {
        while start_date < end_date {
            let mut prefix: String = format!(
                "{}-{}-",
                alpha_storage_engine::storage::DbKeyPrefix::PortfolioInvestment,
                portfolio_id
            );
            prefix.push_str(&start_date.to_string());

            let investments = state.storage_engine.db.get(prefix);
            match investments {
                Ok(investments) => {
                    if let Some(investments) = investments {
                        let archived = unsafe {
                            rkyv::archived_root::<Vec<alpha_storage_engine::states::investment::Investment>>(
                                &investments[..],
                            )
                        };

                        let mut investments_des: Result<
                            Vec<alpha_storage_engine::states::investment::Investment>,
                            std::convert::Infallible,
                        > = archived.deserialize(&mut rkyv::Infallible);
                        if let Ok(inv) = &mut investments_des {
                            for invs in inv {
                                let inv = Investment {
                                    portfolio_id: portfolio_id.clone(),
                                    date: invs.date,
                                    isin: invs.isin.clone(),
                                    holdings: invs.holdings,
                                    market_value: invs.market_value,
                                    name: invs.name.clone(),
                                    average_price: invs.average_price,
                                    current_price: invs.current_price,
                                };
                                holdings.push(inv);
                            }
                        }
                    }
                }
                Err(aum) => {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get AUM From Data")
                    });

                    return Err((StatusCode::BAD_REQUEST, Json(res)));
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in holdings.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./holdings.csv", buff).unwrap();

    Ok::<(StatusCode, Json<PortfolioHoldings>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(PortfolioHoldings { holdings }),
    ))
}
