use std::sync::Arc;

use alpha_storage_engine::storage::StorageWriter;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Months, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use utoipa::ToSchema;

use crate::AppState;

#[derive(Deserialize, Serialize, Clone, Copy, ToSchema)]
pub enum Period {
    OneMonth,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct GetPerformanceForPeriodReq {
    period: Period,
    #[schema(value_type = String, example = "2022-01-22")]
    from_date: NaiveDate,
    total_period: usize,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct PerformanceReportPeriodSummary {
    period: Period,
    #[schema(value_type = String, example = "2022-01-22")]
    date: NaiveDate,
    xirr: String,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct GetPerformanceForPeriodResp {
    performance: Vec<PerformanceReportPeriodSummary>,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/performance_for_periods/{portfolio_id}",
    responses(
        (status = 200, description = "Performance for Given Period", body = GetPerformanceForPeriodReq),
        (status = NOT_FOUND, description = "metrics was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Metrics query"),
        ("period" = Period, Query, description = "Period to query the performance"),
        ("totalPeriod" = usize, Query, description = "Total Period to query the performance")
    )
)]
pub async fn get_performance_for_periods(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetPerformanceForPeriodReq>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());
    let mut from_date = payload.from_date;
    let current_date = Utc::now().naive_utc().date();
    let mut start_period = 1;
    let mut performance = Vec::new();
    while from_date < current_date {
        match payload.period {
            Period::OneMonth => {
                from_date = from_date.checked_add_months(Months::new(start_period)).unwrap();
                let xirr = storage_rw.get_xirr_metrics(&portfolio_id, from_date).map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Xirr From Data")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

                if let Some(xirr) = xirr {
                    performance.push(PerformanceReportPeriodSummary {
                        date: from_date,
                        period: payload.period,
                        xirr: xirr.one_month.map(|f| f.to_string()).unwrap_or("N/A".to_string()),
                    });
                }
            }
        }

        start_period += 1;
    }

    return Ok::<(StatusCode, Json<GetPerformanceForPeriodResp>), (StatusCode, Json<Value>)>((
        StatusCode::OK,
        Json(GetPerformanceForPeriodResp { performance }),
    ));
}
