use std::{fs, sync::Arc};

use alpha_storage_engine::states::aum::Aum;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate};
use csv::Writer;
use rkyv::Deserialize;
use rust_decimal::Decimal;
use serde_json::{json, Value};
use utoipa::ToSchema;

use crate::{types::AumRes, AppState};

#[derive(serde::Serialize, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetAum {
    from_date: NaiveDate,
    to_date: NaiveDate,
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct ModelAumResponse {
    pub aums: Vec<AumRes>,
}
#[utoipa::path(
    get,
    tag = "Model",
    path = "/model/get_aum/{model_id}",
    responses(
        (status = 200, description = "Aum Return", body = ModelAumResponse),
        (status = NOT_FOUND, description = "Aum was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for AUM query"),
        ("toDate" = String, Query, description = "To Date filter for AUM query")
    )
)]

pub async fn get_aum_range(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetAum>,
    Path(model_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let mut aums: Vec<AumRes> = Vec::new();

    while start_date <= end_date {
        let mut prefix: String = format!("{}-{}-", alpha_storage_engine::storage::DbKeyPrefix::ModelAum, model_id);
        prefix.push_str(&start_date.to_string());

        let aum = state.storage_engine.db.get(prefix);
        match aum {
            Ok(aum) => {
                if let Some(aum) = aum {
                    let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                    let aum_des: Result<Aum, std::convert::Infallible> = archived.deserialize(&mut rkyv::Infallible);
                    if let Ok(aum) = aum_des {
                        aums.push(AumRes {
                            cash: aum.cash,
                            change: aum.change,
                            date: aum.date,
                            market_value: aum.market_value,
                            nav: aum.nav,
                            net_cash_flows: aum.net_cash_flows,
                            payables: aum.payables,
                            receivables: aum.receivables,
                            total_aum: aum.total_aum,
                            units: aum.units,
                        });
                    }
                }
            }
            Err(aum) => {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get AUM From Data")
                });

                return Err((StatusCode::BAD_REQUEST, Json(res)));
            }
        }

        start_date = start_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<Vec<AumRes>>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(aums)))
}
