use alpha_storage_engine::duckdb::{DuckDb, QueryResult};
use axum::{
    response::{Html, IntoResponse},
    Form,
};
use serde::Deserialize;

pub async fn query_page() -> Html<&'static str> {
    Html(
        r#"
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>DuckDB SQL Query</title>
        </head>
        <body>
            <h1>Query Delta Lake</h1>
            <form method="POST" action="/query">
                <textarea name="sql" rows="10" cols="80">SELECT * FROM PortfolioAum LIMIT 10;</textarea><br>
                <button type="submit">Run Query</button>
            </form>
        </body>
        </html>
    "#,
    )
}

#[derive(Deserialize)]
pub struct QueryInput {
    sql: String,
}

pub fn render_query_html(result: &QueryResult) -> String {
    let columns_json = serde_json::to_string(&result.columns).unwrap();
    let rows_json = serde_json::to_string(&result.rows).unwrap();

    format!(
        r#"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Query Results</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 2rem;
            background-color: #f9f9f9;
            color: #333;
        }}
        h1 {{
            margin-bottom: 1.5rem;
        }}
        .table-wrapper {{
            overflow-x: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            min-width: 600px;
        }}
        th, td {{
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eaeaea;
            text-align: left;
        }}
        th {{
            background-color: #f3f4f6;
            font-weight: 600;
        }}
        tr:hover {{
            background-color: #f9fafb;
        }}
        .pagination {{
            margin-top: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }}
        .pagination button {{
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            background-color: #3b82f6;
            color: white;
            cursor: pointer;
            font-weight: 500;
        }}
        .pagination button:disabled {{
            background-color: #d1d5db;
            cursor: not-allowed;
        }}
    </style>
</head>
<body>
    <h1>Query Results</h1>
    <div class="table-wrapper">
        <div id="table-container"></div>
    </div>
    <div class="pagination">
        <button onclick="prevPage()" id="prevBtn">Previous</button>
        <span id="page-info"></span>
        <button onclick="nextPage()" id="nextBtn">Next</button>
    </div>

    <script>
        const columns = {columns_json};
        const rows = {rows_json};
        let currentPage = 1;
        const pageSize = 20;

        function renderTable() {{
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            const pageRows = rows.slice(start, end);

            let html = "<table><thead><tr>";
            for (const col of columns) {{
                html += `<th>${{col}}</th>`;
            }}
            html += "</tr></thead><tbody>";

            for (const row of pageRows) {{
                html += "<tr>";
                for (const cell of row) {{
                    html += `<td>${{cell}}</td>`;
                }}
                html += "</tr>";
            }}
            html += "</tbody></table>";

            document.getElementById("table-container").innerHTML = html;
            document.getElementById("page-info").innerText = `Page ${{currentPage}} of ${{Math.ceil(rows.length / pageSize)}}`;

            document.getElementById("prevBtn").disabled = currentPage === 1;
            document.getElementById("nextBtn").disabled = currentPage * pageSize >= rows.length;
        }}

        function nextPage() {{
            if (currentPage * pageSize < rows.length) {{
                currentPage++;
                renderTable();
            }}
        }}

        function prevPage() {{
            if (currentPage > 1) {{
                currentPage--;
                renderTable();
            }}
        }}

        renderTable();
    </script>
</body>
</html>
"#
    )
}

pub async fn handle_query(Form(QueryInput { sql }): Form<QueryInput>) -> impl IntoResponse {
    let duck = DuckDb::new().unwrap();
    duck.load_delta_extension().unwrap();
    //FIXME: Change this later make it globally available using config
    let delta_lake_path = std::env::var("DELTA_LAKE_PATH").expect("DELTA_LAKE_PATH not found");
    duck.create_views(&delta_lake_path);

    match duck.query_lake_structured(&sql) {
        Ok(result) => Html(render_query_html(&result)),
        Err(e) => Html(format!("<h1>Error</h1><pre>{}</pre>", e)),
    }
}
