use std::sync::Arc;

use alpha_storage_engine::{
    computer::{portfolio_state_computer::PortfolioStateComputer, state_computer::StateComputer},
    storage::Storage,
};
use axum::{extract::State, http::StatusCode, response::IntoResponse, Json};
use chrono::{NaiveDate, Utc};
use serde::Deserialize;

use crate::AppState;

#[derive(Deserialize)]
pub struct ComputeFromLatest {
    pub portfolio_id: Option<String>,
    pub date: NaiveDate,
}

pub async fn trigger_as_at(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<ComputeFromLatest>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let clickhouse_pool = state.clickhouse_client.clone();
    let date = payload.date;
    let db_pool = state.db.clone();
    let master_db_pool = state.master_db.clone();
    let storage = Arc::new(Storage {
        db: state.storage_engine.db.clone(),
    });
    let redis_pool = state.redis_pool.clone();

    let computer = StateComputer {
        clickhouse_pool,
        rabbit_mq_queue: state.rabbit_mq_queue.clone(),
        date,
        db_pool,
        master_db_pool,
        redis_pool,
        storage: storage,
    };

    if let Some(portfolio_id) = payload.portfolio_id {
        tokio::spawn(async move { computer.compute_for_a_portfolio_as_at(&portfolio_id).await });
    } else {
        tokio::spawn(async move { computer.compute_as_at().await });
    }

    Ok::<StatusCode, StatusCode>(StatusCode::ACCEPTED)
}
