use std::sync::Arc;

use alpha_storage_engine::{
    computer::{portfolio_state_computer::PortfolioStateComputer, state_computer::StateComputer},
    storage::Storage,
};
use axum::{extract::State, http::StatusCode, response::IntoResponse, Json};
use chrono::{Days, Duration, NaiveDate, Utc};
use serde::Deserialize;

use crate::AppState;

#[derive(Deserialize)]
pub struct ComputeFromLatest {
    pub genesis: bool,
}

pub async fn trigger_all(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<ComputeFromLatest>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let clickhouse_pool = state.clickhouse_client.clone();
    let date = (Utc::now() + Duration::hours(5) + Duration::minutes(30))
        .date_naive()
        .checked_sub_days(Days::new(1))
        .unwrap();
    
    let db_pool = state.db.clone();
    let master_db_pool = state.master_db.clone();
    let redis_pool = state.redis_pool.clone();

    let computer = StateComputer {
        clickhouse_pool,
        rabbit_mq_queue : state.rabbit_mq_queue.clone(),
        date,
        db_pool,
        master_db_pool,
        redis_pool,
        storage: state.storage_engine.clone(),
    };

    if payload.genesis {
        tokio::spawn(async move { computer.compute_from_scratch().await });
    } else {
        tokio::spawn(async move { computer.compute_from_latest().await });
    }

    Ok::<StatusCode, StatusCode>(StatusCode::ACCEPTED)
}
