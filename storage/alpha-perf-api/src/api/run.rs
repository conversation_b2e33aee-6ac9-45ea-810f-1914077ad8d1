use std::sync::Arc;

use alpha_storage_engine::computer::portfolio_state_computer::PortfolioStateComputer;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Json};
use chrono::Utc;
use serde::Deserialize;

use crate::AppState;

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RunPortfolio {
    portfolio_id: String,
}

pub async fn run_engine(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<RunPortfolio>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    tokio::spawn(async move {
        let date = Utc::now().naive_utc();

        let mut state = PortfolioStateComputer {
            clickhouse_pool: state.clickhouse_client.clone(),
            client_id: String::from("ClientId"),
            portfolio_id: payload.portfolio_id,
            date: date.into(),
            db_pool: state.db.clone(),
            master_db_pool: state.master_db.clone(),
            redis_pool: state.redis_pool.clone(),
            storage: &state.storage_engine,
        };

        state.compute_from_latest_block().await;
    });

    Ok::<StatusCode, StatusCode>(StatusCode::ACCEPTED)
}
