use alpha_storage_engine::lake::read_all_schemas;
use axum::{response::Html, routing::get, Router};
use std::net::SocketAddr;

fn render_schema_html(tables: Vec<(String, Vec<(String, String)>)>) -> String {
    let mut html = String::from(
        "<!DOCTYPE html><html><head><meta charset='utf-8'><title>Delta Table Schemas</title>
        <style>
            body { font-family: sans-serif; margin: 2rem; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 2rem; }
            th, td { border: 1px solid #ccc; padding: 0.5rem; text-align: left; }
            th { background-color: #f5f5f5; }
            h2 { margin-top: 2rem; }
        </style>
    </head><body><h1>Delta Lake Table Schemas</h1>",
    );

    for (table, fields) in tables {
        html.push_str(&format!(
            "<h2>{}</h2><table><thead><tr><th>Column</th><th>Type</th></tr></thead><tbody>",
            table
        ));
        for (name, dtype) in fields {
            html.push_str(&format!("<tr><td>{}</td><td>{}</td></tr>", name, dtype));
        }
        html.push_str("</tbody></table>");
    }

    html.push_str("</body></html>");
    html
}

pub async fn schema_page() -> Html<String> {
    //FIXME: Change this later make it globally available using config
    let delta_lake_path = std::env::var("DELTA_LAKE_PATH").expect("DELTA_LAKE_PATH not found");
    let schemas = read_all_schemas(&delta_lake_path).await;
    Html(render_schema_html(schemas))
}
