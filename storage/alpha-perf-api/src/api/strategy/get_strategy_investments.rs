use alpha_storage_engine::{
    states::investment::Investment,
    storage::{strategy::StrategiesInvestments, StorageWriter},
};
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::NaiveDate;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::{collections::HashMap, sync::Arc};
use utoipa::ToSchema;

use crate::AppState;

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetStrategiesInvestmentQuery {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(Serialize, Deserialize, ToSchema)]
pub struct AssetClassSummary {
    #[schema(value_type = f64, example = "0")]
    market_value: Decimal,
    asset_class: String,
}

#[derive(Serialize, Deserialize, ToSchema)]
pub struct StrategyInvestmentDetailsForSebiMonthly {
    data: HashMap<String, Vec<AssetClassSummary>>,
}

#[utoipa::path(
    get,
    tag = "Strategy",
    path = "/strategy/all_strategies_investments_sebi_monthly",
    responses(
        (status = 200, description = "Strategy investment", body = StrategyInvestmentDetailsForSebiMonthly),
        (status = NOT_FOUND, description = "Strategy investment was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Strategy investment query"),
        ("toDate" = String, Query, description = "To Date filter for Strategy investment query")
    )
)]
pub async fn get_strategy_investments_for_sebi_monthly(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetStrategiesInvestmentQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let from_date = payload.from_date;
    let to_date = payload.to_date;
    let mut storage_rw = StorageWriter::new(state.storage_engine.db.clone());

    let strategy_investments = storage_rw.get_all_strategies_investments(from_date, to_date).await;

    let mut required_inv = StrategyInvestmentDetailsForSebiMonthly { data: HashMap::new() };

    for (strategy, inv) in strategy_investments {
        let asset_class_summary = sum_market_value_by_asset_class(&inv);
        required_inv.data.insert(strategy, asset_class_summary);
    }

    Ok::<(StatusCode, Json<StrategyInvestmentDetailsForSebiMonthly>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(required_inv),
    ))
}

pub fn sum_market_value_by_asset_class(securities: &[Investment]) -> Vec<AssetClassSummary> {
    // First pass: group by asset class using a HashMap as an intermediate structure
    let mut temp_map = std::collections::HashMap::new();

    // Calculate sum for each asset class
    for security in securities {
        let entry = temp_map
            .entry(security.asset_class.clone())
            .or_insert(AssetClassSummary {
                asset_class: security.asset_class.clone(),
                market_value: Decimal::ZERO,
            });

        entry.market_value += security.market_value;
    }

    // Convert HashMap to Vec and calculate percentages
    let result: Vec<AssetClassSummary> = temp_map.into_values().collect();

    result
}
