use alpha_storage_engine::storage::StorageWriter;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate, Utc};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::{collections::HashMap, sync::Arc};
use utoipa::ToSchema;

use crate::{
    types::{Metrics, TwrrResults, XirrResults},
    AppState,
};

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioMetrics {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct StrategyReturns {
    pub returns: Vec<Metrics>,
}

#[utoipa::path(
    get,
    tag = "Strategy",
    path = "/strategy/returns/{strategy_id}",
    responses(
        (status = 200, description = "Returns", body = StrategyReturns),
        (status = NOT_FOUND, description = "Returns was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for return query"),
        ("toDate" = String, Query, description = "To Date filter for return query")
    )
)]

pub async fn get_strategy_returns(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<PortfolioMetrics>,
    Path(strategy_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());
    let mut from_date = payload.from_date;
    let to_date = payload.to_date;
    let mut xi = Vec::new();

    while from_date <= to_date {
        let xirr = storage_rw
            .get_strategy_xirr_metrics(&strategy_id, from_date)
            .map_err(|e| {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get Xirr From Data")
                });

                return (StatusCode::BAD_REQUEST, Json(res));
            })?;

        let twrr = storage_rw
            .get_strategy_twrr_metrics(&strategy_id, from_date)
            .map_err(|e| {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get Xirr From Data")
                });

                return (StatusCode::BAD_REQUEST, Json(res));
            })?;

        if let (Some(xirr), Some(twrr)) = (xirr, twrr) {
            let metrics = Metrics {
                date: from_date,
                xirr: XirrResults {
                    fytd: Decimal::from_f64(xirr.fytd.unwrap_or_default()).unwrap_or_default(),
                    one_month: Decimal::from_f64(xirr.one_month.unwrap_or_default()).unwrap_or_default(),
                    one_year: Decimal::from_f64(xirr.one_year.unwrap_or_default()).unwrap_or_default(),
                    seven_year: Decimal::from_f64(xirr.seven_year.unwrap_or_default()).unwrap_or_default(),
                    since_inception: Decimal::from_f64(xirr.since_inception.unwrap_or_default()).unwrap_or_default(),
                    six_month: Decimal::from_f64(xirr.six_month.unwrap_or_default()).unwrap_or_default(),
                    ten_year: Decimal::from_f64(xirr.ten_year.unwrap_or_default()).unwrap_or_default(),
                    three_month: Decimal::from_f64(xirr.three_month.unwrap_or_default()).unwrap_or_default(),
                    three_year: Decimal::from_f64(xirr.three_year.unwrap_or_default()).unwrap_or_default(),
                    two_year: Decimal::from_f64(xirr.two_year.unwrap_or_default()).unwrap_or_default(),
                    ytd: Decimal::from_f64(xirr.ytd.unwrap_or_default()).unwrap_or_default(),
                },
                twrr: TwrrResults {
                    fytd: twrr.fytd.unwrap_or_default(),
                    one_month: twrr.one_month.unwrap_or_default(),
                    one_year: twrr.one_year.unwrap_or_default(),
                    seven_year: twrr.seven_year.unwrap_or_default(),
                    since_inception: twrr.since_inception.unwrap_or_default(),
                    six_month: twrr.six_month.unwrap_or_default(),
                    ten_year: twrr.ten_year.unwrap_or_default(),
                    three_month: twrr.three_month.unwrap_or_default(),
                    three_year: twrr.three_year.unwrap_or_default(),
                    two_year: twrr.two_year.unwrap_or_default(),
                    ytd: twrr.ytd.unwrap_or_default(),
                },
            };

            xi.push(metrics);
        }

        from_date = from_date.checked_add_days(Days::new(1)).unwrap();
    }

    Ok::<(StatusCode, Json<StrategyReturns>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(StrategyReturns { returns: xi }),
    ))
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetAllStrategyReturns {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct StrategyReturnsResponse {
    pub data: HashMap<String, Vec<Metrics>>,
}

pub async fn get_all_strategy_returns(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetAllStrategyReturns>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage_rw = StorageWriter::new(state.storage_engine.db.clone());
    let mut from_date = payload.from_date;
    let to_date = payload.to_date;
    let mut returns: HashMap<String, Vec<Metrics>> = HashMap::new();

    let all_strategies = storage_rw.get_all_strategies_with_start_date();
    while from_date <= to_date {
        //Get all the strategies that exist on this date
        for strategy in &all_strategies {
            if strategy.date > from_date {
                continue;
            }
            let xirr = storage_rw
                .get_strategy_xirr_metrics(&strategy.strategy_id, from_date)
                .map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Xirr From Data")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?
                .ok_or_else(|| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Xirr From Data")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

            let twrr = storage_rw
                .get_strategy_twrr_metrics(&strategy.strategy_id, from_date)
                .map_err(|e| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Xirr From Data")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?
                .ok_or_else(|| {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get Xirr From Data")
                    });

                    return (StatusCode::BAD_REQUEST, Json(res));
                })?;

            let metrics = Metrics {
                date: from_date,
                xirr: XirrResults {
                    fytd: Decimal::from_f64(xirr.fytd.unwrap_or_default()).unwrap_or_default(),
                    one_month: Decimal::from_f64(xirr.one_month.unwrap_or_default()).unwrap_or_default(),
                    one_year: Decimal::from_f64(xirr.one_year.unwrap_or_default()).unwrap_or_default(),
                    seven_year: Decimal::from_f64(xirr.seven_year.unwrap_or_default()).unwrap_or_default(),
                    since_inception: Decimal::from_f64(xirr.since_inception.unwrap_or_default()).unwrap_or_default(),
                    six_month: Decimal::from_f64(xirr.six_month.unwrap_or_default()).unwrap_or_default(),
                    ten_year: Decimal::from_f64(xirr.ten_year.unwrap_or_default()).unwrap_or_default(),
                    three_month: Decimal::from_f64(xirr.three_month.unwrap_or_default()).unwrap_or_default(),
                    three_year: Decimal::from_f64(xirr.three_year.unwrap_or_default()).unwrap_or_default(),
                    two_year: Decimal::from_f64(xirr.two_year.unwrap_or_default()).unwrap_or_default(),
                    ytd: Decimal::from_f64(xirr.ytd.unwrap_or_default()).unwrap_or_default(),
                },
                twrr: TwrrResults {
                    fytd: twrr.fytd.unwrap_or_default(),
                    one_month: twrr.one_month.unwrap_or_default(),
                    one_year: twrr.one_year.unwrap_or_default(),
                    seven_year: twrr.seven_year.unwrap_or_default(),
                    since_inception: twrr.since_inception.unwrap_or_default(),
                    six_month: twrr.six_month.unwrap_or_default(),
                    ten_year: twrr.ten_year.unwrap_or_default(),
                    three_month: twrr.three_month.unwrap_or_default(),
                    three_year: twrr.three_year.unwrap_or_default(),
                    two_year: twrr.two_year.unwrap_or_default(),
                    ytd: twrr.ytd.unwrap_or_default(),
                },
            };

            returns
                .entry(strategy.strategy_id.clone())
                .or_insert_with(Vec::new)
                .push(metrics);
        }

        from_date = from_date.checked_add_days(Days::new(1)).unwrap();
    }

    let response = StrategyReturnsResponse { data: returns };

    Ok::<(StatusCode, Json<StrategyReturnsResponse>), (StatusCode, Json<Value>)>((StatusCode::ACCEPTED, Json(response)))
}
