use std::sync::Arc;

use axum::{
    routing::{get, post},
    Router,
};

use crate::AppState;

pub mod get_aum;
pub mod get_returns;
pub mod get_strategy_investments;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route(
            "/all_strategies_investments_sebi_monthly",
            get(get_strategy_investments::get_strategy_investments_for_sebi_monthly),
        )
        .route("/all_strategies_returns", get(get_returns::get_all_strategy_returns))
        .route("/returns/{strategy_id}", get(get_returns::get_strategy_returns))
        .route("/get_aum/{strategy_id}", get(get_aum::get_aum_range))
}
