use std::sync::Arc;

use alpha_core_db::connection::pool::{deadpool::managed::Pool, Manager};
use alpha_storage_engine::storage::Storage;
use alpha_utils::rabbit_mq::RabbitMq;
use config::Config;

pub mod types;
pub mod api;
pub mod config;

#[derive(Clone)]
pub struct AppState {
    pub env: Config,
    pub db: Pool<Manager>,
    pub master_db: Pool<Manager>,
    pub storage_engine: Arc<Storage>,
    pub rabbit_mq_queue:Arc<RabbitMq>,
    pub clickhouse_client: clickhouse::Client,
    pub redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
}
