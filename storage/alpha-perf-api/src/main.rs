use actlogica_logs::{
    builder::Log<PERSON>uilder,
    log_error, log_info,
    setting::{init_logger, LogOutput},
};
use alpha_core_db::{
    clickhouse::create_clickhouse_client,
    connection::{connect_to_master_data, connect_to_mssql},
};
use alpha_perf_api::{
    api::{client, lake_schema::schema_page, model, portfolio, query_lake::{handle_query, query_page}, strategy, trigger},
    config::Config,
    AppState,
};
use alpha_storage_engine::{
    computer::state_computer::StateComputer, storage::Storage, utils::price_migration::Migration,
};
use alpha_utils::rabbit_mq::RabbitMq;
use axum::{
    routing::{get, post},
    Router,
};
use chrono::{Days, Duration, Utc};
use job_scheduler::{Job, JobScheduler, Schedule};
use rocksdb::{ColumnFamilyDescriptor, Options};
use std::{path::Path, sync::Arc};
use tower_http::cors::{Any, CorsLayer};
use tracing_subscriber::filter::LevelFilter;
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

#[derive(OpenApi)]
#[openapi(paths(
    alpha_perf_api::api::portfolio::get_aum::get_aum_range,
    alpha_perf_api::api::portfolio::get_metrics::get_portfolio_metrics,
    alpha_perf_api::api::portfolio::get_benchmark::get_portfolio_benchmark,
    alpha_perf_api::api::portfolio::get_portfolio::get_portfolio_details,
    alpha_perf_api::api::portfolio::get_holdings::get_holdings,
    alpha_perf_api::api::portfolio::get_aum_units::get_aum_units,
    alpha_perf_api::api::portfolio::get_analytics::get_analytics_for_portfolio,
    alpha_perf_api::api::portfolio::get_performance::get_performance_for_periods,
    alpha_perf_api::api::portfolio::get_portfolio_in_strategy_aum::get_portfolios_in_strategy_aum_range,
    alpha_perf_api::api::strategy::get_returns::get_strategy_returns,
    alpha_perf_api::api::strategy::get_aum::get_aum_range,
    alpha_perf_api::api::strategy::get_strategy_investments::get_strategy_investments_for_sebi_monthly,
    alpha_perf_api::api::client::get_clients_in_strategy_aum::get_aum_range,
    alpha_perf_api::api::model::get_aum::get_aum_range,
))]
struct ApiDoc;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    let redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL not found");
    let delta_lake_path = std::env::var("DELTA_LAKE_PATH").expect("DELTA_LAKE_PATH not found");

    // Initialize logging framework...
    if let Err(err) = init_logger("Alpha-Performance-API", LevelFilter::OFF, LogOutput::StdOut).await {
        log_error(
            LogBuilder::system("Failed to initialize logger in Alpha-Perf-API").add_metadata("error", &err.to_string()),
        );
    };
    log_info(LogBuilder::system("Logger service initialized in Alpha-Perf-API"));

    let path = std::env::var("PERF_ENGINE_DB_PATH").expect("Failed to load database host from env");

    //Migrate Price from Clickhouse to Rocksdb
    let migration = Migration::new(&path, 100000).await.unwrap();
    // migration.migrate_nse().await.unwrap();
    // migration.migrate_bse().await.unwrap();
    // migration.migrate_mf().await.unwrap();
    // migration.migrate_isin_history().await.unwrap();
    // migration.migrate_benchmark_prices().await;
    drop(migration);

    let s: Schedule = (Utc::now() + chrono::Duration::seconds(10))
        .format("%S %M %H %d %m *")
        .to_string()
        .parse()
        .unwrap();

    let cf_descriptors = vec![
        ColumnFamilyDescriptor::new("default", Options::default()),
        ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
        ColumnFamilyDescriptor::new("isin_history", Options::default()),
        ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ColumnFamilyDescriptor::new("benchmark_price", Options::default()),
    ];

    let mut rocks_options = Options::default();
    rocks_options.set_max_write_buffer_number(0);

    let storage_db = rocksdb::DB::open_cf_descriptors(&rocks_options, Path::new(&path), cf_descriptors)
        .expect("Failed to open RocksDB with column families");

    let config = Config::init();
    let db = connect_to_mssql(10).await;
    let master_db = connect_to_master_data(10).await;
    let clickhouse_client = create_clickhouse_client();

    let cfg = deadpool_redis::Config::from_url(redis_url);
    let redis_pool = cfg
        .create_pool(Some(deadpool_redis::Runtime::Tokio1))
        .expect("FAILED to create Pool");

    let queue_name = "performance engine";

    let rabbit_mq_queue = RabbitMq::connect_rabbit_mq().await;

    let app_state = AppState {
        db,
        master_db,
        env: config,
        rabbit_mq_queue: rabbit_mq_queue.into(),
        storage_engine: Arc::new(Storage {
            db: Arc::new(storage_db),
        }),
        clickhouse_client,
        redis_pool,
    };

    let cors = CorsLayer::new().allow_origin(Any).allow_headers(Any).allow_methods(Any);

    let app_state_for_price = app_state.clone();

    setup_price_migration_cron(app_state_for_price);

    let app_state = Arc::new(app_state);

    let app = Router::new()
        .route("/", get(|| async { "Hey Its Storage Engine, Say Hello!" }))
        .route("/state_schema", get(schema_page))
        .route("/query", get(query_page).post(handle_query))
        .route("/portfolio/trigger_genesis", post(trigger::portfolio::trigger_genesis))
        .route("/portfolio/trigger_as_at", post(trigger::portfolio::trigger_as_at))
        .route("/portfolio/trigger_all", post(trigger::portfolio::trigger_all))
        .nest("/client", client::router())
        .nest("/model", model::router())
        .nest("/portfolio", portfolio::router())
        .nest("/strategy", strategy::router())
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .layer(cors)
        .with_state(app_state.clone());

    log_info(LogBuilder::system("Starting HTTP Server"));
    let listener = tokio::net::TcpListener::bind("0.0.0.0:4010").await.unwrap();
    axum::serve(listener, app).await.unwrap();
}

// Function to set up the cron job
pub fn setup_price_migration_cron(app_state_price: AppState) {
    std::thread::spawn(move || {
        let mut scheduler = JobScheduler::new();
        let s: Schedule = "0 30 18 * * *".parse().unwrap();

        scheduler.add(Job::new(s, move || {
            let app_state_clone = app_state_price.clone();
            println!("Executing cron");
            std::thread::spawn(move || {
                let ist = chrono::FixedOffset::east_opt(5 * 3600 + 1800).unwrap(); // IST = UTC+5:30
                let date = (Utc::now() + Duration::hours(5) + Duration::minutes(30))
                    .date_naive()
                    .checked_sub_days(Days::new(1))
                    .unwrap();
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(Migration::migrate_price_as_at(
                    app_state_clone.storage_engine.db.clone(),
                    app_state_clone.clickhouse_client.clone(),
                    date,
                ))
                .unwrap();

                rt.block_on(async {
                    let computer: StateComputer = StateComputer {
                        clickhouse_pool: app_state_clone.clickhouse_client.clone(),
                        rabbit_mq_queue: app_state_clone.rabbit_mq_queue.clone(),
                        date,
                        db_pool: app_state_clone.db.clone(),
                        master_db_pool: app_state_clone.master_db.clone(),
                        redis_pool: app_state_clone.redis_pool.clone(),
                        storage: app_state_clone.storage_engine.clone(),
                    };

                    computer.compute_from_latest().await
                });
            });
        }));

        // Add a loop to keep the scheduler running
        loop {
            scheduler.tick();
            std::thread::sleep(std::time::Duration::from_secs(1));
        }
    });
}
