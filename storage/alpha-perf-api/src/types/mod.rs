use chrono::NaiveDate;

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use utoipa::{openapi::schema, schema, ToSchema};

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct XirrResults {
    #[schema(value_type = f64, example = "0")]
    pub one_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub three_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub six_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub one_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub two_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub three_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub seven_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub ten_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub ytd: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub fytd: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub since_inception: Decimal,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct TwrrResults {
    #[schema(value_type = f64, example = "0")]
    pub one_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub three_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub six_month: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub one_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub two_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub three_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub seven_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub ten_year: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub ytd: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub fytd: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub since_inception: Decimal,
}

#[derive(Serialize, Deserialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct Metrics {
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    pub xirr: XirrResults,
    pub twrr: TwrrResults,
}

#[derive(serde::Serialize, serde::Deserialize, Clone, ToSchema)]
pub struct AumRes {
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    #[schema(value_type = f64, example = "0")]
    pub cash: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub nav: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub change: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub total_aum: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub units: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub net_cash_flows: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub payables: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub receivables: Decimal,
}
