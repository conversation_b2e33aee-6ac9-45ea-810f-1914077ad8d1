use alpha_core_db::connection::pool::{
    Manager,
    deadpool::managed::Object,
    tiberius::{self, ExecuteResult, Query},
};

use alpha_utils::performance_engine::data_update::{InvestmentToUpdate, PortfolioToUpdate, QueueMessage, Updates};
use alpha_utils::rabbit_mq::RabbitMq;
use chrono::Utc;
use futures_lite::StreamExt;
use lapin::options::BasicAckOptions;
use rust_decimal::prelude::ToPrimitive;
use serde_json::Value;
use tokio::task;
use tracing::Level;
use tracing_subscriber::FmtSubscriber;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    let db_name = std::env::var("QUEUE_NAME").expect("QUEUE_NAME NOT FOUND");
    let queue_name = format!("{}/performance-updates", db_name);
    let subscriber = FmtSubscriber::builder().with_max_level(Level::INFO).finish();
    tracing::subscriber::set_global_default(subscriber).expect("setting default subscriber failed");

    let connection = RabbitMq::connect_rabbit_mq().await;
    let pool = alpha_core_db::connection::connect_to_mssql(10).await;

    let mut queue_consumer = connection.create_consumer(&queue_name).await;

    while let Some(delivery) = queue_consumer.next().await {
        let mut pool = pool.get().await.unwrap();
        task::spawn(async move {
            let delivery = delivery.expect("error in consumer");
            let value: Value = serde_json::from_slice(&delivery.data).unwrap();
            // Acknowledge the message
            delivery.ack(BasicAckOptions::default()).await.unwrap();
            let message: QueueMessage = serde_json::from_value(value).unwrap();
            match message.update_type {
                Updates::Investment(message) => {
                    let update = update_investments(&mut pool, &message.portfolio_id, message.investments).await;
                    match update {
                        Ok(update) => {
                            tracing::info!("Success {:?}", update);
                        }
                        Err(err) => {
                            tracing::error!("failed to update investments, err = {}", err);
                        }
                    }
                }
                Updates::Portfolio(message) => {
                    let update = update_portfolio(&mut pool, message).await;
                    match update {
                        Ok(update) => {
                            tracing::info!("Success {:?}", update);
                        }
                        Err(err) => {
                            tracing::error!("failed to update portfolio, err = {}", err);
                        }
                    }
                }
            };
        });
    }
}

pub async fn update_investments(
    db_conn: &mut Object<Manager>,
    portfolio_id: &str,
    investments: Vec<InvestmentToUpdate>,
) -> Result<ExecuteResult, tiberius::error::Error> {
    let mut query = String::from("");

    for inv in investments {
        //Create a query to update in the sql server investments table
        let q = format!(
            "
        UPDATE 
            Investments 
        SET
            CurrentPrice = {},
            AveragePrice = {},
            CurrentHolding = {},
            TotalCapital = {},
            InvestedCapital = {},
            MarketValue = {},
            RealisedGainLoss = {},
            UnRealisedGainLoss = {},
            Dividends = {},
            IrrSinceInception ={},
            IrrCurrent = {},
            MarketCap = '{}' ,
            AveragePrice = {},
            LastUpdatedDate = '{}'
        WHERE
            isin = '{}'
        AND
            portfolioId = '{}' ;
            ",
            inv.current_price.to_f64().unwrap_or_default(),
            inv.average_price.to_f64().unwrap_or_default(),
            inv.holdings.to_f64().unwrap_or_default(),
            inv.total_capital.to_f64().unwrap_or_default(),
            inv.invested_capital.to_f64().unwrap_or_default(),
            inv.market_value.to_f64().unwrap_or_default(),
            inv.realised_gain_loss.to_f64().unwrap_or_default(),
            inv.unrealised_gain_loss.to_f64().unwrap_or_default(),
            inv.dividends_paid.to_f64().unwrap_or_default(),
            inv.irr_inception.to_f64().unwrap_or_default(),
            inv.irr_current.to_f64().unwrap_or_default(),
            inv.market_cap,
            inv.average_price.to_f64().unwrap_or_default(),
            Utc::now().naive_local(),
            inv.isin,
            portfolio_id
        );

        query += &q;
    }

    db_conn.execute(query, &[]).await
}

pub async fn update_portfolio(
    db_conn: &mut Object<Manager>,
    portfolio: PortfolioToUpdate,
) -> Result<ExecuteResult, tiberius::error::Error> {
    let mut query = Query::new(
        "
            UPDATE
                Portfolios
            SET
                TotalCapital = @P1,
                InvestedCapital = @P2,
                MarketValue = @P3,
                CurrentCashBalance = @P4,
                RealisedGainLoss = @P5,
                UnRealisedGainLoss = @P6,
                TwrrSinceInception = @P7,
                AnnualReturnIrr = @P8,
                AnnualPerformanceTwrr = @P9,
                LastUpdatedDate = @P10

            WHERE
                Id = @P11
        ",
    );

    query.bind(portfolio.total_capital.to_f64());
    query.bind(portfolio.invested_capital.to_f64());
    query.bind(portfolio.market_value.to_f64());
    query.bind(portfolio.balance.to_f64());
    query.bind(portfolio.realised_gain_loss.to_f64());
    query.bind(portfolio.unrealised_gain_loss.to_f64());
    query.bind(portfolio.twrr_since_inception.to_f64());
    query.bind(portfolio.xirr_1year.to_f64());
    query.bind(portfolio.twrr_1year.to_f64());
    query.bind(Utc::now());
    query.bind(portfolio.portfolio_id.clone());

    query.execute(db_conn).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use dotenv;
    use rust_decimal::Decimal;

    #[tokio::test]
    async fn test_update_investments() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let mut db_conn = pool.get().await.unwrap();

        let test_isin = "TEST123";
        let portfolio = "022c20dadd004b03971b36597594bd5f";

        let investments = vec![InvestmentToUpdate {
            current_price: Decimal::from(100),
            holdings: Decimal::from(100),
            total_capital: Decimal::from(100),
            invested_capital: Decimal::from(100),
            market_value: Decimal::from(100),
            realised_gain_loss: Decimal::from(100),
            unrealised_gain_loss: Decimal::from(100),
            dividends_paid: Decimal::from(100),
            irr_inception: Decimal::from(100),
            irr_current: Decimal::from(100),
            market_cap: String::from("hi"),
            average_price: Decimal::from(100),
            isin: "ISIN".to_string(),
        }];

        let result = update_investments(&mut db_conn, portfolio, investments).await;

        assert!(result.is_ok(), "Update query failed: {:?}", result.err());
    }
}
