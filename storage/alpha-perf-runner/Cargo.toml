[package]
name = "alpha-perf-runner"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
alpha-utils = { path = "../../alpha-utils" }
alpha-core-db = { path = "../../alpha-core-db" }
alpha-storage-engine = { path = "../alpha-storage-engine" }
tokio = { version = "1.40.0", features = ["full"] }
dotenv = "0.15.0"
native-tls = "0.2.12"
futures = "0.3.30"
futures-util = "0.3.30"
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
rocksdb = { version = "0.22.0", features = ["multi-threaded-cf"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
axum = { version = "0.7.7", features = ["macros"] }
axum-extra = { version = "0.9.4", features = ["cookie"] }
tower-http = { version = "0.6.1", features = ["cors"] }
clickhouse = { workspace = true }
bb8-redis = { workspace = true }
deadpool-redis = { workspace = true }
deadpool = { workspace = true }
actlogica_logs = { workspace = true }
clap = { version = "4.5.38", features = ["derive"] }
