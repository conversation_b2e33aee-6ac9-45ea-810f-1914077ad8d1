use actlogica_logs::{
    builder::Log<PERSON>uilder,
    log_error, log_info,
    setting::{init_logger, LogOutput},
};
use alpha_core_db::clickhouse::create_clickhouse_client;
use alpha_storage_engine::{
    computer::state_computer::StateComputer, lake::DeltaLakeImporter, storage::Storage,
    utils::price_migration::Migration,
};
use alpha_utils::rabbit_mq::RabbitMq;
use chrono::{NaiveDate, Utc};
use clap::Parser;
use rocksdb::{ColumnFamilyDescriptor, Options, TransactionDBOptions};
use std::{path::Path, sync::Arc};
use tracing::Level;
use tracing_subscriber::{filter::LevelFilter, FmtSubscriber};

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long, default_value = "latest")]
    compute: String,

    #[arg(long, action = clap::ArgAction::SetTrue)]
    fetchprice: bool,

    #[arg(long)]
    portfolio: Option<String>,

    #[arg(long, action = clap::ArgAction::SetTrue)]
    lakeonly: bool,
}

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    let args = Args::parse();

    let queue_name = "performance-engine";
    let redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL not found");
    let delta_lake_path = std::env::var("DELTA_LAKE_PATH").expect("DELTA_LAKE_PATH not found");
    let subscriber = FmtSubscriber::builder().with_max_level(Level::INFO).finish();
    tracing::subscriber::set_global_default(subscriber).expect("setting default subscriber failed");

    // Initialize logging framework...
    // if let Err(err) =
    //     actlogica_logs::setting::init_logger("Alpha-Performance-Runner", LevelFilter::INFO, LogOutput::StdOut).await
    // {
    //     log_error(
    //         LogBuilder::system("Failed to initialize logger in Alpha-Perf-Runner")
    //             .add_metadata("error", &err.to_string()),
    //     );
    // };

    // log_info(LogBuilder::system("Logger service initialized in Alpha-Perf-Runner"));

    let db_pool = alpha_core_db::connection::connect_to_mssql(30).await;
    let master_db_pool = alpha_core_db::connection::connect_to_master_data(2).await;
    let rabbit_mq_queue = Arc::new(RabbitMq::connect_rabbit_mq().await);
    let mut cfg = deadpool_redis::Config::from_url(redis_url);
    let redis_pool = cfg
        .create_pool(Some(deadpool_redis::Runtime::Tokio1))
        .expect("FAILED to create Pool");

    let rocks_options = Options::default();
    let _tx_rocks_options = TransactionDBOptions::default();
    let path = std::env::var("PERF_ENGINE_DB_PATH").expect("Failed to load database host from env");

    let cf_descriptors = vec![
        ColumnFamilyDescriptor::new("default", Options::default()),
        ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
        ColumnFamilyDescriptor::new("isin_history", Options::default()),
        ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ColumnFamilyDescriptor::new("benchmark_price", Options::default()),
    ];

    if args.fetchprice {
        let migration = Migration::new(&path, 100000).await.unwrap();
        migration.migrate_nse().await.unwrap();
        migration.migrate_bse().await.unwrap();
        migration.migrate_mf().await.unwrap();
        migration.migrate_isin_history().await.unwrap();
        migration.migrate_benchmark_prices().await;
        drop(migration);
    }

    let db = rocksdb::DB::open_cf_descriptors(&rocks_options, Path::new(&path), cf_descriptors)
        .expect("Failed to open RocksDB with column families");

    let storage = Arc::new(Storage { db: Arc::new(db) });
    let clickhouse_pool = create_clickhouse_client();
    let date = NaiveDate::from_ymd_opt(2025, 02, 28).unwrap();

    let delta_lake = DeltaLakeImporter {
        rocks_store: storage.db.clone(),
        table_uri: delta_lake_path,
    };

    if args.lakeonly {
        delta_lake.execute().await;
        return;
    }

    let computer = StateComputer {
        clickhouse_pool,
        date,
        rabbit_mq_queue,
        db_pool,
        master_db_pool,
        redis_pool,
        storage: storage.clone(),
    };

    if let Some(portfolio_id) = args.portfolio {
        match args.compute.as_str() {
            "latest" => computer.compute_for_a_portfolio(&portfolio_id, false).await,
            "scratch" => computer.compute_for_a_portfolio(&portfolio_id, true).await,
            _ => panic!("Invalid value for --compute. Use 'latest' or 'scratch'."),
        }
    } else {
        match args.compute.as_str() {
            "latest" => computer.compute_from_latest().await,
            "scratch" => computer.compute_from_scratch().await,
            _ => panic!("Invalid value for --compute. Use 'latest' or 'scratch'."),
        }
    }

    delta_lake.execute().await;
}
