[package]
name = "alpha-storage-engine"
version = "0.1.0"
edition = "2021"


[dependencies]
alpha-utils = { path = "../../alpha-utils" }
alpha-core-db = { path = "../../alpha-core-db" }
alpha-macros = { path = "../alpha-macros" }
tokio = { version = "1.40.0", features = ["full"] }
dotenv = "0.15.0"
native-tls = "0.2.12"
futures = "0.3.30"
futures-util = "0.3.30"
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true, features = ["rkyv"] }
rocksdb = { version = "0.22.0", features = ["multi-threaded-cf"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
bb8-redis = { workspace = true }
clickhouse = { workspace = true }
time = { workspace = true }
tracing-test = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
lazy_static = "1.5.0"
bincode = { workspace = true }
rkyv = { workspace = true }
anyhow = { workspace = true }
rayon = { version = "1.10.0" }
deadpool = { workspace = true }
deadpool-redis = { workspace = true }
xirr = { version = "0.2.3" }
thiserror = { version = "2.0.12" }
nom = "7"
lapin = { workspace = true }
arrow = { version = "55.1.0" }
deltalake = { version = "0.26.2", features = ["json", "datafusion"] }
duckdb = { version = "1.2.2", features = ["bundled"] }
