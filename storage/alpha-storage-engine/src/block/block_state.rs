use crate::{
    states::{
        analytics::TransactionForAnalytics, aum::Aum, capital_register::CapitalRegister, investment::Investment,
        ledger::Ledger, portfolio::Portfolio, transaction::Transaction,
    },
    storage::StorageWriter,
};
use chrono::NaiveDate;

use super::portfolio_block::PortfolioBlock;

/// Represent the state inside a block
/// Each day is a block for a portfolio
pub struct BlockState {
    /// Investment of the portfolio
    pub investments: Vec<Investment>,

    /// Capital Register entries on this day (If any)
    pub capital_registers: Vec<CapitalRegister>,

    /// Transaction that occured (If any)
    pub investment_transactions: Vec<Transaction>,

    /// Ledger entries
    pub ledgers: Vec<Ledger>,

    /// State of the Portfolio
    pub portfolio_state: Portfolio,

    /// Aum of the Portfolio
    pub aum: Aum,

    /// Transactions that is used for computing analytics
    pub transaction_for_analytics: Vec<TransactionForAnalytics>,

    /// Date of the block
    pub date: NaiveDate,
}

impl BlockState {
    pub async fn commit(&self, mut storage: StorageWriter) {
        //Insert Portfolio Investmnents
        let inv_key = storage
            .insert_portfolio_investment(&self.investments, self.portfolio_state.portfolio_id.clone(), self.date)
            .await;

        //Insert CR
        let cr_key = storage
            .insert_capital_registers(
                &self.capital_registers,
                self.portfolio_state.portfolio_id.clone(),
                self.date,
            )
            .await;

        //Insert the transaction on that day
        let txn_key = storage
            .insert_transactions(
                &self.investment_transactions,
                self.portfolio_state.portfolio_id.clone(),
                self.date,
            )
            .await;

        //Insert Ledgers
        let ledger_key = storage
            .insert_ledgers(&self.ledgers, self.portfolio_state.portfolio_id.clone(), self.date)
            .await;

        let ptf_key = storage
            .insert_portfolio(
                &self.portfolio_state,
                self.portfolio_state.portfolio_id.clone(),
                self.date,
            )
            .await;

        let _aum_key = storage
            .insert_portfolio_aum(&self.aum, self.portfolio_state.portfolio_id.clone(), self.date)
            .await;

        let portfolio_block = PortfolioBlock {
            capital_registers: cr_key,
            cash_ledgers: ledger_key,
            transactions: txn_key,
            date: self.date,
            investments: inv_key,
            portfolo: ptf_key,
        };

        let _portfolio_block_key = storage
            .insert_portfolio_block(&portfolio_block, self.portfolio_state.portfolio_id.clone(), self.date)
            .await;

        let _tx_for_analytics_key = storage
            .insert_transaction_for_analytics(
                &self.portfolio_state.portfolio_id,
                self.date,
                &self.transaction_for_analytics,
            )
            .await;

        storage
            .insert_latest_block(self.date, self.portfolio_state.portfolio_id.clone())
            .await;

        let _ = storage.commit_batch().await;
    }
}
