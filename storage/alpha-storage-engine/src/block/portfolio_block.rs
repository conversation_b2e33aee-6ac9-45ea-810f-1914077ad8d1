/// Portfolio Need AUM and Capital Register to calculate the XIRR and TWRR
/// Investments need the AUM and transaction  to calculate the XIRR and TWRR
/// Categories for which Analytics should be calculted
/// => Asset Type, Asset Class, Market Cap, Portfolio, Client, Strategy, AMC
use std::{collections::HashMap, sync::Arc, vec};

use alpha_utils::types::{SecurityDetails, SecurityTypeForPrice};
use chrono::{Days, NaiveDate};
use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::{
    prelude::{FromPrimitive, ToPrimitive},
    Decimal,
};
use rust_decimal_macros::dec;
use tracing::error;

use crate::{
    block::block_state::BlockState,
    chrono_to_time_date,
    computer::investment_analytics::InvestmentAnalyticsComputer,
    states::{
        analytics::TransactionForAnalytics,
        aum::Aum,
        capital_register::CapitalRegister,
        enums::{SecurityType, TransactionSubType, TransactionType},
        investment::Investment,
        ledger::{Ledger, LedgerTransaction},
        legacy_ledger::LegacyLedger,
        metrics::{benchmark::BenchmarkXirr, portfolio::{TwrrResults, XirrResults}},
        portfolio::Portfolio,
        quantity_queue::QuantityQueue,
        receivable_payable::ReceivablePayable,
        transaction::Transaction,
    },
    storage::{Storage, StorageWriter},
    types::{EligbleStateTransitionTransactions, ExternalDBState},
    utils::{
        benchmark::BenchmarkIndices, financial::{twrr::TwrrCalculator, xirr::PortfolioXirrCalculator}, transaction_computer::TransactionComputer
    },
};

/// This is a Data structure just for the convenience of block architecture
/// We are ok to store this in a separate key it only takes less space
/// If you feel this is not required you can remove it and manually compute the keys
#[derive(Serialize, Deserialize, Archive, Debug)]
pub struct PortfolioBlock {
    /// Date of the Portfolio Block
    pub date: NaiveDate,

    /// Details of the Portfolio
    pub portfolo: String,

    /// Root of the Investments stored in db
    pub investments: String,

    /// Root of the transactions that this portfolio held on the particular day
    pub transactions: String,

    /// Root of the capital register Entries
    pub capital_registers: String,

    /// Root of the Cash Ledgers which was generated on applying the above things
    /// This is kind of a receipt after applying the transaction
    pub cash_ledgers: String,
}

impl PortfolioBlock {
    /// This Applies All Sort of Transaction and create a new Portfolio Block for the next day
    pub async fn apply_transactions(
        &mut self,
        db_state: ExternalDBState,
        storage_db: Arc<Storage>,
        ledger_txn: EligbleStateTransitionTransactions,
    ) {
        let mut storage = StorageWriter::new(storage_db.db.clone());
        let next_date = self.date.checked_add_days(Days::new(1)).unwrap();

        let previous_investment_state: Vec<Investment> = storage.get_investment_by_key(&self.investments).unwrap();
        let previous_portfolio_state: Portfolio = storage.get_portfolio_by_key(&self.portfolo).unwrap();
        let portfolio_id = previous_portfolio_state.portfolio_id.clone();

        let transactions_by_isin =
            ledger_txn
                .investment_transactions
                .clone()
                .into_iter()
                .fold(HashMap::new(), |mut acc, txn| {
                    acc.entry(txn.isin.clone()).or_insert_with(Vec::new).push(txn);
                    acc
                });

        // Process investments
        let (mut new_investment_state, transaction_analytics) = self
            .process_investments(
                &mut storage,
                previous_investment_state,
                &transactions_by_isin,
                &portfolio_id,
                next_date,
                db_state.clone(),
                storage_db.clone(),
            )
            .await;

        //Create New State of the Portfolio
        let (new_portfolio_state, ledgers) = self
            .compute_portfolio_state(
                &mut storage,
                &ledger_txn.capital_register_transactions,
                ledger_txn.legacy_ledger_transactions,
                ledger_txn.receivables_payable_transactions,
                &new_investment_state,
            )
            .await;

        // Update AUM and analytics
        let previous_aum = storage.get_portfolio_aum(&portfolio_id, self.date).unwrap();
        let cash_flow = self.compute_cash_flows(&ledger_txn.capital_register_transactions);
        let current_aum = previous_aum.compute_aum_next_day(
            new_portfolio_state.balance,
            new_portfolio_state.market_value,
            cash_flow,
            next_date,
            new_portfolio_state.payables,
            new_portfolio_state.receivables,
        );

        // Compute investment weights
        let portfolio_cash = new_portfolio_state.balance;
        self.compute_investments_weights(&mut new_investment_state, portfolio_cash);

        // Create and commit block state
        let state_result = BlockState {
            aum: current_aum.clone(),
            capital_registers: ledger_txn.capital_register_transactions,
            date: next_date,
            investment_transactions: ledger_txn.investment_transactions,
            investments: new_investment_state.clone(),
            ledgers,
            portfolio_state: new_portfolio_state,
            transaction_for_analytics: transaction_analytics.clone(),
        };

        state_result.commit(storage).await;

        // Compute the Analytics
        let mut storage = StorageWriter::new(storage_db.db.clone());

        let transactions_for_analytics = storage.get_transactions_for_analytics_range(&portfolio_id, next_date);

        let inv_analytics = InvestmentAnalyticsComputer::new(self.date);
        let _ = inv_analytics
            .compute_analytics(
                &mut storage,
                &portfolio_id,
                &transactions_for_analytics,
                new_investment_state,
                portfolio_cash,
            )
            .await;

        self.compute_portfolio_analytics(&mut storage, &portfolio_id, current_aum)
            .await;

        storage.commit_batch().await.unwrap();
    }

    /// Processes all investments - both existing and new ones from transactions
    async fn process_investments(
        &self,
        storage: &mut StorageWriter,
        previous_investments: Vec<Investment>,
        transactions_by_isin: &HashMap<String, Vec<Transaction>>,
        portfolio_id: &str,
        next_date: NaiveDate,
        db_state: ExternalDBState,
        storage_db: Arc<Storage>,
    ) -> (Vec<Investment>, Vec<TransactionForAnalytics>) {
        let mut isin_exist_map = HashMap::new();
        let mut new_investment_state = Vec::new();
        let mut transaction_analytics = Vec::new();

        // Process existing investments
        for inv in previous_investments {
            isin_exist_map.insert(inv.isin.clone(), true);

            // Get price for the investment
            let price = self.get_security_price(storage, &inv.isin, &inv.symbol, next_date);

            // Get quantity queue for the investment
            let mut quantity_queue = storage.get_quantity_queue(portfolio_id, &inv.isin, inv.date).unwrap();

            // Process transactions for this investment, if any
            if let Some(txns) = transactions_by_isin.get(&inv.isin) {
                let eligible_txns: Vec<&Transaction> = txns
                    .iter()
                    .filter(|f| {
                        f.transaction_type == TransactionType::Buy || f.transaction_type == TransactionType::Sell
                    })
                    .collect();

                let next_state =
                    inv.apply_transactions(eligible_txns, Decimal::from_f64(price).unwrap(), &mut quantity_queue);

                let mut txns_for_analytics: Vec<TransactionForAnalytics> = txns
                    .iter()
                    .map(|f| TransactionForAnalytics {
                        amount: if f.transaction_type == TransactionType::Buy {
                            f.amount
                        } else {
                            -f.amount
                        },
                        asset_class: inv.asset_class.clone(),
                        asset_type: inv.asset_type.clone(),
                        isin: inv.isin.clone(),
                        market_cap: inv.market_cap.clone(),
                        industry: inv.industry.clone(),
                        transaction_date: f.transaction_date.date(),
                    })
                    .collect();

                storage
                    .insert_quantity_queue(portfolio_id.to_string(), inv.isin.clone(), next_date, quantity_queue)
                    .await;

                transaction_analytics.append(&mut txns_for_analytics);
                new_investment_state.push(next_state);
            } else {
                // No new transactions for this investment
                let next_state = inv.apply_transactions(vec![], Decimal::from_f64(price).unwrap(), &mut quantity_queue);

                storage
                    .insert_quantity_queue(portfolio_id.to_string(), inv.isin.clone(), next_date, quantity_queue)
                    .await;

                new_investment_state.push(next_state);
            }
        }

        // Process new investments from transactions
        for (isin, txns) in transactions_by_isin {
            if !isin_exist_map.contains_key(isin) {
                if let Ok((new_inv, mut analytics_txn)) = self
                    .create_new_investment(db_state.clone(), &storage_db, isin.clone(), txns)
                    .await
                {
                    new_investment_state.push(new_inv);
                    transaction_analytics.append(&mut analytics_txn);
                }
            }
        }

        (new_investment_state, transaction_analytics)
    }

    /// Gets the price for a security, trying multiple sources if needed
    fn get_security_price(&self, storage: &StorageWriter, isin: &str, symbol: &str, date: NaiveDate) -> f64 {
        // Try NSE price with ISIN
        let mut price = storage.get_nse_price(isin, date).unwrap_or(0f64);

        if price == 0f64 {
            // Try NSE price with symbol
            price = storage.get_nse_price(symbol, date).unwrap_or(0f64);

            if price == 0f64 {
                // Check if the ISIN has changed
                if let Some(old_isin) = storage.get_old_isin(isin) {
                    price = storage.get_nse_price(&old_isin, date).unwrap_or(0f64);
                } else {
                    // Try BSE price
                    price = storage.get_bse_price(isin, date).unwrap_or(0f64);

                    if price == 0f64 {
                        // Try mutual fund price
                        price = storage.get_mf_price(isin, date).unwrap_or(0f64);
                    }
                }
            }
        }

        price
    }

    /// Applies Capital Register Transactions On Top of the Portfolio State
    /// It updated the total capital, total withdrawal, and invested_capital
    fn apply_capital_register_transactions(
        &self,
        storage: &StorageWriter,
        capital_register_txns: &Vec<CapitalRegister>,
    ) -> Portfolio {
        let (total_capital, total_withdrawal, invested_capital) = self.compute_capital_values(capital_register_txns);

        let previous_portfolio_state: Portfolio = storage.get_portfolio_by_key(&self.portfolo).unwrap();

        let new_portfolio_state = Portfolio {
            date: self.date.checked_add_days(Days::new(1)).unwrap(),
            total_capital: previous_portfolio_state.total_capital + total_capital,
            withdrawals: previous_portfolio_state.withdrawals + total_withdrawal,
            invested_capital: previous_portfolio_state.invested_capital + invested_capital,
            ..previous_portfolio_state
        };

        new_portfolio_state
    }

    fn compute_investments_weights(&self, investments: &mut Vec<Investment>, cash: Decimal) {
        let total_value: Decimal = investments.iter().map(|inv| inv.market_value).sum::<Decimal>() + cash;

        for inv in investments {
            //FIXME: total value won't be zero
            //WIll be zero when market value is not there meaning price is missing
            inv.weight = if total_value == dec!(0) {
                dec!(0)
            } else {
                (inv.market_value / total_value) * dec!(100)
            };
        }
    }

    fn compute_capital_values(&self, capital_register_txns: &Vec<CapitalRegister>) -> (Decimal, Decimal, Decimal) {
        let total_capital: Decimal = capital_register_txns
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Inflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalIn
                        || txn.transaction_sub_type == TransactionSubType::SecurityIn)
            })
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: Decimal = capital_register_txns
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Outflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalOut
                        || txn.transaction_sub_type == TransactionSubType::SecurityOut)
            })
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: Decimal = total_capital - total_withdrawal;

        (total_capital, total_withdrawal, invested_capital)
    }

    /// Creates a new investment structure from the given transaction
    async fn create_new_investment(
        &self,
        db_state: ExternalDBState,
        storage: &Storage,
        isin: String,
        transactions: &Vec<Transaction>,
    ) -> Result<(Investment, Vec<TransactionForAnalytics>), ()> {
        let mut storage = StorageWriter::new(storage.db.clone());
        let single_transaction = &transactions[0];
        let next_date = self.date.checked_add_days(Days::new(1)).unwrap();
        let mut redis_conn = db_state.redis.get().await.unwrap();

        let mut new_invested_capital = dec!(0);
        let mut quantity_queue = QuantityQueue::new();

        let eligible_txn: Vec<&Transaction> = transactions
            .iter()
            .filter(|f| f.transaction_type == TransactionType::Buy || f.transaction_type == TransactionType::Sell)
            .collect();

        for tx in &eligible_txn {
            //If the transaction is Sell Reduce the quantity from quantity queue
            if tx.transaction_type == TransactionType::Sell {
                let capital = quantity_queue.reduce_quantity(tx.quantity).unwrap_or(dec!(0));
                new_invested_capital -= capital;
            } else if tx.transaction_type == TransactionType::Buy {
                quantity_queue.add_quantity(tx.quantity, tx.price, tx.transaction_date.into());
                new_invested_capital += tx.amount;
            }
        }

        let previous_portfolio_state: Portfolio = storage.get_portfolio_by_key(&self.portfolo).unwrap();

        storage
            .insert_quantity_queue(
                previous_portfolio_state.portfolio_id.clone(),
                isin.clone(),
                next_date,
                quantity_queue,
            )
            .await;

        let click_date = chrono_to_time_date(next_date);

        let mut security_details = match SecurityDetails::build_as_at(
            db_state.clickhouse_client.clone(),
            &mut redis_conn,
            db_state.master_pool.clone(),
            isin.clone(),
            click_date,
            SecurityTypeForPrice::Equity,
        )
        .await
        {
            Ok(details) => details,
            Err(_e) => {
                let fallback_isin = storage.get_new_isin(&isin).unwrap();
                let sec = SecurityDetails::build_as_at(
                    db_state.clickhouse_client.clone(),
                    &mut redis_conn,
                    db_state.master_pool.clone(),
                    fallback_isin.to_string(),
                    click_date,
                    SecurityTypeForPrice::Equity,
                )
                .await;

                match sec {
                    Ok(details) => details,
                    Err(_e) => SecurityDetails::build_from_security_master(db_state.db_pool, &isin)
                        .await
                        .expect("Expect security to be Atleast in Security Master"),
                }
            }
        };

        let mut price = storage.get_nse_price(&isin, next_date).unwrap_or(0f64);

        if price == 0f64 {
            price = storage
                .get_nse_price(&single_transaction.symbol, next_date)
                .unwrap_or(0f64);
            if price == 0f64 {
                //Check if the isin change
                let old_isin = storage.get_old_isin(&isin);
                if let Some(isin) = old_isin {
                    price = storage.get_nse_price(&isin, next_date).unwrap_or(0f64);
                } else {
                    price = storage.get_bse_price(&isin, next_date).unwrap_or(0f64);

                    if price == 0f64 {
                        price = storage.get_mf_price(&isin, next_date).unwrap_or(0f64);
                    }
                }
            }
        }

        if price == 0f64 {
            error!(
                "Price Not Found for Isin : {:?} taking Price as {}",
                isin, single_transaction.price
            );
            price = single_transaction.price.to_f64().unwrap();
        }

        security_details.set_price(price);

        let price = Decimal::from_f64(price).unwrap();

        let txn_computer = TransactionComputer;
        let (total_capital, total_withdrawals, dividends_paid) = txn_computer.calculate_capital_value(&eligible_txn);

        let new_holdings = txn_computer.calculate_change_in_holdings_from_transactions(&eligible_txn);

        let unit_holding_split =
            txn_computer.long_short_term_holdings(&security_details.get_company_name(), &eligible_txn);

        let new_market_value = (new_holdings * price).round_dp(2);

        let new_average_price = if new_holdings > dec!(0) {
            (new_invested_capital / new_holdings).round_dp(2)
        } else {
            dec!(0)
        };

        let new_absolute_return_value = (new_market_value + dividends_paid) - new_invested_capital;
        let absolute_return_percent = if new_invested_capital == dec!(0) {
            dec!(0)
        } else {
            (new_absolute_return_value / new_invested_capital).round_dp(2)
        };

        let investment_genesis = Investment {
            portfolio_id: single_transaction.portfolio_id.clone(),
            absolute_return_percent: absolute_return_percent,
            absolute_return_value: new_absolute_return_value,
            asset_class: security_details.get_asset_class(),
            asset_type: security_details.get_company_name(),
            market_value: new_market_value,
            close_price: Decimal::from_f64(security_details.get_latest_price()).unwrap(),
            close_price_change: Decimal::from_f64(security_details.get_latest_price()).unwrap(),
            close_price_date: next_date.into(),
            current_price: price,
            date: next_date,
            dividends_paid,
            exchange: single_transaction.exchange.clone(),
            holdings: new_holdings,
            industry: security_details.get_industry().unwrap_or("Not Available".to_string()),
            invested_capital: new_invested_capital,
            isin: security_details.get_isin(),
            withdrawals: total_withdrawals,
            total_capital,
            symbol: security_details.get_identifier(),
            name: security_details.get_company_name(),
            market_value_change: dec!(0),
            market_value_change_percent: dec!(0),
            long_term_units: unit_holding_split.0,
            short_term_units: unit_holding_split.1,
            client_id: single_transaction.client_id.clone(),
            market_cap: security_details.get_marketcap(),
            realised_gain_loss: dec!(0),
            unrealised_gain_loss: new_invested_capital,
            series: security_details.get_series(),
            xirr: dec!(0),
            average_price: new_average_price,
            security_type: SecurityType::Stocks,
            irr_current: dec!(0),
            irr_inception: dec!(0),
            weight: dec!(0),
        };

        let transactions_for_analytics: Vec<TransactionForAnalytics> = transactions
            .iter()
            .map(|f| {
                return TransactionForAnalytics {
                    amount: if f.transaction_type == TransactionType::Buy {
                        f.amount
                    } else {
                        -f.amount
                    },
                    asset_class: investment_genesis.asset_class.clone(),
                    asset_type: investment_genesis.asset_type.clone(),
                    isin: investment_genesis.isin.clone(),
                    market_cap: investment_genesis.market_cap.clone(),
                    industry: investment_genesis.industry.clone(),
                    transaction_date: f.transaction_date.date(),
                };
            })
            .collect();

        Ok((investment_genesis, transactions_for_analytics))
    }

    /// Compute the total inflow and outflow from CR
    fn compute_cash_flows(&self, capital_register_txns: &Vec<CapitalRegister>) -> Decimal {
        capital_register_txns
            .iter()
            .map(|c| {
                if c.transaction_type == TransactionType::Inflow {
                    return c.amount;
                } else if c.transaction_type == TransactionType::Outflow {
                    return -c.amount;
                }
                dec!(0)
            })
            .sum()
    }

    /// Compute the new Portfolio State
    /// From  capital_register_txns, legacy_ledger_txns, receivable_payables_txns
    async fn compute_portfolio_state(
        &self,
        storage: &mut StorageWriter,
        capital_register_txns: &Vec<CapitalRegister>,
        legacy_ledger_txns: Vec<LegacyLedger>,
        receivable_payables_txns: Vec<ReceivablePayable>,
        new_investment_state: &Vec<Investment>,
    ) -> (Portfolio, Vec<Ledger>) {
        let next_date = self.date.checked_add_days(Days::new(1)).unwrap();

        let mut new_portfolio_state = self.apply_capital_register_transactions(&storage, &capital_register_txns);

        new_portfolio_state.apply_investments(new_investment_state);

        let merged_transactions: Vec<&dyn LedgerTransaction> = legacy_ledger_txns
            .iter()
            .map(|it| it as &dyn LedgerTransaction)
            .collect();

        //Get the Payables that needs to added to cash on date T
        let payables_for_today = storage.get_payables(
            &new_portfolio_state.portfolio_id,
            self.date.checked_add_days(Days::new(1)).unwrap(),
        );

        let (ledgers, payables) = new_portfolio_state.process_ledger(merged_transactions);

        new_portfolio_state.market_value += new_portfolio_state.assets_and_liabilities;

        //Apply Payables On Cash
        new_portfolio_state.balance += payables_for_today;

        new_portfolio_state.payables -= payables_for_today;

        new_portfolio_state.process_receivables_payables(&receivable_payables_txns);

        new_portfolio_state.update_unrealised_gain_loss();

        //Make the current payable entry zero (FIXME:)
        if payables_for_today != dec!(0) {
            storage
                .insert_payables(new_portfolio_state.portfolio_id.clone(), next_date, dec!(0))
                .await;
        }

        //Insert Payables
        for (date, amount) in &payables {
            //Check if there is already any payable on that date
            let existing_payables = storage.get_payables(&new_portfolio_state.portfolio_id, date.clone());

            //Insert the new payables
            storage
                .insert_payables(
                    new_portfolio_state.portfolio_id.clone(),
                    date.clone(),
                    amount + existing_payables,
                )
                .await;
        }

        (new_portfolio_state, ledgers)
    }

    /// Compute the Analytics for the Given portfolio at Date T
    async fn compute_portfolio_analytics(&self, storage: &mut StorageWriter, portfolio_id: &str, current_aum: Aum) {
        let next_date = self.date.checked_add_days(Days::new(1)).unwrap();

        let aums = storage.get_aum_for_analytics_range(next_date, portfolio_id);

        let aums: Vec<Aum> = aums.iter().cloned().filter(|a| a.net_cash_flows != dec!(0)).collect();

        let xirr_calculator = PortfolioXirrCalculator {
            aums,
            current_aum: current_aum.clone(),
            date: next_date,
            portfolio_id: portfolio_id.to_string(),
        };

        let calculated_xirr = xirr_calculator.calculate_all_periods(storage).unwrap();

        let xirr_state = XirrResults {
            fytd: calculated_xirr.fytd,
            one_month: calculated_xirr.one_month,
            one_year: calculated_xirr.one_year,
            seven_year: calculated_xirr.seven_year,
            since_inception: calculated_xirr.since_inception,
            six_month: calculated_xirr.six_month,
            ten_year: calculated_xirr.ten_year,
            three_month: calculated_xirr.three_month,
            three_year: calculated_xirr.three_year,
            two_year: calculated_xirr.two_year,
            ytd: calculated_xirr.ytd,
        };

        storage.insert_xirr_metrics(portfolio_id, next_date, xirr_state).await;

        let twrr_calculator = TwrrCalculator {
            date: next_date,
            portfolio_id: portfolio_id.to_string(),
        };

        let calculated_twrr = twrr_calculator.calculate_twrr_all_periods(storage).unwrap();

        let twrr_state = TwrrResults {
            fytd: calculated_twrr.fytd.map(|f| f.into()),
            one_month: calculated_twrr.one_month,
            one_year: calculated_twrr.one_year,
            seven_year: calculated_twrr.seven_year,
            since_inception: calculated_twrr.since_inception,
            six_month: calculated_twrr.six_month,
            ten_year: calculated_twrr.ten_year,
            three_month: calculated_twrr.three_month,
            three_year: calculated_twrr.three_year,
            two_year: calculated_twrr.two_year,
            ytd: calculated_twrr.ytd,
        };

        storage
            .insert_portfolio_twrr_metrics(portfolio_id, next_date, twrr_state)
            .await;

        //Benchmark
        // for bench in BenchmarkIndices::all() {
        //     let calculated_data = bench
        //         .calculate_xirr(portfolio_id.to_owned(), next_date, current_aum.clone(), storage)
        //         .unwrap();

        //     let data = BenchmarkXirr {
        //         fytd: calculated_data.fytd,
        //         one_month: calculated_data.one_month,
        //         one_year: calculated_data.one_year,
        //         seven_year: calculated_data.seven_year,
        //         since_inception: calculated_data.since_inception,
        //         six_month: calculated_data.six_month,
        //         ten_year: calculated_data.ten_year,
        //         three_month: calculated_data.three_month,
        //         three_year: calculated_data.three_year,
        //         two_year: calculated_data.two_year,
        //         ytd: calculated_data.ytd,
        //     };

        //     storage
        //         .insert_benchmark_xirr_metrics(portfolio_id, next_date, bench, data)
        //         .await;
        // }
    }
}
