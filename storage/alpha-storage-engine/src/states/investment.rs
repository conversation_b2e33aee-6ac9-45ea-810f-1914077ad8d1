use crate::lake::ReflectFieldAccess;
use crate::states::quantity_queue::QuantityQueue;
use crate::utils::transaction_computer::TransactionComputer;
use alpha_core_db::connection::pool::tiberius::time::chrono::{NaiveDate, NaiveDateTime};
use alpha_macros::ReflectFields;
use chrono::Days;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

use super::{
    enums::{SecurityType, TransactionType},
    transaction::Transaction,
};

#[derive(rkyv::Serialize, rkyv::Deserialize, rkyv::Archive, Clone, Debug, ReflectFields)]
pub struct Investment {
    pub date: NaiveDate,
    pub portfolio_id: String,
    pub client_id: String,
    pub isin: String,
    pub symbol: String,
    pub series: Option<String>,
    pub exchange: String,
    pub security_type: SecurityType,
    pub name: String,
    pub asset_class: String,
    pub asset_type: String,
    pub industry: String,
    pub market_cap: String,
    pub market_value: Decimal,

    /// The Number of Quantity in the portfolio of this Security
    pub holdings: Decimal,

    /// The Closing Price of Stock on the day
    pub close_price: Decimal,
    pub close_price_date: NaiveDateTime,
    pub close_price_change: Decimal,
    pub current_price: Decimal,
    pub average_price: Decimal,
    pub absolute_return_value: Decimal,
    pub absolute_return_percent: Decimal,
    pub long_term_units: Decimal,
    pub short_term_units: Decimal,
    pub total_capital: Decimal,
    pub invested_capital: Decimal,
    pub withdrawals: Decimal,
    pub dividends_paid: Decimal,
    pub market_value_change: Decimal,
    pub market_value_change_percent: Decimal,
    pub realised_gain_loss: Decimal,
    pub unrealised_gain_loss: Decimal,
    pub xirr: Decimal,
    pub irr_inception: Decimal,
    pub irr_current: Decimal,
    pub weight: Decimal,
}

impl Investment {
    /// This Applies the given transaction to the state which results in new investment state
    /// Never Mutate the existing state
    pub fn apply_transactions(
        &self,
        transactions: Vec<&Transaction>,
        price: Decimal,
        quantity_queue: &mut QuantityQueue,
    ) -> Self {
        let txn_computer = TransactionComputer;

        let (total_capital, total_withdrawals, dividends_paid) = txn_computer.calculate_capital_value(&transactions);

        let unit_holding_split = txn_computer.long_short_term_holdings(&self.asset_class, &transactions);

        let new_holdings = self.calculate_current_holdings(&transactions);
        let mut new_invested_capital = self.invested_capital;

        //Compute the Invested Capital By FIFO
        for tx in &transactions {
            if tx.transaction_type == TransactionType::Buy {
                new_invested_capital += tx.amount;
                quantity_queue.add_quantity(tx.quantity, tx.price, tx.transaction_date.into());
            } else if tx.transaction_type == TransactionType::Sell {
                let capital_to_be_reduced = quantity_queue.reduce_quantity(tx.quantity).unwrap_or(dec!(0));

                new_invested_capital -= capital_to_be_reduced;
            }
        }

        let price_to_take = if price == dec!(0) { self.current_price } else { price };

        let new_market_value = new_holdings * price_to_take;

        let new_average_price = if new_holdings > dec!(0) {
            new_invested_capital / new_holdings
        } else {
            dec!(0)
        };

        let new_absolute_return_value = (new_market_value + dividends_paid) - new_invested_capital;
        let absolute_return_percent = if new_invested_capital == dec!(0) {
            dec!(0)
        } else {
            (new_absolute_return_value / new_invested_capital).round_dp(2)
        };

        let mut new_investment_state: Investment = Investment {
            date: self.date.checked_add_days(Days::new(1)).unwrap(),
            client_id: self.client_id.clone(),
            portfolio_id: self.portfolio_id.clone(),
            total_capital: total_capital + self.total_capital,
            dividends_paid: dividends_paid + self.dividends_paid,
            unrealised_gain_loss: self.market_value - new_invested_capital,
            absolute_return_percent,
            absolute_return_value: new_absolute_return_value,
            asset_class: self.asset_class.clone(),
            asset_type: self.asset_type.clone(),
            close_price: price_to_take,
            close_price_change: if self.close_price > dec!(0) {
                if price > dec!(0) {
                    ((price - self.close_price) / self.close_price) * dec!(100)
                } else {
                    //Means holiday
                    self.close_price_change
                }
            } else {
                dec!(0)
            },
            close_price_date: self.date.checked_add_days(Days::new(1)).unwrap().into(),
            current_price: price_to_take,
            holdings: new_holdings,
            market_value: new_market_value,
            invested_capital: new_invested_capital,
            withdrawals: total_withdrawals + self.withdrawals,
            long_term_units: unit_holding_split.0,
            short_term_units: unit_holding_split.1,
            market_value_change_percent: if self.market_value > dec!(0) {
                ((new_market_value - self.market_value) / self.market_value) * dec!(100)
            } else {
                if new_market_value > dec!(0) {
                    dec!(100)
                } else {
                    dec!(0)
                }
            },
            market_value_change: new_market_value - self.market_value,
            realised_gain_loss: self.realised_gain_loss,
            average_price: new_average_price,
            ..self.clone()
        };

        new_investment_state.calculate_realised_gain_loss(&transactions);
        new_investment_state.calculate_unrealised_gain_loss();

        new_investment_state
    }

    /// Transaction passed to this function should be the transaction fora single security
    /// It checks the sell transactions and the quantity and price at which it got sold
    pub fn calculate_realised_gain_loss(&mut self, transactions: &Vec<&Transaction>) {
        for txn in transactions {
            if txn.transaction_type == TransactionType::Sell {
                let realised_gain_loss = (txn.price - self.average_price) * txn.quantity;
                self.realised_gain_loss += realised_gain_loss;
            }
        }
    }

    /// MarketValue - Invested Capital
    /// The current value if your stocks -  total amount of money you have invested will give the unrealised gain loss
    pub fn calculate_unrealised_gain_loss(&mut self) {
        self.unrealised_gain_loss = self.market_value - self.invested_capital;
    }

    /// Calculates the InvestedCapital and the Holdings from the transaction
    /// The transactions are applied on top of the previous day investment state
    /// And the values are derived
    pub fn calculate_current_holdings(&self, transactions: &Vec<&Transaction>) -> Decimal {
        let mut current_holdings = self.holdings;

        for txn in transactions {
            if txn.transaction_type == TransactionType::Buy {
                current_holdings += txn.quantity;
            } else {
                current_holdings -= txn.quantity
            }
        }

        current_holdings
    }
}
