use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(Serialize, Deserialize, Archive, Default)]
pub struct MarketCapAllocation {
    pub large_cap: Decimal,
    pub mid_cap: Decimal,
    pub small_cap: Decimal,
    pub others: Decimal,
}

impl MarketCapAllocation {
    pub fn assign_value(&mut self, cap: &str, value: Decimal) {
        let cap = cap.to_lowercase();
        if cap == "smallcap" || cap == "small cap" {
            self.small_cap = value;
        } else if cap == "midcap" || cap == "mid cap" {
            self.mid_cap = value
        } else if cap == "largecap" || cap == "large cap" {
            self.large_cap = value
        } else {
            self.others = value;
        }
    }
}
