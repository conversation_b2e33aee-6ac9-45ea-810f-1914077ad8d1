use serde::{Deserialize, Serialize};

#[derive(Serial<PERSON>, Deserialize, <PERSON><PERSON>)]
pub struct TwrrReturn {
    pub one_month: Option<f64>,
    pub three_month: Option<f64>,
    pub six_month: Option<f64>,
    pub one_year: Option<f64>,
    pub two_year: Option<f64>,
    pub three_year: Option<f64>,
    pub seven_year: Option<f64>,
    pub ten_year: Option<f64>,
    pub ytd: Option<f64>,
    pub fytd: Option<f64>,
    pub since_inception: Option<f64>,
}
