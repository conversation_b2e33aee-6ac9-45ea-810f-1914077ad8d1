use chrono::NaiveDate;
use rust_decimal::Decimal;

pub mod asset_class;
pub mod industries;
pub mod investment_returns;
pub mod market_cap_allocation;
pub mod portfolio_return;
pub mod portfolio_returns;
pub mod twrr_return;
pub mod xirr_return;

#[derive(rkyv::Serialize, rkyv::Deserialize, rkyv::Archive, Debug, Clone)]
pub struct TransactionForAnalytics {
    pub amount: Decimal,
    pub market_cap: String,
    pub isin: String,
    pub asset_type: String,
    pub asset_class: String,
    pub transaction_date: NaiveDate,
    pub industry: String,
}
