use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(Serialize, Deserialize, Archive, Default)]
pub struct AssetClassAllocation {
    pub equity: Decimal,
    pub debt: Decimal,
    pub cash: Decimal,
    pub others: Decimal,
}

impl AssetClassAllocation {
    pub fn assign(&mut self, key: &str, value: Decimal) {
        match key.to_lowercase().as_str() {
            "equity" => self.equity = value,
            "debt" => self.debt = value,
            "cash" => self.cash = value,
            _ => self.others = value,
        }
    }
}
