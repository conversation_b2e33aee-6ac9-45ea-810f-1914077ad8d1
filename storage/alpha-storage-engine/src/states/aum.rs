use crate::lake::ReflectFieldAccess;
use alpha_macros::ReflectFields;
use chrono::NaiveDate;
use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;
use tracing::error;

#[derive(Serialize, Deserialize, Archive, Debug, <PERSON><PERSON>, Default, ReflectFields)]
pub struct Aum {
    pub date: NaiveDate,
    pub cash: Decimal,
    pub market_value: Decimal,
    pub nav: Decimal,
    pub change: Decimal,
    pub total_aum: Decimal,
    pub units: Decimal,
    pub net_cash_flows: Decimal,
    pub payables: Decimal,
    pub receivables: Decimal,
}

impl Aum {
    pub fn build_genesis(
        cash: Decimal,
        market_value: Decimal,
        net_cash_flows: Decimal,
        date: NaiveDate,
        payables: Decimal,
        receivables: Decimal,
    ) -> Self {
        let total_aum = (cash + market_value + payables + receivables).round_dp(2);

        let change = if total_aum == dec!(0) {
            dec!(0)
        } else {
            (((total_aum) / net_cash_flows) - Decimal::from_u8(1).unwrap()).round_dp(6)
        };

        let nav = (Decimal::from_u32(100).unwrap() * (Decimal::from_u32(1).unwrap() + change)).round_dp(6);

        let units = if nav == dec!(0) {
            dec!(0)
        } else {
            (net_cash_flows / Decimal::from_u32(100).unwrap()).round_dp(6)
        };

        Self {
            cash,
            market_value,
            total_aum,
            change,
            nav,
            units,
            net_cash_flows,
            payables,
            date,
            receivables,
        }
    }

    pub fn compute_aum_next_day(
        &self,
        cash: Decimal,
        market_value: Decimal,
        net_cash_flows: Decimal,
        date: NaiveDate,
        payables: Decimal,
        receivables: Decimal,
    ) -> Self {
        let total_aum = (cash
            .checked_add(market_value + payables + receivables)
            .unwrap_or_else(|| {
                error!("OVERFLOW");
                rust_decimal::Decimal::ZERO
            }))
        .round_dp(2);

        let previous_aum = self.total_aum;

        let change = if previous_aum == dec!(0) {
            dec!(1)
        } else {
            (((total_aum - net_cash_flows) / previous_aum) - Decimal::from_u8(1).unwrap()).round_dp(6)
        };

        let nav = (self.nav.checked_mul(Decimal::from_u32(1).unwrap() + change))
            .unwrap_or_default()
            .round_dp(6);

        let units = if nav == dec!(0) {
            dec!(0)
        } else {
            (net_cash_flows / nav).round_dp(6)
        };

        Self {
            cash,
            market_value,
            total_aum,
            nav,
            change,
            units,
            net_cash_flows,
            date,
            payables,
            receivables,
        }
    }
}
