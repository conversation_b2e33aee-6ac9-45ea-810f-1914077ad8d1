use chrono::NaiveDate;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::collections::VecDeque;

#[derive(Debug, rkyv::Deserialize, rkyv::Serialize, rkyv::Archive, Clone)]
pub struct PriceQuantity {
    pub price: Decimal,
    pub quantity: Decimal,
    pub date: NaiveDate,
}

#[derive(rkyv::Deserialize, rkyv::Serialize, rkyv::Archive, Debug)]
pub struct QuantityQueue {
    pub queue: VecDeque<PriceQuantity>,
}

impl QuantityQueue {
    pub fn new() -> Self {
        QuantityQueue { queue: VecDeque::new() }
    }

    /// Add a new quantity and price to the queue
    /// Make sure only for transaction type Buy this should be called
    pub fn add_quantity(&mut self, quantity: Decimal, price: Decimal, date: NaiveDate) {
        self.queue.push_back(PriceQuantity { price, quantity, date });
    }

    /// Get the current quantity and price at the front without removing it
    pub fn get_current_quantity(&self) -> Option<PriceQuantity> {
        self.queue.front().cloned()
    }

    /// Reduce the current quantity by the given amount
    /// And returns the total cost of removed quantities
    /// Make sure only for transaction type Sell this should be called
    pub fn reduce_quantity(&mut self, mut quanity: Decimal) -> Result<Decimal, &'static str> {
        if self.is_empty() {
            return Err("Queue Is Empty");
        }

        let mut total_cost = dec!(0);

        //Loop till either the amount is zero or queue is empty
        while quanity > dec!(0) && !self.is_empty() {
            //Reduce the quantity from the first element
            if let Some(vec) = self.queue.front_mut() {
                let remaining_quantity = vec.quantity - quanity;

                //If there are remaining quantities
                //We should move to next element
                if remaining_quantity < dec!(0) {
                    total_cost = total_cost + (vec.quantity * vec.price);
                    quanity -= vec.quantity;
                    //Remove it from the queue
                    //Since it is consumed
                    self.queue.pop_front();
                } else {
                    total_cost = total_cost + (quanity * vec.price);
                    quanity = dec!(0);
                    vec.quantity = remaining_quantity;
                }
            }
        }

        return Ok(total_cost.round_dp(2));
    }

    /// Check if the queue is empty
    pub fn is_empty(&self) -> bool {
        self.queue.is_empty()
    }

    /// Get the number of quantities in the queue
    pub fn len(&self) -> usize {
        self.queue.len()
    }
}
