use std::str::FromStr;

use alpha_core_db::connection::pool::tiberius::Row;
use chrono::NaiveDateTime;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

use super::{
    common::deserialize_decimal,
    enums::{TransactionSubType, TransactionType},
    ledger::LedgerTransaction,
};

#[derive(Serialize, Deserialize, rkyv::Serialize, rkyv::Deserialize, rkyv::Archive, Debug, Clone)]
#[serde(rename_all = "PascalCase")]
pub struct Transaction {
    pub id: String,
    pub portfolio_id: String,
    pub client_id: String,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    #[serde(rename = "CGTDate")]
    pub cgt_date: NaiveDateTime,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub quantity: Decimal,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub unrealised_holding: Decimal,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub current_holding: Decimal,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub price: Decimal,
    pub market_rate: f64,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub amount: Decimal,
    pub brokerage: f64,
    pub service_tax: f64,
    pub stt_amount: f64,
    pub turn_tax: f64,
    pub other_tax: f64,
    #[serde(rename = "Type")]
    pub transaction_type: TransactionType,
    #[serde(rename = "SubType")]
    pub transaction_sub_type: TransactionSubType,
    pub symbol: String,
    pub exchange: String,
    pub isin: String,
    pub name: String,
    pub mf_folio: Option<String>,
    pub currency: Option<String>,
    pub currency_conversion_rate: f64,
}

impl Transaction {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            cgt_date: row.get::<NaiveDateTime, _>("CGTDate").unwrap(),
            quantity: row.get::<Decimal, _>("Quantity").unwrap(),
            unrealised_holding: row.get::<Decimal, _>("UnrealisedHolding").unwrap(),
            current_holding: row.get::<Decimal, _>("CurrentHolding").unwrap(),
            price: row.get::<Decimal, _>("Price").unwrap(),
            market_rate: row.get::<f64, _>("MarketRate").unwrap(),
            amount: row.get::<Decimal, _>("Amount").unwrap(),
            brokerage: row.get::<f64, _>("Brokerage").unwrap(),
            service_tax: row.get::<f64, _>("ServiceTax").unwrap(),
            stt_amount: row.get::<f64, _>("SttAmount").unwrap(),
            turn_tax: row.get::<f64, _>("TurnTax").unwrap(),
            other_tax: row.get::<f64, _>("OtherTax").unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("Type").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("SubType").unwrap()).unwrap(),
            symbol: row.get::<&str, _>("Symbol").unwrap().to_string(),
            exchange: row.get::<&str, _>("Exchange").unwrap().to_string(),
            isin: row.get::<&str, _>("Isin").unwrap().to_string(),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            mf_folio: row.get::<&str, _>("MFFolio").map(String::from),
            currency: row.get::<&str, _>("Currency").map(String::from),
            currency_conversion_rate: row.get::<f64, _>("CurrencyConversionRate").unwrap(),
        }
    }
}

impl LedgerTransaction for Transaction {
    fn get_amount(&self) -> Decimal {
        self.amount
    }
    fn get_transaction_type(&self) -> super::enums::TransactionType {
        self.transaction_type.clone()
    }
    fn get_description(&self) -> String {
        String::from("Security In")
    }
    fn get_settlement_date(&self) -> NaiveDateTime {
        self.settlement_date
    }
    fn get_transaction_date(&self) -> NaiveDateTime {
        self.transaction_date
    }
    fn get_transaction_sub_type(&self) -> TransactionSubType {
        self.transaction_sub_type.clone()
    }
}
