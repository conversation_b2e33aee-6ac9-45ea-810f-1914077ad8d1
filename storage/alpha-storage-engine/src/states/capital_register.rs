use std::str::FromStr;

use alpha_core_db::connection::pool::tiberius::Row;
use chrono::NaiveDateTime;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Deserializer, Serialize};

use super::{
    enums::{TransactionSubType, TransactionType},
    ledger::LedgerTransaction,
};

#[derive(Serialize, Deserialize, rkyv::Serialize, rkyv::Deserialize, rkyv::Archive, Debug)]
#[serde(rename_all = "PascalCase")]
pub struct CapitalRegister {
    pub id: String,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub amount: Decimal,
    pub description: String,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
}

fn deserialize_decimal<'de, D>(deserializer: D) -> Result<Decimal, D::Error>
where
    D: Deserializer<'de>,
{
    let num = serde_json::Number::deserialize(deserializer)?;
    if let Some(n) = num.as_f64() {
        Decimal::from_f64(n).ok_or_else(|| serde::de::Error::custom("Failed to parse decimal"))
    } else {
        Err(serde::de::Error::custom("Invalid number format"))
    }
}

impl CapitalRegister {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            transaction_date: row.get::<NaiveDateTime, _>("TransactionDate").unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("TransactionSubType").unwrap())
                .unwrap(),
            amount: Decimal::from_f64(row.get::<f64, _>("Amount").unwrap()).unwrap(),
            description: row.get::<&str, _>("Description").unwrap().to_string(),
        }
    }
}

impl LedgerTransaction for CapitalRegister {
    fn get_amount(&self) -> Decimal {
        self.amount
    }
    fn get_transaction_type(&self) -> TransactionType {
        self.transaction_type.clone()
    }

    fn get_description(&self) -> String {
        self.description.clone()
    }
    fn get_settlement_date(&self) -> NaiveDateTime {
        self.settlement_date
    }
    fn get_transaction_date(&self) -> NaiveDateTime {
        self.transaction_date
    }
    fn get_transaction_sub_type(&self) -> TransactionSubType {
        self.transaction_sub_type.clone()
    }
}
