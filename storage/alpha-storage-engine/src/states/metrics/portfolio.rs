use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::Decimal;

#[derive(Serialize, Deserialize, Archive)]
pub struct XirrResults {
    pub one_month: Option<f64>,
    pub three_month: Option<f64>,
    pub six_month: Option<f64>,
    pub one_year: Option<f64>,
    pub two_year: Option<f64>,
    pub three_year: Option<f64>,
    pub seven_year: Option<f64>,
    pub ten_year: Option<f64>,
    pub ytd: Option<f64>,
    pub fytd: Option<f64>,
    pub since_inception: Option<f64>,
}

#[derive(Serialize, Deserialize, Archive)]
pub struct TwrrResults {
    pub one_month: Option<Decimal>,
    pub three_month: Option<Decimal>,
    pub six_month: Option<Decimal>,
    pub one_year: Option<Decimal>,
    pub two_year: Option<Decimal>,
    pub three_year: Option<Decimal>,
    pub seven_year: Option<Decimal>,
    pub ten_year: Option<Decimal>,
    pub ytd: Option<Decimal>,
    pub fytd: Option<Decimal>,
    pub since_inception: Option<Decimal>,
}
