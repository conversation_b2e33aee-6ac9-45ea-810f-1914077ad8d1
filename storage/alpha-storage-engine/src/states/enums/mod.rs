use std::{fmt, str::FromStr};

use serde::{Deserialize, Serialize};

#[derive(
    Debug, <PERSON><PERSON>ult, Clone, PartialEq, Serialize, Deserialize, rkyv::Serialize, rkyv::Deserialize, rkyv::Archive,
)]
pub enum TransactionType {
    #[default]
    Buy,
    Sell,
    DividendReinvestment,
    DividendPaid,
    Inflow,
    Outflow,
    Debit,
    Credit,
}

impl fmt::Display for TransactionType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            TransactionType::Buy => write!(f, "Buy"),
            TransactionType::Sell => write!(f, "Sell"),
            TransactionType::DividendReinvestment => write!(f, "DividendReinvestment"),
            TransactionType::DividendPaid => write!(f, "DividendPaid"),
            TransactionType::Inflow => write!(f, "Inflow"),
            TransactionType::Outflow => write!(f, "Outflow"),
            TransactionType::Debit => write!(f, "Debit"),
            TransactionType::Credit => write!(f, "Credit"),
        }
    }
}

#[derive(
    Debug, <PERSON><PERSON><PERSON>, Clone, PartialEq, Serialize, Deserialize, rkyv::Serialize, rkyv::Deserialize, rkyv::Archive,
)]
pub enum TransactionSubType {
    #[default]
    ExitLoad,
    Buy,
    Stt,
    BuyAdjustment,
    OtherAssetsLiabilities,
    Sell,
    SellAdjustment,
    Purchase,
    Redemption,
    DividendReinvestment,
    Dividend,
    SwitchIn,
    SwitchOut,
    TransferIn,
    TransferOut,
    Split,
    BuySplitAdjustment,
    Bonus,
    BuyBonusAdjustment,
    SecurityIn,
    SecurityOut,
    CapitalIn,
    CapitalOut,
    Fees,
    PerformanceFees,
    Charges,
    Tds,
    Other,
    Receipt,
    Payment,
    Interest,
    Tax,
    FractionPayment,
    SplitPartial,
    BonusPartial,
    ReturnOnCapital,
    IncomeSurplus,
    Merger,
    Demerger,
    Amalgamation,
    MergerPartial,
    DemergerPartial,
    AmalgamationPartial,
    SpinOff,
    SpinOffPartial,
    CashBalance,
    PeakMargin,
    AccountOpeningCharges,
    OperatingExpenses,
    CustodyCharges,
    ContraEntry,
    AuditCharges,
    FundAccountingCharges,
}

impl fmt::Display for TransactionSubType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TransactionSubType::ExitLoad => write!(f, "ExitLoad"),
            TransactionSubType::Buy => write!(f, "Buy"),
            TransactionSubType::BuyAdjustment => write!(f, "BuyAdjustment"),
            TransactionSubType::Sell => write!(f, "Sell"),
            TransactionSubType::SellAdjustment => write!(f, "SellAdjustment"),
            TransactionSubType::Purchase => write!(f, "Purchase"),
            TransactionSubType::Redemption => write!(f, "Redemption"),
            TransactionSubType::DividendReinvestment => write!(f, "DividendReinvestment"),
            TransactionSubType::Dividend => write!(f, "Dividend"),
            TransactionSubType::SwitchIn => write!(f, "SwitchIn"),
            TransactionSubType::SwitchOut => write!(f, "SwitchOut"),
            TransactionSubType::TransferIn => write!(f, "TransferIn"),
            TransactionSubType::TransferOut => write!(f, "TransferOut"),
            TransactionSubType::Split => write!(f, "Split"),
            TransactionSubType::BuySplitAdjustment => write!(f, "BuySplitAdjustment"),
            TransactionSubType::Bonus => write!(f, "Bonus"),
            TransactionSubType::BuyBonusAdjustment => write!(f, "BuyBonusAdjustment"),
            TransactionSubType::SecurityIn => write!(f, "SecurityIn"),
            TransactionSubType::SecurityOut => write!(f, "SecurityOut"),
            TransactionSubType::CapitalIn => write!(f, "CapitalIn"),
            TransactionSubType::CapitalOut => write!(f, "CapitalOut"),
            TransactionSubType::Fees => write!(f, "Fees"),
            TransactionSubType::PerformanceFees => write!(f, "PerformanceFees"),
            TransactionSubType::Charges => write!(f, "Charges"),
            TransactionSubType::Tds => write!(f, "Tds"),
            TransactionSubType::Other => write!(f, "Other"),
            TransactionSubType::Receipt => write!(f, "Receipt"),
            TransactionSubType::Payment => write!(f, "Payment"),
            TransactionSubType::Interest => write!(f, "Interest"),
            TransactionSubType::Tax => write!(f, "Tax"),
            TransactionSubType::FractionPayment => write!(f, "FractionPayment"),
            TransactionSubType::SplitPartial => write!(f, "SplitPartial"),
            TransactionSubType::BonusPartial => write!(f, "BonusPartial"),
            TransactionSubType::ReturnOnCapital => write!(f, "ReturnOnCapital"),
            TransactionSubType::IncomeSurplus => write!(f, "IncomeSurplus"),
            TransactionSubType::Merger => write!(f, "Merger"),
            TransactionSubType::Demerger => write!(f, "Demerger"),
            TransactionSubType::Amalgamation => write!(f, "Amalgamation"),
            TransactionSubType::MergerPartial => write!(f, "MergerPartial"),
            TransactionSubType::DemergerPartial => write!(f, "DemergerPartial"),
            TransactionSubType::AmalgamationPartial => write!(f, "AmalgamationPartial"),
            TransactionSubType::SpinOff => write!(f, "SpinOff"),
            TransactionSubType::SpinOffPartial => write!(f, "SpinOffPartial"),
            TransactionSubType::CashBalance => write!(f, "CashBalance"),
            TransactionSubType::PeakMargin => write!(f, "PeakMargin"),
            TransactionSubType::Stt => write!(f, "Stt"),
            TransactionSubType::OtherAssetsLiabilities => write!(f, "OtherAssetsLiabilities"),
            TransactionSubType::AccountOpeningCharges => write!(f, "AccountOpeningCharges"),
            TransactionSubType::OperatingExpenses => write!(f, "OperatingExpenses"),
            TransactionSubType::CustodyCharges => write!(f, "CustodyCharges"),
            TransactionSubType::ContraEntry => write!(f, "ContraEntry"),
            TransactionSubType::AuditCharges => write!(f, "AuditCharges"),
            TransactionSubType::FundAccountingCharges => write!(f, "FundAccountingCharges"),
        }
    }
}

impl FromStr for TransactionType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Buy" => Ok(TransactionType::Buy),
            "Sell" => Ok(TransactionType::Sell),
            "DividendReinvestment" => Ok(TransactionType::DividendReinvestment),
            "DividendPaid" => Ok(TransactionType::DividendPaid),
            "Inflow" => Ok(TransactionType::Inflow),
            "Outflow" => Ok(TransactionType::Outflow),
            "Debit" => Ok(TransactionType::Debit),
            "Credit" => Ok(TransactionType::Credit),
            _ => Err(()),
        }
    }
}

impl FromStr for TransactionSubType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "Buy" => Ok(TransactionSubType::Buy),
            "BuyAdjustment" => Ok(TransactionSubType::BuyAdjustment),
            "Sell" => Ok(TransactionSubType::Sell),
            "SellAdjustment" => Ok(TransactionSubType::SellAdjustment),
            "Purchase" => Ok(TransactionSubType::Purchase),
            "Redemption" => Ok(TransactionSubType::Redemption),
            "DividendReinvestment" => Ok(TransactionSubType::DividendReinvestment),
            "Dividend" => Ok(TransactionSubType::Dividend),
            "SwitchIn" => Ok(TransactionSubType::SwitchIn),
            "SwitchOut" => Ok(TransactionSubType::SwitchOut),
            "TransferIn" => Ok(TransactionSubType::TransferIn),
            "TransferOut" => Ok(TransactionSubType::TransferOut),
            "Split" => Ok(TransactionSubType::Split),
            "BuySplitAdjustment" => Ok(TransactionSubType::BuySplitAdjustment),
            "Bonus" => Ok(TransactionSubType::Bonus),
            "BuyBonusAdjustment" => Ok(TransactionSubType::BuyBonusAdjustment),
            "SecurityIn" => Ok(TransactionSubType::SecurityIn),
            "SecurityOut" => Ok(TransactionSubType::SecurityOut),
            "CapitalIn" => Ok(TransactionSubType::CapitalIn),
            "CapitalOut" => Ok(TransactionSubType::CapitalOut),
            "Fees" => Ok(TransactionSubType::Fees),
            "PerformanceFees" => Ok(TransactionSubType::PerformanceFees),
            "Charges" => Ok(TransactionSubType::Charges),
            "Tds" => Ok(TransactionSubType::Tds),
            "Other" => Ok(TransactionSubType::Other),
            "Receipt" => Ok(TransactionSubType::Receipt),
            "Payment" => Ok(TransactionSubType::Payment),
            "Interest" => Ok(TransactionSubType::Interest),
            "Tax" => Ok(TransactionSubType::Tax),
            "FractionPayment" => Ok(TransactionSubType::FractionPayment),
            "SplitPartial" => Ok(TransactionSubType::SplitPartial),
            "BonusPartial" => Ok(TransactionSubType::BonusPartial),
            "ReturnOnCapital" => Ok(TransactionSubType::ReturnOnCapital),
            "IncomeSurplus" => Ok(TransactionSubType::IncomeSurplus),
            "Merger" => Ok(TransactionSubType::Merger),
            "Demerger" => Ok(TransactionSubType::Demerger),
            "Amalgamation" => Ok(TransactionSubType::Amalgamation),
            "MergerPartial" => Ok(TransactionSubType::MergerPartial),
            "DemergerPartial" => Ok(TransactionSubType::DemergerPartial),
            "AmalgamationPartial" => Ok(TransactionSubType::AmalgamationPartial),
            "SpinOff" => Ok(TransactionSubType::SpinOff),
            "SpinOffPartial" => Ok(TransactionSubType::SpinOffPartial),
            "CashBalance" => Ok(TransactionSubType::CashBalance),
            "PeakMargin" => Ok(TransactionSubType::PeakMargin),
            _ => Err(()),
        }
    }
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    rkyv::Serialize,
    rkyv::Deserialize,
    rkyv::Archive,
    Clone,
    Default,
    PartialEq,
    Eq,
    Hash,
)]
pub enum SecurityType {
    #[default]
    Stocks,
    ETF,
    MutualFund,
    FixedIncome,
    Commodity,
    EquityDerivatives,
    PlainDebt,
    StructuredDebt,
    Others,
    CommodityDerivatives,
    Goods,
    CashEquivalent,
    Cash,
}

impl fmt::Display for SecurityType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SecurityType::Stocks => write!(f, "Stocks"),
            SecurityType::ETF => write!(f, "ETF"),
            SecurityType::MutualFund => write!(f, "MutualFund"),
            SecurityType::FixedIncome => write!(f, "FixedIncome"),
            SecurityType::Commodity => write!(f, "Commodity"),
            SecurityType::EquityDerivatives => write!(f, "EquityDerivatives"),
            SecurityType::PlainDebt => write!(f, "PlainDebt"),
            SecurityType::StructuredDebt => write!(f, "StructuredDebt"),
            SecurityType::Others => write!(f, "Others"),
            SecurityType::CommodityDerivatives => write!(f, "CommodityDerivatives"),
            SecurityType::Goods => write!(f, "Goods"),
            SecurityType::CashEquivalent => write!(f, "CashEquivalent"),
            SecurityType::Cash => write!(f, "Cash"),
        }
    }
}
