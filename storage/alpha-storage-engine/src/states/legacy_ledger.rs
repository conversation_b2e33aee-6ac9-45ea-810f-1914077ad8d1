use std::str::FromStr;

use alpha_core_db::connection::pool::tiberius::Row;
use chrono::NaiveDateTime;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Deserializer, Serialize};

use super::{
    enums::{TransactionSubType, TransactionType},
    ledger::LedgerTransaction,
};

#[derive(Debug, Serialize, Deserialize, rkyv::Deserialize, rkyv::Serialize, rkyv::Archive)]
#[serde(rename_all = "PascalCase")]
pub struct LegacyLedger {
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub amount: Decimal,
    pub description: Option<String>,
    pub portfolio_id: Option<String>,
    pub model_portfolio_id: Option<String>,
}

fn deserialize_decimal<'de, D>(deserializer: D) -> Result<Decimal, D::Error>
where
    D: Deserializer<'de>,
{
    let num = serde_json::Number::deserialize(deserializer)?;
    if let Some(n) = num.as_f64() {
        Decimal::from_f64(n).ok_or_else(|| serde::de::Error::custom("Failed to parse decimal"))
    } else {
        Err(serde::de::Error::custom("Invalid number format"))
    }
}

impl LegacyLedger {
    pub fn from_row(row: &Row) -> Self {
        Self {
            amount: row.get::<Decimal, _>("Amount").unwrap(),
            description: row.get::<&str, _>("Description").map(String::from),
            model_portfolio_id: row.get::<&str, _>("ModelportfolioId").map(String::from),
            portfolio_id: row.get::<&str, _>("PortfolioId").map(String::from),
            settlement_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            transaction_date: row.get::<NaiveDateTime, _>("SettlementDate").unwrap(),
            transaction_sub_type: TransactionSubType::from_str(row.get::<&str, _>("TransactionSubType").unwrap())
                .unwrap(),
            transaction_type: TransactionType::from_str(row.get::<&str, _>("TransactionType").unwrap()).unwrap(),
        }
    }
}

impl LedgerTransaction for LegacyLedger {
    fn get_amount(&self) -> Decimal {
        self.amount
    }

    fn get_description(&self) -> String {
        if let Some(des) = &self.description {
            return des.clone();
        }

        return format!("Legacy Transaction");
    }
    fn get_settlement_date(&self) -> NaiveDateTime {
        self.settlement_date
    }
    fn get_transaction_date(&self) -> NaiveDateTime {
        self.transaction_date
    }
    fn get_transaction_sub_type(&self) -> super::enums::TransactionSubType {
        self.transaction_sub_type.clone()
    }
    fn get_transaction_type(&self) -> super::enums::TransactionType {
        self.transaction_type.clone()
    }
}
