use rust_decimal::{prelude::FromPrimitive, Decimal};
use serde::{Deserialize, Deserializer};

pub fn deserialize_decimal<'de, D>(deserializer: D) -> Result<Decimal, D::Error>
where
    D: Deserializer<'de>,
{
    let num = serde_json::Number::deserialize(deserializer)?;
    if let Some(n) = num.as_f64() {
        Decimal::from_f64(n).ok_or_else(|| serde::de::Error::custom("Failed to parse decimal"))
    } else {
        Err(serde::de::Error::custom("Invalid number format"))
    }
}
