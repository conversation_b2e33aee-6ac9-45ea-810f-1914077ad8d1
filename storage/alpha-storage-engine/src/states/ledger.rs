use chrono::NaiveDateTime;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

use super::enums::{TransactionSubType, TransactionType};

pub trait LedgerTransaction {
    fn get_amount(&self) -> Decimal;
    fn get_transaction_type(&self) -> TransactionType;
    fn get_transaction_date(&self) -> NaiveDateTime;
    fn get_settlement_date(&self) -> NaiveDateTime;
    fn get_transaction_sub_type(&self) -> TransactionSubType;
    fn get_description(&self) -> String;
}

#[derive(Serialize, Deserialize, rkyv::Deserialize, rkyv::Serialize, rkyv::Archive, Debug)]
pub struct Ledger {
    pub running_balance: Decimal,
    pub amount: Decimal,
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub description: String,
}
