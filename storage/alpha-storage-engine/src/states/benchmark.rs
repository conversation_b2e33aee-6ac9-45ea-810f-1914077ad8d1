use chrono::NaiveDate;
use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal::Decimal;

use crate::utils::benchmark::BenchmarkIndices;

#[derive(Serialize, Deserialize, Archive, Debug,Clone)]
pub struct BenchmarkPrice {
    pub index: BenchmarkIndices,
    pub date: NaiveDate,
    pub open: Decimal,
    pub high: Decimal,
    pub low: Decimal,
    pub close: Decimal,
    pub change: Decimal,
    pub name: String,
}
