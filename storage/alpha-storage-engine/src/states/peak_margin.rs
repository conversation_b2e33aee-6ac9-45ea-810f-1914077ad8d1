use alpha_core_db::connection::pool::tiberius::Row;
use chrono::NaiveDateTime;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

use super::common::deserialize_decimal;
use super::{
    enums::{TransactionSubType, TransactionType},
    ledger::LedgerTransaction,
};

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct PeakMargin {
    pub portfolio_id: String,
    pub peak_margin_pct: f64,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub amount: Decimal,
    pub status: String,
    pub date: NaiveDateTime,
    #[serde(rename = "Type")]
    pub type_name: String,
}

impl PeakMargin {
    pub fn from_row(row: &Row) -> Self {
        Self {
            amount: row.get::<Decimal, _>("Amount").unwrap(),
            date: row.get::<NaiveDateTime, _>("Date").unwrap(),
            peak_margin_pct: row.get::<f64, _>("PeakMarginpct").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            status: row.get::<&str, _>("Status").unwrap().to_string(),
            type_name: row.get::<&str, _>("Type").unwrap().to_string(),
        }
    }
}

impl LedgerTransaction for PeakMargin {
    fn get_amount(&self) -> Decimal {
        self.amount
    }

    fn get_description(&self) -> String {
        format!("Peak margin {}", self.status)
    }
    fn get_settlement_date(&self) -> NaiveDateTime {
        self.date
    }
    fn get_transaction_date(&self) -> NaiveDateTime {
        self.date
    }
    fn get_transaction_sub_type(&self) -> super::enums::TransactionSubType {
        TransactionSubType::PeakMargin
    }
    fn get_transaction_type(&self) -> super::enums::TransactionType {
        TransactionType::Buy //FXIME:
    }
}
