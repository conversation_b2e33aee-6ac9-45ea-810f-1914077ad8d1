use chrono::NaiveDateTime;
use rust_decimal::Decimal;
use serde::{self, Deserialize, Serialize};

use super::common::deserialize_decimal;
use super::enums::{TransactionSubType, TransactionType};

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ReceivablePayable {
    pub transaction_date: NaiveDateTime,
    pub settlement_date: NaiveDateTime,
    #[serde(rename = "CgtDate")]
    pub cgt_date: NaiveDateTime,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    #[serde(deserialize_with = "deserialize_decimal")]
    pub amount: Decimal,
    pub remarks: Option<String>,
    pub receivable_status: String,
    pub portfolio_id: Option<String>,
    pub model_portfolio_id: Option<String>,
}
