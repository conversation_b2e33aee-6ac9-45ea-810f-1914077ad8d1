use duckdb::{
    types::{FromSql, ValueRef},
    Connection, ToSql,
};
use serde_json::{Number, Value};

pub struct DuckDb {
    connection: Connection,
}

pub fn value_ref_to_json(val: ValueRef<'_>) -> Value {
    match val {
        ValueRef::Null => Value::Null,
        ValueRef::Boolean(b) => Value::Bool(b),
        ValueRef::TinyInt(i) => Value::Number(Number::from(i)),
        ValueRef::SmallInt(i) => Value::Number(Number::from(i)),
        ValueRef::Int(i) => Value::Number(Number::from(i)),
        ValueRef::BigInt(i) => Value::Number(Number::from(i)),
        ValueRef::HugeInt(i) => Value::String(i.to_string()),
        ValueRef::UTinyInt(i) => Value::Number(Number::from(i)),
        ValueRef::USmallInt(i) => Value::Number(Number::from(i)),
        ValueRef::UInt(i) => Value::Number(Number::from(i)),
        ValueRef::UBigInt(i) => Value::Number(Number::from(i)),
        ValueRef::Float(f) => Number::from_f64(f as f64).map_or(Value::Null, Value::Number),
        ValueRef::Double(f) => Number::from_f64(f).map_or(Value::Null, Value::Number),
        ValueRef::Decimal(d) => Value::String(d.to_string()),
        ValueRef::Timestamp(_, ts) => Value::Number(Number::from(ts)),
        ValueRef::Text(bytes) => Value::String(String::from_utf8_lossy(bytes).to_string()),
        ValueRef::Blob(bytes) => todo!(),
        ValueRef::Date32(days) => Value::Number(Number::from(days)),
        ValueRef::Time64(_, v) => Value::Number(Number::from(v)),
        ValueRef::Interval { months, days, nanos } => todo!(),
        ValueRef::List(_, _)
        | ValueRef::Enum(_, _)
        | ValueRef::Struct(_, _)
        | ValueRef::Array(_, _)
        | ValueRef::Map(_, _)
        | ValueRef::Union(_, _) => Value::String(format!("{:?}", val)),
    }
}

impl DuckDb {
    pub fn new() -> Result<Self, duckdb::Error> {
        let connection = duckdb::Connection::open_in_memory()?;
        Ok(Self { connection })
    }

    pub fn load_delta_extension(&self) -> Result<(), duckdb::Error> {
        self.connection.execute_batch("INSTALL delta; LOAD delta;")?;
        Ok(())
    }

    pub fn create_views(&self, uri: &str) -> Result<(), duckdb::Error> {
        let views = vec![
            ("PortfolioInvestments", "portfolio_investments"),
            ("PortfolioAum", "portfolio_aum"),
            // Add more views here
        ];

        for (view_name, path) in views {
            let query = format!(
                "CREATE OR REPLACE VIEW {view} AS SELECT * FROM delta_scan('{uri}/{path}');",
                view = view_name,
                path = path
            );
            self.connection.execute_batch(&query)?;
        }

        Ok(())
    }

    pub fn read_lake(&self, uri: &str) {
        let query = format!(
            "CREATE OR REPLACE TABLE portfolio_analytics AS SELECT * FROM delta_scan('{}');",
            uri
        );
        self.connection.execute_batch(&query).unwrap();

        // let mut stmt = self
        //     .connection
        //     .prepare("SELECT * FROM delta_scan('./delta/portfolio_analytics');")
        //     .unwrap();
        // stmt.execute([]).unwrap();
        // let column_count = stmt.column_names().len();

        // let mut rows = stmt.query([]).unwrap();
        // while let Ok(row) = rows.next() {
        //     if let Some(row) = row {
        //         let val: Option<String> = row.get(1).unwrap();
        //         println!("Row 1{:?}", val);
        //     }
        // }
    }

    pub fn query_lake(&self, sql: &str) -> Result<Vec<Value>, duckdb::Error> {
        let mut stmt = self.connection.prepare(sql)?;
        stmt.execute([])?;
        let column_count = stmt.column_count();
        let column_names = stmt.column_names().to_vec();

        let mut rows = stmt.query([])?;

        let mut result = Vec::new();

        while let Some(row) = rows.next()? {
            let mut obj = serde_json::Map::new();
            for i in 0..column_count {
                let val = value_ref_to_json(row.get_ref(i)?);
                obj.insert(column_names[i].to_string(), val);
            }
            result.push(Value::Object(obj));
        }

        Ok(result)
    }

    pub fn query_lake_structured(&self, sql: &str) -> Result<QueryResult, duckdb::Error> {
        let mut stmt = self.connection.prepare(sql)?;
        stmt.execute([])?;
        let column_names: Vec<String> = stmt.column_names().iter().map(|s| s.to_string()).collect();

        let mut rows = stmt.query([])?;
        let mut data_rows = Vec::new();
        while let Some(row) = rows.next()? {
            let mut row_data = Vec::new();
            for i in 0..column_names.len() {
                let val = value_ref_to_json(row.get_ref(i)?);
                row_data.push(val.to_string());
            }
            data_rows.push(row_data);
        }

        Ok(QueryResult {
            columns: column_names,
            rows: data_rows,
        })
    }
}

#[derive(serde::Serialize)]
pub struct QueryResult {
    pub columns: Vec<String>,
    pub rows: Vec<Vec<String>>,
}

#[test]
pub fn test_duckdb() {
    let duckdb = DuckDb::new().unwrap();
    duckdb.load_delta_extension().unwrap();
    duckdb.read_lake("./delta/portfolio_investments");
}

#[test]
fn test_dynamic_query() {
    let duckdb = DuckDb::new().unwrap();
    duckdb.load_delta_extension().unwrap();

    let results = duckdb
    .query_lake("SELECT market_value  FROM delta_scan('./delta/portfolio_investments') WHERE client_id = '146f82d2fc0046e8b5550f8632bc9930'")
    .unwrap();

    for row in results {
        println!("{:?}", row);
    }
    //println!("len = {:}",results.len());
}
