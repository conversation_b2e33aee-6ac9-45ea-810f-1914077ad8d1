use chrono::{Datelike, NaiveDate};
use time::{Date, Month};

pub mod block;
pub mod computer;
pub mod duckdb;
pub mod lake;
pub mod parser;
pub mod states;
pub mod storage;
pub mod tests;
pub mod types;
pub mod utils;

fn chrono_to_time_date(naive: NaiveDate) -> Date {
    Date::from_calendar_date(
        naive.year(),
        // Convert month number to time::Month enum
        match naive.month() {
            1 => Month::January,
            2 => Month::February,
            3 => Month::March,
            4 => Month::April,
            5 => Month::May,
            6 => Month::June,
            7 => Month::July,
            8 => Month::August,
            9 => Month::September,
            10 => Month::October,
            11 => Month::November,
            12 => Month::December,
            _ => unreachable!(),
        },
        naive.day() as u8,
    )
    .unwrap()
}

fn convert_time_date_to_chrono_naive_date(date: time::Date) -> NaiveDate {
    // Extract year, month, and day components from time::Date
    let year = date.year();
    let month = date.month() as u32; // time::Month to u32
    let day = date.day() as u32; // time::Day to u32

    // Create a new chrono::NaiveDate
    NaiveDate::from_ymd_opt(year, month, day)
        .expect("Invalid date components when converting from time::Date to chrono::NaiveDate")
}
