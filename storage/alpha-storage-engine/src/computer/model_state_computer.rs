use alpha_core_db::connection::pool::{deadpool::managed::Pool, Manager};
use chrono::{Days, Utc};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;
use tracing::info;

use crate::{
    states::aggregation::InvestmentAggregation,
    storage::{Storage, StorageWriter},
    utils::StateAggregator,
};

pub struct ModelStateComputer<'a> {
    /// Main Database connection pool
    pub db_pool: Pool<Manager>,

    /// State Storage
    pub storage: &'a Storage,
}

impl<'a> ModelStateComputer<'a> {
    pub async fn compute_for_a_model(&self, model_id: &str) -> Result<(), String> {
        let mut storage_writer = StorageWriter::new(self.storage.db.clone());
        storage_writer.begin_batch();

        let mut model_start_date = storage_writer
            .get_strategy_model_start_date(model_id)
            .expect("Client Start Date Not Found");

        let current_date = Utc::now().naive_utc().date();
        while model_start_date < current_date {
            info!("Computing model {} for the day {}", model_id, model_start_date);
            let client_portfolios = storage_writer.get_strategy_model_portfolios(model_id, model_start_date);
            let portfolio_ids: Vec<String> = client_portfolios.iter().map(|p| p.portfolio_id.clone()).collect();

            let mut storage = StorageWriter::new(self.storage.db.clone());
            let aggregator = StateAggregator { storage: &storage };

            //Investments by Isin
            let portfolio_state = aggregator.portfolio_state_aggregation(&portfolio_ids, model_start_date);
            let investments = aggregator.portfolios_investments_aggregation(&portfolio_ids, model_start_date);
            let aum = aggregator.portfolios_aum_aggregation(&portfolio_ids, model_start_date);

            let investment_aggregation = InvestmentAggregation {
                market_value: portfolio_state.market_value,
                invested_capital: portfolio_state.invested_capital,
                total_capital: portfolio_state.total_capital,
                gain_loss: portfolio_state.market_value - portfolio_state.invested_capital,
                total_investment: investments.len() as u64,
                xirr: Decimal::from_f64(0f64).unwrap(),
                weight_over_portfolio: dec!(0), //FIXME
            };

            storage_writer
                .insert_aggregated_investment_for_strategy_model(model_id, &investment_aggregation, model_start_date)
                .await;

            storage_writer.insert_model_aum(&aum, model_id, model_start_date).await;

            model_start_date = model_start_date.checked_add_days(Days::new(1)).unwrap();
        }

        let _ = storage_writer.commit_batch().await;

        Ok(())
    }
}
