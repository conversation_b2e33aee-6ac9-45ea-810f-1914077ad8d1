use std::sync::Arc;

use alpha_core_db::{
    connection::pool::{deadpool::managed::Pool, Manager},
    schema::{
        client::Client,
        portfolio::PortfolioForPerformanceEngine,
        strategy::{strategy_model::StrategyModels, Strategies},
    },
};
use alpha_utils::{
    performance_engine::data_update::{
        InvestmentToUpdate, PerformanceEngineInvestmentUpdate, PortfolioToUpdate, QueueMessage, Updates,
    },
    rabbit_mq::RabbitMq,
};
use chrono::{Days, NaiveDate, Utc};
use futures::future::{join_all, try_join_all};
use lapin::{options::BasicPublishOptions, BasicProperties};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use tokio::sync::Semaphore;
use tracing::info;

use crate::{
    storage::{DbKeyPrefix, Storage, StorageWriter},
    utils::date::Ist,
};

use super::{
    client_state_computer::ClientStateComputer, model_state_computer::ModelStateComputer,
    portfolio_state_computer::PortfolioStateComputer, strategy_state_computer::StrategyStateComputer,
};

pub struct StateComputer {
    pub db_pool: Pool<Manager>,
    pub master_db_pool: Pool<Manager>,
    pub rabbit_mq_queue: Arc<RabbitMq>,
    pub redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pub clickhouse_pool: clickhouse::Client,
    pub date: NaiveDate,
    pub storage: Arc<Storage>,
}

impl StateComputer {
    async fn send_data_updation(&self) {
        let db_name = std::env::var("QUEUE_NAME").expect("QUEUE_NAME NOT FOUND");
        let queue_name = format!("{}/performance-updates", db_name);
        let queue = self
            .rabbit_mq_queue
            .create_channel(&queue_name)
            .await
            .expect("Expect queue init");

        let current_date = Utc::now().date_naive().to_ist();
        let date_yesterday = current_date.checked_sub_days(Days::new(1)).unwrap();

        let storage = StorageWriter::new(self.storage.db.clone());
        let portfolios = storage
            .get_all_portfolios_as_at(date_yesterday)
            .expect("expect to get all portfolios");

        let options = BasicPublishOptions::default();

        for portfolio in &portfolios {
            let investments = storage
                .get_portfolio_investment(&portfolio.portfolio_id, date_yesterday)
                .expect("Expect portfolio investment to be there");

            let investment_payload = QueueMessage {
                update_type: Updates::Investment(PerformanceEngineInvestmentUpdate {
                    investments: investments
                        .iter()
                        .map(|inv| InvestmentToUpdate {
                            average_price: inv.average_price,
                            current_price: inv.current_price,
                            dividends_paid: inv.dividends_paid,
                            holdings: inv.holdings,
                            invested_capital: inv.invested_capital,
                            irr_current: inv.irr_current,
                            irr_inception: inv.irr_inception,
                            market_cap: inv.market_cap.clone(),
                            market_value: inv.market_value,
                            realised_gain_loss: inv.realised_gain_loss,
                            total_capital: inv.total_capital,
                            isin: inv.isin.clone(),
                            unrealised_gain_loss: inv.unrealised_gain_loss,
                        })
                        .collect(),
                    portfolio_id: portfolio.portfolio_id.clone(),
                }),
            };

            let xirr_metrics = storage
                .get_xirr_metrics(&portfolio.portfolio_id, date_yesterday)
                .expect("Failed to get metrics")
                .expect("Expect xirr results");

            let twrr_metrics = storage
                .get_portfolio_twrr_metrics(&portfolio.portfolio_id, date_yesterday)
                .expect("Failed to get metrics")
                .expect("Expect xirr results");

            let portfolio_payload = QueueMessage {
                update_type: Updates::Portfolio(PortfolioToUpdate {
                    absolute_return_percent: portfolio.absolute_return_percent,
                    absolute_return_value: portfolio.absolute_return_value,
                    balance: portfolio.balance,
                    invested_capital: portfolio.invested_capital,
                    market_value: portfolio.market_value,
                    portfolio_id: portfolio.portfolio_id.clone(),
                    realised_gain_loss: portfolio.realised_gain_loss,
                    total_capital: portfolio.total_capital,
                    unrealised_gain_loss: portfolio.unrealised_gain_loss,
                    withdrawals: portfolio.withdrawals,
                    xirr: portfolio.xirr,
                    twrr_since_inception: portfolio.twrr,
                    xirr_1year: Decimal::from_f64(xirr_metrics.one_year.unwrap_or_default()).unwrap_or_default(),
                    twrr_1year: twrr_metrics.one_year.unwrap_or_default(),
                }),
            };

            for payload in [
                //serde_json::to_vec(&investment_payload).unwrap(),
                serde_json::to_vec(&portfolio_payload).unwrap(),
            ] {
                queue
                    .basic_publish("", &queue_name, options, &payload, BasicProperties::default())
                    .await
                    .expect("Expect queue to work");
            }
            break;
        }
    }

    async fn spawn_portfolio_tasks<F>(
        &self,
        portfolios: Vec<PortfolioForPerformanceEngine>,
        scratch: bool,
        compute_fn: F,
    ) where
        F: Fn(PortfolioStateComputer) -> tokio::task::JoinHandle<()> + Copy + Send + 'static,
    {
        let sem = Arc::new(Semaphore::new(20));
        let mut handlers = Vec::new();

        for ptf in portfolios {
            if scratch {
                let key = format!("{}-{}", DbKeyPrefix::LatestBlock.to_string(), ptf.id);
                self.storage.db.delete(key).unwrap();
            }

            let get_sem = sem.clone().acquire_owned().await.unwrap();
            let state = PortfolioStateComputer {
                clickhouse_pool: self.clickhouse_pool.clone(),
                client_id: ptf.client_id,
                portfolio_id: ptf.id,
                strategy_id: ptf.strategy_id,
                date: self.date,
                db_pool: self.db_pool.clone(),
                master_db_pool: self.master_db_pool.clone(),
                redis_pool: self.redis_pool.clone(),
                storage: self.storage.clone(),
                model_id: ptf.model_id,
            };

            let handler = compute_fn(state);
            handlers.push(tokio::spawn(async move {
                handler.await;
                drop(get_sem);
            }));
        }

        join_all(handlers).await;
    }

    pub async fn compute_from_scratch(&self) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = alpha_core_db::schema::portfolio::Portfolio::get_all_for_performance_engine(&mut conn)
            .await
            .unwrap();
        self.spawn_portfolio_tasks(portfolios, true, |mut state| {
            tokio::spawn(async move {
                let _ = state.compute_from_latest_block().await;
            })
        })
        .await;

        self.send_data_updation().await;
    }

    pub async fn compute_from_latest(&self) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = alpha_core_db::schema::portfolio::Portfolio::get_all_for_performance_engine(&mut conn)
            .await
            .unwrap();
        self.spawn_portfolio_tasks(portfolios, false, |mut state| {
            tokio::spawn(async move {
                let _ = state.compute_from_latest_block().await;
            })
        })
        .await;

        self.send_data_updation().await;
    }

    pub async fn compute_for_a_portfolio(&self, portfolio_id: &str, scratch: bool) {
        let mut conn = self.db_pool.get().await.unwrap();
        let ptf = alpha_core_db::schema::portfolio::Portfolio::get(&mut conn, portfolio_id.to_string())
            .await
            .unwrap()
            .unwrap();

        if scratch {
            let key = format!("{}-{}", DbKeyPrefix::LatestBlock.to_string(), ptf.id);
            self.storage.db.delete(key).unwrap();
        }

        let mut state = PortfolioStateComputer {
            clickhouse_pool: self.clickhouse_pool.clone(),
            client_id: ptf.client_id,
            portfolio_id: ptf.id,
            date: self.date,
            strategy_id: ptf.model_id.clone(), // FIXME:,
            db_pool: self.db_pool.clone(),
            master_db_pool: self.master_db_pool.clone(),
            redis_pool: self.redis_pool.clone(),
            storage: self.storage.clone(),
            model_id: ptf.model_id,
        };

        let _ = state.compute_from_latest_block().await;
    }

    pub async fn compute_for_a_portfolio_as_at(&self, portfolio_id: &str) {
        let mut conn = self.db_pool.get().await.unwrap();
        let ptf = alpha_core_db::schema::portfolio::Portfolio::get(&mut conn, portfolio_id.to_string())
            .await
            .unwrap()
            .unwrap();

        let mut state = PortfolioStateComputer {
            clickhouse_pool: self.clickhouse_pool.clone(),
            client_id: ptf.client_id,
            portfolio_id: ptf.id,
            date: self.date,
            db_pool: self.db_pool.clone(),
            strategy_id: ptf.model_id.clone(), //FIXME:
            master_db_pool: self.master_db_pool.clone(),
            redis_pool: self.redis_pool.clone(),
            storage: self.storage.clone(),
            model_id: ptf.model_id,
        };

        let _ = state.compute_from_latest_block_as_at(self.date).await;
    }

    pub async fn compute_as_at(&self) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = alpha_core_db::schema::portfolio::Portfolio::get_all_for_performance_engine(&mut conn)
            .await
            .unwrap();

        for ptf in portfolios {
            let mut state = PortfolioStateComputer {
                clickhouse_pool: self.clickhouse_pool.clone(),
                client_id: ptf.client_id,
                portfolio_id: ptf.id,
                date: self.date,
                db_pool: self.db_pool.clone(),
                strategy_id: ptf.strategy_id,
                master_db_pool: self.master_db_pool.clone(),
                redis_pool: self.redis_pool.clone(),
                storage: self.storage.clone(),
                model_id: ptf.model_id,
            };

            state.compute_from_latest_block_as_at(self.date).await;
        }
    }

    pub async fn compute_for_all_strategy(&self) {
        let strategies = StorageWriter::new(self.storage.db.clone()).get_all_strategies_with_start_date();
        let pool = self.db_pool.clone();

        for strategy in strategies {
            StrategyStateComputer {
                db_pool: pool.clone(),
                storage: self.storage.clone(),
            }
            .compute_for_a_strategy(&strategy.strategy_id, strategy.date)
            .await
            .unwrap();
        }
    }

    pub async fn compute_for_a_strategy_model(&self, strategy_model_id: &str) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = StrategyModels::get_portfolios_in_model(&mut conn, strategy_model_id)
            .await
            .unwrap();

        let mut handlers = Vec::new();
        for ptf in portfolios {
            let mut state = PortfolioStateComputer {
                clickhouse_pool: self.clickhouse_pool.clone(),
                client_id: "FIXME".to_string(),
                portfolio_id: ptf.portfolio_id.clone(),
                strategy_id: "".to_string(),
                date: self.date,
                db_pool: self.db_pool.clone(),
                master_db_pool: self.master_db_pool.clone(),
                redis_pool: self.redis_pool.clone(),
                storage: self.storage.clone(),
                model_id: "FIXME".to_string(),
            };
            handlers.push(tokio::spawn(async move {
                let _ = state.compute_from_latest_block().await;
            }));
        }

        try_join_all(handlers).await.unwrap();

        ModelStateComputer {
            db_pool: self.db_pool.clone(),
            storage: &self.storage,
        }
        .compute_for_a_model(strategy_model_id)
        .await
        .unwrap();
    }

    pub async fn compute_for_a_strategy(&self, strategy_id: &str) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = Strategies::get_portfolios_in_strategy(&mut conn, strategy_id)
            .await
            .unwrap();

        for ptf in portfolios {
            let key = format!("{}-{}", DbKeyPrefix::LatestBlock.to_string(), ptf.portfolio_id);
            self.storage.db.delete(key).unwrap();
            let mut state = PortfolioStateComputer {
                clickhouse_pool: self.clickhouse_pool.clone(),
                client_id: ptf.client_id,
                portfolio_id: ptf.portfolio_id,
                strategy_id: strategy_id.to_string(),
                date: self.date,
                db_pool: self.db_pool.clone(),
                master_db_pool: self.master_db_pool.clone(),
                redis_pool: self.redis_pool.clone(),
                storage: self.storage.clone(),
                model_id: ptf.model_id,
            };
            let _ = state.compute_from_latest_block().await;
        }
        self.compute_for_all_strategy().await;
    }

    pub async fn compute_for_all_clients_in_strategy(&self, strategy_id: &str) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = Strategies::get_portfolios_in_strategy(&mut conn, strategy_id)
            .await
            .unwrap();
        let clients: Vec<String> = portfolios.iter().map(|ptf| ptf.client_id.clone()).collect();

        for client_id in clients {
            ClientStateComputer { storage: &self.storage }
                .compute_for_client(&client_id)
                .await;
        }
    }

    pub async fn compute_from_scratch_for_a_client_portfolios(&self, client_id: &str) {
        let mut conn = self.db_pool.get().await.unwrap();
        let portfolios = Client::get_all_portfolios(&mut conn, client_id).await.unwrap();

        if portfolios.is_empty() {
            info!("No Portfolio Found for the Client");
            return;
        }

        let mut handlers = Vec::new();
        for ptf in portfolios {
            let mut state = PortfolioStateComputer {
                clickhouse_pool: self.clickhouse_pool.clone(),
                client_id: ptf.client_id.clone(),
                portfolio_id: ptf.id.clone(),
                strategy_id: "".to_string(),
                date: self.date,
                db_pool: self.db_pool.clone(),
                master_db_pool: self.master_db_pool.clone(),
                redis_pool: self.redis_pool.clone(),
                storage: self.storage.clone(),
                model_id: ptf.model_id.clone(),
            };
            handlers.push(tokio::spawn(async move {
                let _ = state.compute_from_latest_block().await;
            }));
        }
        try_join_all(handlers).await.unwrap();

        ClientStateComputer { storage: &self.storage }
            .compute_for_client(client_id)
            .await;
    }
}
