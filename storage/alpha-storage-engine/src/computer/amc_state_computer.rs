use alpha_core_db::connection::pool::{deadpool::managed::Pool, Manager};

use crate::storage::Storage;

/// Compute the state for each client from its portfolios
pub struct AmcStateComputer<'a> {
    /// Main Database connection pool
    pub db_pool: Pool<Manager>,

    /// State Storage
    pub storage: &'a Storage,
}

impl<'a> AmcStateComputer<'a> {
    /// This should be only called after all the portfolios for this client has been processed
    pub async fn compute(&self) {
        let mut _conn = self.db_pool.get().await.unwrap();
        // let portfolios = alpha_core_db::schema::client::get_all_for_performance_engine(&mut conn)
        //     .await
        //     .unwrap();

        // let portfolio_ids: Vec<String> = portfolios.iter().map(|p| p.id.clone()).collect();

        // let mut earliest_date = portfolios
        //     .iter()
        //     .min_by_key(|p| p.start_date)
        //     .map(|p| p.start_date)
        //     .unwrap()
        //     .date_naive();

        // let current_date = Utc::now().naive_utc().date();

        // let storage_tx = self.storage.begin_transaction();

        // while earliest_date < current_date {
        //     let aggregator = StateAggregator { storage: &self.storage };

        //     //Investments by Isin
        //     let client_aggregated_investment = self.storage.get_client_aggregated_investment(client_id, date)
        //     let aums = aggregator.portfolios_aum_aggregation(&portfolio_ids, earliest_date);

        //     let investment_aggregation = InvestmentAggregation {
        //         market_value: portfolio_state.market_value,
        //         invested_capital: portfolio_state.invested_capital,
        //         total_capital: portfolio_state.total_capital,
        //         gain_loss: portfolio_state.market_value - portfolio_state.invested_capital,
        //         total_investment: investments.len() as u64,
        //         xirr: Decimal::from_f64(0f64).unwrap(),
        //         weight_over_portfolio: dec!(0), //FIXME
        //     };

        //     storage_tx.insert_aggregated_investment_for_client(
        //         client_id,
        //         &investment_aggregation,
        //         earliest_date
        //     );

        //     earliest_date = earliest_date.checked_add_days(Days::new(1)).unwrap();
        // }

        // client_inv_tx.commit();
    }

    /// This should be only called after all the portfolios for this client has been processed
    /// Only process for the portfolio that is passed
    #[allow(unused)]
    pub fn compute_from_portfolio_id(&self, portfolio_ids: &Vec<String>) {}
}
