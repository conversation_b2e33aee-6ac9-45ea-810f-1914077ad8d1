use chrono::{Days, Utc};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;

use crate::{
    states::aggregation::InvestmentAggregation,
    storage::{Storage, StorageWriter},
    utils::StateAggregator,
};

/// Compute the state for each client from its portfolios
pub struct ClientStateComputer<'a> {
    /// State Storage
    pub storage: &'a Storage,
}

impl<'a> ClientStateComputer<'a> {
    /// This should be only called after all the portfolios for this client has been processed
    pub async fn compute_for_client(&self, client_id: &str) {
        let mut storage = StorageWriter::new(self.storage.db.clone());
        storage.begin_batch();

        let mut client_start_date = storage
            .get_client_start_date(client_id)
            .expect("Client Start Date Not Found");

        let current_date = Utc::now().naive_utc().date();

        while client_start_date < current_date {
            //Get all the portfolio ids on this Day
            let client_portfolios = storage.get_client_portfolios(client_id, client_start_date);
            let portfolio_ids: Vec<String> = client_portfolios.iter().map(|p| p.portfolio_id.clone()).collect();
            let mut storage = StorageWriter::new(self.storage.db.clone());
            let aggregator = StateAggregator { storage: &storage };

            //Investments by Isin
            let portfolio_state = aggregator.portfolio_state_aggregation(&portfolio_ids, client_start_date);
            let investments = aggregator.portfolios_investments_aggregation(&portfolio_ids, client_start_date);
            let aums = aggregator.portfolios_aum_aggregation(&portfolio_ids, client_start_date);

            let investment_aggregation = InvestmentAggregation {
                market_value: portfolio_state.market_value,
                invested_capital: portfolio_state.invested_capital,
                total_capital: portfolio_state.total_capital,
                gain_loss: portfolio_state.market_value - portfolio_state.invested_capital,
                total_investment: investments.len() as u64,
                xirr: Decimal::from_f64(0f64).unwrap(),
                weight_over_portfolio: dec!(0), //FIXME
            };

            storage
                .insert_client_investment(investments, client_id, client_start_date)
                .await;

            storage
                .insert_aggregated_investment_for_client(client_id, &investment_aggregation, client_start_date)
                .await;

            storage.insert_client_aum(&aums, client_id, client_start_date).await;

            client_start_date = client_start_date.checked_add_days(Days::new(1)).unwrap();
        }

        let _ = storage.commit_batch().await;
    }

    /// This should be only called after all the portfolios for this client has been processed
    /// Only process for the portfolio that is passed
    #[allow(unused)]
    pub fn compute_from_portfolio_id(&self, portfolio_ids: &Vec<String>) {}
}
