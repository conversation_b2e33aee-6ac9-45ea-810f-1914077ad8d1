use std::sync::Arc;
use alpha_core_db::connection::pool::{deadpool::managed::Pool, Manager};
use chrono::{Days, NaiveDate, Utc};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;

use crate::{
    states::{
        aggregation::InvestmentAggregation,
        metrics::portfolio::{TwrrResults, XirrResults},
    },
    storage::{Storage, StorageWriter},
    utils::{
        financial::strategy::{twrr::StrategyTwrrCalculator, xirr::StrategyXirrCalculator},
        StateAggregator,
    },
};

pub struct StrategyStateComputer {
    /// Main Database connection pool
    pub db_pool: Pool<Manager>,

    /// State Storage
    pub storage: Arc<Storage>,
}

impl StrategyStateComputer {
    pub async fn compute_for_a_strategy(
        &self,
        strategy_id: &str,
        mut strategy_start_date: NaiveDate,
    ) -> Result<(), String> {
        let mut storage_writer = StorageWriter::new(self.storage.db.clone());

        let current_date = Utc::now().naive_utc().date();
        while strategy_start_date < current_date {
            let client_portfolios = storage_writer.get_strategy_portfolios_as_at(strategy_id, strategy_start_date);
            let portfolio_ids: Vec<String> = client_portfolios.iter().map(|p| p.portfolio_id.clone()).collect();

            let aggregator = StateAggregator {
                storage: &storage_writer,
            };

            //Investments by Isin
            let portfolio_state = aggregator.portfolio_state_aggregation(&portfolio_ids, strategy_start_date);
            let investments = aggregator.portfolios_investments_aggregation(&portfolio_ids, strategy_start_date);
            let aum = aggregator.portfolios_aum_aggregation(&portfolio_ids, strategy_start_date);

            let investment_aggregation = InvestmentAggregation {
                market_value: portfolio_state.market_value,
                invested_capital: portfolio_state.invested_capital,
                total_capital: portfolio_state.total_capital,
                gain_loss: portfolio_state.market_value - portfolio_state.invested_capital,
                total_investment: investments.len() as u64,
                xirr: Decimal::from_f64(0f64).unwrap(),
                weight_over_portfolio: dec!(0), //FIXME
            };

            storage_writer
                .insert_strategy_investments(strategy_id, &investments, strategy_start_date)
                .await;

            storage_writer
                .insert_aggregated_investment_for_strategy(strategy_id, &investment_aggregation, strategy_start_date)
                .await;

            storage_writer
                .insert_strategy_aum(&aum, strategy_id, strategy_start_date)
                .await;

            let strategy_xirr_calculator = StrategyXirrCalculator {
                current_aum: aum,
                date: strategy_start_date,
                strategy_id: strategy_id.to_owned(),
            };

            let calculated_xirr = strategy_xirr_calculator.calculate_all_periods(&storage_writer).unwrap();

            let xirr_state = XirrResults {
                fytd: calculated_xirr.fytd,
                one_month: calculated_xirr.one_month,
                one_year: calculated_xirr.one_year,
                seven_year: calculated_xirr.seven_year,
                since_inception: calculated_xirr.since_inception,
                six_month: calculated_xirr.six_month,
                ten_year: calculated_xirr.ten_year,
                three_month: calculated_xirr.three_month,
                three_year: calculated_xirr.three_year,
                two_year: calculated_xirr.two_year,
                ytd: calculated_xirr.ytd,
            };

            storage_writer
                .insert_strategy_xirr_metrics(strategy_id, strategy_start_date, xirr_state)
                .await;

            let twrr_calculator = StrategyTwrrCalculator {
                date: strategy_start_date,
                strategy_id: strategy_id.to_string(),
            };

            let calculated_twrr = twrr_calculator.calculate_twrr_all_periods(&storage_writer).unwrap();

            let twrr_state = TwrrResults {
                fytd: calculated_twrr.fytd.map(|f| f.into()),
                one_month: calculated_twrr.one_month,
                one_year: calculated_twrr.one_year,
                seven_year: calculated_twrr.seven_year,
                since_inception: calculated_twrr.since_inception,
                six_month: calculated_twrr.six_month,
                ten_year: calculated_twrr.ten_year,
                three_month: calculated_twrr.three_month,
                three_year: calculated_twrr.three_year,
                two_year: calculated_twrr.two_year,
                ytd: calculated_twrr.ytd,
            };

            storage_writer
                .insert_strategy_twrr_metrics(strategy_id, strategy_start_date, twrr_state)
                .await;

            strategy_start_date = strategy_start_date.checked_add_days(Days::new(1)).unwrap();
        }

        storage_writer.commit_batch().await.unwrap();

        Ok(())
    }
}
