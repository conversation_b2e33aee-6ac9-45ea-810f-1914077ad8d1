use std::collections::HashMap;

use chrono::NaiveDate;
use rust_decimal::{prelude::FromPrimitive, Decimal};

use crate::{
    states::investment::Investment,
    storage::{Storage, StorageWriter},
    utils::{financial::xirr::InvestmentXirr, StateAggregator},
};

pub struct AnalyticsComputer<'a> {
    storage: &'a Storage,
}

impl<'a> AnalyticsComputer<'a> {
    pub fn compute(&self, portfolio_ids: &Vec<String>, date: NaiveDate) {
        let storage = StorageWriter::new(self.storage.db.clone());
        let aggregator = StateAggregator { storage: &storage };

        //Investments by Isin
        let mut investments = aggregator.portfolios_investments_aggregation(&portfolio_ids, date);

        let transactions_for_analytics = aggregator.portfolio_analytics_aggregation(&portfolio_ids, date);

        let investment_xirr = InvestmentXirr { date: date };

        //We should first do for Holdings(Isin level) and update the investments
        let transactions_for_analytics_by_isin =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.isin.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        //Update the Investments After Calculating XIRR
        for (isin, transactions) in transactions_for_analytics_by_isin {
            let matching_investments: &mut Investment = investments.iter_mut().find(|inv| inv.isin == isin).unwrap(); //We are sure inv exist

            let market_value: Decimal = matching_investments.market_value;

            let xirr = investment_xirr
                .calculate_for_holdings(market_value, &transactions)
                .unwrap_or_default();

            matching_investments.xirr = Decimal::from_f64(xirr).unwrap_or_default();
        }

        //Asset Class Level Analytics computation
        let transactions_for_analytics_by_asset_class =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.asset_class.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (asset_class, transactions) in transactions_for_analytics_by_asset_class {
            let matching_investments: Vec<&Investment> = investments
                .iter()
                .filter(|inv| inv.asset_class == asset_class)
                .collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_asset_class(market_value, &transactions);
        }

        //Asset Type Level Analytics computation
        let transactions_for_analytics_by_asset_type =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.asset_type.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (asset_type, transactions) in transactions_for_analytics_by_asset_type {
            let matching_investments: Vec<&Investment> =
                investments.iter().filter(|inv| inv.asset_type == asset_type).collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_asset_type(market_value, &transactions);
        }

        //Market Cap Level Analytics computation
        let transactions_for_analytics_by_market_cap =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.market_cap.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (market_cap, transactions) in transactions_for_analytics_by_market_cap {
            let matching_investments: Vec<&Investment> =
                investments.iter().filter(|inv| inv.market_cap == market_cap).collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_market_cap(market_value, &transactions);
        }

        //Industry Level Analytics computation
        let transactions_for_analytics_by_industry =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.industry.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (industry, transactions) in transactions_for_analytics_by_industry {
            let matching_investments: Vec<&Investment> =
                investments.iter().filter(|inv| inv.industry == industry).collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_industry(market_value, &transactions);
        }
    }
}
