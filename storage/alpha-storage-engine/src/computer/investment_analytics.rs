use std::collections::HashMap;

use crate::{
    states::{
        aggregation::InvestmentAggregation,
        analytics::{
            asset_class::AssetClassAllocation, industries::IndustriesAllocation,
            market_cap_allocation::MarketCapAllocation, TransactionForAnalytics,
        },
        investment::Investment,
    },
    storage::{AggregationType, StorageWriter},
    utils::financial::xirr::InvestmentXirr,
};
use chrono::{Days, NaiveDate};
use rayon::iter::{IntoParallelRefIterator as _, ParallelIterator};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;
use std::hash::Hash;

/// This is used to compute various kind of aggregation based on Investment Holdings
pub struct InvestmentAnalyticsComputer {
    date: NaiveDate,
}

impl InvestmentAnalyticsComputer {
    /// Creates a new instance of the analytics computer
    pub fn new(date: NaiveDate) -> Self {
        Self { date }
    }

    /// Main entry point for computing investment analytics
    pub async fn compute_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        transactions_for_analytics: &Vec<TransactionForAnalytics>,
        mut investments: Vec<Investment>,
        cash: Decimal,
    ) -> anyhow::Result<()> {
        let next_date = self.date.checked_add_days(Days::new(1)).unwrap();
        let investment_xirr = InvestmentXirr { date: next_date };
        let total_market_value: Decimal = investments.iter().map(|inv| inv.market_value).sum::<Decimal>();

        // Process investments at ISIN level
        self.process_isin_level_analytics(&investment_xirr, transactions_for_analytics, &mut investments)?;

        // Persist updated investments
        storage
            .insert_portfolio_investment(&investments, portfolio_id.to_owned(), next_date)
            .await;

        // Process analytics at different aggregation levels
        self.process_asset_class_analytics(
            storage,
            portfolio_id,
            &investment_xirr,
            transactions_for_analytics,
            &investments,
            total_market_value,
            next_date,
            cash,
        )
        .await?;

        self.process_asset_type_analytics(
            storage,
            portfolio_id,
            &investment_xirr,
            transactions_for_analytics,
            &investments,
            total_market_value,
            next_date,
        )
        .await?;

        self.process_market_cap_analytics(
            storage,
            portfolio_id,
            &investment_xirr,
            transactions_for_analytics,
            &investments,
            total_market_value,
            next_date,
        )
        .await?;

        self.process_industry_analytics(
            storage,
            portfolio_id,
            &investment_xirr,
            transactions_for_analytics,
            &investments,
            total_market_value,
            next_date,
        )
        .await?;

        Ok(())
    }

    fn group_transactions_by<'a, F, K>(
        &self,
        transactions: &'a [TransactionForAnalytics],
        key_fn: F,
    ) -> HashMap<K, Vec<&'a TransactionForAnalytics>>
    where
        F: Fn(&'a TransactionForAnalytics) -> K,
        K: Eq + Hash + Clone,
    {
        transactions.iter().fold(HashMap::new(), |mut acc, txn| {
            acc.entry(key_fn(txn)).or_insert_with(Vec::new).push(txn);
            acc
        })
    }

    // Process ISIN level analytics
    fn process_isin_level_analytics(
        &self,
        investment_xirr: &InvestmentXirr,
        transactions: &[TransactionForAnalytics],
        investments: &mut [Investment],
    ) -> anyhow::Result<()> {
        let transactions_by_isin = self.group_transactions_by(transactions, |txn| txn.isin.clone());

        let xirr_results: Vec<(String, Decimal)> = transactions_by_isin
            .par_iter()
            .map(|(isin, txns)| {
                let investment = investments.iter().find(|inv| inv.isin == *isin).unwrap();
                let market_value = investment.market_value;

                let xirr = investment_xirr
                    .calculate_for_holdings(market_value, txns)
                    .unwrap_or_default();

                (isin.clone(), Decimal::from_f64(xirr).unwrap_or_default())
            })
            .collect();

        // Update investments with calculated XIRR values
        for (isin, xirr_value) in xirr_results {
            if let Some(investment) = investments.iter_mut().find(|inv| inv.isin == isin) {
                investment.xirr = xirr_value.checked_mul(dec!(100)).unwrap_or_default();
            }
        }

        Ok(())
    }

    // Process asset class level analytics
    async fn process_asset_class_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        investment_xirr: &InvestmentXirr,
        transactions: &[TransactionForAnalytics],
        investments: &[Investment],
        mut total_market_value: Decimal,
        next_date: NaiveDate,
        cash: Decimal,
    ) -> anyhow::Result<()> {
        let transactions_by_asset_class = self.group_transactions_by(transactions, |txn| txn.asset_class.clone());
        let mut asset_class_allocation = AssetClassAllocation::default();
        total_market_value += cash;
        let aggregations_results: Vec<(String, InvestmentAggregation)> = transactions_by_asset_class
            .par_iter()
            .map(|(asset_class, txns)| {
                let matching_investments: Vec<&Investment> = investments
                    .iter()
                    .filter(|inv| inv.asset_class == *asset_class)
                    .collect();

                let market_value = matching_investments.iter().map(|inv| inv.market_value).sum();
                let invested_capital = matching_investments.iter().map(|inv| inv.invested_capital).sum();
                let total_capital = matching_investments.iter().map(|inv| inv.total_capital).sum();

                let xirr = investment_xirr
                    .calculate_for_asset_class(market_value, txns)
                    .unwrap_or_default();

                let weight = if total_market_value > dec!(0) {
                    (market_value / total_market_value) * dec!(100)
                } else {
                    dec!(0)
                };

                let aggregation = InvestmentAggregation {
                    market_value,
                    invested_capital,
                    total_capital,
                    gain_loss: market_value - invested_capital,
                    total_investment: matching_investments.len() as u64,
                    xirr: Decimal::from_f64(xirr)
                        .unwrap_or_default()
                        .checked_mul(dec!(100))
                        .unwrap_or_default(),
                    weight_over_portfolio: weight,
                };

                (asset_class.clone(), aggregation)
            })
            .collect();

        let mut aggregations = Vec::with_capacity(aggregations_results.len());

        // Process results sequentially
        for (asset_class, aggregation) in aggregations_results {
            asset_class_allocation.assign(&asset_class, aggregation.weight_over_portfolio);
            aggregations.push(aggregation);
        }

        let cash_weight = if total_market_value > dec!(0) {
            (cash / total_market_value) * dec!(100)
        } else {
            dec!(0)
        };

        asset_class_allocation.assign("cash", cash_weight);

        // Store results
        storage
            .insert_assetclass_allocation(portfolio_id, &asset_class_allocation, next_date)
            .await;

        storage
            .insert_aggregated_investment_for_portfolio(
                portfolio_id,
                AggregationType::AssetClass,
                &aggregations,
                next_date,
            )
            .await;

        Ok(())
    }

    async fn process_asset_type_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        investment_xirr: &InvestmentXirr,
        transactions: &[TransactionForAnalytics],
        investments: &[Investment],
        total_market_value: Decimal,
        next_date: NaiveDate,
    ) -> anyhow::Result<()> {
        let transactions_by_asset_type = self.group_transactions_by(transactions, |txn| txn.asset_type.clone());

        // Use Rayon to parallelize the processing
        let aggregations: Vec<InvestmentAggregation> = transactions_by_asset_type
            .par_iter()
            .map(|(asset_type, txns)| {
                let matching_investments: Vec<&Investment> =
                    investments.iter().filter(|inv| inv.asset_type == *asset_type).collect();

                let market_value = matching_investments.iter().map(|inv| inv.market_value).sum();
                let invested_capital = matching_investments.iter().map(|inv| inv.invested_capital).sum();
                let total_capital = matching_investments.iter().map(|inv| inv.total_capital).sum();

                let xirr = investment_xirr
                    .calculate_for_asset_type(market_value, txns)
                    .unwrap_or_default();

                let weight = if total_market_value > dec!(0) {
                    (market_value / total_market_value) * dec!(100)
                } else {
                    dec!(0)
                };

                InvestmentAggregation {
                    market_value,
                    invested_capital,
                    total_capital,
                    gain_loss: market_value - invested_capital,
                    total_investment: matching_investments.len() as u64,
                    xirr: Decimal::from_f64(xirr)
                        .unwrap_or_default()
                        .checked_mul(dec!(100))
                        .unwrap_or_default(),
                    weight_over_portfolio: weight,
                }
            })
            .collect();

        storage
            .insert_aggregated_investment_for_portfolio(
                portfolio_id,
                AggregationType::AssetType,
                &aggregations,
                next_date,
            )
            .await;

        Ok(())
    }

    async fn process_market_cap_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        investment_xirr: &InvestmentXirr,
        transactions: &[TransactionForAnalytics],
        investments: &[Investment],
        total_market_value: Decimal,
        next_date: NaiveDate,
    ) -> anyhow::Result<()> {
        let transactions_by_market_cap = self.group_transactions_by(transactions, |txn| txn.market_cap.clone());
        let mut market_cap_allocation = MarketCapAllocation::default();

        // Use Rayon for parallel processing
        let computation_results: Vec<(String, Decimal, InvestmentAggregation)> = transactions_by_market_cap
            .par_iter()
            .map(|(market_cap, txns)| {
                let matching_investments: Vec<&Investment> =
                    investments.iter().filter(|inv| inv.market_cap == *market_cap).collect();

                let market_value = matching_investments.iter().map(|inv| inv.market_value).sum();
                let invested_capital = matching_investments.iter().map(|inv| inv.invested_capital).sum();
                let total_capital = matching_investments.iter().map(|inv| inv.total_capital).sum();

                let xirr = investment_xirr
                    .calculate_for_market_cap(market_value, txns)
                    .unwrap_or_default();

                let weight = if total_market_value > dec!(0) {
                    (market_value / total_market_value) * dec!(100)
                } else {
                    dec!(0)
                };

                let aggregation = InvestmentAggregation {
                    market_value,
                    invested_capital,
                    total_capital,
                    gain_loss: market_value - invested_capital,
                    total_investment: matching_investments.len() as u64,
                    xirr: Decimal::from_f64(xirr)
                        .unwrap_or_default()
                        .checked_mul(dec!(100))
                        .unwrap_or_default(),
                    weight_over_portfolio: weight,
                };

                (market_cap.clone(), weight, aggregation)
            })
            .collect();

        // Process the results sequentially for allocation updates
        let mut aggregations = Vec::with_capacity(computation_results.len());
        for (market_cap, weight, aggregation) in computation_results {
            market_cap_allocation.assign_value(&market_cap, weight);
            aggregations.push(aggregation);
        }

        storage
            .insert_marketcap_allocation(portfolio_id, &market_cap_allocation, next_date)
            .await;

        storage
            .insert_aggregated_investment_for_portfolio(
                portfolio_id,
                AggregationType::MarketCap,
                &aggregations,
                next_date,
            )
            .await;

        Ok(())
    }

    // Process industry level analytics
    async fn process_industry_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        investment_xirr: &InvestmentXirr,
        transactions: &[TransactionForAnalytics],
        investments: &[Investment],
        total_market_value: Decimal,
        next_date: NaiveDate,
    ) -> anyhow::Result<()> {
        let transactions_by_industry = self.group_transactions_by(transactions, |txn| txn.industry.clone());

        // Use Rayon to parallelize the calculations
        let computation_results: Vec<(String, Decimal, Decimal, InvestmentAggregation)> = transactions_by_industry
            .par_iter()
            .map(|(industry, txns)| {
                let matching_investments: Vec<&Investment> =
                    investments.iter().filter(|inv| inv.industry == *industry).collect();

                let market_value = matching_investments.iter().map(|inv| inv.market_value).sum();
                let invested_capital = matching_investments.iter().map(|inv| inv.invested_capital).sum();
                let total_capital = matching_investments.iter().map(|inv| inv.total_capital).sum();

                let xirr = investment_xirr
                    .calculate_for_industry(market_value, txns)
                    .unwrap_or_default();

                let xirr_decimal = Decimal::from_f64(xirr)
                    .unwrap_or_default()
                    .checked_mul(dec!(100))
                    .unwrap_or_default();

                let weight = if total_market_value > dec!(0) {
                    (market_value / total_market_value) * dec!(100)
                } else {
                    dec!(0)
                };

                let aggregation = InvestmentAggregation {
                    market_value,
                    invested_capital,
                    total_capital,
                    gain_loss: market_value - invested_capital,
                    total_investment: matching_investments.len() as u64,
                    xirr: xirr_decimal,
                    weight_over_portfolio: weight,
                };

                (industry.clone(), weight, xirr_decimal, aggregation)
            })
            .collect();

        // Process the results sequentially to build the final collections
        let mut industry_allocations = Vec::with_capacity(computation_results.len());
        let mut aggregations = Vec::with_capacity(computation_results.len());

        for (industry, weight, xirr_decimal, aggregation) in computation_results {
            industry_allocations.push(IndustriesAllocation {
                name: industry,
                weight,
                xirr: xirr_decimal,
            });

            aggregations.push(aggregation);
        }

        storage
            .insert_industries_allocation(portfolio_id, &industry_allocations, next_date)
            .await;

        storage
            .insert_aggregated_investment_for_portfolio(
                portfolio_id,
                AggregationType::Industry,
                &aggregations,
                next_date,
            )
            .await;

        Ok(())
    }
}
