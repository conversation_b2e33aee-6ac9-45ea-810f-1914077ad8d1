use std::{collections::HashMap, sync::Arc};

use crate::{
    block::genesis_block::GenesisBlock,
    states::{
        enums::TransactionType,
        ledger::{Ledger, LedgerTransaction},
    },
    storage::{Storage, StorageWriter},
    types::ExternalDBState,
    utils::transaction_computer::{fetch_transactions_for_portfolio, Message},
};
use alpha_core_db::{
    clickhouse::bse_stock_price::bse_stock_price::BseSecurityPrice,
    connection::pool::{deadpool::managed::Pool, Manager},
};
use chrono::{Days, NaiveDate, Utc};
use lazy_static::lazy_static;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tokio::sync::{mpsc, RwLock};
use tracing::info;

lazy_static! {
    pub static ref GLOBAL_PRICE_DATA: RwLock<HashMap<String, f64>> = RwLock::new(HashMap::new());
    pub static ref GLOBAL_BSE_PRICE_DATA: RwLock<HashMap<String, BseSecurityPrice>> = RwLock::new(HashMap::new());
}

pub struct PortfolioStateComputer {
    /// Main Database connection pool
    pub db_pool: Pool<Manager>,

    /// A pool of master_data db connection (TODO: This should be removed)
    pub master_db_pool: Pool<Manager>,

    /// A pool of redis Connections
    pub redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,

    /// A Pool Of Clickhouse connections
    pub clickhouse_pool: clickhouse::Client,

    /// Client Id of the state
    pub client_id: String,

    /// Portfolio Id of the state
    pub portfolio_id: String,

    /// Strategy Id of the state
    pub strategy_id: String,

    ///Model id of the State
    pub model_id: String,

    /// State For the Portfolio to be computed
    /// If Genesis is found it will starts from computing this date
    pub date: NaiveDate,

    /// State Storage
    pub storage: Arc<Storage>,
}

impl PortfolioStateComputer {
    /// Compute from the Latest Available Block
    pub async fn compute_from_latest_block(&mut self) -> anyhow::Result<()> {
        let mut storage = StorageWriter::new(self.storage.db.clone());

        let latest_block_date = storage.get_latest_block(&self.portfolio_id);

        //If latest block is not found compute the genesis firstz
        if latest_block_date.is_none() {
            self.compute_genesis().await?;
            info!("Finished Computing Genesis Block");
        }

        // We are sure that after computing genesis Latest Block State will be available
        let mut latest_block_date = storage.get_latest_block(&self.portfolio_id).unwrap();
        let current_date = Utc::now().naive_utc().date();

        let (tx, mut rcv) = mpsc::unbounded_channel::<Message>();

        let next_date = latest_block_date.checked_add_days(Days::new(1)).unwrap();

        if latest_block_date >= current_date {
            info!("Portfolio is In Latest State");
            return Ok(());
        }

        // This spawn a thread to get transactions from the Database
        // We pass a channel sender also so that we can receive these fetched transaction in the below operations
        fetch_transactions_for_portfolio(self.db_pool.clone(), tx, self.portfolio_id.clone(), next_date);

        //This gets the transaction fetched by the transaction fetcher
        info!("Fetching Transactions For Next State");
        while let Some(message) = rcv.recv().await {
            let txn = message.content;

            let mut latest_block_state = storage
                .get_portfolio_block(&self.portfolio_id, latest_block_date)
                .unwrap();
            info!(
                "Apply All Eligible Transaction to the portfolio state on {}",
                latest_block_date
            );

            let external_db_state = ExternalDBState {
                clickhouse_client: self.clickhouse_pool.clone(),
                db_pool: self.db_pool.clone(),
                master_pool: self.master_db_pool.clone(),
                redis: self.redis_pool.clone(),
            };
            latest_block_state
                .apply_transactions(external_db_state, self.storage.clone(), txn)
                .await;

            latest_block_date = message.date;

            //Make sure to ack after the processing
            if message.date >= current_date {
                message.ack_sender.send(true).unwrap();
            } else {
                message.ack_sender.send(false).unwrap();
            }
        }

        Ok(())
    }

    /// This compute the geneis Block
    /// It will have the date of least date in entire Transaction Types(CR, IE, InvTcn)
    pub async fn compute_genesis(&self) -> anyhow::Result<()> {
        let mut genesis_block = GenesisBlock {
            clickhouse_pool: self.clickhouse_pool.clone(),
            client_id: self.client_id.clone(),
            model_id: self.model_id.clone(),
            strategy_id: self.strategy_id.clone(),
            date: self.date.clone(),
            db_pool: self.db_pool.clone(),
            master_db_pool: self.master_db_pool.clone(),
            portfolio_id: self.portfolio_id.clone(),
            redis_pool: self.redis_pool.clone(),
            storage_db: self.storage.clone(),
            transactions: Vec::new(),
        };

        info!("Computing Genesis Block");
        genesis_block.compute_genesis().await
    }

    /// Compute from the As At Date block
    pub async fn compute_from_latest_block_as_at(&mut self, date: NaiveDate) -> anyhow::Result<()> {
        let mut storage = StorageWriter::new(self.storage.db.clone());
        let latest_block_date = storage.get_latest_block(&self.portfolio_id);

        //If latest block is not found compute the genesis firstz
        if latest_block_date.is_none() {
            return Err(anyhow::anyhow!("Genesis Block Not available"));
        }

        // We are sure that after computing genesis Latest Block State will be available
        let _current_date = Utc::now().naive_utc().date();

        let (tx, mut rcv) = mpsc::unbounded_channel::<Message>();

        let latest_block_date = date.checked_sub_days(Days::new(1)).unwrap();

        // This spawn a thread to get transactions from the Database
        // We pass a channel sender also so that we can receive these fetched transaction in the below operations
        fetch_transactions_for_portfolio(self.db_pool.clone(), tx, self.portfolio_id.clone(), date);

        //This gets the transaction fetched by the transaction fetcher
        info!("Fetching Transactions For Next State");
        while let Some(message) = rcv.recv().await {
            let txn = message.content;

            let mut latest_block_state = storage
                .get_portfolio_block(&self.portfolio_id, latest_block_date)
                .unwrap();
            info!(
                "Apply All Eligible Transaction to the portfolio state on {}",
                latest_block_date
            );

            let external_db_state = ExternalDBState {
                clickhouse_client: self.clickhouse_pool.clone(),
                db_pool: self.db_pool.clone(),
                master_pool: self.master_db_pool.clone(),
                redis: self.redis_pool.clone(),
            };

            latest_block_state
                .apply_transactions(external_db_state, self.storage.clone(), txn)
                .await;
            message.ack_sender.send(true).unwrap();
            break;
        }

        Ok(())
    }
}

/// Generate the Ledger Entries from All Kind of Transactions
#[allow(unused)]
fn compute_ledger_entries_from_transactions(transactions: Vec<&dyn LedgerTransaction>) -> (Vec<Ledger>, Decimal) {
    let mut ledgers = Vec::new();
    let mut current_balance = dec!(0);

    for txn in transactions {
        let txn_type = txn.get_transaction_type();
        let txn_amount = txn.get_amount();
        if txn_type == TransactionType::Buy {
            current_balance -= txn_amount;
        } else {
            current_balance += txn_amount;
        }
        let ledger = Ledger {
            running_balance: current_balance,
            amount: txn_amount,
            description: txn.get_description(),
            settlement_date: txn.get_settlement_date(),
            transaction_date: txn.get_transaction_date(),
            transaction_sub_type: txn.get_transaction_sub_type(),
            transaction_type: txn.get_transaction_type(),
        };

        ledgers.push(ledger);
    }

    (ledgers, current_balance)
}
