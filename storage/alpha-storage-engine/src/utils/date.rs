use chrono::{NaiveDate, NaiveDateTime, FixedOffset, TimeZone};

pub trait Ist {
    fn to_ist(&self) -> NaiveDate;
}

impl Ist for NaiveDate {
    fn to_ist(&self) -> NaiveDate {
        let naive_dt = self.and_hms_opt(0, 0, 0).unwrap();
        let utc_dt = chrono::Utc.from_utc_datetime(&naive_dt);
        let ist_offset = FixedOffset::east_opt(5 * 3600 + 30 * 60).unwrap(); // +5:30
        let ist_dt = utc_dt.with_timezone(&ist_offset);
        ist_dt.date_naive()
    }
}
