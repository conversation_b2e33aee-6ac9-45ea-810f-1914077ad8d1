use alpha_core_db::connection::pool::{
    deadpool::managed::Object,
    tiberius::{self, ExecuteResult},
    Manager,
};
use chrono::NaiveDate;

use crate::storage::StorageWriter;

pub async fn update_investments(
    db_conn: &mut Object<Manager>,
    storage: &StorageWriter,
    portfolio_id: &str,
    date: NaiveDate,
) -> Result<ExecuteResult, tiberius::error::Error> {
    let investments = storage
        .get_portfolio_investment(portfolio_id, date)
        .expect("Expected Investments to be there in the db ");

    let mut query = String::from("");

    for inv in investments {
        //Create a query to update in the sql server investments table
        let q = format!(
            "
        UPDATE 
            Investments 
        SET
            CurrentPrice = {},
            CurrentHolding = {},
            TotalCapital = {},
            InvestedCapital = {},
            MarketValue = {},
            RealisedGainLoss = {},
            UnRealisedGainLoss = {},
            Dividends = {},
            IrrSinceInception ={},
            IrrCurrent = {},
            MarketCap = {},
            AveragePrice = {};
            ",
            inv.current_price,
            inv.holdings,
            inv.total_capital,
            inv.invested_capital,
            inv.market_value,
            inv.realised_gain_loss,
            inv.realised_gain_loss,
            inv.dividends_paid,
            inv.irr_inception,
            inv.irr_current,
            inv.market_cap,
            inv.average_price
        );

        query += &q;
    }

    db_conn.execute(query, &[]).await
}
