use chrono::NaiveDate;
use rkyv::{Archive, Deserialize, Serialize};
use rust_decimal_macros::dec;

use crate::{states::aum::Aum, storage::StorageWriter};

use super::financial::xirr::{PortfolioXirrCalculator, XirrResults};

#[derive(Serialize, Deserialize, Archive, Debug, Clone)]
pub enum BenchmarkIndices {
    SpBseSensex = 16,
    SpBse500 = 22,
    Nifty50 = 62,
    Index379 = 379,
    Index450 = 450,
    Index60 = 60,
    Index437 = 437,
}

impl BenchmarkIndices {
    pub fn from_number(value: u32) -> Option<Self> {
        match value {
            16 => Some(Self::SpBseSensex),
            22 => Some(Self::SpBse500),
            62 => Some(Self::Nifty50),
            379 => Some(Self::Index379),
            450 => Some(Self::Index450),
            60 => Some(Self::Index60),
            437 => Some(Self::Index437),
            _ => None,
        }
    }

    // Get all variants as a slice
    pub fn all() -> &'static [BenchmarkIndices] {
        static VARIANTS: [BenchmarkIndices; 2] = [
            BenchmarkIndices::Nifty50,
            BenchmarkIndices::SpBse500, // BenchmarkIndices::SpBseSensex,
                                        // BenchmarkIndices::Index379,
                                        // BenchmarkIndices::Index450,
                                        // BenchmarkIndices::Index60,
                                        // BenchmarkIndices::Index437,
        ];
        &VARIANTS
    }

    // Get the integer value of the variant
    #[allow(unused)]
    fn value(&self) -> i32 {
        match self {
            BenchmarkIndices::SpBseSensex => 16,
            BenchmarkIndices::SpBse500 => 22,
            BenchmarkIndices::Nifty50 => 62,
            BenchmarkIndices::Index379 => 379,
            BenchmarkIndices::Index450 => 450,
            BenchmarkIndices::Index60 => 60,
            BenchmarkIndices::Index437 => 437,
        }
    }

    pub fn calculate_xirr(
        &self,
        portfolio_id: String,
        date: NaiveDate,
        current_aum: Aum,
        storage: &StorageWriter,
    ) -> Result<XirrResults, String> {
        let aums = storage.get_aum_for_analytics_range(date, &portfolio_id);

        let aums: Vec<Aum> = aums.iter().cloned().filter(|a| a.net_cash_flows != dec!(0)).collect();
        let benchmark = PortfolioXirrCalculator {
            aums,
            current_aum,
            date,
            portfolio_id,
        };

        let benchmark_result = benchmark.calculate_benchmark_all_periods(storage, self.clone());
        benchmark_result
    }

    pub fn calculate_twrr(&self) {}
}

impl std::fmt::Display for BenchmarkIndices {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BenchmarkIndices::SpBseSensex => write!(f, "S&P BSE Sensex"),
            BenchmarkIndices::SpBse500 => write!(f, "S&P BSE 500"),
            BenchmarkIndices::Nifty50 => write!(f, "Nifty 50"),
            BenchmarkIndices::Index379 => write!(f, "Index 379"),
            BenchmarkIndices::Index450 => write!(f, "Index 450"),
            BenchmarkIndices::Index60 => write!(f, "Index 60"),
            BenchmarkIndices::Index437 => write!(f, "Index 437"),
        }
    }
}
