use rust_decimal::Decimal;
use rust_decimal_macros::dec;

use crate::states::transaction::Transaction;

pub fn calculate_daily_twrr(start_value: Decimal, end_value: Decimal, transactions: Vec<Transaction>) -> Decimal {
    // If no transactions, simple return calculation
    if transactions.is_empty() {
        return (end_value / start_value) - dec!(1.0);
    }

    // Calculate sub-period returns
    let mut sub_period_returns: Vec<Decimal> = Vec::new();
    let mut current_value = start_value;

    for (i, txn) in transactions.iter().enumerate() {
        // For first transaction, use start_value as beginning value
        let begin_value = if i == 0 { start_value } else { current_value };

        // Adjust for the transaction
        current_value += txn.amount;

        // Calculate period return
        let period_return = if i == transactions.len() - 1 {
            // For last transaction, use end_value
            (end_value / begin_value) - (dec!(1.0))
        } else {
            // For intermediate periods, use value after transaction
            (current_value / begin_value) - (dec!(1.0))
        };

        sub_period_returns.push(dec!(1.0) + period_return);
    }

    // Calculate geometric link of all sub-period returns
    let daily_twrr = sub_period_returns.iter().fold(dec!(1.0), |acc, &x| acc * x) - dec!(1.0);
    daily_twrr
}
