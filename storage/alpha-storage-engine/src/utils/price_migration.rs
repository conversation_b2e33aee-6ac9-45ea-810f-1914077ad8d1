use std::sync::Arc;

use alpha_core_db::clickhouse::{
    benchmark_price::BenchmarkPrice, bse_stock_price::bse_stock_price::BseSecurityPrice, create_clickhouse_client,
    isin_history::IsinHistory, mutual_fund_price::mutual_fund_price::MutualFundSecurityPrice,
    nse_stock_price::nse_stock_price::NseSecurityPrice,
};
use chrono::{Days, NaiveDate};
use clickhouse::Client;
use rocksdb::{ColumnFamilyDescriptor, Options as RocksOptions, WriteBatch, DB};
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};

use crate::{
    chrono_to_time_date, convert_time_date_to_chrono_naive_date, storage::DbKeyPrefix,
    utils::benchmark::BenchmarkIndices,
};

#[derive(Debug, Serialize, Deserialize)]
struct StockPrice {
    symbol: String,
    timestamp: i64,
    open: f64,
    high: f64,
    low: f64,
    close: f64,
    volume: i64,
}

pub struct Migration {
    clickhouse_pool: Client,
    rocksdb: DB,
    batch_size: usize,
}

impl Migration {
    pub async fn new(rocksdb_path: &str, batch_size: usize) -> Result<Self, ()> {
        let clickhouse_pool = create_clickhouse_client();

        // Configure and open RocksDB
        let mut opts = RocksOptions::default();
        opts.create_if_missing(true);
        opts.set_max_open_files(300000);
        opts.set_write_buffer_size(64 * 1024 * 1024); // 64MB
        opts.set_max_write_buffer_number(3);
        opts.set_target_file_size_base(64 * 1024 * 1024); // 64MB
        opts.set_compression_type(rocksdb::DBCompressionType::Lz4);
        opts.create_missing_column_families(true);

        // Configure CF options with prefix extractor for better performance
        let mut cf_opts = RocksOptions::default();
        cf_opts.set_compression_type(rocksdb::DBCompressionType::Lz4);
        cf_opts.create_missing_column_families(true);

        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", RocksOptions::default()),
            ColumnFamilyDescriptor::new("nse_security_price", cf_opts.clone()),
            ColumnFamilyDescriptor::new("bse_security_price", cf_opts.clone()),
            ColumnFamilyDescriptor::new("mf_security_price", cf_opts.clone()),
            ColumnFamilyDescriptor::new("isin_history", cf_opts.clone()),
            ColumnFamilyDescriptor::new("isin_history_old", cf_opts.clone()),
            ColumnFamilyDescriptor::new("benchmark_price", cf_opts.clone()),
        ];

        // Open DB with column families
        let rocksdb = DB::open_cf_descriptors(&opts, rocksdb_path, cf_descriptors).unwrap();

        Ok(Self {
            clickhouse_pool,
            rocksdb,
            batch_size,
        })
    }

    pub async fn migrate_price_as_at(
        rocksdb: Arc<rocksdb::DB>,
        clickhouse_pool: Client,
        date: NaiveDate,
    ) -> anyhow::Result<()> {
        let date = chrono_to_time_date(date);
        let cf_stocks = rocksdb.cf_handle("nse_security_price").unwrap();
        let query = format!(
            "
                SELECT
                    *
                FROM NseStockPrices
                WHERE
                    date = '{}'

            ",
            date
        );

        let rows = clickhouse_pool.query(&query).fetch_all::<NseSecurityPrice>().await?;

        println!("{:?}", rows.len());

        for row in rows {
            // Create compound key
            let key = format!("{}-{}", row.isin, row.date);

            // Serialize value to JSON
            let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

            rocksdb.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes()).unwrap();
        }

        let cf_stocks = rocksdb.cf_handle("bse_security_price").unwrap();
        let query = format!(
            "
                SELECT
                    *
                FROM BseStockPrices
                WHERE
                    date = '{}'

        ",
            date
        );

        let rows = clickhouse_pool
            .query(&query)
            .fetch_all::<BseSecurityPrice>()
            .await
            .unwrap();

        for row in rows {
            // Create compound key
            let key = format!("{}-{}", row.isin, row.date);

            // Serialize value to JSON
            let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

            rocksdb.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes()).unwrap();
        }

        let cf_stocks = rocksdb.cf_handle("mf_security_price").unwrap();

        let query = format!(
            "
                SELECT
                    *
                FROM MutualFundPrices
                WHERE
                    date = '{}'

        ",
            date
        );

        let rows = clickhouse_pool
            .query(&query)
            .fetch_all::<MutualFundSecurityPrice>()
            .await
            .unwrap();

        for row in rows {
            // Create compound key
            let key = format!("{}-{}", row.isin, row.date);

            // Serialize value to JSON
            let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

            rocksdb.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes()).unwrap();
        }

        Ok(())
    }

    pub async fn migrate_nse(&self) -> Result<(), ()> {
        let mut offset = 0;
        println!("Started Migration");
        let cf_stocks = self.rocksdb.cf_handle("nse_security_price").unwrap();
        loop {
            let client = &self.clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    *
                FROM NseStockPrices
                ORDER BY isin, date
                LIMIT {}
                OFFSET {}
                "#,
                self.batch_size, offset
            );

            let mut cursor = client.query(&query).fetch::<NseSecurityPrice>().unwrap();

            let mut batch = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.unwrap() {
                let row = row_result;

                // Create compound key
                let key = format!("{}-{}", row.symbol, row.date);

                // Serialize value to JSON
                let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

                batch.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes());

                // Create compound key
                let key = format!("{}-{}", row.isin, row.date);

                // Serialize value to JSON
                let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

                batch.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes());

                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            self.rocksdb.write(batch).unwrap();
            println!("Processed {} records at offset {}", count, offset);

            offset += self.batch_size;
        }

        Ok(())
    }

    pub async fn migrate_bse(&self) -> Result<(), ()> {
        let mut offset = 0;
        println!("Started Migration");
        let cf_stocks = self.rocksdb.cf_handle("bse_security_price").unwrap();
        loop {
            let client = &self.clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    *
                FROM BseStockPrices
                ORDER BY isin, date
                LIMIT {}
                OFFSET {}
                "#,
                self.batch_size, offset
            );

            let mut cursor = client.query(&query).fetch::<BseSecurityPrice>().unwrap();

            let mut batch = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.unwrap() {
                let row = row_result;

                // Create compound key
                let key = format!("{}-{}", row.isin, row.date);

                // Serialize value to JSON
                let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

                batch.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes());
                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            self.rocksdb.write(batch).unwrap();
            println!("Processed {} records at offset {}", count, offset);

            offset += self.batch_size;
        }

        Ok(())
    }

    pub async fn migrate_mf(&self) -> Result<(), ()> {
        let mut offset = 0;
        println!("Started Migration OF MF");
        let cf_stocks = self.rocksdb.cf_handle("mf_security_price").unwrap();
        loop {
            let client = &self.clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    *
                FROM MutualFundPrices
                where
                isin = '{}'
                ORDER BY isin, date
                LIMIT {}
                OFFSET {}
                "#,
                "INF179KB1HT1", self.batch_size, offset
            );

            let mut cursor = client.query(&query).fetch::<MutualFundSecurityPrice>().unwrap();

            let mut batch = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.unwrap() {
                let row = row_result;

                // Create compound key
                let key = format!("{}-{}", row.isin, row.date);

                // Serialize value to JSON
                let value = serde_json::to_string(&(row.price as f64 / 10000f64)).unwrap();

                batch.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes());
                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            self.rocksdb.write(batch).unwrap();
            println!("Processed {} records at offset {}", count, offset);

            offset += self.batch_size;
        }

        Ok(())
    }

    pub async fn migrate_isin_history(&self) -> Result<(), ()> {
        let mut offset = 0;
        println!("Started Migration For Isin History");
        let cf_stocks = self.rocksdb.cf_handle("isin_history").unwrap();
        let cf_stocks_old = self.rocksdb.cf_handle("isin_history_old").unwrap();
        loop {
            let client = &self.clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    OldISIN,
                    NewISIN
                FROM IsinChangeHistory
                LIMIT {}
                OFFSET {}
                "#,
                self.batch_size, offset
            );

            let mut cursor = client.query(&query).fetch::<IsinHistory>().unwrap();

            let mut batch = WriteBatch::default();
            let mut batch_old = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.unwrap() {
                println!("{:?}", row_result);
                let row = row_result;

                // Create compound key
                let key = row.NewISIN;

                // Serialize value to JSON
                let value = row.OldISIN;

                batch.put_cf(&cf_stocks, key.as_bytes(), value.as_bytes());
                batch_old.put_cf(&cf_stocks_old, value.as_bytes(), key.as_bytes());
                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            self.rocksdb.write(batch).unwrap();
            self.rocksdb.write(batch_old).unwrap();
            println!("Processed {} records at offset {}", count, offset);

            offset += self.batch_size;
        }

        Ok(())
    }

    pub async fn migrate_benchmark_prices(&self) {
        println!("Started Migration For Benchmark Price History");
        let cf_benchmark_price = self.rocksdb.cf_handle("benchmark_price").unwrap();
        let mut offset = 0;
        let mut previous_row: Option<crate::states::benchmark::BenchmarkPrice> = None;
        loop {
            let client = &self.clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    IndexCode,
                    FullName,
                    Date,
                    Open,
                    Close,
                    High,
                    Low
                FROM MarketIndexData
                ORDER BY Date ASC
                LIMIT {}
                OFFSET {}
                "#,
                self.batch_size, offset
            );

            let mut cursor = client
                .query(&query)
                .fetch::<BenchmarkPrice>()
                .expect("failed to parse benchmark price to type");

            let mut batch = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.expect("Expect Benchmark fetch not to fail") {
                if let Some(previous_row) = previous_row.clone() {
                    let date_received = convert_time_date_to_chrono_naive_date(row_result.date);
                    let mut next_expected_date = previous_row.date.checked_add_days(Days::new(1)).unwrap();

                    while next_expected_date < date_received {
                        //Insert the last entry again with the next_expected_date

                        let key = format!(
                            "{}:{}:{}",
                            DbKeyPrefix::BenchmarkPrice,
                            previous_row.index,
                            next_expected_date
                        );
                        let ser_value = rkyv::to_bytes::<_, 256>(&previous_row).unwrap();
                        batch.put_cf(&cf_benchmark_price, key.as_bytes(), ser_value);
                        next_expected_date = next_expected_date.checked_add_days(Days::new(1)).unwrap();
                    }
                }

                let row = row_result;
                let index = BenchmarkIndices::from_number(row.index_code);
                if let Some(index) = index{
                    let key = format!("{}:{}:{}", DbKeyPrefix::BenchmarkPrice, index, row.date);
                    let value = crate::states::benchmark::BenchmarkPrice {
                        index,
                        close: Decimal::from_f64(row.close as f64 / 10000f64).unwrap(),
                        change: dec!(0),
                        date: convert_time_date_to_chrono_naive_date(row.date),
                        high: Decimal::from_f64(row.high as f64 / 10000f64).unwrap(),
                        low: Decimal::from_f64(row.low as f64 / 10000f64).unwrap(),
                        name: row.full_name.to_string(),
                        open: Decimal::from_f64(row.open as f64 / 10000f64).unwrap(),
                    };
    
                    let ser_value = rkyv::to_bytes::<_, 256>(&value).unwrap();
                    batch.put_cf(&cf_benchmark_price, key.as_bytes(), ser_value);
                    previous_row = Some(value);
                }else{
                    println!("Index {:?} not known",row.index_code);
                }
                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            self.rocksdb
                .write(batch)
                .expect("benchmark price write to rocksdb failed");

            println!("Processed {} records at offset {}", count, offset);

            offset += self.batch_size;
        }
    }

    pub async fn migrate_benchmark_price_as_at(rocksdb: Arc<rocksdb::DB>, clickhouse_pool: Client, date: NaiveDate) {
        println!("Started Migration For Benchmark Price History");
        let cf_benchmark_price = rocksdb.cf_handle("benchmark_price").unwrap();
        let offset = 0;
        loop {
            let client = &clickhouse_pool;

            // Query data in batches
            let query = format!(
                r#"
                SELECT
                    OldISIN,
                    NewISIN
                FROM IsinChangeHistory
                "#,
            );

            let mut cursor = client
                .query(&query)
                .fetch::<IsinHistory>()
                .expect("failed to parse benchmark price to type");

            let mut batch = WriteBatch::default();
            let mut count = 0;

            while let Some(row_result) = cursor.next().await.expect("Expect Benchmark fetch not to fail") {
                let row = row_result;

                let key = row.NewISIN;

                let value = row.OldISIN;

                batch.put_cf(&cf_benchmark_price, key.as_bytes(), value.as_bytes());
                count += 1;
            }

            if count == 0 {
                break;
            }

            // Write batch to RocksDB
            rocksdb.write(batch).expect("benchmark price write to rocksdb failed");
            println!("Processed {} records at offset {}", count, offset);
        }
    }
}
