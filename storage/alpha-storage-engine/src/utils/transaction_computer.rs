use core::panic;
use std::{collections::BTreeMap, sync::Arc};

use alpha_core_db::{
    connection::pool::{deadpool::managed::Pool, Manager},
    types::storage_engine::StorageEngineTransactions,
};
use chrono::{Days, NaiveDate, Utc};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use tokio::sync::{mpsc::UnboundedSender, oneshot, RwLock};

use crate::{
    states::{
        capital_register::CapitalRegister, enums::TransactionType, legacy_ledger::LegacyLedger,
        peak_margin::PeakMargin, receivable_payable::ReceivablePayable, transaction::Transaction,
    },
    types::EligbleStateTransitionTransactions,
};

pub struct TransactionComputer;

impl TransactionComputer {
    /// This calculated the total capital, withdrawal,capital that is invested
    /// As well as the dividends paid from the transactions
    pub fn calculate_capital_value(&self, transactions: &Vec<&Transaction>) -> (Decimal, Decimal, Decimal) {
        //Just the total capital overall the user has
        let total_capital: Decimal = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| txn.amount)
            .sum();

        //The capital that is withdrawn
        let total_withdrawal: Decimal = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .map(|txn| txn.amount)
            .sum();

        let dividends_paid: Decimal = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::DividendPaid)
            .map(|txn| txn.amount)
            .sum();

        (total_capital, total_withdrawal, dividends_paid)
    }

    /// Transaction passed to this function should be the transaction for a single security
    pub fn calculate_unrealised_gain_lose(&self, transactions: &Vec<Transaction>) -> Decimal {
        let realized_gain_loss = dec!(0);

        //Get the total quantities of the securities that is sold
        let _quantity_sold: Decimal = transactions.iter().map(|txn| txn.quantity).sum();

        realized_gain_loss
    }

    /// Calculates the change in holding for the given set of transaction
    /// It can be negative/positive
    /// Should only pass transaction of a single security
    /// If you pass transactions of multiple security then it will mess up there is no validation
    pub fn calculate_change_in_holdings_from_transactions(&self, transactions: &Vec<&Transaction>) -> Decimal {
        let mut current_bal = dec!(0);
        for txn in transactions.iter() {
            if txn.transaction_type == TransactionType::Buy {
                current_bal += txn.quantity;
            } else if txn.transaction_type == TransactionType::Sell {
                current_bal -= txn.quantity;
            }
        }

        current_bal.round_dp(4)
    }

    pub fn long_short_term_holdings(&self, asset_class: &str, transactions: &Vec<&Transaction>) -> (Decimal, Decimal) {
        let mut long = dec!(0);
        let mut short = dec!(0);

        let current_date = chrono::Local::now().naive_local().date();

        let current_held_quantity_txns: Vec<&Transaction> = transactions
            .iter()
            .map(|&txn| txn)
            .filter(|txn| txn.transaction_type == TransactionType::Buy && txn.unrealised_holding > dec!(0))
            .collect();

        let long_short_threshold = match asset_class.to_lowercase().as_str() {
            "debt" | "commodity" | "realestate" | "fixedincome" => 3.0,
            _ if asset_class.to_lowercase().contains("gold") => 3.0,
            _ => 1.0,
        };

        for holding in current_held_quantity_txns {
            let years_difference = current_date.years_since(holding.cgt_date.into()).unwrap_or(0) as f64;

            if years_difference > long_short_threshold {
                long += holding.unrealised_holding;
            } else {
                short += holding.unrealised_holding;
            }
        }

        (long, short)
    }
}

#[derive(Debug)]
pub struct Message {
    pub content: EligbleStateTransitionTransactions,
    pub ack_sender: oneshot::Sender<bool>,
    pub date: NaiveDate,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DateRangeResponse {
    pub date: NaiveDate,
    pub capital_registers: Vec<CapitalRegister>,
    pub investment_transactions: Vec<Transaction>,
    pub peak_margin_transactions: Vec<PeakMargin>,
    pub cash_ledger: Vec<LegacyLedger>,
    pub receivable_payables: Vec<ReceivablePayable>,
}

pub fn fetch_transactions_for_portfolio(
    pool: Pool<Manager>,
    tx: UnboundedSender<Message>,
    portfolio_id: String,
    date: NaiveDate,
) {
    let txns_buffer: Arc<RwLock<BTreeMap<NaiveDate, EligbleStateTransitionTransactions>>> =
        Arc::new(RwLock::new(BTreeMap::new()));

    // Get transactions
    let txns_buffer_for_get = txns_buffer.clone();
    tokio::spawn(async move {
        let current_date = Utc::now().naive_local();
        let mut from_date = date;
        let mut to_date = from_date.checked_add_days(Days::new(365)).unwrap();
        let portfolio_id = portfolio_id.clone();

        loop {
            //Exit the thread if from_date > current_date
            if from_date > current_date.date() {
                //Inform the Transaction giving thread to exit

                break;
            }

            //Only Proceed if buffer is not empty
            let buff_size = {
                let guard = txns_buffer_for_get.read().await;
                guard.len()
            };

            if buff_size >= 50 {
                //Sleep For Sometime
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                continue;
            }

            let mut conn = pool.get().await.unwrap();

            let transactions_string =
                StorageEngineTransactions::get_txns_by_dates(&mut conn, &portfolio_id, from_date, to_date)
                    .await
                    .unwrap();

            let transactions_vec: Vec<DateRangeResponse> = serde_json::from_str(&transactions_string).unwrap();

            drop(conn);

            for tx in transactions_vec {
                let eligible_transactions = EligbleStateTransitionTransactions {
                    capital_register_transactions: tx.capital_registers,
                    investment_transactions: tx.investment_transactions,
                    peak_margin_transactions: tx.peak_margin_transactions,
                    legacy_ledger_transactions: tx.cash_ledger,
                    receivables_payable_transactions: tx.receivable_payables,
                };

                //Insert into Buffer
                let mut buffer_guard = txns_buffer_for_get.write().await;
                buffer_guard.insert(tx.date, eligible_transactions);
            }

            from_date = to_date.checked_add_days(Days::new(1)).unwrap();

            to_date = from_date.checked_add_days(Days::new(365)).unwrap();
        }
    });

    //Send via channel for the specific date from txns_buffer
    let txns_buffer_for_write = txns_buffer.clone();

    tokio::spawn(async move {
        loop {
            let txn = {
                let mut guard = txns_buffer_for_write.write().await;
                (guard).pop_first()
            };

            if txn.is_none() {
                continue;
            }

            let txn = txn.unwrap();
            let (ack_tx, ack_rcv) = oneshot::channel::<bool>();

            let message = Message {
                ack_sender: ack_tx,
                content: txn.1,
                date: txn.0,
            };

            if tx.send(message).is_ok() {
                //Wait for Consumer Ack
                match ack_rcv.await {
                    Ok(e) => {
                        // If the consumer thread send true
                        // Break it
                        // That means consumer is finished consuming so that this thread can exit
                        // Consumer should Only send true if it computed for the latest date
                        if e == true {
                            break;
                        }

                        continue;
                    }
                    Err(_e) => {
                        panic!("Abort")
                    }
                };
            }
        }
    });
}
