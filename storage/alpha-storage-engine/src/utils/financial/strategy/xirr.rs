use crate::{
    states::aum::Aum,
    storage::{Storage, StorageWriter},
    utils::{
        benchmark::BenchmarkIndices,
        financial::{get_month_end, is_month_end, xirr::XirrResults},
    },
};
use chrono::Datelike;
use chrono::{Months, NaiveDate};
use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use rust_decimal::prelude::ToPrimitive;
use rust_decimal_macros::dec;
use xirr::Payment;

pub struct StrategyXirrCalculator {
    pub strategy_id: String,
    pub current_aum: Aum,
    pub date: NaiveDate,
}

impl StrategyXirrCalculator {
    pub fn calculate_all_periods(self, storage: &StorageWriter) -> Result<XirrResults, String> {
        let portfolio_start_date = storage.get_strategy_start_date(&self.strategy_id).unwrap();
        let next_date = self.date;
        let storage = Storage { db: storage.db.clone() };

        let results = [
            (1, "one_month"),
            (3, "three_month"),
            (6, "six_month"),
            (12, "one_year"),
            (24, "two_year"),
            (36, "three_year"),
            (60, "five_year"),
            (84, "seven_year"),
            (120, "ten_year"),
        ]
        .par_iter()
        .map(|(months, name)| {
            let result = self
                .calculate_period_xirr(Months::new(*months), next_date, portfolio_start_date, &storage)
                .unwrap();
            (*name, result)
        })
        .collect::<Vec<_>>();

        // Extract the results
        let one_month = results.iter().find(|(name, _)| *name == "one_month").unwrap().1;
        let three_month = results.iter().find(|(name, _)| *name == "three_month").unwrap().1;

        let six_month = results.iter().find(|(name, _)| *name == "six_month").unwrap().1;
        let one_year = results.iter().find(|(name, _)| *name == "one_year").unwrap().1;
        let two_year = results.iter().find(|(name, _)| *name == "two_year").unwrap().1;
        let three_year = results.iter().find(|(name, _)| *name == "three_year").unwrap().1;
        let five_year = results.iter().find(|(name, _)| *name == "five_year").unwrap().1;
        let seven_year = results.iter().find(|(name, _)| *name == "seven_year").unwrap().1;
        let ten_year = results.iter().find(|(name, _)| *name == "ten_year").unwrap().1;

        let special_results = [("ytd", 0), ("fytd", 1), ("since_inception", 2)]
            .par_iter()
            .map(|(name, calc_type)| {
                let result = match calc_type {
                    0 => self
                        .calculate_ytd_xirr(next_date, portfolio_start_date, &storage)
                        .unwrap(),
                    1 => self
                        .calculate_fytd_xirr(next_date, portfolio_start_date, &storage)
                        .unwrap(),
                    2 => self
                        .calculate_since_inception_xirr(next_date, portfolio_start_date, &storage)
                        .unwrap(),
                    _ => unreachable!(),
                };
                (*name, result)
            })
            .collect::<Vec<_>>();

        let ytd = special_results.iter().find(|(name, _)| *name == "ytd").unwrap().1;
        let fytd = special_results.iter().find(|(name, _)| *name == "fytd").unwrap().1;
        let since_inception = special_results
            .iter()
            .find(|(name, _)| *name == "since_inception")
            .unwrap()
            .1;

        Ok(XirrResults {
            fytd,
            one_month,
            seven_year,
            six_month,
            ten_year,
            three_month,
            one_year,
            two_year,
            three_year,
            ytd,
            since_inception,
        })
    }

    pub fn calculate_benchmark_all_periods(
        self,
        storage: &StorageWriter,
        benchmark_index: BenchmarkIndices,
    ) -> Result<XirrResults, String> {
        let portfolio_start_date = storage.get_portfolio_start_date(&self.strategy_id).unwrap();
        let next_date = self.date;
        let storage = Storage { db: storage.db.clone() };

        let results = [
            (1, "one_month"),
            (3, "three_month"),
            (6, "six_month"),
            (12, "one_year"),
            (24, "two_year"),
            (36, "three_year"),
            (60, "five_year"),
            (84, "seven_year"),
            (120, "ten_year"),
        ]
        .par_iter()
        .map(|(months, name)| {
            let result = self
                .calculate_period_benchmark_xirr(
                    Months::new(*months),
                    next_date,
                    portfolio_start_date,
                    benchmark_index.clone(),
                    &storage,
                )
                .unwrap();
            (*name, result)
        })
        .collect::<Vec<_>>();

        // Extract the results
        let one_month = results.iter().find(|(name, _)| *name == "one_month").unwrap().1;
        let three_month = results.iter().find(|(name, _)| *name == "three_month").unwrap().1;

        let six_month = results.iter().find(|(name, _)| *name == "six_month").unwrap().1;
        let one_year = results.iter().find(|(name, _)| *name == "one_year").unwrap().1;
        let two_year = results.iter().find(|(name, _)| *name == "two_year").unwrap().1;
        let three_year = results.iter().find(|(name, _)| *name == "three_year").unwrap().1;
        let five_year = results.iter().find(|(name, _)| *name == "five_year").unwrap().1;
        let seven_year = results.iter().find(|(name, _)| *name == "seven_year").unwrap().1;
        let ten_year = results.iter().find(|(name, _)| *name == "ten_year").unwrap().1;

        let special_results = [("ytd", 0), ("fytd", 1), ("since_inception", 2)]
            .par_iter()
            .map(|(name, calc_type)| {
                let result = match calc_type {
                    0 => self
                        .calculate_benchmark_ytd_xirr(
                            next_date,
                            portfolio_start_date,
                            benchmark_index.clone(),
                            &storage,
                        )
                        .unwrap(),
                    1 => self
                        .calculate_benchmark_fytd_xirr(
                            next_date,
                            portfolio_start_date,
                            benchmark_index.clone(),
                            &storage,
                        )
                        .unwrap(),
                    2 => self
                        .calculate_since_inception_benchmark_xirr(
                            next_date,
                            portfolio_start_date,
                            benchmark_index.clone(),
                            &storage,
                        )
                        .unwrap(),
                    _ => unreachable!(),
                };
                (*name, result)
            })
            .collect::<Vec<_>>();

        let ytd = special_results.iter().find(|(name, _)| *name == "ytd").unwrap().1;
        let fytd = special_results.iter().find(|(name, _)| *name == "fytd").unwrap().1;
        let since_inception = special_results
            .iter()
            .find(|(name, _)| *name == "since_inception")
            .unwrap()
            .1;

        Ok(XirrResults {
            fytd,
            one_month,
            seven_year,
            six_month,
            ten_year,
            three_month,
            one_year,
            two_year,
            three_year,
            ytd,
            since_inception,
        })
    }

    fn calculate_since_inception_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let storage = StorageWriter::new(storage.db.clone());

        let mut payments: Vec<xirr::Payment> = Vec::new();

        // Get all cash flows since inception
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);

        for aum in aums.iter().filter(|a| a.net_cash_flows != dec!(0)) {
            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        // Add final AUM value
        payments.push(Payment {
            amount: -self.current_aum.total_aum.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        let days = (next_date - portfolio_start_date).num_days();
        if days < 365 {
            //(1+r)^t - 1
            //Unannualise it
            let duration = days as f64 / 365f64;
            xirr = (1f64 + (xirr)).powf(duration) - 1f64;
        }

        Ok(Some(xirr))
    }

    pub fn calculate_period_xirr(
        &self,
        period: Months,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        //Check if the date in month end
        //If yes then period_start should be
        let mut period_start = next_date - period;
        if is_month_end(next_date) {
            period_start = get_month_end(period_start);
        }

        let storage = StorageWriter::new(storage.db.clone());
        if period_start < portfolio_start_date {
            return Ok(None);
        }

        let mut payments: Vec<xirr::Payment> = Vec::new();

        // Get initial AUM for period start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, period_start).unwrap();

        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        // Get all cash flows within the period
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= period_start && a.net_cash_flows != dec!(0))
        {
            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        payments.push(Payment {
            amount: -self.current_aum.total_aum.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        if period < Months::new(12) {
            //(1+r)^t - 1
            //Unannualise it
            let duration = (next_date - period_start).num_days() as f64 / 365f64;
            xirr = (1f64 + (xirr)).powf(duration) - 1f64;
        }
        Ok(Some(xirr))
    }

    fn calculate_ytd_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let year_start = NaiveDate::from_ymd_opt(next_date.year(), 1, 1).unwrap();
        let storage = StorageWriter::new(storage.db.clone());
        if year_start < portfolio_start_date {
            return Ok(None);
        }

        let mut payments: Vec<Payment> = Vec::new();

        // Get initial AUM for year start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, year_start).unwrap();

        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        // Get all cash flows within the year
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= year_start && a.net_cash_flows != dec!(0))
        {
            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        // Add final AUM value
        payments.push(Payment {
            amount: -self.current_aum.total_aum.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();
        let days = (next_date - year_start).num_days();
        if days <= 365 {
            //(1+r)^t - 1
            //Unannualise it
            let duration = days as f64 / 365f64;
            xirr = (1f64 + (xirr)).powf(duration) - 1f64;
        }

        Ok(Some(xirr))
    }

    fn calculate_fytd_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let (year, month, day) = (next_date.year(), next_date.month(), next_date.day());
        let mut year_start = if month < 4 {
            NaiveDate::from_ymd_opt(year - 1, 4, 1).unwrap()
        } else {
            NaiveDate::from_ymd_opt(year, 4, 1).unwrap()
        };

        let storage = StorageWriter::new(storage.db.clone());

        if year_start < portfolio_start_date {
            return Ok(None);
        }

        if is_month_end(next_date) {
            year_start = get_month_end(year_start);
        }

        let mut payments: Vec<Payment> = Vec::new();

        // Get initial AUM for year start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, year_start).unwrap();

        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        // Get all cash flows within the year
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= year_start && a.net_cash_flows != dec!(0))
        {
            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        // Add final AUM value
        payments.push(Payment {
            amount: -self.current_aum.total_aum.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();
        let days = (next_date - year_start).num_days();
        if days <= 365 {
            //(1+r)^t - 1
            //Unannualise it
            let duration = days as f64 / 365f64;
            xirr = (1f64 + (xirr)).powf(duration) - 1f64;
        }

        Ok(Some(xirr))
    }

    pub fn calculate_period_benchmark_xirr(
        &self,
        period: Months,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        benchmark_index: BenchmarkIndices,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let mut period_start = next_date.checked_sub_months(period).unwrap();
        let storage = StorageWriter::new(storage.db.clone());

        if period_start <= portfolio_start_date {
            return Ok(None);
        }

        if is_month_end(next_date) {
            period_start = get_month_end(period_start);
        }

        let mut payments: Vec<Payment> = Vec::new();

        // Get initial AUM for period start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, period_start).unwrap();

        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        let mut benchmark_units = dec!(0);

        let benchmark_index_price = storage
            .get_benchmark_price_as_at(&benchmark_index, start_aum.date)
            .expect("Storage I/O Failed")
            .expect("Expect benchmark index to be there");

        assert_ne!(benchmark_index_price.close, dec!(0));
        benchmark_units += start_aum.total_aum / benchmark_index_price.close;

        // Get all cash flows within the period
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= period_start && a.net_cash_flows != dec!(0))
        {
            //Get the Price of benchmark index at this day

            let benchmark_index_price = storage
                .get_benchmark_price_as_at(&benchmark_index, aum.date)
                .expect("Storage I/O Failed")
                .expect("Expect benchmark index to be there");

            assert_ne!(benchmark_index_price.close, dec!(0));
            benchmark_units += aum.net_cash_flows / benchmark_index_price.close;

            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        let latest_bch_price = storage
            .get_benchmark_price_as_at(&benchmark_index, next_date)
            .expect("Storage I/O failed")
            .expect("Expect price to be there");

        let bch_mv = latest_bch_price.close * benchmark_units;

        payments.push(Payment {
            amount: -bch_mv.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        if period < Months::new(12) {
            //(1+r)^t - 1
            //Unannualise it
            let duration = (next_date - period_start).num_days() as f64 / 365f64;
            xirr = (1f64 + (xirr)).powf(duration) - 1f64;
        }

        Ok(Some(xirr))
    }

    fn calculate_benchmark_fytd_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        benchmark_index: BenchmarkIndices,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let (year, month, day) = (next_date.year(), next_date.month(), next_date.day());
        let mut year_start = if month < 4 {
            NaiveDate::from_ymd_opt(year - 1, 4, 1).unwrap()
        } else {
            NaiveDate::from_ymd_opt(year, 4, 1).unwrap()
        };

        if year_start < portfolio_start_date {
            return Ok(None);
        }

        let storage = StorageWriter::new(storage.db.clone());

        let mut payments: Vec<Payment> = Vec::new();

        // Get initial AUM for year start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, year_start).unwrap();
        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        let mut benchmark_units = dec!(0);
        let benchmark_index_price = storage
            .get_benchmark_price_as_at(&benchmark_index, start_aum.date)
            .expect("Storage I/O Failed")
            .expect("Expect benchmark index to be there");

        assert_ne!(benchmark_index_price.close, dec!(0));
        benchmark_units += start_aum.total_aum / benchmark_index_price.close;

        // Get all cash flows within the year
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= year_start && a.net_cash_flows != dec!(0))
        {
            //Get the Price of benchmark index at this day
            let benchmark_index_price = storage
                .get_benchmark_price_as_at(&benchmark_index, aum.date)
                .expect("Storage I/O Failed")
                .expect("Expect benchmark index to be there");

            assert_ne!(benchmark_index_price.close, dec!(0));
            benchmark_units += aum.net_cash_flows / benchmark_index_price.close;

            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        let latest_bch_price = storage
            .get_benchmark_price_as_at(&benchmark_index, next_date)
            .expect("Storage I/O failed")
            .expect("Expect price to be there");

        let bch_mv = latest_bch_price.close * benchmark_units;

        // Add final AUM value
        payments.push(Payment {
            amount: -bch_mv.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        Ok(Some(xirr))
    }

    fn calculate_benchmark_ytd_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        benchmark_index: BenchmarkIndices,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let mut year_start = NaiveDate::from_ymd_opt(next_date.year(), 1, 1).unwrap();
        let storage = StorageWriter::new(storage.db.clone());

        if year_start < portfolio_start_date {
            return Ok(None);
        }

        let mut payments: Vec<Payment> = Vec::new();

        // Get initial AUM for year start
        let start_aum = storage.get_strategy_aum(&self.strategy_id, year_start).unwrap();

        payments.push(Payment {
            amount: start_aum.total_aum.to_f64().unwrap(),
            date: start_aum.date,
        });

        let mut benchmark_units = dec!(0);

        let benchmark_index_price = storage
            .get_benchmark_price_as_at(&benchmark_index, start_aum.date)
            .expect("Storage I/O Failed")
            .expect("Expect benchmark index to be there");

        assert_ne!(benchmark_index_price.close, dec!(0));
        benchmark_units += start_aum.total_aum / benchmark_index_price.close;

        // Get all cash flows within the year
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);
        for aum in aums
            .iter()
            .filter(|a| a.date >= year_start && a.net_cash_flows != dec!(0))
        {
            //Get the Price of benchmark index at this day
            let benchmark_index_price = storage
                .get_benchmark_price_as_at(&benchmark_index, aum.date)
                .expect("Storage I/O Failed")
                .expect("Expect benchmark index to be there");

            assert_ne!(benchmark_index_price.close, dec!(0));
            benchmark_units += aum.net_cash_flows / benchmark_index_price.close;
            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        let latest_bch_price = storage
            .get_benchmark_price_as_at(&benchmark_index, next_date)
            .expect("Storage I/O failed")
            .expect("Expect price to be there");

        let bch_mv = latest_bch_price.close * benchmark_units;

        // Add final AUM value
        payments.push(Payment {
            amount: -bch_mv.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        Ok(Some(xirr))
    }

    fn calculate_since_inception_benchmark_xirr(
        &self,
        next_date: NaiveDate,
        portfolio_start_date: NaiveDate,
        benchmark_index: BenchmarkIndices,
        storage: &Storage,
    ) -> Result<Option<f64>, String> {
        let mut payments: Vec<Payment> = Vec::new();

        let storage = StorageWriter::new(storage.db.clone());
        // Get all cash flows since inception
        let aums = storage.get_strategy_aum_for_analytics_range(next_date, &self.strategy_id);

        let mut benchmark_units = dec!(0);

        for aum in aums.iter().filter(|a| a.net_cash_flows != dec!(0)) {
            //Get the Price of benchmark index at this day
            let benchmark_index_price = storage
                .get_benchmark_price_as_at(&benchmark_index, aum.date)
                .expect("Storage I/O Failed")
                .expect("Expect benchmark index to be there");

            assert_ne!(benchmark_index_price.close, dec!(0));
            benchmark_units += aum.net_cash_flows / benchmark_index_price.close;

            payments.push(Payment {
                amount: aum.net_cash_flows.to_f64().unwrap(),
                date: aum.date,
            });
        }

        let latest_bch_price = storage
            .get_benchmark_price_as_at(&benchmark_index, next_date)
            .expect("Storage I/O failed")
            .expect("Expect price to be there at {}");

        let bch_mv = latest_bch_price.close * benchmark_units;

        // Add final AUM value
        payments.push(Payment {
            amount: -bch_mv.to_f64().unwrap(),
            date: next_date,
        });

        let mut xirr = xirr::compute(&payments).unwrap_or_default();

        Ok(Some(xirr))
    }
}
