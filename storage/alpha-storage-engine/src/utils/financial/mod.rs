use chrono::Datelike;
use chrono::Duration;
use chrono::NaiveDate;

pub mod strategy;
pub mod twrr;
pub mod xirr;

pub fn get_month_end(date: NaiveDate) -> NaiveDate {
    // Get the first day of the next month
    let next_month = if date.month() == 12 {
        NaiveDate::from_ymd_opt(date.year() + 1, 1, 1).unwrap()
    } else {
        NaiveDate::from_ymd_opt(date.year(), date.month() + 1, 1).unwrap()
    };

    // Subtract one day to get the last day of the current month
    next_month - Duration::days(1)
}

pub fn is_month_end(date: NaiveDate) -> bool {
    // Add one day and see if it's a different month
    let next_day = date + Duration::days(1);
    date.month() != next_day.month()
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{Months, NaiveDate};
    use rust_decimal_macros::dec;

    #[test]
    fn test_month() {
        let date = NaiveDate::from_ymd_opt(2025, 02, 28).unwrap();
        let months = Months::new(12);
        let mut period_start = date - months;
        if is_month_end(date) {
            period_start = get_month_end(period_start);
        }

        println!("{:?}", period_start);
    }
}
