use crate::{
    states::aum::Aum,
    storage::{Storage, StorageOperations, StorageWriter},
    utils::benchmark::BenchmarkIndices,
};
use chrono::Datelike;
use chrono::{Months, NaiveDate};
use rust_decimal::{prelude::FromPrimitive, Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use tracing::info;

use super::{get_month_end, is_month_end};

#[derive(Debug)]
pub struct TwrrResults {
    pub one_month: Option<Decimal>,
    pub three_month: Option<Decimal>,
    pub six_month: Option<Decimal>,
    pub one_year: Option<Decimal>,
    pub two_year: Option<Decimal>,
    pub three_year: Option<Decimal>,
    pub seven_year: Option<Decimal>,
    pub ten_year: Option<Decimal>,
    pub ytd: Option<Decimal>,
    pub fytd: Option<Decimal>,
    pub since_inception: Option<Decimal>,
}

/// We store the (1+R) on each day and it will be available in the state
/// On Each day we multiply the previous return with the current return so we don't need to pass the full aum
pub struct TwrrCalculator {
    pub date: NaiveDate,
    pub portfolio_id: String,
}

impl TwrrCalculator {
    pub fn _calculate_twrr(periods: &[Aum]) -> Result<Decimal, String> {
        if periods.len() < 2 {
            return Err("Need at least two periods to calculate TWRR".into());
        }

        // Validate inputs
        for period in periods {
            if period.units <= dec!(0) || period.nav <= dec!(0) {
                return Err("Units and NAV must be positive".into());
            }
        }

        // Calculate holding period returns
        let mut holding_period_returns: Vec<Decimal> = Vec::with_capacity(periods.len() - 1);

        for window in periods.windows(2) {
            let start = &window[0];
            let end = &window[1];

            // Calculate sub-period return using NAV values
            let period_return = (end.nav / start.nav) - dec!(1);
            //(1+R)
            holding_period_returns.push(dec!(1) + period_return);
        }

        // TWRR = [(1 + R₁) × (1 + R₂) × ... × (1 + Rₙ)] - 1
        // 1+R is already computed in holding_period_returns so just multiply everything and subtract 1
        let twrr = holding_period_returns.iter().fold(dec!(1), |acc, &x| acc * x) - dec!(1);

        Ok(twrr)
    }

    pub fn calculate_benchmark_twrr_all_periods(&self, storage: &StorageWriter) -> Result<TwrrResults, String> {
        let portfolio_start_date = storage.get_portfolio_start_date(&self.portfolio_id).unwrap();

        let one_month = self
            .calculate_period_twrr(Months::new(1), portfolio_start_date, storage)
            .unwrap();

        let three_month = self
            .calculate_period_twrr(Months::new(3), portfolio_start_date, storage)
            .unwrap();

        let six_month = self
            .calculate_period_twrr(Months::new(6), portfolio_start_date, storage)
            .unwrap();

        let one_year = self
            .calculate_period_twrr(Months::new(12), portfolio_start_date, storage)
            .unwrap();

        let two_year = self
            .calculate_period_twrr(Months::new(24), portfolio_start_date, storage)
            .unwrap();

        let three_year = self
            .calculate_period_twrr(Months::new(36), portfolio_start_date, storage)
            .unwrap();

        let five_year = self
            .calculate_period_twrr(Months::new(60), portfolio_start_date, storage)
            .unwrap();

        let seven_year = self
            .calculate_period_twrr(Months::new(84), portfolio_start_date, storage)
            .unwrap();

        let ten_year = self
            .calculate_period_twrr(Months::new(120), portfolio_start_date, storage)
            .unwrap();

        let ytd = self.calculate_ytd_twrr(portfolio_start_date, storage).unwrap();

        let fytd = self.calculate_fytd_twrr(portfolio_start_date, storage).unwrap();

        let since_inception = self
            .calculate_since_inception_twrr(portfolio_start_date, storage)
            .unwrap();

        Ok(TwrrResults {
            fytd,
            one_month,
            seven_year,
            six_month,
            ten_year,
            three_month,
            one_year,
            two_year,
            three_year,
            ytd,
            since_inception,
        })
    }

    pub fn calculate_twrr_all_periods(&self, storage: &StorageWriter) -> Result<TwrrResults, String> {
        let portfolio_start_date = storage.get_portfolio_start_date(&self.portfolio_id).unwrap();

        let one_month = self
            .calculate_period_twrr(Months::new(1), portfolio_start_date, storage)
            .unwrap();

        let three_month = self
            .calculate_period_twrr(Months::new(3), portfolio_start_date, storage)
            .unwrap();

        let six_month = self
            .calculate_period_twrr(Months::new(6), portfolio_start_date, storage)
            .unwrap();

        let one_year = self
            .calculate_period_twrr(Months::new(12), portfolio_start_date, storage)
            .unwrap();

        let two_year = self
            .calculate_period_twrr(Months::new(24), portfolio_start_date, storage)
            .unwrap();

        let three_year = self
            .calculate_period_twrr(Months::new(36), portfolio_start_date, storage)
            .unwrap();

        let five_year = self
            .calculate_period_twrr(Months::new(60), portfolio_start_date, storage)
            .unwrap();

        let seven_year = self
            .calculate_period_twrr(Months::new(84), portfolio_start_date, storage)
            .unwrap();

        let ten_year = self
            .calculate_period_twrr(Months::new(120), portfolio_start_date, storage)
            .unwrap();

        let ytd = self.calculate_ytd_twrr(portfolio_start_date, storage).unwrap();

        let fytd = self.calculate_fytd_twrr(portfolio_start_date, storage).unwrap();

        let since_inception = self
            .calculate_since_inception_twrr(portfolio_start_date, storage)
            .unwrap();

        Ok(TwrrResults {
            fytd,
            one_month,
            seven_year,
            six_month,
            ten_year,
            three_month,
            one_year,
            two_year,
            three_year,
            ytd,
            since_inception,
        })
    }

    fn calculate_since_inception_twrr(
        &self,
        portfolio_start_date: NaiveDate,
        storage: &StorageWriter,
    ) -> Result<Option<Decimal>, String> {
        let nav_on_start = storage
            .get_portfolio_aum(&self.portfolio_id, portfolio_start_date)
            .unwrap()
            .nav;

        let nav_today = storage.get_portfolio_aum(&self.portfolio_id, self.date).unwrap().nav;

        let total_year = Decimal::from_i64((self.date - portfolio_start_date).num_days()).unwrap() / dec!(365);
        if total_year > dec!(1) {
            let ratio = nav_today / nav_on_start;
            let twrr = ratio.checked_powd(dec!(1) / total_year).unwrap_or(dec!(1)) - dec!(1);
            return Ok(Some(twrr));
        } else {
            let mut twrr = if nav_on_start > dec!(0) {
                (nav_today / nav_on_start) - dec!(1)
            } else {
                dec!(0)
            };

            return Ok(Some(twrr));
        }
    }

    fn calculate_fytd_twrr(
        &self,
        portfolio_start_date: NaiveDate,
        storage: &StorageWriter,
    ) -> Result<Option<Decimal>, String> {
        let (year, month, day) = (self.date.year(), self.date.month(), self.date.day());
        let year_start = if month < 4 {
            NaiveDate::from_ymd_opt(year - 1, 4, 1).unwrap()
        } else {
            NaiveDate::from_ymd_opt(year, 4, 1).unwrap()
        };

        if year_start < portfolio_start_date {
            return Ok(None);
        }

        let nav_on_start = storage.get_portfolio_aum(&self.portfolio_id, year_start).unwrap().nav;
        let nav_today = storage.get_portfolio_aum(&self.portfolio_id, self.date).unwrap().nav;

        let mut twrr = if nav_on_start > dec!(0) {
            (nav_today / nav_on_start) - dec!(1)
        } else {
            dec!(0)
        };
        Ok(Some(twrr))
    }

    fn calculate_ytd_twrr(
        &self,
        portfolio_start_date: NaiveDate,
        storage: &StorageWriter,
    ) -> Result<Option<Decimal>, String> {
        let year_start = NaiveDate::from_ymd_opt(self.date.year() - 1, 12, 31).unwrap();

        if year_start < portfolio_start_date {
            return Ok(None);
        }

        let nav_on_start = storage.get_portfolio_aum(&self.portfolio_id, year_start).unwrap().nav;
        let nav_today = storage.get_portfolio_aum(&self.portfolio_id, self.date).unwrap().nav;

        let mut twrr = if nav_on_start > dec!(0) {
            (nav_today / nav_on_start) - dec!(1)
        } else {
            dec!(0)
        };

        Ok(Some(twrr))
    }

    fn calculate_period_twrr(
        &self,
        period: Months,
        portfolio_start_date: NaiveDate,
        storage: &StorageWriter,
    ) -> Result<Option<Decimal>, String> {
        let mut period_start = self.date.checked_sub_months(period).unwrap();

        if period_start < portfolio_start_date {
            return Ok(None);
        }

        if is_month_end(self.date) {
            period_start = get_month_end(period_start);
        }

        let nav_on_start = storage.get_portfolio_aum(&self.portfolio_id, period_start).unwrap().nav;

        let nav_today = storage.get_portfolio_aum(&self.portfolio_id, self.date).unwrap().nav;

        let mut twrr = if nav_on_start > dec!(0) {
            (nav_today / nav_on_start) - dec!(1)
        } else {
            dec!(0)
        };

        Ok(Some(twrr))
    }

    fn calculate_benchmark_period_twrr(
        &self,
        period: Months,
        benchmark_index: &BenchmarkIndices,
        as_at: NaiveDate,
        storage: &StorageWriter,
    ) -> Result<Option<Decimal>, String> {
        let period_start = as_at.checked_sub_months(period).unwrap();

        let nav_on_start = storage
            .get_benchmark_price_as_at(benchmark_index, period_start)
            .expect("Storage I/O Failed")
            .expect("Expect benchmark Price to be there")
            .close;

        let nav_today = storage
            .get_benchmark_price_as_at(benchmark_index, self.date)
            .expect("Storage I/O Failed")
            .expect("Expect benchmark Price to be there")
            .close;

        let twrr = (nav_today / nav_on_start) - dec!(1);
        Ok(Some(twrr))
    }
}
