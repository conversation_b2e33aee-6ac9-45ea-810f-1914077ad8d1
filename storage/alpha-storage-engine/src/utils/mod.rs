use std::collections::HashMap;

use chrono::NaiveDate;
use rust_decimal::{prelude::FromPrimitive, Decimal};

use crate::{
    states::{analytics::TransactionForAnalytics, aum::Aum, investment::Investment, portfolio::Portfolio},
    storage::StorageWriter,
};

pub mod aggregator;
pub mod benchmark;
pub mod financial;
pub mod price_migration;
pub mod transaction_computer;
pub mod twrr;
pub mod update_investments;
pub mod date;

pub struct StateAggregator<'a> {
    pub storage: &'a StorageWriter,
}

impl<'a> StateAggregator<'a> {
    /// Sum up the investments in all the portfolios
    pub fn portfolios_investments_aggregation(&self, portfolio_ids: &Vec<String>, date: NaiveDate) -> Vec<Investment> {
        let mut security_investments: HashMap<String, Investment> = HashMap::new();

        //Add up the market value of same security
        for id in portfolio_ids {
            let ptf_inv = self.storage.get_portfolio_investment(id, date);

            if let Some(invs) = ptf_inv {
                for inv in invs {
                    security_investments
                        .entry(inv.isin.clone())
                        .and_modify(|investment| {
                            investment.market_value += inv.market_value;
                            investment.holdings += inv.holdings
                        })
                        .or_insert(inv);
                }
            }
        }

        security_investments.into_values().collect()
    }

    pub fn portfolio_state_aggregation(&self, portfolio_ids: &Vec<String>, date: NaiveDate) -> Portfolio {
        let mut portfolio = Portfolio { ..Default::default() };

        //Add up the market value of same security
        for id in portfolio_ids {
            let un_portfolio = self.storage.get_portfolio(id, date);

            if let Some(ptf) = un_portfolio {
                portfolio.market_value += ptf.market_value;
                portfolio.total_capital += ptf.total_capital;
                portfolio.invested_capital += ptf.invested_capital;
            }
        }
        portfolio
    }

    pub fn portfolios_aum_aggregation(&self, portfolio_ids: &Vec<String>, date: NaiveDate) -> Aum {
        let mut client_aum = Aum {
            date,
            ..Default::default()
        };

        for id in portfolio_ids {
            let ptf_aum = self.storage.get_portfolio_aum(id, date);

            if let Some(aum) = ptf_aum {
                client_aum.cash += aum.cash;
                client_aum.change += aum.change;
                client_aum.market_value += aum.market_value;
                client_aum.nav += aum.nav;
                client_aum.net_cash_flows += aum.net_cash_flows;
                client_aum.payables += aum.payables;
                client_aum.receivables += aum.receivables;
                client_aum.total_aum += aum.total_aum;
                client_aum.units += aum.units;
            }
        }

        client_aum.change = client_aum.change / Decimal::from_usize(portfolio_ids.len()).unwrap();
        client_aum.nav = client_aum.nav / Decimal::from_usize(portfolio_ids.len()).unwrap();
        client_aum.units = client_aum.units / Decimal::from_usize(portfolio_ids.len()).unwrap();

        client_aum
    }

    pub fn portfolios_aggregated_aum_for_xirr(&self) {}

    pub fn portfolio_analytics_aggregation(
        &self,
        portfolio_ids: &Vec<String>,
        date: NaiveDate,
    ) -> Vec<TransactionForAnalytics> {
        let mut security_transactions: Vec<TransactionForAnalytics> = Vec::new();

        for id in portfolio_ids {
            let mut transactions = self.storage.get_transactions_for_analytics_range(id, date);
            security_transactions.append(&mut transactions);
        }

        security_transactions
    }
}
