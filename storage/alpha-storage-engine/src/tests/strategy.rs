#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use chrono::{NaiveDate, Utc};
    use rocksdb::{ColumnFamilyDescriptor, Options, DB};

    use crate::storage::{Storage, StorageWriter};

    // Function to initialize and return the storage writer
    fn init_storage() -> StorageWriter {
        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", Options::default()),
            ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
            ColumnFamilyDescriptor::new("isin_history", Options::default()),
            ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ];

        let mut rocks_options = Options::default();
        rocks_options.set_max_write_buffer_number(0);

        let storage_db = DB::open_cf_descriptors(&rocks_options, Path::new("/home/<USER>/rocksdb"), cf_descriptors)
            .expect("Failed to open RocksDB with column families");

        StorageWriter::new(Arc::new(storage_db))
    }

    #[test]
    fn get_strategies() {
        let storage_rw = init_storage();
        let date = Utc::now().date_naive();
        let strategies = storage_rw.get_all_strategies_and_portfolios(date);
        println!("{:?}", strategies);
    }
}
