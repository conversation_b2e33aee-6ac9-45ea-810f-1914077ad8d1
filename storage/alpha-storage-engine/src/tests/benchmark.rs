#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use chrono::{NaiveDate, Utc};
    use rocksdb::{ColumnFamilyDescriptor, Options, DB};

    use crate::{
        storage::{Storage, StorageWriter},
        utils::benchmark::BenchmarkIndices,
    };

    // Function to initialize and return the storage writer
    fn init_storage() -> StorageWriter {
        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", Options::default()),
            ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
            ColumnFamilyDescriptor::new("isin_history", Options::default()),
            ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
            ColumnFamilyDescriptor::new("benchmark_price", Options::default()),
        ];

        let mut rocks_options = Options::default();
        rocks_options.set_max_write_buffer_number(0);

        let storage_db = DB::open_cf_descriptors(&rocks_options, Path::new("/home/<USER>/rocksdb"), cf_descriptors)
            .expect("Failed to open RocksDB with column families");

        StorageWriter::new(Arc::new(storage_db))
    }

    #[test]
    fn get_benchmark_as_at() {
        let storage_rw = init_storage();
        let date = NaiveDate::from_ymd_opt(2025, 03, 02).unwrap();
        let portfolio_id = "166f6c4d771a4e128a6626a7d1b3e770";
        let index = BenchmarkIndices::Nifty50;
        let benchmark_xirr = storage_rw.get_benchmark_xirr_metrics(portfolio_id, date, &index);
        println!("{:?}", benchmark_xirr);
    }
}
