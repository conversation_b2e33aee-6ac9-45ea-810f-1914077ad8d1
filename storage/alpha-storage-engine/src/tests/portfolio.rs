#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use chrono::NaiveDate;
    use rocksdb::{ColumnFamilyDescriptor, Options, DB};

    use crate::storage::{Storage, StorageWriter};

    // Function to initialize and return the storage writer
    fn init_storage() -> StorageWriter {
        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", Options::default()),
            ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
            ColumnFamilyDescriptor::new("isin_history", Options::default()),
            ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
            ColumnFamilyDescriptor::new("benchmark_price", Options::default()),
        ];

        let mut rocks_options = Options::default();
        rocks_options.set_max_write_buffer_number(0);

        let storage_db = DB::open_cf_descriptors(&rocks_options, Path::new("/home/<USER>/rocksdb"), cf_descriptors)
            .expect("Failed to open RocksDB with column families");

        StorageWriter::new(Arc::new(storage_db))
    }

    #[test]
    fn get_all_portfolios() {
        let storage_rw = init_storage();
        let date = NaiveDate::from_ymd_opt(2025, 05, 13).unwrap();
        let mut portfolios = storage_rw.get_all_portfolios_as_at(date).unwrap();
        println!("{:?}", portfolios);
    }
}
