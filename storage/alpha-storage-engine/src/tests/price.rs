#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use alpha_core_db::clickhouse::create_clickhouse_client;
    use chrono::{Days, NaiveDate, Utc};
    use rocksdb::{ColumnFamilyDescriptor, Options, DB};
    use time::convert::Day;

    use crate::{
        storage::{Storage, StorageWriter},
        utils::price_migration::Migration,
    };

    // Function to initialize and return the storage writer
    fn init_storage() -> StorageWriter {
        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", Options::default()),
            ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
            ColumnFamilyDescriptor::new("isin_history", Options::default()),
            ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ];

        let mut rocks_options = Options::default();
        rocks_options.set_max_write_buffer_number(0);

        let storage_db = DB::open_cf_descriptors(&rocks_options, Path::new("/home/<USER>/rocksdb"), cf_descriptors)
            .expect("Failed to open RocksDB with column families");

        StorageWriter::new(Arc::new(storage_db))
    }

    #[tokio::test]
    async fn test_price() {
        dotenv::dotenv().ok();
        let storage = init_storage();
        let clickhouse_pool = create_clickhouse_client();
        let date = Utc::now().naive_utc().date().checked_sub_days(Days::new(1)).unwrap();
        let migration = Migration::migrate_price_as_at(storage.db.clone(), clickhouse_pool, date).await;
        assert_eq!(migration.is_ok(), true);
    }
}
