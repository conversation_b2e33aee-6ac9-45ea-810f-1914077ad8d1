pub mod benchmark;
pub mod price;
pub mod strategy;
pub mod portfolio;

#[cfg(test)]
mod tests {
    use std::{path::Path, sync::Arc};

    use chrono::NaiveDate;
    use rocksdb::{ColumnFamilyDescriptor, Options, DB};

    use crate::storage::{Storage, StorageWriter};

    // Function to initialize and return the storage writer
    fn init_storage() -> StorageWriter {
        let cf_descriptors = vec![
            ColumnFamilyDescriptor::new("default", Options::default()),
            ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
            ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
            ColumnFamilyDescriptor::new("isin_history", Options::default()),
            ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ];

        let mut rocks_options = Options::default();
        rocks_options.set_max_write_buffer_number(0);

        let storage_db = DB::open_cf_descriptors(&rocks_options, Path::new("/home/<USER>/rocksdb"), cf_descriptors)
            .expect("Failed to open RocksDB with column families");

        StorageWriter::new(Arc::new(storage_db))
    }

    #[test]
    fn test_analytics() {
        let storage_rw = init_storage();
        let portfolio_id = "ce56a2acc4e94ab191cd89230237b3d4";
        let date = NaiveDate::from_ymd_opt(2025, 03, 11).unwrap();
        let mut investments = storage_rw.get_portfolio_investment(portfolio_id, date).unwrap();

        investments.sort_by(|a, b| b.xirr.partial_cmp(&a.close_price_change).unwrap());

        println!("inv = {:?}", investments);
    }

    #[test]
    fn get_portfolio_investments() {
        let storage_rw = init_storage();
        let portfolio_id = "442bf7cd7f7342eb8d61a30e6043c84f";
        let date = NaiveDate::from_ymd_opt(2025, 03, 17).unwrap();
        let mut investments = storage_rw.get_portfolio_investment(portfolio_id, date).unwrap();
        println!("{:?}", investments);
    }
}
