use crate::states::aum::Aum;
use crate::{states::investment::Investment, storage::DbKeyPrefix};
use alpha_macros::ReflectFields;
use arrow::array::{
    Array, ArrayRef, BooleanArray, Date32Array, Date64Array, Float64Array, Int32Array, Int64Array, RecordBatch,
    StringArray, TimestampMicrosecondArray,
};
use arrow::datatypes::DataType as ArrowType;
use arrow::datatypes::{Field, Schema};
use deltalake::kernel::{DataType as DeltaType, StructField};
use deltalake::operations::create::CreateBuilder;
use deltalake::protocol::SaveMode;
use deltalake::writer::{<PERSON><PERSON>rite<PERSON>, JsonWriter, RecordBatchWriter};
use deltalake::{DeltaOps, DeltaTable, DeltaTableBuilder, DeltaTableError};
use duckdb::Connection;
use rkyv::archived_root;
use rkyv::Deserialize;
use rocksdb::ColumnFamilyDescriptor;
use rocksdb::IteratorMode;
use rocksdb::Options;
use serde_json::json;
use std::future::IntoFuture;
use std::path::Path;
use std::sync::Arc;
use tracing_subscriber::fmt::format::json;

pub trait ReflectFieldAccess {
    fn reflect_fields() -> Vec<(&'static str, &'static str)>;
    fn reflect_get(&self, field: &str) -> Option<Box<dyn std::any::Any>>;
}

pub struct DeltaLakeImporter {
    pub rocks_store: Arc<rocksdb::DB>,
    pub table_uri: String,
}

impl DeltaLakeImporter {
    pub async fn execute(&self) {
        // Portfolio Investments
        let iter = self.rocks_store.iterator(IteratorMode::Start);
        let mut investment_batches = Vec::new();
        let mut aum_batches = Vec::new();

        for item in iter {
            if let Ok((key, value)) = item {
                let key_str = String::from_utf8_lossy(&key).to_string();

                if key_str.starts_with(&DbKeyPrefix::PortfolioInvestment.to_string()) {
                    let archived = unsafe { rkyv::archived_root::<Vec<Investment>>(&value[..]) };
                    let record: Vec<Investment> = archived.deserialize(&mut rkyv::Infallible).expect("TODO:");
                    if record.len() == 0 {
                        continue;
                    }
                    let fields = Investment::reflect_fields();
                    let columns = self.create_data(fields, record);

                    investment_batches.push(columns);
                } else if key_str.starts_with(&DbKeyPrefix::PortfolioAum.to_string()) {
                    let archived = unsafe { rkyv::archived_root::<Aum>(&value[..]) };
                    let record: Aum = archived.deserialize(&mut rkyv::Infallible).expect("TODO:");
                    let fields = Aum::reflect_fields();

                    let columns = self.create_data(fields, vec![record]);

                    aum_batches.push(columns);
                }
            }
        }

        self.write_to_delta(
            "portfolio_investments",
            investment_batches,
            Investment::reflect_fields(),
        )
        .await;
        self.write_to_delta("portfolio_aum", aum_batches, Aum::reflect_fields())
            .await;
    }

    async fn write_to_delta(
        &self,
        table_name: &str,
        data_batches: Vec<Vec<ArrayRef>>,
        fields: Vec<(&'static str, &'static str)>,
    ) {
        let arrow_schema = Arc::new(self.generate_arrow_schema(&fields));
        let lake_fields = self.generate_lake_schema(&fields);
        let table_path = format!("{}/{}", self.table_uri, table_name);

        let mut table = create_or_get_table(&table_path, lake_fields).await.unwrap();
        let mut writer = RecordBatchWriter::for_table(&table).unwrap();

        for data in data_batches {
            let batch = RecordBatch::try_new(arrow_schema.clone(), data).unwrap();
            writer.write(batch).await.unwrap();
        }

        writer.flush_and_commit(&mut table).await.unwrap();
        println!("Written to Delta: {}", table_name);
    }

    fn create_data<T: ReflectFieldAccess>(
        &self,
        fields: Vec<(&'static str, &'static str)>,
        items: Vec<T>,
    ) -> Vec<ArrayRef> {
        let timezone = Arc::<str>::from("UTC");
        let columns: Vec<ArrayRef> = fields
            .iter()
            .map(|(name, ty)| {
                let clean_ty = ty.replace(" ", "");

                match clean_ty.as_str() {
                    "String" | "Option<String>" => {
                        let col: Vec<Option<String>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).and_then(|v| v.downcast_ref::<String>().cloned()))
                            .collect();

                        Arc::new(StringArray::from(col)) as ArrayRef
                    }
                    "Decimal" | "Option<Decimal>" => {
                        let col: Vec<Option<String>> = items
                            .iter()
                            .map(|r| {
                                r.reflect_get(name)
                                    .and_then(|v| v.downcast_ref::<rust_decimal::Decimal>().map(|d| d.to_string()))
                            })
                            .collect();
                        Arc::new(StringArray::from(col)) as ArrayRef
                    }
                    "i32" | "Option<i32>" => {
                        let col: Vec<Option<i32>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).and_then(|v| v.downcast_ref::<i32>().cloned()))
                            .collect();
                        Arc::new(Int32Array::from(col)) as ArrayRef
                    }
                    "i64" | "Option<i64>" => {
                        let col: Vec<Option<i64>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).and_then(|v| v.downcast_ref::<i64>().cloned()))
                            .collect();
                        Arc::new(Int64Array::from(col)) as ArrayRef
                    }
                    "f64" | "Option<f64>" => {
                        let col: Vec<Option<f64>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).and_then(|v| v.downcast_ref::<f64>().cloned()))
                            .collect();
                        Arc::new(Float64Array::from(col)) as ArrayRef
                    }
                    "bool" | "Option<bool>" => {
                        let col: Vec<Option<bool>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).and_then(|v| v.downcast_ref::<bool>().cloned()))
                            .collect();
                        Arc::new(BooleanArray::from(col)) as ArrayRef
                    }
                    "NaiveDateTime" | "Option<NaiveDateTime>" => {
                        let col: Vec<Option<i64>> = items
                            .iter()
                            .map(|r| {
                                r.reflect_get(name).and_then(|v| {
                                    v.downcast_ref::<chrono::NaiveDateTime>()
                                        .map(|dt| dt.timestamp_micros())
                                })
                            })
                            .collect();
                        let array = TimestampMicrosecondArray::from(col);
                        let array_data = array
                            .into_data()
                            .into_builder()
                            .data_type(ArrowType::Timestamp(
                                arrow::datatypes::TimeUnit::Microsecond,
                                Some(timezone.clone()),
                            ))
                            .build()
                            .unwrap();

                        Arc::new(TimestampMicrosecondArray::from(array_data)) as ArrayRef
                    }
                    "NaiveDate" | "Option<NaiveDate>" => {
                        let col: Vec<Option<i32>> = items
                            .iter()
                            .map(|r| {
                                r.reflect_get(name).and_then(|v| {
                                    v.downcast_ref::<chrono::NaiveDate>().map(|d| {
                                        d.signed_duration_since(chrono::NaiveDate::from_ymd_opt(1970, 1, 1).unwrap())
                                            .num_days() as i32
                                    })
                                })
                            })
                            .collect();
                        Arc::new(Date32Array::from(col)) as ArrayRef
                    }
                    _ => {
                        let col: Vec<Option<String>> = items
                            .iter()
                            .map(|r| r.reflect_get(name).map(|v| format!("{:?}", v)))
                            .collect();
                        Arc::new(StringArray::from(col)) as ArrayRef
                    }
                }
            })
            .collect();
        columns
    }

    fn generate_arrow_schema(&self, fields: &Vec<(&'static str, &'static str)>) -> Schema {
        let timezone = Arc::<str>::from("UTC");
        let arrow_fields: Vec<Field> = fields
            .iter()
            .map(|(name, ty)| {
                let clean_ty = ty.replace(" ", "");
                let nullable = clean_ty.starts_with("Option<");

                let dtype = match clean_ty.as_str() {
                    "String" | "Option<String>" | "Decimal" | "Option<Decimal>" => ArrowType::Utf8,
                    "i32" | "Option<i32>" => ArrowType::Date32,
                    "i64" | "Option<i64>" => ArrowType::Int64,
                    "f64" | "Option<f64>" => ArrowType::Float64,
                    "bool" | "Option<bool>" => ArrowType::Boolean,
                    "NaiveDate" | "Option<NaiveDate>" => ArrowType::Date32,
                    "NaiveDateTime" | "Option<NaiveDateTime>" => {
                        ArrowType::Timestamp(arrow::datatypes::TimeUnit::Microsecond, Some(timezone.clone()))
                    }
                    _ => ArrowType::Utf8, // fallback for enums
                };

                Field::new(name.to_string(), dtype, nullable)
            })
            .collect();

        let arrow_schema = Schema::new(arrow_fields);
        arrow_schema
    }

    fn generate_lake_schema(&self, fields: &Vec<(&'static str, &'static str)>) -> Vec<StructField> {
        fields
            .iter()
            .map(|(name, ty)| {
                let clean_ty = ty.replace(" ", "");
                let nullable = clean_ty.starts_with("Option<");

                let dtype = match clean_ty.as_str() {
                    "String" | "Option<String>" | "Decimal" | "Option<Decimal>" => DeltaType::STRING,
                    "i32" | "Option<i32>" => DeltaType::INTEGER,
                    "i64" | "Option<i64>" => DeltaType::LONG,
                    "f64" | "Option<f64>" => DeltaType::DOUBLE,
                    "bool" | "Option<bool>" => DeltaType::BOOLEAN,
                    "NaiveDate" | "Option<NaiveDate>" => DeltaType::DATE,
                    "NaiveDateTime" | "Option<NaiveDateTime>" => DeltaType::TIMESTAMP,
                    _ => DeltaType::STRING,
                };

                StructField::new(name.to_string(), dtype, nullable)
            })
            .collect()
    }
}

pub async fn read_all_schemas(base_path: &str) -> Vec<(String, Vec<(String, String)>)> {
    let mut schemas = Vec::new();
    for table_path in list_delta_tables(base_path) {
        if let Ok(table) = deltalake::open_table(&table_path).await {
            if let Some(schema) = table.schema() {
                let fields: Vec<(String, String)> = schema
                    .fields()
                    .map(|f| (f.name.to_string(), format!("{:?}", f.data_type)))
                    .collect();
                schemas.push((table_path, fields));
            }
        }
    }
    schemas
}

fn list_delta_tables(base_path: &str) -> Vec<String> {
    std::fs::read_dir(base_path)
        .unwrap()
        .filter_map(|entry| {
            let path = entry.ok()?.path();
            if path.is_dir() && path.join("_delta_log").exists() {
                Some(path.to_string_lossy().to_string())
            } else {
                None
            }
        })
        .collect()
}

async fn create_or_get_table(table_uri: &str, schema: Vec<StructField>) -> Result<DeltaTable, DeltaTableError> {
    let table = match deltalake::open_table(table_uri).await {
        Ok(table) => table,
        Err(err) => {
            let ops = DeltaOps::try_from_uri(table_uri).await.unwrap();
            let table = ops.create().with_columns(schema).await;
            return table;
        }
    };
    Ok(table)
}

#[tokio::test]
async fn test_lake() {
    let cf_descriptors = vec![
        ColumnFamilyDescriptor::new("default", Options::default()),
        ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
        ColumnFamilyDescriptor::new("isin_history", Options::default()),
        ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
        ColumnFamilyDescriptor::new("benchmark_price", Options::default()),
    ];

    let mut rocks_options = Options::default();
    rocks_options.set_max_write_buffer_number(0);

    let path = "/home/<USER>/rocksdb";
    let storage_db = rocksdb::DB::open_cf_descriptors(&rocks_options, Path::new(&path), cf_descriptors)
        .expect("Failed to open RocksDB with column families");

    let lake = DeltaLakeImporter {
        rocks_store: Arc::new(storage_db),
        table_uri: String::from("/home/<USER>/delta"),
    };

    lake.execute().await;
}

#[tokio::test]
async fn test_export_lake_schema() {

    let names = read_all_schemas("./delta/").await;
    println!("{:?}", names);
}
