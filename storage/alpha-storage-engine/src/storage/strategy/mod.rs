use std::{collections::HashMap, str::FromStr, sync::Arc};

use crate::states::{aggregation::InvestmentAggregation, aum::Aum, investment::Investment};
use chrono::{Days, NaiveDate};
use rkyv::Deserialize;
use rocksdb::{ColumnFamilyDescriptor, Options};

use super::{AggregationType, DbKeyPrefix, StorageWriter};

pub mod metrics;

#[derive(Clone, Debug)]
pub struct StrategyPortfolio {
    /// Start date of this Portfolio
    pub date: NaiveDate,

    /// Id of the Portfolio
    pub portfolio_id: String,
}

#[derive(Clone, Debug)]
pub struct StrategyWithStartDate {
    /// Start date of this Strategy
    pub date: NaiveDate,

    /// Id of the Strategy
    pub strategy_id: String,
}

/// StrategyId -> Vec<Portfolios>
pub type StrategyPortfolios = HashMap<String, Vec<StrategyPortfolio>>;

pub type StrategiesInvestments = HashMap<String, Vec<Investment>>;

impl StorageWriter {
    /// Fetches all strategies in the system
    pub fn get_all_strategies_with_start_date(&self) -> Vec<StrategyWithStartDate> {
        let prefix = format!("{}", DbKeyPrefix::StrategyStartDate);

        let mut strategies: Vec<StrategyWithStartDate> = Vec::new();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyStartDate-StrategyId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 2 || key_split[0] != DbKeyPrefix::StrategyStartDate.to_string() {
                        break;
                    }
                    let strategy_id = key_split[1].to_owned();

                    let data = value.to_vec();
                    let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
                    let date = archived.deserialize(&mut rkyv::Infallible).unwrap();
                    strategies.push(StrategyWithStartDate { date, strategy_id });
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        strategies
    }

    /// This fetch All the strategies and their respective portfolios
    pub fn get_all_strategies_and_portfolios(&self, current_date: NaiveDate) -> StrategyPortfolios {
        let prefix = format!("{}", DbKeyPrefix::StrategyPortfolios);

        let mut strategy_portfolios = StrategyPortfolios::default();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyPortfolios-StrategyId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 4 || key_split[0] != DbKeyPrefix::StrategyPortfolios.to_string() {
                        break;
                    }

                    let strategy_id = key_split[1].to_owned();

                    let date = NaiveDate::from_str(&key_split[2]);

                    match date {
                        Ok(date) => {
                            if date > current_date {
                                break;
                            }

                            let data = value.to_vec();
                            let archived = unsafe { rkyv::archived_root::<String>(&data[..]) };
                            let portfolio_id = archived.deserialize(&mut rkyv::Infallible).unwrap();
                            let portfolio = StrategyPortfolio { date, portfolio_id };

                            strategy_portfolios
                                .entry(strategy_id.clone())
                                .or_insert_with(Vec::new)
                                .push(portfolio);
                        }
                        Err(err) => break,
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        strategy_portfolios
    }

    pub fn get_strategy_portfolios_as_at(&self, strategy_id: &str, current_date: NaiveDate) -> Vec<StrategyPortfolio> {
        let prefix = format!("{}:{}", DbKeyPrefix::StrategyPortfolios, strategy_id);

        let mut strategy_portfolios = Vec::new();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyPortfolios-StrategyId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 4 || key_split[0] != DbKeyPrefix::StrategyPortfolios.to_string() {
                        break;
                    }

                    if key_split[1] != strategy_id {
                        break;
                    }

                    let date = NaiveDate::from_str(&key_split[2]);

                    match date {
                        Ok(date) => {
                            if date > current_date {
                                break;
                            }

                            let data = value.to_vec();
                            let archived = unsafe { rkyv::archived_root::<String>(&data[..]) };
                            let portfolio_id = archived.deserialize(&mut rkyv::Infallible).unwrap();
                            let portfolio = StrategyPortfolio { date, portfolio_id };

                            strategy_portfolios.push(portfolio);
                        }
                        Err(err) => {}
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        strategy_portfolios
    }

    pub fn get_strategy_portfolios(&self, strategy_id: &str) -> Vec<StrategyPortfolio> {
        let prefix = format!("{}:{}", DbKeyPrefix::StrategyPortfolios, strategy_id);

        let mut strategy_portfolios = Vec::new();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyPortfolios-StrategyId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 4 || key_split[0] != DbKeyPrefix::StrategyPortfolios.to_string() {
                        break;
                    }

                    if key_split[1] != strategy_id {
                        break;
                    }

                    let date = NaiveDate::from_str(&key_split[2]);

                    match date {
                        Ok(date) => {
                            let data = value.to_vec();
                            let archived = unsafe { rkyv::archived_root::<String>(&data[..]) };
                            let portfolio_id = archived.deserialize(&mut rkyv::Infallible).unwrap();
                            let portfolio = StrategyPortfolio { date, portfolio_id };

                            strategy_portfolios.push(portfolio);
                        }
                        Err(err) => {}
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        strategy_portfolios
    }

    pub fn insert_strategy_start_date(&self, model_id: &str, date: NaiveDate) {
        //Check if start date is already there and compare
        let model_start_date = self.get_strategy_start_date(model_id);
        if let Some(model_start_date) = model_start_date {
            if date < model_start_date {
                let key = format!("{}:{}", DbKeyPrefix::StrategyStartDate, model_id);
                self.db.put(key, rkyv::to_bytes::<_, 256>(&date).unwrap()).unwrap();
            }
        } else {
            let key = format!("{}:{}", DbKeyPrefix::StrategyStartDate, model_id);
            self.db.put(key, rkyv::to_bytes::<_, 256>(&date).unwrap()).unwrap();
        }
    }

    pub fn get_strategy_start_date(&self, model_id: &str) -> Option<NaiveDate> {
        let key = format!("{}:{}", DbKeyPrefix::StrategyStartDate.to_string(), model_id);

        let data = self.db.get(key).unwrap();

        if let Some(data) = data {
            let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
            let date = archived.deserialize(&mut rkyv::Infallible).unwrap();
            Some(date)
        } else {
            None
        }
    }

    pub fn insert_strategy_portfolio(&self, strategy_id: &str, portfolio_id: String, start_date: NaiveDate) {
        let key = format!(
            "{}:{}:{}:{}",
            DbKeyPrefix::StrategyPortfolios.to_string(),
            strategy_id,
            start_date,
            portfolio_id
        );

        self.db
            .put(key, rkyv::to_bytes::<_, 256>(&portfolio_id).unwrap())
            .unwrap();
    }

    pub async fn insert_aggregated_investment_for_strategy(
        &mut self,
        strategy_id: &str,
        investment_aggregation: &InvestmentAggregation,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}:{}:{}:{}",
            DbKeyPrefix::PortfolioAggregation.to_string(),
            AggregationType::Strategy,
            strategy_id,
            date
        );
        let serialized = rkyv::to_bytes::<InvestmentAggregation, 256>(investment_aggregation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    /// Insert AUM of a Strategy On Day T
    pub async fn insert_strategy_aum(&mut self, aum: &Aum, model_id: &str, date: NaiveDate) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::StrategyAum.to_string(), model_id, date);
        let serialized = rkyv::to_bytes::<Aum, 256>(aum).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub fn get_strategy_aum(&self, strategy_id: &str, date: NaiveDate) -> Option<Aum> {
        let key = format!("{}-{}-{}", DbKeyPrefix::StrategyAum.to_string(), strategy_id, date,);
        self.get_data(&key).unwrap()
    }

    pub fn get_strategy_aum_for_analytics_range(&self, date: NaiveDate, strategy_id: &str) -> Vec<Aum> {
        let mut start_date = self
            .get_strategy_start_date(strategy_id)
            .expect("Expect Portfolio Start Date");

        let end_date = date;

        let mut aums: Vec<Aum> = Vec::new();

        while start_date <= end_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::StrategyAum.to_string(), strategy_id);
            prefix.push_str(&start_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub fn get_strategy_aum_for_period(
        &self,
        model_id: &str,
        mut from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> Vec<Aum> {
        let mut aums: Vec<Aum> = Vec::new();

        while from_date <= to_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::StrategyAum.to_string(), model_id);
            prefix.push_str(&from_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            from_date = from_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub async fn insert_strategy_investments(
        &mut self,
        strategy_id: &str,
        investments: &Vec<Investment>,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}:{}:{}",
            DbKeyPrefix::StrategyInvestments.to_string(),
            strategy_id,
            date
        );
        let serialized = rkyv::to_bytes::<Vec<Investment>, 256>(investments).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn get_all_strategies_investments(
        &mut self,
        from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> StrategiesInvestments {
        let prefix = format!("{}:", DbKeyPrefix::StrategyInvestments.to_string());

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        let mut strategies_investments = StrategiesInvestments::default();

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyInvestments-StrategyId-Date
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    println!("{:?}", key_str);
                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 3 || key_split[0] != DbKeyPrefix::StrategyInvestments.to_string() {
                        break;
                    }
                    let strategy_id = key_split[1].to_owned();
                    let date = NaiveDate::from_str(&key_split[2]);
                    match date {
                        Ok(date) => {
                            if date > to_date {
                                break;
                            }

                            if date < from_date {
                                continue;
                            }

                            let data = value.to_vec();
                            let archived = unsafe { rkyv::archived_root::<Vec<Investment>>(&data[..]) };
                            let investments = archived.deserialize(&mut rkyv::Infallible).unwrap();

                            strategies_investments.insert(strategy_id, investments);
                        }
                        Err(err) => {
                            break;
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        strategies_investments
    }

    pub async fn get_strategy_investments(&mut self, strategy_id: &str, date: NaiveDate) -> Option<Vec<Investment>> {
        let key = format!(
            "{}:{}:{}",
            DbKeyPrefix::StrategyInvestments.to_string(),
            strategy_id,
            date
        );

        let data = self.db.get(key).unwrap();

        if let Some(data) = data {
            let archived = unsafe { rkyv::archived_root::<Vec<Investment>>(&data[..]) };
            let data = archived.deserialize(&mut rkyv::Infallible).unwrap();
            Some(data)
        } else {
            None
        }
    }
}

#[tokio::test]
async fn test_fetch_using_full_key() {
    dotenv::dotenv().ok();
    let mut rocks_options = Options::default();
    let path = std::env::var("PERF_ENGINE_DB_PATH").expect("Failed to load database host from env");
    let cf_descriptors = vec![
        ColumnFamilyDescriptor::new("default", Options::default()),
        ColumnFamilyDescriptor::new("nse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("bse_security_price", Options::default()),
        ColumnFamilyDescriptor::new("mf_security_price", Options::default()),
        ColumnFamilyDescriptor::new("isin_history", Options::default()),
        ColumnFamilyDescriptor::new("isin_history_old", Options::default()),
    ];
    let storage_db = rocksdb::DB::open_cf_descriptors(&rocks_options, std::path::Path::new(&path), cf_descriptors)
        .expect("Failed to open RocksDB with column families");
    let storage_writer = StorageWriter::new(Arc::new(storage_db));

    // Now try to fetch it using the exact same key format
    let key = format!(
        "{}:{}:{}",
        DbKeyPrefix::StrategyInvestments.to_string(),
        "4f1cd13725984bfebc49223cec124566",
        "2025-02-11"
    );
    println!("{:?}", key);
    // Fetch the data directly using the key
    let result = storage_writer.db.get(key.as_bytes()).unwrap();
    println!("{:?}", result);
    // Assert data was found
    assert!(result.is_some(), "data found for key: {}", key);
}
