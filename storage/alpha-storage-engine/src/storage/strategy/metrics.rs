use chrono::NaiveDate;

use crate::{
    states::metrics::portfolio::{TwrrResults, XirrResults},
    storage::{DbKeyPrefix, StorageWriter},
};

impl StorageWriter {
    pub async fn insert_strategy_xirr_metrics(&mut self, strategy_id: &str, date: NaiveDate, metrics: XirrResults) {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::StrategyXirrMetrics.to_string(),
            strategy_id,
            date
        );
        let serialized = rkyv::to_bytes::<_, 256>(&metrics).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_strategy_twrr_metrics(&mut self, strategy_id: &str, date: NaiveDate, metrics: TwrrResults) {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::StrategyTwrrMetrics.to_string(),
            strategy_id,
            date
        );
        let serialized = rkyv::to_bytes::<_, 256>(&metrics).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }


    pub fn get_strategy_xirr_metrics(&self, strategy_id: &str, date: NaiveDate) -> Result<Option<XirrResults>, String> {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::StrategyXirrMetrics.to_string(),
            strategy_id,
            date
        );

        self.get_data(&key)
    }
}
