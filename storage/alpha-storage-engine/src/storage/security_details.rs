use chrono::NaiveDate;

use super::StorageWriter;

impl StorageWriter {
    pub fn get_nse_price(&self, isin: &str, date: NaiveDate) -> Option<f64> {
        let cf = self.db.cf_handle("nse_security_price").unwrap();
        let db = self.db.get_cf(&cf, format!("{}-{}", isin, date)).unwrap();
        if let Some(data) = db {
            let price = serde_json::from_slice::<f64>(&data).unwrap();
            return Some(price);
        }

        None
    }

    pub fn get_mf_price(&self, isin: &str, date: NaiveDate) -> Option<f64> {
        let cf = self.db.cf_handle("mf_security_price").unwrap();
        let db = self.db.get_cf(&cf, format!("{}-{}", isin, date)).unwrap();
        if let Some(data) = db {
            let price = serde_json::from_slice::<f64>(&data).unwrap();
            return Some(price);
        }

        None
    }

    pub fn get_bse_price(&self, isin: &str, date: NaiveDate) -> Option<f64> {
        let cf = self.db.cf_handle("bse_security_price").unwrap();
        let db = self.db.get_cf(&cf, format!("{}-{}", isin, date)).unwrap();
        if let Some(data) = db {
            let price = serde_json::from_slice::<f64>(&data).unwrap();
            return Some(price);
        }

        None
    }

    pub fn get_old_isin(&self, new_isin: &str) -> Option<String> {
        let cf = self.db.cf_handle("isin_history").unwrap();

        let db = self.db.get_cf(&cf, new_isin).unwrap();
        if let Some(data) = db {
            let old_isin = String::from_utf8(data).unwrap();
            return Some(old_isin);
        }

        None
    }

    pub fn get_new_isin(&self, old_isin: &str) -> Option<String> {
        let cf = self.db.cf_handle("isin_history_old").unwrap();

        let db = self.db.get_cf(&cf, old_isin).unwrap();
        if let Some(data) = db {
            let new_isin = String::from_utf8(data).unwrap();
            return Some(new_isin);
        }

        None
    }

    /// Gets the price for a security, trying multiple sources if needed
    #[allow(unused)]
    fn get_security_price(&self, isin: &str, symbol: &str, date: NaiveDate) -> f64 {
        // Try NSE price with ISIN
        let mut price = self.get_nse_price(isin, date).unwrap_or(0f64);

        if price == 0f64 {
            // Try NSE price with symbol
            price = self.get_nse_price(symbol, date).unwrap_or(0f64);

            if price == 0f64 {
                // Check if the ISIN has changed
                if let Some(old_isin) = self.get_old_isin(isin) {
                    price = self.get_nse_price(&old_isin, date).unwrap_or(0f64);
                } else {
                    // Try BSE price
                    price = self.get_bse_price(isin, date).unwrap_or(0f64);

                    if price == 0f64 {
                        // Try mutual fund price
                        price = self.get_mf_price(isin, date).unwrap_or(0f64);
                    }
                }
            }
        }

        price
    }
}
