use std::str::FromStr;

use chrono::NaiveDate;
use rkyv::Deserialize;

use crate::states::analytics::TransactionForAnalytics;

use super::{DbKeyPrefix, StorageWriter};

impl StorageWriter {
    /// Returns the transaction from start date of portfolio till to_date mentioned
    pub fn get_transactions_for_analytics_range(
        &self,
        portfolio_id: &str,
        to_date: NaiveDate,
    ) -> Vec<TransactionForAnalytics> {
        let key = format!("{}-{}", DbKeyPrefix::PortfolioStartDate.to_string(), portfolio_id);

        let start_date: NaiveDate = self.get_data(&key).unwrap().unwrap();

        let prefix = format!(
            "{}-{}-{}",
            DbKeyPrefix::TransactionForAnalytics,
            portfolio_id,
            start_date
        );

        let mut txns = Vec::new();
        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let key_split: Vec<&str> = key_str.split("-").collect();

                    let date =
                        NaiveDate::from_str(&(key_split[2].to_owned() + "-" + key_split[3] + "-" + key_split[4]))
                            .unwrap();

                    if date > to_date
                        || key_split[0] != DbKeyPrefix::TransactionForAnalytics.to_string()
                        || key_split[1] != portfolio_id
                    {
                        break;
                    }
                    let data = value.to_vec();
                    let archived = unsafe { rkyv::archived_root::<Vec<TransactionForAnalytics>>(&data[..]) };
                    let mut tx = archived.deserialize(&mut rkyv::Infallible).unwrap();

                    txns.append(&mut tx);
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        txns
    }

    pub async fn insert_transactions(
        &mut self,
        transactions: &Vec<crate::states::transaction::Transaction>,
        portfolio_id: String,
        date: NaiveDate,
    ) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::Transaction.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<Vec<crate::states::transaction::Transaction>, 256>(transactions).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub async fn insert_transaction_for_analytics(
        &mut self,
        portfolio_id: &str,
        date: NaiveDate,
        txns: &Vec<TransactionForAnalytics>,
    ) {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::TransactionForAnalytics.to_string(),
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<Vec<TransactionForAnalytics>, 256>(txns).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }
}
