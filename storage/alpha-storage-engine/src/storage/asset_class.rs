use chrono::NaiveDate;

use crate::states::analytics::asset_class::AssetClassAllocation;

use super::{AnalyticsPrefix, DbKeyPrefix, StorageWriter};

impl StorageWriter {
    pub async fn insert_assetclass_allocation(
        &mut self,
        portfolio_id: &str,
        allocation: &AssetClassAllocation,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::AssetClassAllocation.to_string(),
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<AssetClassAllocation, 256>(allocation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }
}
