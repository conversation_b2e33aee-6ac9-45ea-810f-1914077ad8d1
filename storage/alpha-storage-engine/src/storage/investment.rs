use chrono::NaiveDate;

use crate::states::aggregation::InvestmentAggregation;

use super::{AggregationType, DbKeyPrefix, StorageWriter};

impl StorageWriter {
    pub async fn insert_aggregated_investment_for_portfolio(
        &mut self,
        portfolio_id: &str,
        aggregation_type: AggregationType,
        investment_aggregation: &Vec<InvestmentAggregation>,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::PortfolioAggregation.to_string(),
            aggregation_type,
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<Vec<InvestmentAggregation>, 256>(investment_aggregation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }
}
