use chrono::{Days, NaiveDate};
use rkyv::{Archive, Deserialize};
use rocksdb::{WriteBatch, WriteOptions, DB};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::{fmt, sync::Arc};
use tokio::sync::Mutex;
pub mod asset_class;
pub mod benchmark;
pub mod clients;
pub mod error;
pub mod investment;
pub mod metrics;
pub mod model;
pub mod portfolio;
pub mod security_details;
pub mod strategy;
pub mod transaction;

use crate::{
    block::portfolio_block::PortfolioBlock,
    states::{
        analytics::{
            asset_class::AssetClassAllocation, industries::IndustriesAllocation, investment_returns::InvestmentReturns,
            market_cap_allocation::MarketCapAllocation,
        },
        aum::Aum,
        capital_register::CapitalRegister,
        investment::Investment,
        ledger::Ledger,
        portfolio::Portfolio,
        quantity_queue::QuantityQueue,
        returns::Returns,
        transaction::Transaction,
    },
};

pub enum DbKeyPrefix {
    LatestBlock,
    PortfolioInvestment,
    CapitalRegister,
    Portfolio,
    PortfolioBlock,
    Transaction,
    TransactionForAnalytics,
    Ledger,
    PortfolioAum,
    ModelAum,
    StrategyAum,
    ClientAum,
    QuantityQueue,
    PortfolioPayable,
    PortfolioReturns,
    InvestmentReturns,
    PortfolioStartDate,
    ClientInvestment,
    ModelInvestment,
    Analytics,
    PortfolioAggregation,
    ClientsPortfolio,
    ClientStartDate,
    ModelsPortfolio,
    ModelsStartDate,
    XirrMetrics,
    StrategyXirrMetrics,
    TwrrMetrics,
    StrategyTwrrMetrics,
    StrategyStartDate,
    StrategyPortfolios,
    StrategyInvestments,
    BenchmarkPrice,
}

pub enum AggregationType {
    AssetType,
    AssetClass,
    Sector,
    MarketCap,
    Industry,
    Portfolio,
    Client,
    Strategy,
    StrategyModel,
    Amc,
}

pub enum AnalyticsPrefix {
    AssetClassAllocation,
    MarketCapAllocation,
    IndustriesAllocation,
}

impl fmt::Display for AggregationType {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            AggregationType::AssetType => write!(f, "ASSET_TYPE"),
            AggregationType::AssetClass => write!(f, "ASSET_CLASS"),
            AggregationType::Sector => write!(f, "SECTOR"),
            AggregationType::MarketCap => write!(f, "MARKET_CAP"),
            AggregationType::Portfolio => write!(f, "PORTFOLIO"),
            AggregationType::Industry => write!(f, "INDUSTRY"),
            AggregationType::Client => write!(f, "CLIENT"),
            AggregationType::Strategy => write!(f, "STRATEGY"),
            AggregationType::StrategyModel => write!(f, "STRATEGYMODEL"),
            AggregationType::Amc => write!(f, "AMC"),
        }
    }
}

impl fmt::Display for AnalyticsPrefix {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            AnalyticsPrefix::AssetClassAllocation => write!(f, "ASSETCLASSALLOCATION"),
            AnalyticsPrefix::MarketCapAllocation => write!(f, "MARKETCAPALLOCATION"),
            AnalyticsPrefix::IndustriesAllocation => write!(f, "INDUSTRIESALLOCATION"),
        }
    }
}

impl fmt::Display for DbKeyPrefix {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            DbKeyPrefix::LatestBlock => write!(f, "LATEST_BLOCK"),
            DbKeyPrefix::PortfolioInvestment => write!(f, "PORTFOLIOINVESTMENT"),
            DbKeyPrefix::Portfolio => write!(f, "PORTFOLIO"),
            DbKeyPrefix::PortfolioBlock => write!(f, "PORTFOLIOBLOCK"),
            DbKeyPrefix::Transaction => write!(f, "TRANSACTION"),
            DbKeyPrefix::TransactionForAnalytics => write!(f, "TRANSACTIONFORANALYTICS"),
            DbKeyPrefix::CapitalRegister => write!(f, "CAPITALREGISTER"),
            DbKeyPrefix::Ledger => write!(f, "LEDGER"),
            DbKeyPrefix::PortfolioAum => write!(f, "PORTFOLIOAUM"),
            DbKeyPrefix::ModelAum => write!(f, "MODELAUM"),
            DbKeyPrefix::StrategyAum => write!(f, "STRATEGYAUM"),
            DbKeyPrefix::ClientAum => write!(f, "CLIENTAUM"),
            DbKeyPrefix::QuantityQueue => write!(f, "QUANTITYQUEUE"),
            DbKeyPrefix::PortfolioPayable => write!(f, "PORTFOLIOPAYABLE"),
            DbKeyPrefix::PortfolioReturns => write!(f, "PORTFOLIORETURNS"),
            DbKeyPrefix::InvestmentReturns => write!(f, "INVESTMENTRETURNS"),
            DbKeyPrefix::PortfolioStartDate => write!(f, "PORTFOLIOSTARTDATE"),
            DbKeyPrefix::ClientInvestment => write!(f, "CLIENTINVESTMENT"),
            DbKeyPrefix::ModelInvestment => write!(f, "MODELINVESTMENT"),
            DbKeyPrefix::Analytics => write!(f, "ANALYTICS"),
            DbKeyPrefix::PortfolioAggregation => write!(f, "PORTFOLIOAGGREGATION"),
            DbKeyPrefix::ClientsPortfolio => write!(f, "CLIENTSPORTFOLIO"),
            DbKeyPrefix::ClientStartDate => write!(f, "CLIENTSTARTDATE"),
            DbKeyPrefix::ModelsPortfolio => write!(f, "MODELSPORTFOLIO"),
            DbKeyPrefix::ModelsStartDate => write!(f, "MODELSSTARTDATE"),
            DbKeyPrefix::XirrMetrics => write!(f, "XIRRMETRICS"),
            DbKeyPrefix::TwrrMetrics => write!(f, "TWRRMETRICS"),
            DbKeyPrefix::StrategyXirrMetrics => write!(f, "STRATEGYXIRRMETRICS"),
            DbKeyPrefix::StrategyTwrrMetrics => write!(f, "STRATEGYTWRRMETRICS"),
            DbKeyPrefix::StrategyStartDate => write!(f, "STRATEGYSTARTDATE"),
            DbKeyPrefix::StrategyPortfolios => write!(f, "STRATEGYPORTFOLIOS"),
            DbKeyPrefix::StrategyInvestments => write!(f, "STRATEGYINVESTMENTS"),
            DbKeyPrefix::BenchmarkPrice => write!(f, "BENCHMARKPRICE"),
        }
    }
}

pub struct Storage {
    pub db: Arc<rocksdb::DB>,
}

impl Storage {
    pub fn new(path: &str) -> Result<Self, String> {
        let db = DB::open_default(path).unwrap();
        Ok(Self { db: Arc::new(db) })
    }
}

pub struct StorageWriter {
    pub db: Arc<rocksdb::DB>,
    batch: Option<Arc<Mutex<WriteBatch>>>,
}

impl StorageWriter {
    pub fn new(db: Arc<rocksdb::DB>) -> Self {
        Self { db, batch: None }
    }

    pub fn begin_batch(&mut self) {
        self.batch = Some(Arc::new(Mutex::new(WriteBatch::default())));
    }

    pub fn cancel_batch(&mut self) {
        self.batch = None;
    }

    pub async fn commit_batch(mut self) -> Result<(), String> {
        if let Some(batch) = self.batch.take() {
            let batch = match Arc::try_unwrap(batch) {
                Ok(mutex) => mutex.into_inner(),
                Err(_) => {
                    panic!("Unable to get exclusive ownership of the batch");
                }
            };
            let write_options = WriteOptions::default();
            self.db.write_opt(batch, &write_options).unwrap();
        }
        Ok(())
    }

    pub fn get_all_ledgers(&self) {
        // Create an iterator with the given prefix
        let iter = self.db.prefix_iterator(DbKeyPrefix::PortfolioInvestment.to_string());

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    let value_str = String::from_utf8(value.to_vec()).unwrap();

                    if key_str.starts_with("INVESTMENT") {
                        println!("Key: {} Value:{}", key_str, value_str);
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }
    }

    pub fn get_payables(&self, portfolio_id: &str, settlement_date: NaiveDate) -> Decimal {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::PortfolioPayable.to_string(),
            portfolio_id,
            settlement_date,
        );

        let payable: Option<Decimal> = self.get_data(&key).unwrap();
        if let Some(payable) = payable {
            payable
        } else {
            dec!(0)
        }
    }

    pub fn get_aum_for_analytics_range(&self, date: NaiveDate, portfolio_id: &str) -> Vec<Aum> {
        let mut start_date = self
            .get_portfolio_start_date(portfolio_id)
            .expect("Expect Portfolio Start Date");

        let end_date = date;

        let mut aums: Vec<Aum> = Vec::new();

        while start_date <= end_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::PortfolioAum.to_string(), portfolio_id);
            prefix.push_str(&start_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub fn get_aum_for_period(&self, portfolio_id: &str, mut from_date: NaiveDate, to_date: NaiveDate) -> Vec<Aum> {
        let mut aums: Vec<Aum> = Vec::new();

        while from_date <= to_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::PortfolioAum.to_string(), portfolio_id);
            prefix.push_str(&from_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            from_date = from_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub fn get_data<T>(&self, key: &str) -> Result<Option<T>, String>
    where
        T: Archive,
        T::Archived: Deserialize<T, rkyv::Infallible>,
    {
        match self.db.get(key).unwrap() {
            Some(bytes) => {
                // Safety: This is safe because we trust that the bytes we stored
                // were serialized correctly using rkyv
                let archived = unsafe { rkyv::archived_root::<T>(&bytes[..]) };
                match archived.deserialize(&mut rkyv::Infallible) {
                    Ok(value) => Ok(Some(value)),
                    Err(_) => Err("Failed to deserialize data".to_string()),
                }
            }
            None => Ok(None),
        }
    }

    pub fn get_latest_block(&self, portfolio_id: &str) -> Option<NaiveDate> {
        let key = format!("{}-{}", DbKeyPrefix::LatestBlock.to_string(), portfolio_id);
        self.get_data(&key).unwrap()
    }

    pub fn get_portfolio_block(&self, portfolio_id: &str, date: NaiveDate) -> Option<PortfolioBlock> {
        let key = format!("{}-{}-{}", DbKeyPrefix::PortfolioBlock.to_string(), portfolio_id, date,);
        self.get_data(&key).unwrap()
    }

    pub fn get_investment_returns(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<InvestmentReturns>> {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::InvestmentReturns.to_string(),
            portfolio_id,
            date,
        );
        self.get_data(&key).unwrap()
    }

    pub fn get_ledgers(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<Ledger>> {
        let key = format!("{}-{}-{}", DbKeyPrefix::Ledger.to_string(), portfolio_id, date,);

        self.get_data(&key).unwrap()
    }

    pub fn get_quantity_queue(&self, portfolio_id: &str, isin: &str, date: NaiveDate) -> Option<QuantityQueue> {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::QuantityQueue.to_string(),
            portfolio_id,
            isin,
            date,
        );

        self.get_data(&key).unwrap()
    }

    pub fn get_investment_by_key(&self, key: &str) -> Option<Vec<Investment>> {
        self.get_data(key).unwrap()
    }

    pub fn get_asset_class_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<AssetClassAllocation> {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::AssetClassAllocation.to_string(),
            portfolio_id,
            date,
        );

        self.get_data(&key).unwrap()
    }

    pub fn get_industries_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<IndustriesAllocation>> {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::IndustriesAllocation.to_string(),
            portfolio_id,
            date,
        );

        self.get_data(&key).unwrap()
    }

    pub fn get_market_cap_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<MarketCapAllocation> {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::MarketCapAllocation.to_string(),
            portfolio_id,
            date,
        );

        self.get_data(&key).unwrap()
    }

    pub async fn insert_latest_block(&mut self, date: NaiveDate, portfolio_id: String) -> String {
        let key = format!("{}-{}", DbKeyPrefix::LatestBlock.to_string(), portfolio_id);
        let serialized = rkyv::to_bytes::<_, 256>(&date).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub async fn insert_ledgers(&mut self, ledgers: &Vec<Ledger>, portfolio_id: String, date: NaiveDate) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::Ledger.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<Vec<Ledger>, 256>(ledgers).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub async fn insert_capital_registers(
        &mut self,
        capital_register_txns: &Vec<CapitalRegister>,
        portfolio_id: String,
        date: NaiveDate,
    ) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::CapitalRegister.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<Vec<CapitalRegister>, 256>(capital_register_txns).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub async fn insert_portfolio_block(
        &mut self,
        portfolio_block: &PortfolioBlock,
        portfolio_id: String,
        date: NaiveDate,
    ) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::PortfolioBlock.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<PortfolioBlock, 256>(portfolio_block).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub async fn insert_quantity_queue(
        &mut self,
        portfolio_id: String,
        isin: String,
        date: NaiveDate,
        quantity_queue: QuantityQueue,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::QuantityQueue.to_string(),
            portfolio_id,
            isin,
            date
        );
        let serialized = rkyv::to_bytes::<_, 256>(&quantity_queue).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_payables(&mut self, portfolio_id: String, settlement_date: NaiveDate, payable_amount: Decimal) {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::PortfolioPayable.to_string(),
            portfolio_id,
            settlement_date
        );
        let serialized = rkyv::to_bytes::<_, 256>(&payable_amount).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_marketcap_allocation(
        &mut self,
        portfolio_id: &str,
        allocation: &MarketCapAllocation,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::MarketCapAllocation.to_string(),
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<MarketCapAllocation, 256>(allocation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_industries_allocation(
        &mut self,
        portfolio_id: &str,
        allocation: &Vec<IndustriesAllocation>,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::Analytics.to_string(),
            AnalyticsPrefix::IndustriesAllocation.to_string(),
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<Vec<IndustriesAllocation>, 256>(allocation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }
}

pub trait StorageOperations {
    fn get_data<T: serde::de::DeserializeOwned>(&self, key: &str) -> Option<T>;
    fn put_data<T: serde::Serialize>(&self, key: &str, value: &T) -> Result<(), Box<dyn std::error::Error>>;
    fn get_latest_block(&self, portfolio_id: &str) -> Option<NaiveDate>;
    fn get_portfolio_block(&self, portfolio_id: &str, date: NaiveDate) -> Option<PortfolioBlock>;
    fn get_investment(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<Investment>>;
    fn get_investment_by_key(&self, key: &str) -> Option<Vec<Investment>>;
    fn get_portfolio(&self, portfolio_id: &str, date: NaiveDate) -> Option<Portfolio>;
    fn get_portfolio_by_key(&self, key: &str) -> Option<Portfolio>;
    fn get_ledgers(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<Ledger>>;
    fn get_transactions_by_key(&self, key: &str) -> Option<Vec<Transaction>>;
    fn get_portfolio_aum(&self, portfolio_id: &str, date: NaiveDate) -> Option<Aum>;
    fn get_portfolio_returns(&self, portfolio_id: &str, date: NaiveDate) -> Option<Returns>;
    fn get_quantity_queue(&self, portfolio_id: &str, isin: &str, date: NaiveDate) -> Option<QuantityQueue>;
    fn get_portfolio_start_date(&self, portfolio_id: &str) -> Option<NaiveDate>;
    fn get_client_investment(&self, client_id: &str, date: NaiveDate) -> Option<Vec<Investment>>;
    fn get_model_investment(&self, model_id: &str, date: NaiveDate) -> Option<Vec<Investment>>;
    fn get_investment_returns(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<InvestmentReturns>>;
    fn get_market_cap_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<MarketCapAllocation>;

    fn get_industries_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<IndustriesAllocation>;
    fn get_asset_class_allocation(&self, portfolio_id: &str, date: NaiveDate) -> Option<AssetClassAllocation>;
}
