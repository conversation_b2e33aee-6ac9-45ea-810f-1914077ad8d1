use std::str::FromStr;

use chrono::NaiveDate;
use rkyv::Deserialize;

use crate::states::{aggregation::InvestmentAggregation, aum::Aum, investment::Investment};

use super::{AggregationType, DbKeyPrefix, StorageWriter};

pub struct ClientPortfolios {
    pub date: NaiveDate,
    pub portfolio_id: String,
}

impl StorageWriter {
    pub async fn insert_aggregated_investment_for_client(
        &mut self,
        client_id: &str,
        investment_aggregation: &InvestmentAggregation,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}={}",
            DbKeyPrefix::PortfolioAggregation.to_string(),
            AggregationType::Client,
            client_id,
            date
        );
        let serialized = rkyv::to_bytes::<InvestmentAggregation, 256>(investment_aggregation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_clients_portfolio(&mut self, client_id: &str, portfolio_id: String, start_date: NaiveDate) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::ClientsPortfolio.to_string(),
            client_id,
            start_date,
            portfolio_id
        );
        let serialized = rkyv::to_bytes::<_, 256>(&start_date).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_client_start_date(&mut self, client_id: &str, date: NaiveDate) {
        let client_start_date = self.get_client_start_date(client_id);
        let key = format!("{}-{}", DbKeyPrefix::ClientStartDate, client_id);
        let serialized = rkyv::to_bytes::<_, 256>(&date).unwrap();

        if let Some(client_start_date) = client_start_date {
            if date < client_start_date {
                if let Some(batch) = self.batch.as_mut() {
                    let mut batch_guard = batch.lock().await;
                    batch_guard.put(&key, serialized);
                } else {
                    self.db.put(&key, serialized).unwrap();
                }
            }
        } else {
            if let Some(batch) = self.batch.as_mut() {
                let mut batch_guard = batch.lock().await;
                batch_guard.put(&key, serialized);
            } else {
                self.db.put(&key, serialized).unwrap();
            }
        }
    }

    pub async fn insert_client_investment(
        &mut self,
        investments: Vec<Investment>,
        client_id: &str,
        date: NaiveDate,
    ) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::ClientInvestment.to_string(), client_id, date);
        let serialized = rkyv::to_bytes::<_, 256>(&investments).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Insert AUM of a Strategy On Day T
    pub async fn insert_client_aum(&mut self, aum: &Aum, client_id: &str, date: NaiveDate) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::ClientAum.to_string(), client_id, date);
        let serialized = rkyv::to_bytes::<Aum, 256>(aum).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub fn get_client_aum(&self, client_id: &str, date: NaiveDate) -> Option<Aum> {
        let key = format!("{}-{}-{}", DbKeyPrefix::ClientAum.to_string(), client_id, date,);
        self.get_data(&key).unwrap()
    }

    pub fn get_client_start_date(&self, client_id: &str) -> Option<NaiveDate> {
        let key = format!("{}-{}", DbKeyPrefix::ClientStartDate.to_string(), client_id);

        let data = self.db.get(key).unwrap();

        if let Some(data) = data {
            let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
            let date = archived.deserialize(&mut rkyv::Infallible).unwrap();
            Some(date)
        } else {
            None
        }
    }

    pub fn get_client_aggregated_investment(
        &self,
        client_id: &str,
        date: NaiveDate,
    ) -> Result<Option<InvestmentAggregation>, String> {
        let key = format!(
            "{}-{}-{}={}",
            DbKeyPrefix::PortfolioAggregation.to_string(),
            AggregationType::Client,
            client_id,
            date
        );

        self.get_data(&key)
    }

    pub fn get_client_investment(&self, client_id: &str, date: NaiveDate) -> Option<Vec<Investment>> {
        let key = format!("{}-{}-{}", DbKeyPrefix::ClientInvestment.to_string(), client_id, date,);
        self.get_data(&key).unwrap()
    }

    /// Date is the Current State Date
    pub fn get_client_portfolios(&self, client_id: &str, current_date: NaiveDate) -> Vec<ClientPortfolios> {
        let prefix = format!("{}-{}", DbKeyPrefix::ClientsPortfolio, client_id);

        let mut client_portfolios = Vec::new();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have ClientPortfolios-ClientId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();
                    println!("{:?}", key_str);
                    let key_split: Vec<&str> = key_str.split("-").collect();
                    if key_split[0] != DbKeyPrefix::ClientsPortfolio.to_string() || key_split[1] != client_id {
                        break;
                    }
                    let portfolio_id = key_split[5].to_owned();

                    let date =
                        NaiveDate::from_str(&(key_split[2].to_owned() + "-" + key_split[3] + "-" + key_split[4]))
                            .unwrap();

                    if date > current_date {
                        break;
                    }

                    let data = value.to_vec();
                    let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
                    let date = archived.deserialize(&mut rkyv::Infallible).unwrap();

                    client_portfolios.push(ClientPortfolios { date, portfolio_id });
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        client_portfolios
    }
}
