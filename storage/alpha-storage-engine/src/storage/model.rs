use std::str::FromStr;

use chrono::{Days, NaiveDate};
use rkyv::Deserialize;

use crate::states::{aggregation::InvestmentAggregation, aum::Aum, investment::Investment};

use super::{AggregationType, DbKeyPrefix, StorageWriter};

pub struct StrategyModelPortfolios {
    pub date: NaiveDate,
    pub portfolio_id: String,
}

impl StorageWriter {
    /// Get All the Portfolios for the given ModelId that existed on the provided date
    pub fn get_strategy_model_portfolios(
        &self,
        model_id: &str,
        current_date: NaiveDate,
    ) -> Vec<StrategyModelPortfolios> {
        let prefix = format!("{}-{}", DbKeyPrefix::ModelsPortfolio, model_id);

        let mut model_portfolios = Vec::new();

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have ClientPortfolios-ModelId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();

                    let key_split: Vec<&str> = key_str.split("-").collect();
                    let portfolio_id = key_split[5].to_owned();
                    if key_split[1] != model_id {
                        break;
                    }

                    let date =
                        NaiveDate::from_str(&(key_split[2].to_owned() + "-" + key_split[3] + "-" + key_split[4]))
                            .unwrap();

                    if date > current_date {
                        break;
                    }

                    let data = value.to_vec();
                    let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
                    let date = archived.deserialize(&mut rkyv::Infallible).unwrap();

                    model_portfolios.push(StrategyModelPortfolios { date, portfolio_id });
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        model_portfolios
    }

    /// Function to Insert the Start date of each model
    pub async fn insert_strategy_model_start_date(&mut self, model_id: &str, date: NaiveDate) {
        //Check if start date is already there and compare
        let model_start_date = self.get_strategy_model_start_date(model_id);
        if let Some(model_start_date) = model_start_date {
            if date < model_start_date {
                let key = format!("{}-{}", DbKeyPrefix::ModelsStartDate, model_id);
                let serialized = rkyv::to_bytes::<_, 256>(&date).unwrap();

                if let Some(batch) = self.batch.as_mut() {
                    let mut batch_guard = batch.lock().await;
                    batch_guard.put(&key, serialized);
                } else {
                    self.db.put(&key, serialized).unwrap();
                }
            }
        } else {
            let key = format!("{}-{}", DbKeyPrefix::ModelsStartDate, model_id);
            let serialized = rkyv::to_bytes::<_, 256>(&date).unwrap();

            if let Some(batch) = self.batch.as_mut() {
                let mut batch_guard = batch.lock().await;
                batch_guard.put(&key, serialized);
            } else {
                self.db.put(&key, serialized).unwrap();
            }
        }
    }

    /// Retrieve the Model Start Date
    pub fn get_strategy_model_start_date(&self, model_id: &str) -> Option<NaiveDate> {
        let key = format!("{}-{}", DbKeyPrefix::ModelsStartDate.to_string(), model_id);

        let data = self.db.get(key).unwrap();

        if let Some(data) = data {
            let archived = unsafe { rkyv::archived_root::<NaiveDate>(&data[..]) };
            let date = archived.deserialize(&mut rkyv::Infallible).unwrap();
            Some(date)
        } else {
            None
        }
    }

    pub async fn insert_strategy_models_portfolio(
        &mut self,
        model_id: &str,
        portfolio_id: String,
        start_date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::ModelsPortfolio.to_string(),
            model_id,
            start_date,
            portfolio_id
        );

        let serialized = rkyv::to_bytes::<_, 256>(&start_date).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_aggregated_investment_for_strategy_model(
        &mut self,
        client_id: &str,
        investment_aggregation: &InvestmentAggregation,
        date: NaiveDate,
    ) {
        let key = format!(
            "{}-{}-{}-{}",
            DbKeyPrefix::PortfolioAggregation.to_string(),
            AggregationType::StrategyModel,
            client_id,
            date
        );
        let serialized = rkyv::to_bytes::<InvestmentAggregation, 256>(investment_aggregation).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_model_investment(
        &mut self,
        investments: Vec<Investment>,
        model_id: &str,
        date: NaiveDate,
    ) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::ModelInvestment.to_string(), model_id, date);
        let serialized = rkyv::to_bytes::<_, 256>(&investments).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Insert AUM of a Model On Day T
    pub async fn insert_model_aum(&mut self, aum: &Aum, model_id: &str, date: NaiveDate) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::ModelAum.to_string(), model_id, date);
        let serialized = rkyv::to_bytes::<Aum, 256>(aum).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    pub fn get_model_aum_for_analytics_range(&self, date: NaiveDate, model_id: &str) -> Vec<Aum> {
        let mut start_date = self
            .get_strategy_model_start_date(model_id)
            .expect("Expect Portfolio Start Date");

        let end_date = date;

        let mut aums: Vec<Aum> = Vec::new();

        while start_date <= end_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::StrategyAum.to_string(), model_id);
            prefix.push_str(&start_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub fn get_model_aum_for_period(&self, model_id: &str, mut from_date: NaiveDate, to_date: NaiveDate) -> Vec<Aum> {
        let mut aums: Vec<Aum> = Vec::new();

        while from_date <= to_date {
            let mut prefix: String = format!("{}-{}-", DbKeyPrefix::ModelAum.to_string(), model_id);
            prefix.push_str(&from_date.to_string());

            let aum = self.db.get(prefix);
            match aum {
                Ok(aum) => {
                    if let Some(aum) = aum {
                        let archived = unsafe { rkyv::archived_root::<Aum>(&aum[..]) };
                        let aum_des = archived.deserialize(&mut rkyv::Infallible);

                        if let Ok(aum) = aum_des {
                            aums.push(aum);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }

            from_date = from_date.checked_add_days(Days::new(1)).unwrap();
        }

        aums
    }

    pub fn get_model_investment(&self, model_id: &str, date: NaiveDate) -> Option<Vec<Investment>> {
        let key = format!("{}-{}-{}", DbKeyPrefix::ModelInvestment.to_string(), model_id, date,);
        self.get_data(&key).unwrap()
    }
}
