use chrono::{Days, NaiveDate};
use rkyv::Deserialize;

use crate::{
    states::{benchmark::BenchmarkPrice, metrics::benchmark::BenchmarkXirr},
    utils::benchmark::BenchmarkIndices,
};

use super::{DbKeyPrefix, StorageWriter};

impl StorageWriter {
    pub fn get_benchmark_price_as_at(
        &self,
        index: &BenchmarkIndices,
        mut date: NaiveDate,
    ) -> Result<Option<BenchmarkPrice>, String> {
        let cf_benchmark_price = self.db.cf_handle("benchmark_price").unwrap();
        let mut count = 0;
        loop {
            let key = format!("{}:{}:{}", DbKeyPrefix::BenchmarkPrice, index, date);
            let db = self.db.get_cf(&cf_benchmark_price, key)?;

            if count == 30 {
                return Ok(None);
            }
            match db {
                Some(bytes) => {
                    let archived = unsafe { rkyv::archived_root::<BenchmarkPrice>(&bytes[..]) };
                    match archived.deserialize(&mut rkyv::Infallible) {
                        Ok(value) => return Ok(Some(value)),
                        Err(_) => return Err("Failed to deserialize data".to_string()),
                    }
                }
                None => {
                    date = date.checked_sub_days(Days::new(1)).unwrap();
                }
            }

            count += 1;
        }
    }

    pub async fn insert_benchmark_xirr_metrics(
        &mut self,
        portfolio_id: &str,
        date: NaiveDate,
        index: &BenchmarkIndices,
        metrics: BenchmarkXirr,
    ) {
        let key = format!(
            "{}:{}:{}:{}",
            DbKeyPrefix::XirrMetrics.to_string(),
            index,
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<_, 256>(&metrics).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub fn get_benchmark_xirr_metrics(
        &self,
        portfolio_id: &str,
        date: NaiveDate,
        index: &BenchmarkIndices,
    ) -> Result<Option<BenchmarkXirr>, String> {
        let key = format!(
            "{}:{}:{}:{}",
            DbKeyPrefix::XirrMetrics.to_string(),
            index,
            portfolio_id,
            date
        );

        self.get_data(&key)
    }
}
