use std::str::FromStr;

use anyhow::Context;
use chrono::NaiveDate;
use rkyv::Deserialize;

use crate::states::{aum::Aum, investment::Investment, portfolio::Portfolio};

use super::{Db<PERSON>eyPrefix, StorageWriter};

pub struct PortfolioWithStartDate {
    pub portfolio_id: String,
    pub start_date: NaiveDate,
}

impl StorageWriter {
    /// Insert the start date of a portfolio
    pub async fn insert_portfolio_start_date(&mut self, date: NaiveDate, portfolio_id: &str) -> String {
        let key = format!("{}-{}", DbKeyPrefix::PortfolioStartDate.to_string(), portfolio_id);
        let serialized = rkyv::to_bytes::<_, 256>(&date).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Insert Portfolio Info on day T
    pub async fn insert_portfolio(&mut self, portfolio: &Portfolio, portfolio_id: String, date: NaiveDate) -> String {
        let key = format!("{}:{}:{}", DbKeyPrefix::Portfolio.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<Portfolio, 256>(portfolio).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Insert AUM of a Portfolio On Day T
    pub async fn insert_portfolio_aum(&mut self, aum: &Aum, portfolio_id: String, date: NaiveDate) -> String {
        let key = format!("{}-{}-{}", DbKeyPrefix::PortfolioAum.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<Aum, 256>(aum).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Insert the Investments of a portfolio
    pub async fn insert_portfolio_investment(
        &mut self,
        investments: &Vec<Investment>,
        portfolio_id: String,
        date: NaiveDate,
    ) -> String {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::PortfolioInvestment.to_string(),
            portfolio_id,
            date
        );
        let serialized = rkyv::to_bytes::<Vec<Investment>, 256>(investments).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }

        key
    }

    /// Utility get Functions

    pub fn get_portfolio_aum(&self, portfolio_id: &str, date: NaiveDate) -> Option<Aum> {
        let key = format!("{}-{}-{}", DbKeyPrefix::PortfolioAum.to_string(), portfolio_id, date,);
        self.get_data(&key).unwrap()
    }

    pub fn get_portfolio_investment(&self, portfolio_id: &str, date: NaiveDate) -> Option<Vec<Investment>> {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::PortfolioInvestment.to_string(),
            portfolio_id,
            date,
        );
        self.get_data(&key).unwrap()
    }

    pub fn get_portfolio(&self, portfolio_id: &str, date: NaiveDate) -> Option<Portfolio> {
        let key = format!("{}:{}:{}", DbKeyPrefix::Portfolio.to_string(), portfolio_id, date,);
        self.get_data(&key).unwrap()
    }

    // If you need this helper method to be async
    pub fn get_portfolio_start_date(&self, portfolio_id: &str) -> Option<NaiveDate> {
        let key = format!("{}-{}", DbKeyPrefix::PortfolioStartDate.to_string(), portfolio_id);
        self.get_data(&key).unwrap()
    }

    pub fn get_portfolio_by_key(&self, key: &str) -> Option<Portfolio> {
        self.get_data(key).unwrap()
    }

    pub fn get_all_portfolio_with_start_date(&self) -> Vec<PortfolioWithStartDate> {
        let prefix = format!("{}", DbKeyPrefix::PortfolioStartDate);

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        let mut portfolios = Vec::new();
        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have StrategyPortfolios-StrategyId-Date-PortfolioId
                    let key_str = String::from_utf8(key.to_vec()).unwrap();

                    let key_split: Vec<&str> = key_str.split("-").collect();

                    if key_split.len() != 2 || key_split[0] != DbKeyPrefix::PortfolioStartDate.to_string() {
                        break;
                    }

                    let portfolio_id = key_split[1].to_owned();

                    let archived = unsafe { rkyv::archived_root::<NaiveDate>(&value[..]) };
                    let date = archived.deserialize(&mut rkyv::Infallible).unwrap();

                    portfolios.push(PortfolioWithStartDate {
                        portfolio_id,
                        start_date: date,
                    });
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        portfolios
    }

    pub fn get_all_portfolios_as_at(&self, as_at: NaiveDate) -> anyhow::Result<Vec<Portfolio>> {
        let prefix = format!("{}", DbKeyPrefix::Portfolio);

        // Create an iterator with the given prefix
        let iter = self.db.iterator(rocksdb::IteratorMode::From(
            prefix.as_bytes(),
            rocksdb::Direction::Forward,
        ));

        let mut portfolios = Vec::new();
        // Iterate over all entries with the prefix
        for item in iter {
            match item {
                Ok((key, value)) => {
                    //Will have Portfolios-PortfolioId-Date
                    let key_str = String::from_utf8(key.to_vec())?;

                    let key_split: Vec<&str> = key_str.split(":").collect();

                    if key_split.len() != 3 || key_split[0] != DbKeyPrefix::Portfolio.to_string() {
                        break;
                    }

                    let date = NaiveDate::from_str(key_split[2]).context("invalid date found")?;

                    if date != as_at {
                        continue;
                    }

                    let archived = unsafe { rkyv::archived_root::<Portfolio>(&value[..]) };
                    let portfolio = archived.deserialize(&mut rkyv::Infallible)?;

                    portfolios.push(portfolio);
                }
                Err(e) => {
                    eprintln!("Error reading item: {}", e);
                }
            }
        }

        Ok(portfolios)
    }
}
