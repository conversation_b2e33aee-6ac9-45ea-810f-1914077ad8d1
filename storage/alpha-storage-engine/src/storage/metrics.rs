use chrono::NaiveDate;

use crate::states::metrics::portfolio::{TwrrResults, XirrResults};

use super::{DbKeyPrefix, StorageWriter};

impl StorageWriter {
    pub fn get_xirr_metrics(&self, portfolio_id: &str, date: NaiveDate) -> Result<Option<XirrResults>, String> {
        let key = format!("{}-{}-{}", DbKeyPrefix::XirrMetrics.to_string(), portfolio_id, date);

        self.get_data(&key)
    }



    pub fn get_portfolio_twrr_metrics(
        &self,
        portfolio_id: &str,
        date: NaiveDate,
    ) -> Result<Option<TwrrResults>, String> {
        let key = format!("{}-{}-{}", DbKeyPrefix::TwrrMetrics.to_string(), portfolio_id, date);

        self.get_data(&key)
    }

    pub fn get_strategy_twrr_metrics(&self, strategy_id: &str, date: NaiveDate) -> Result<Option<TwrrResults>, String> {
        let key = format!(
            "{}-{}-{}",
            DbKeyPrefix::StrategyTwrrMetrics.to_string(),
            strategy_id,
            date
        );

        self.get_data(&key)
    }

    pub async fn insert_xirr_metrics(&mut self, portfolio_id: &str, date: NaiveDate, metrics: XirrResults) {
        let key = format!("{}-{}-{}", DbKeyPrefix::XirrMetrics.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<_, 256>(&metrics).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }

    pub async fn insert_portfolio_twrr_metrics(&mut self, portfolio_id: &str, date: NaiveDate, metrics: TwrrResults) {
        let key = format!("{}-{}-{}", DbKeyPrefix::TwrrMetrics.to_string(), portfolio_id, date);
        let serialized = rkyv::to_bytes::<_, 256>(&metrics).unwrap();

        if let Some(batch) = self.batch.as_mut() {
            let mut batch_guard = batch.lock().await;
            batch_guard.put(&key, serialized);
        } else {
            self.db.put(&key, serialized).unwrap();
        }
    }
}
