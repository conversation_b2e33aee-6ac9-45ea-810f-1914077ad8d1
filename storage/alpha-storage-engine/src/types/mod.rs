use alpha_core_db::connection::pool::{deadpool::managed::Pool, Manager};

use crate::states::{
    capital_register::CapitalRegister, legacy_ledger::LegacyLedger, peak_margin::PeakMargin,
    receivable_payable::ReceivablePayable, transaction::Transaction,
};

#[derive(Debug)]
pub struct EligbleStateTransitionTransactions {
    pub capital_register_transactions: Vec<CapitalRegister>,
    pub investment_transactions: Vec<Transaction>,
    pub peak_margin_transactions: Vec<PeakMargin>,
    pub legacy_ledger_transactions: Vec<LegacyLedger>,
    pub receivables_payable_transactions: Vec<ReceivablePayable>,
}

#[derive(Clone)]
pub struct ExternalDBState {
    pub clickhouse_client: clickhouse::Client,
    pub redis: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pub db_pool: Pool<Manager>,
    pub master_pool: Pool<Manager>,
}
