use std::collections::HashMap;

use chrono::NaiveDate;
use nom::{
    branch::alt,
    bytes::complete::{tag, take_until, take_while1},
    character::complete::{char, digit1, multispace0, multispace1},
    combinator::{map, opt},
    multi::separated_list0,
    sequence::{delimited, preceded, tuple},
    IResult,
};
use rkyv::{Deserialize, Infallible as RkyvInfallible};
use rocksdb::{IteratorMode, DB};

use crate::states::aum::Aum;

#[derive(Debug)]
enum Value {
    Str(String),
    Num(i64),
}

#[derive(Debug)]
enum Op {
    Eq,
    Gt,
    Lt,
}

#[derive(Debug, PartialEq)]
enum FilterTarget {
    Key,
    Value,
}

#[derive(Debug)]
struct FilterCondition {
    field: String,
    op: Op,
    value: Value,
    target: FilterTarget,
}

#[derive(Debug)]
struct ParsedQuery {
    table: String,
    key_fields: Vec<String>,
    iter_conditions: Vec<FilterCondition>,
    value_conditions: Vec<FilterCondition>,
    iter_key_field_positions: HashMap<String, usize>, // <field, original index in key_fields>
}

#[test]
fn test_sql() {
    let input = "SELECT * FROM PortfolioInvestment clientid date strategyid ITER OVER WHERE key.strategyid > 'S1' AND key.date > '2022-01-01' WHERE value.holdings > 0";
    let (_, query) = parse_query(input).unwrap();

    println!("Table: {}", query.table);
    println!("Key fields: {:?}", query.key_fields);
    println!("Iter conditions: {:?}", query.iter_conditions);
    println!("Value conditions: {:?}", query.value_conditions);
    println!("Iter key field positions: {:?}", query.iter_key_field_positions);
}

fn extract_field_literal(conds: &[FilterCondition], field: &str) -> Option<String> {
    for cond in conds {
        if cond.target == FilterTarget::Key && cond.field == field {
            if let Value::Str(s) = &cond.value {
                if matches!(cond.op, Op::Eq) {
                    return Some(s.clone());
                }
            }
        }
    }
    None
}

// fn extract_iter_key_bounds(
//     iter_conditions: &[FilterCondition],
// ) -> HashMap<String, (Option<String>, Option<String>)> {
//     let mut map = HashMap::new();
//     for cond in iter_conditions {
//         if cond.target == FilterTarget::Key {
//             let entry = map.entry(cond.field.clone()).or_default();
//             match (&cond.op, &cond.value) {
//                 (Op::Gt, Value::Str(v)) => entry.0 = Some(v.clone()),
//                 (Op::Lt, Value::Str(v)) => entry.1 = Some(v.clone()),
//                 (Op::Eq, Value::Str(v)) => {
//                     entry.0 = Some(v.clone());
//                     entry.1 = Some(v.clone());
//                 }
//                 _ => {}
//             }
//         }
//     }
//     map
// }

fn parse_query(input: &str) -> IResult<&str, ParsedQuery> {
    let (input, _) = tag("SELECT")(input)?;
    let (input, _) = multispace1(input)?;
    let (input, _) = tag("*")(input)?;
    let (input, _) = multispace1(input)?;
    let (input, _) = tag("FROM")(input)?;
    let (input, _) = multispace1(input)?;
    let (mut input, table) = identifier(input)?;
    let (mut input, _) = multispace1(input)?;

    let mut key_fields = Vec::new();
    while !input.trim_start().starts_with("ITER OVER")
        && !input.trim_start().starts_with("WHERE")
        && !input.trim_start().is_empty()
    {
        let (next_input, field) = identifier(input)?;
        key_fields.push(field);
        let (next_input, _) = multispace0(next_input)?;
        input = next_input;
    }

    let (input, iter_conditions) = opt(preceded(
        tuple((multispace0, tag("ITER OVER"), multispace1, tag("WHERE"), multispace0)),
        parse_filters,
    ))(input)?;

    // Extract iterated key positions before pruning
    let mut iter_key_field_positions = HashMap::new();
    if let Some(conds) = &iter_conditions {
        for cond in conds {
            if cond.target == FilterTarget::Key {
                if let Some(pos) = key_fields.iter().position(|f| f == &cond.field) {
                    iter_key_field_positions.insert(cond.field.clone(), pos);
                }
            }
        }
    }

    // Remove iterated-over key fields
    for key in iter_key_field_positions.keys() {
        key_fields.retain(|f| f != key);
    }

    let (input, value_conditions) =
        opt(preceded(tuple((multispace0, tag("WHERE"), multispace0)), parse_filters))(input)?;

    Ok((
        input,
        ParsedQuery {
            table,
            key_fields,
            iter_conditions: iter_conditions.unwrap_or_default(),
            value_conditions: value_conditions.unwrap_or_default(),
            iter_key_field_positions,
        },
    ))
}

fn identifier(input: &str) -> IResult<&str, String> {
    map(take_while1(|c: char| c.is_alphanumeric() || c == '_'), |s: &str| {
        s.to_string()
    })(input)
}

fn parse_filters(input: &str) -> IResult<&str, Vec<FilterCondition>> {
    separated_list0(tuple((multispace0, tag("AND"), multispace0)), parse_filter)(input)
}

fn parse_filter(input: &str) -> IResult<&str, FilterCondition> {
    let (input, target) = alt((
        map(tag("key."), |_| FilterTarget::Key),
        map(tag("value."), |_| FilterTarget::Value),
    ))(input)?;

    let (input, field) = identifier(input)?;
    let (input, _) = multispace0(input)?;
    let (input, op) = alt((map(tag("="), |_| Op::Eq), map(tag(">"), |_| Op::Gt)))(input)?;
    let (input, _) = multispace0(input)?;
    let (input, val) = alt((
        map(delimited(char('\''), take_until("'"), char('\'')), |s: &str| {
            Value::Str(s.to_string())
        }),
        map(digit1, |s: &str| Value::Num(s.parse().unwrap())),
    ))(input)?;

    Ok((
        input,
        FilterCondition {
            field,
            op,
            value: val,
            target,
        },
    ))
}
