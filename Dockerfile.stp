# ---- Builder Stage ----
FROM rust:1.82 AS chef
RUN cargo install cargo-chef
WORKDIR /app

FROM chef AS planner
COPY . .
WORKDIR /app
RUN cargo chef prepare --recipe-path recipe.json

FROM chef AS builder
COPY . .
WORKDIR /app
COPY --from=planner /app/recipe.json ./recipe.json
# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    cmake \
    clang \
    openssh-client \
    libclang-dev \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /root/.ssh && ssh-keyscan ssh.dev.azure.com >> /root/.ssh/known_hosts

# Cook dependencies
RUN --mount=type=ssh cargo chef cook --release --recipe-path recipe.json

# Build only the alpha-stp-crons binary
RUN --mount=type=ssh cargo build --release --package alpha-stp-crons

# ---- Runtime Stage ----
FROM debian:bookworm-slim AS runtime
WORKDIR /app

# Copy the specific binary from the builder stage
COPY --from=builder /app/alpha-crons/alpha-stp-crons/target/release/alpha-stp-crons /usr/local/bin

EXPOSE 4010

# Install any runtime dependencies (if needed)
RUN apt-get update && apt-get upgrade -y  && apt-get install -y  --no-install-recommends  \
    build-essential \
    cmake \
    openssl \
    clang \
    libssl3 \
    libclang-dev \
    ca-certificates\
    && rm -rf /var/lib/apt/lists/*
    
CMD ["/usr/local/bin/alpha-stp-crons"]
