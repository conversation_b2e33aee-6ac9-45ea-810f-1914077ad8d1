[workspace]

members = [
    "alpha-core",
    "alpha-core-db",
    "alpha-oms",
    "alpha-utils",
    "alpha-reports",
    "storage/alpha-perf-runner",
    "storage/alpha-perf-api",
    "storage/alpha-updates",
    "storage/alpha-storage-engine",
    "alpha-crons/alpha-stp-crons",
    "alpha-recons/alpha-recon-api",
    "alpha-recons/recon-runner",
    "alpha-recons/recon-util",
]


[workspace.dependencies]
clickhouse = { version = "0.13.3", features = ["native-tls", "time", "chrono"] }
thiserror = "1.0.64"
tracing = { version = "0.1.40", features = ["attributes"] }
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
tracing-test = "0.2.5"
dotenv = "0.15.0"
tiberius = { version = "0.12.3", features = [
    "vendored-openssl",
    "tds73",
    "time",
    "rust_decimal",
    "chrono",
], default-features = false }
deadpool = { version = "0.12.1", features = ["rt_tokio_1"] }
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.122"
tokio = { version = "1.40.0", features = ["full"] }
chrono = { version = "0.4.40", features = ["serde"] }
time = { version = "0.3.36", features = ["serde"] }
redis = { version = "0.29.0", features = [
    "r2d2",
    "aio",
    "tokio-comp",
    "tokio-rustls-comp",
    "json",
] }
bb8-redis = { version = "0.21.0" }
bb8 = { version = "0.9.0" }

uuid = "1.10.0"
rust_decimal = { version = "1.36.0", features = ["maths", "rkyv", "serde"] }
rust_decimal_macros = "1.36"
bincode = { version = "1.3.3" }
rkyv = { version = "0.7.45", features = ["alloc"] }
anyhow = { version = "1.0.95" }

deadpool-redis = { version = "0.20.0", features = [
    "tokio-comp",
    "tokio-rustls-comp",
    "json",
] }
actlogica_logs = { git = "ssh://*********************/v3/actlogica/actlogica-logger/actlogica-logger", branch = "enable-kafka" }
lapin = { version = "2.5.0", features = ["native-tls"] }
csv = { version = "1.3.0" }
dashmap = { version = "6.1.0" }
