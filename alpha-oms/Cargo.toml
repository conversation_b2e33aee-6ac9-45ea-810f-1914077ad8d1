[package]
name = "alpha-oms"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { workspace = true }
dotenv = { workspace = true }
alpha-utils = { path = "../alpha-utils" }
alpha-core-db = { path = "../alpha-core-db" }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
axum = { version = "0.7.7", features = ["macros"] }
axum-extra = { version = "0.9.4", features = ["cookie"] }
jsonwebtoken = "9.3.0"
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-test = { workspace = true }
futures-util = "0.3.30"
futures = "0.3.30"
tower-http = { version = "0.6.1", features = ["cors"] }
redis = { workspace = true }
bb8 = { workspace = true }
bb8-redis = { workspace = true }
uuid = { workspace = true }
deadpool-redis = { workspace = true }
deadpool = { workspace = true }
anyhow = { workspace = true }
csv = "1.3.0"
clickhouse = { workspace = true }

actlogica_logs = { workspace = true }
