use std::str::FromStr;
use std::sync::Arc;

use alpha_core_db::tenant_clickhouse::pre_trade_compliance_report::PreTradeComplianceReportDb;
use alpha_utils::constants::RedisKey;
use anyhow::Context;
use deadpool::managed::Pool;
use deadpool_redis::redis::AsyncCommands;
use deadpool_redis::Connection;
use deadpool_redis::Manager;
use redis::Value;
use tracing_test::traced_test;
use uuid::Uuid;

use crate::models::update_order::UpdateOrder;
use crate::models::AppState;
use crate::types::ClientComputedOrders;
use alpha_core_db::connection::pool::Manager as DbManager;

use super::save_orders;
use super::trade_compliance::pre_trade::PreTradeCompliance;

pub async fn save_order_from_session(id: Uuid, app_state: Arc<AppState>, user: String) -> anyhow::Result<()> {
    let mut redis_conn = app_state.tenant_redis_url.get().await.context("failed to get connection")?;

    let key = format!("{}:{}", RedisKey::ClientComputedOrders, id);
    let order_string: Vec<String> = redis_conn.zrange(&key, 0, -1).await.context("failed to get order")?;
    if order_string.is_empty() {
        return Err(anyhow::anyhow!("Not Orders found in this session"));
    }

    let orders: Vec<ClientComputedOrders> = order_string
        .into_iter()
        .map(|s| serde_json::from_str::<ClientComputedOrders>(&s).context("failed to deserialise the orders"))
        .collect::<Result<_, _>>()?;

    //Get the compliance report for this
    let compliance = PreTradeCompliance {
        app_state: app_state.clone(),
        session_id: id,
    };

    let compliance_reports = compliance
        .get_all_report()
        .await
        .context("failed to get compliance report")?;


    let reports_logs: Vec<PreTradeComplianceReportDb> = compliance_reports.iter().flat_map(|r| r.flatten(id)).collect();
    let mut db_conn = app_state.db.get().await.context("get db conn")?;

    save_orders(&mut db_conn, orders, user)
        .await
        .map_err(|e| anyhow::anyhow!("{}", e))?;

    if !reports_logs.is_empty() {
        println!("Saving compliance report");
        if let Err(err) = PreTradeComplianceReportDb::bulk_insert(&app_state.tenant_clickhouse, reports_logs).await {
            tracing::error!("failed to save pre trade report into database")
        }
    }
    else{
        tracing::info!("Report is empty");
    }

    //Delete the orders from cache
    if let Err(err) = redis_conn.del::<String, Value>(key).await {
        tracing::info!("Failed to delete the session id from cache after saving orders {}", err);
    }

    Ok(())
}

/// Function to update order for the mentioned indices in the provided session id
pub async fn edit_order(id: Uuid, redis: Pool<Manager, Connection>, indices: Vec<UpdateOrder>) -> anyhow::Result<()> {
    let mut redis_conn = redis.get().await.context("failed to get connection")?;

    let key = format!("{}:{}", RedisKey::ClientComputedOrders, id);

    let indices_to_update: Vec<u64> = indices.iter().map(|f| f.index).collect();
    // Create a pipeline for the bulk fetch
    let mut fetch_pipe = deadpool_redis::redis::pipe();
    for id in indices_to_update {
        fetch_pipe.zrangebyscore::<String, u64, u64>(key.clone(), id, id);
    }

    let fetch_results: Vec<Option<Vec<String>>> = match fetch_pipe.query_async(&mut *redis_conn).await {
        Ok(results) => results,
        Err(err) => return Err(anyhow::anyhow!("Failed to fetch orders to update")),
    };

    if fetch_results.is_empty() {
        return Err(anyhow::anyhow!("Not Orders found in this session"));
    }

    let mut update_pipe = deadpool_redis::redis::pipe();
    update_pipe.atomic();

    // Process all orders and add update commands to the pipeline
    for (i, change) in indices.iter().enumerate() {
        let result = &fetch_results[i];

        if let Some(order_data) = result {
            if order_data.is_empty() {
                return Err(anyhow::anyhow!(format!("Order with ID {} not found", change.index)));
            }

            let order: ClientComputedOrders = match serde_json::from_str(&order_data[0]) {
                Ok(order) => order,
                Err(_) => {
                    return Err(anyhow::anyhow!(format!(
                        "Failed to deserialize order with ID {}",
                        change.index
                    )));
                }
            };

            let mut updated_order = order;
            updated_order.quantity = change.quantity;
            updated_order.pending_quantity = change.quantity;

            if let Some(price) = change.price {
                updated_order.price = price;
            }

            updated_order.transaction_amount = updated_order.price * updated_order.quantity;
            let serialized_order = match serde_json::to_vec(&updated_order) {
                Ok(serialized) => serialized,
                Err(_) => {
                    return Err(anyhow::anyhow!(format!(
                        "Failed to serialize updated order with ID {}",
                        change.index
                    ),));
                }
            };

            // Add commands to remove old order and add updated one
            update_pipe.zrembyscore(&key, updated_order.id, updated_order.id).zadd(
                &key,
                serialized_order,
                updated_order.id,
            );

            // Execute the update pipeline in a single network call
            match update_pipe
                .query_async::<deadpool_redis::redis::Value>(&mut redis_conn)
                .await
            {
                Ok(_) => (),
                Err(err) => {
                    return Err(anyhow::anyhow!("Failed to update quantities"));
                }
            };
        } else {
            return Err(anyhow::anyhow!(format!("Order with ID {} not found", change.index)));
        }
    }
    Ok(())
}

pub async fn get_orders_in_session(
    id: Uuid,
    redis: Pool<Manager, Connection>,
) -> anyhow::Result<Vec<ClientComputedOrders>> {
    let mut redis_conn = redis.get().await.context("failed to get connection")?;

    let key = format!("{}:{}", RedisKey::ClientComputedOrders, id);
    let order_string: Vec<String> = redis_conn.zrange(&key, 0, -1).await.context("failed to get order")?;

    let orders: Vec<ClientComputedOrders> = order_string
        .into_iter()
        .map(|s| serde_json::from_str::<ClientComputedOrders>(&s).context("failed to deserialise the orders"))
        .collect::<Result<_, _>>()?;

    Ok(orders)
}


#[tokio::test]
#[traced_test]
async fn test_save_orders_in_session(){
    let app_state = Arc::new(AppState::new().await);
    let session_id = Uuid::from_str("a57666d1-6f88-41c4-8500-71f5cf088cfa").unwrap();
    let user = String::from("Athul");

    save_order_from_session(session_id, app_state, user).await.unwrap();
}
