use std::{collections::HashMap, sync::Arc};

use alpha_core_db::schema::{
    client_order_entry::TransactionType,
    compliance_pre_trade::trade_rule::CompliancePreTradeRules,
    investment::Investments,
    portfolio::{Portfolio, PortfolioCashPosition},
};
use alpha_utils::constants::RedisKey;
use redis::{AsyncCommands, JsonAsyncCommands};
use tokio::net::unix::pipe;
use uuid::Uuid;

use crate::{
    models::{compliance::PreTradeComplianceRulesSummary, AppState},
    types::{paginated::PaginatedResponse, ClientComputedOrders},
};

use super::rule_checker::PreTradeComplianceRulesComputer;
#[derive(Clone)]
pub struct InvestmentForCompliance {
    pub isin: String,
    pub market_cap: String,
    pub industry: String,
    pub holdings: f64,
    pub market_value: f64,
}

pub struct PreTradeCompliance {
    /// Session id of this compliance computation
    pub session_id: Uuid,

    pub app_state: Arc<AppState>,
}

impl PreTradeCompliance {
    const EXPIRY: u64 = 5 * 60;

    async fn save_compliance_report(&self, reports: &Vec<PreTradeComplianceRulesSummary>) -> anyhow::Result<()> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await?;
        let key = format!("{}:{}", RedisKey::PreTradeComplianceSummary, self.session_id);
        let mut pipe = deadpool_redis::redis::pipe();
        pipe.atomic();

        for report in reports {
            let json = serde_json::to_string(report)?;
            pipe.rpush(&key, json);
        }

        pipe.expire(&key, Self::EXPIRY as i64);

        match pipe.exec_async(&mut redis_conn).await {
            Ok(_) => {
                println!("Successfully Saved Compliance Report into redis");
                return Ok(());
            }
            Err(err) => {
                eprintln!("Failed to save {:?}", err);
                return Err(anyhow::anyhow!("failed to save compliance report, please try again"));
            }
        };
    }

    pub async fn get_compliance_report(
        &self,
        offset: u64,
        limit: u64,
    ) -> anyhow::Result<PaginatedResponse<PreTradeComplianceRulesSummary>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await?;
        let key = format!("{}:{}", RedisKey::PreTradeComplianceSummary, self.session_id);

        //First Try to renew the key expiry
        match redis_conn.expire::<&str, redis::Value>(&key, Self::EXPIRY as i64).await {
            Ok(_) => {}
            Err(err) => {
                return Err(anyhow::anyhow!(
                    "failed to get compliance report, maybe the session expired"
                ));
            }
        };

        let total: usize = redis_conn.llen(&key).await?;

        let raw: Vec<String> = redis_conn
            .lrange(&key, offset as isize, (offset + limit - 1) as isize)
            .await?;

        let data = raw
            .into_iter()
            .map(|s| serde_json::from_str(&s))
            .collect::<Result<_, _>>()?;

        let is_last_page = (offset + limit as u64) >= total as u64;

        Ok(PaginatedResponse {
            data,
            total,
            is_last_page,
        })
    }

    pub async fn get_all_report(&self) -> anyhow::Result<Vec<PreTradeComplianceRulesSummary>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await?;
        let key = format!("{}:{}", RedisKey::PreTradeComplianceSummary, self.session_id);

        redis_conn
            .expire::<&str, redis::Value>(&key, Self::EXPIRY as i64)
            .await
            .map_err(|_| anyhow::anyhow!("failed to get compliance report, maybe the session expired"))?;

        let raw: Vec<String> = redis_conn.lrange(&key, 0, -1).await?;

        let result = raw
            .into_iter()
            .map(|s| serde_json::from_str(&s))
            .collect::<Result<_, _>>()?;

        Ok(result)
    }

    pub async fn compute_compliance_for_orders(
        &self,
        orders: &Vec<ClientComputedOrders>,
    ) -> anyhow::Result<(Vec<PreTradeComplianceRulesSummary>, usize)> {
        let mut conn = self.app_state.db.get().await?;

        let orders_by_portfolio = orders.iter().fold(HashMap::new(), |mut acc, order| {
            acc.entry(order.portfolio_id.clone())
                .or_insert_with(Vec::new)
                .push(order);
            acc
        });

        let mut rule_report: Vec<PreTradeComplianceRulesSummary> = Vec::new();
        for (portfolio_id, orders) in orders_by_portfolio {
            // Get the Trade Compliance rules of the portfolio
            let trade_rules =
                match CompliancePreTradeRules::get_active_rules_for_portfolio(&mut conn, &portfolio_id).await {
                    Ok(rules) => rules,
                    Err(err) => {
                        return Err(anyhow::anyhow!("Failed to get portfolio trade rules"));
                    }
                };

            // Get investments of this portfolio
            // Simulate the change to investments according to the orders
            // Check if the investment after applying the orders violate the rules
            let investments = match Investments::get_by_portfolio_id(&mut conn, &portfolio_id).await {
                Ok(inv) => inv,
                Err(err) => {
                    return Err(anyhow::anyhow!("Failed to get portfolio investments"));
                }
            };

            let pre_trade_portfolio_cash_position =
                match Portfolio::get_portfolio_cash_position_by_id(&mut conn, portfolio_id.to_string()).await {
                    Ok(cash_pos) => match cash_pos {
                        Some(cash_pos) => cash_pos,
                        None => PortfolioCashPosition::default(),
                    },
                    Err(err) => {
                        return Err(anyhow::anyhow!("Failed to get portfolio cash Positions"));
                    }
                };

            let mut post_trade_portfolio_cash_position = pre_trade_portfolio_cash_position.clone();

            let pre_trade_investments: Vec<InvestmentForCompliance> = investments
                .iter()
                .map(|f| InvestmentForCompliance {
                    holdings: f.current_holding,
                    industry: f.sector.clone().unwrap_or_default(),
                    isin: f.isin.clone(),
                    market_cap: f.market_cap.clone().unwrap_or("Unknown".to_string()),
                    market_value: f.market_value,
                })
                .collect();

            let mut post_trade_investments = pre_trade_investments.clone();

            for order in &orders {
                let mut found = false;
                for inv in &mut post_trade_investments {
                    if order.isin == inv.isin {
                        inv.holdings = if order.transaction_type == TransactionType::Buy {
                            inv.holdings + order.quantity
                        } else {
                            inv.holdings - order.quantity
                        };

                        if order.transaction_type == TransactionType::Buy {
                            inv.holdings += order.quantity;
                            inv.market_value += order.transaction_amount;
                        } else {
                            inv.holdings -= order.quantity;
                            inv.market_value -= order.transaction_amount;
                        }

                        found = true;
                    }
                }

                if !found {
                    //Its a new security not in investments
                    let new_inv = InvestmentForCompliance {
                        holdings: order.quantity,
                        industry: order.industry.clone(),
                        isin: order.isin.clone(),
                        market_cap: order.market_cap.clone(),
                        market_value: order.transaction_amount,
                    };

                    post_trade_investments.push(new_inv);
                }

                if order.transaction_type == TransactionType::Buy {
                    post_trade_portfolio_cash_position.available_cash_balance -= order.transaction_amount;
                } else {
                    post_trade_portfolio_cash_position.available_cash_balance += order.transaction_amount;
                }
            }

            let portfolio_name = orders[0].strategy_model_name.clone();
            for rule in trade_rules {
                let rules_computer = PreTradeComplianceRulesComputer {
                    portfolio_name: portfolio_name.clone(),
                    rule_type: rule.rule_type_name,
                    rule_sub_type_value_explicit: rule.rule_sub_type_value_explicit,
                    post_trade_investments: &post_trade_investments,
                    pre_trade_investments: &pre_trade_investments,
                    rule_sub_type: rule.rule_sub_type,
                    value: rule.value,
                    pre_trade_portfolio_cash_position: &pre_trade_portfolio_cash_position,
                    post_trade_portfolio_cash_position: &post_trade_portfolio_cash_position,
                    orders: &orders,
                    value_type: rule.value_type,
                };

                let rule_violations = rules_computer.compute_rule();
                for violation in rule_violations {
                    if let Some(report) = rule_report.iter_mut().find(|f| f.isin == violation.isin) {
                        report.rules.insert(violation.rule, violation.violate);
                    } else {
                        let mut checked_rules = HashMap::new();
                        checked_rules.insert(violation.rule, violation.violate);
                        rule_report.push(PreTradeComplianceRulesSummary {
                            id: violation.id,
                            isin: violation.isin,
                            amount: violation.amount,
                            market_cap: violation.market_cap,
                            sector: violation.sector,
                            security_name: violation.security_name,
                            portfolio_name: violation.portfolio,
                            portfolio_id: portfolio_id.clone(),
                            symbol: violation.symbol,
                            transaction_type: violation.transaction_type,
                            quantity: violation.quantity,
                            rules: checked_rules,
                        });
                    }
                }
            }
        }

        // We should save this Rule Report into Redis and allow pagination
        self.save_compliance_report(&rule_report).await.unwrap();
        let reports: Vec<PreTradeComplianceRulesSummary> = rule_report.iter().take(10).cloned().collect();
        Ok((reports, rule_report.len()))
    }
}

#[cfg(test)]
mod test {
    use std::{str::FromStr, sync::Arc};

    use alpha_core_db::{
        connection::{connect_to_master_data, connect_to_mssql},
        redis::connect_to_redis,
    };
    use uuid::Uuid;

    use crate::{
        config::Config,
        models::AppState,
        oms::{orders::get_orders_in_session, trade_compliance::pre_trade::PreTradeCompliance},
    };

    #[tokio::test]
    pub async fn test_compliance() {
        dotenv::dotenv().ok();
        let app_state = Arc::new(AppState::new().await);
        let session_id = Uuid::from_str("3bf068ca-7db4-4d8e-8b22-fa82f9db0de5").unwrap();
        let compliance_report = PreTradeCompliance {
            app_state: app_state.clone(),
            session_id,
        };
        let orders = get_orders_in_session(session_id, app_state.tenant_redis_url.clone())
            .await
            .unwrap();
        let reports = compliance_report.compute_compliance_for_orders(&orders).await.unwrap();
        // println!("Total Reports found = {}", reports.data.len())
    }
}
