use std::{collections::HashMap, fmt::format};

use alpha_core_db::schema::{
    client_order_entry::TransactionType,
    compliance_pre_trade::{rule_subtype::ComplianceRuleSubType, rule_type::PreTradeRuleType, value_type::ValueType},
    portfolio::PortfolioCashPosition,
};
use tracing::Instrument;

use crate::{models::compliance::PreTradeComplianceRulesReport, types::ClientComputedOrders};

use super::pre_trade::InvestmentForCompliance;

/// Computes pre-trade compliance rules based on investment types and thresholds.
pub struct PreTradeComplianceRulesComputer<'a> {
    pub portfolio_name: String,
    pub orders: &'a Vec<&'a ClientComputedOrders>,
    pub rule_type: PreTradeRuleType,
    pub rule_sub_type: Option<ComplianceRuleSubType>,
    pub rule_sub_type_value_explicit: Option<String>,
    pub pre_trade_investments: &'a Vec<InvestmentForCompliance>,
    pub post_trade_investments: &'a Vec<InvestmentForCompliance>,
    pub pre_trade_portfolio_cash_position: &'a PortfolioCashPosition,
    pub post_trade_portfolio_cash_position: &'a PortfolioCashPosition,
    pub value: String,
    pub value_type: ValueType,
}

impl<'a> PreTradeComplianceRulesComputer<'a> {
    /// Get an array of all the orders with the status whether it violated the mentioned rule or not
    pub fn compute_rule(&self) -> Vec<PreTradeComplianceRulesReport> {
        match self.rule_type {
            PreTradeRuleType::AssetPreference => self.check_market_cap_max(),
            PreTradeRuleType::MarketCapMax => self.check_market_cap_max(),
            PreTradeRuleType::MarketCapMin => self.check_market_cap_min(),
            PreTradeRuleType::SectorMax => self.check_sector_max(),
            PreTradeRuleType::SectorMin => self.check_sector_min(),
            PreTradeRuleType::CashWeightMin => self.check_min_cash(),
            PreTradeRuleType::CashWeightMax => self.check_max_cash(),
            PreTradeRuleType::MaxNumberOfStocks => self.check_max_number_of_stocks(),
            PreTradeRuleType::MinNumberOfStocks => self.check_min_number_of_stocks(),
            PreTradeRuleType::SingleStockExposureMax => self.check_single_stock_exposure_max(),
            PreTradeRuleType::SingleStockExposureMin => self.check_single_stock_exposure_min(),
        }
    }

    pub fn check_single_stock_exposure_min(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();

        // This can be in percentage as well as in absolute number indicating market value
        let min_stock_exposure_required: f64 = self.value.parse().unwrap_or_default();
        let post_trade_cash = self.post_trade_portfolio_cash_position.available_cash_balance;
        let total_market_value: f64 = self.post_trade_investments.iter().map(|f| f.market_value).sum();
        let total_portfolio_value = post_trade_cash + total_market_value;
        for order in self.orders {
            let inv = self
                .post_trade_investments
                .iter()
                .find(|f| f.isin == order.isin)
                .unwrap();

            let post_trade_value = if self.value_type == ValueType::Absolute {
                inv.market_value
            } else {
                (inv.market_value / total_portfolio_value) * 100f64
            };

            let mut violate = if post_trade_value < min_stock_exposure_required {
                true
            } else {
                false
            };

            //Make violate false if order is to sell full holdings
            if order.quantity == order.current_holding && order.transaction_type == TransactionType::Sell {
                violate = false
            }

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        rule_violations
    }

    pub fn check_single_stock_exposure_max(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();

        // This can be in percentage as well as in absolute number indicating market value
        let max_stock_exposure_allowed: f64 = self.value.parse().unwrap_or_default();
        let post_trade_cash = self.post_trade_portfolio_cash_position.available_cash_balance;
        let total_market_value: f64 = self.post_trade_investments.iter().map(|f| f.market_value).sum();
        let total_portfolio_value = post_trade_cash + total_market_value;
        for order in self.orders {
            let inv = self
                .post_trade_investments
                .iter()
                .find(|f| f.isin == order.isin)
                .unwrap();

            let post_trade_value = if self.value_type == ValueType::Absolute {
                inv.market_value
            } else {
                (inv.market_value / total_portfolio_value) * 100f64
            };

            let mut violate = if post_trade_value > max_stock_exposure_allowed {
                true
            } else {
                false
            };

            //Make violate false if order is to sell full holdings
            if order.quantity == order.current_holding && order.transaction_type == TransactionType::Sell {
                violate = false
            }

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        rule_violations
    }

    /// Check if the order violate min cash rule
    fn check_min_cash(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();
        let min_cash_required: f64 = self.value.parse().unwrap_or_default();

        let post_trade_mv: f64 = self.post_trade_investments.iter().map(|inv| inv.market_value).sum();
        let post_trade_cash = self.post_trade_portfolio_cash_position.available_cash_balance;

        let post_trade_cash_amount = if self.value_type == ValueType::Absolute {
            post_trade_cash
        } else {
            (post_trade_cash / (post_trade_mv + post_trade_cash)) * 100f64
        };

        for order in self.orders {
            let violate = if post_trade_cash_amount < min_cash_required {
                true
            } else {
                false
            };

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        return rule_violations;
    }

    /// Check if the order violate max cash rule
    fn check_max_cash(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();
        let max_cash_allowed: f64 = self.value.parse().unwrap_or_default();

        let post_trade_mv: f64 = self.post_trade_investments.iter().map(|inv| inv.market_value).sum();
        let post_trade_cash = self.post_trade_portfolio_cash_position.available_cash_balance;

        let post_trade_cash_amount = if self.value_type == ValueType::Absolute {
            post_trade_cash
        } else {
            (post_trade_cash / (post_trade_mv + post_trade_cash)) * 100f64
        };

        for order in self.orders {
            let violate = if post_trade_cash_amount > max_cash_allowed {
                true
            } else {
                false
            };

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        return rule_violations;
    }

    fn check_min_number_of_stocks(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();
        let required_min_stocks: usize = self.value.parse().unwrap_or_default();

        let post_trade_stocks_count = self.post_trade_investments.len();

        for order in self.orders {
            let violate = if post_trade_stocks_count < required_min_stocks {
                true
            } else {
                false
            };

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        rule_violations
    }

    fn check_max_number_of_stocks(&self) -> Vec<PreTradeComplianceRulesReport> {
        let mut rule_violations = Vec::new();
        let required_max_stocks: usize = self.value.parse().unwrap_or_default();

        let post_trade_stocks_count = self.post_trade_investments.len();

        for order in self.orders {
            let violate = if post_trade_stocks_count > required_max_stocks {
                true
            } else {
                false
            };

            rule_violations.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: self.rule_type.to_string(),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        rule_violations
    }

    fn check_sector_min(&self) -> Vec<PreTradeComplianceRulesReport> {
        self.check_threshold(
            |i, sector| i.industry.eq_ignore_ascii_case(sector),
            |percent, value| percent < value,
        )
    }

    fn check_sector_max(&self) -> Vec<PreTradeComplianceRulesReport> {
        self.check_threshold(
            |i, sector| i.industry.eq_ignore_ascii_case(sector),
            |percent, value| percent > value,
        )
    }

    fn check_market_cap_min(&self) -> Vec<PreTradeComplianceRulesReport> {
        self.check_threshold(
            |i, cap| i.market_cap.eq_ignore_ascii_case(cap),
            |percent, value| percent < value,
        )
    }

    fn check_market_cap_max(&self) -> Vec<PreTradeComplianceRulesReport> {
        self.check_threshold(
            |i, cap| i.market_cap.eq_ignore_ascii_case(cap),
            |percent, value| percent > value,
        )
    }

    fn check_threshold<F, G>(&self, match_fn: F, compare_fn: G) -> Vec<PreTradeComplianceRulesReport>
    where
        F: Fn(&InvestmentForCompliance, &str) -> bool,
        G: Fn(f64, f64) -> bool,
    {
        let mut rule_violation = Vec::new();
        let rule_sub_type = match self.rule_sub_type_value_explicit.as_ref() {
            Some(sub_type) => sub_type,
            _ => return Vec::new(),
        };

        let mut total_market_value: f64 = self.post_trade_investments.iter().map(|o| o.market_value).sum();
        total_market_value += self.post_trade_portfolio_cash_position.available_cash_balance;
        if total_market_value == 0.0 {
            return Vec::new();
        }

        let category = rule_sub_type.to_lowercase();
        let value: f64 = self.value.parse().unwrap_or_default();

        let pre_trade_mv: f64 = self
            .pre_trade_investments
            .iter()
            .filter(|o| match_fn(o, &category))
            .map(|o| o.market_value)
            .sum();

        let post_trade_mv: f64 = self
            .post_trade_investments
            .iter()
            .filter(|o| match_fn(o, &category))
            .map(|o| o.market_value)
            .sum();

        let post_trade_value = if self.value_type == ValueType::Absolute {
            post_trade_mv
        } else {
            (post_trade_mv / total_market_value) * 100.0
        };

        for order in self.orders {
            let violate = if compare_fn(post_trade_value, value) {
                true
            } else {
                false
            };

            rule_violation.push(PreTradeComplianceRulesReport {
                id: order.id,
                isin: order.isin.clone(),
                market_cap: order.market_cap.clone(),
                sector: order.industry.clone(),
                security_name: order.scrip_name.clone(),
                portfolio: self.portfolio_name.clone(),
                quantity: order.quantity,
                amount: order.transaction_amount,
                rule: format!("{}-{}", self.rule_type, rule_sub_type),
                violate,
                symbol: order.identifier.clone(),
                transaction_type: order.transaction_type,
            });
        }

        rule_violation
    }
}
