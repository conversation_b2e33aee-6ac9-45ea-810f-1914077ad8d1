use crate::{
    models::trade_idea::{BuyTradeIdeaOrderComputation, SellTradeIdeaOrderComputation},
    types::ClientComputedOrders,
};
use actlogica_logs::builder::LogBuilder;
use actlogica_logs::{log_error, log_info};
use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::{
        client_order_entry::{ClientOrderEntry, TransactionType},
        investment_transaction::InvestmentTransaction,
        oms::{ClientForOms, PortfolioForOms},
        portfolio::{Portfolio, PortfolioCashPosition, TradingMode},
    },
    types::{compute_order::StrategyForOrderComputation, OrderSourceType},
};
use alpha_utils::types::SecurityDetails;

impl BuyTradeIdeaOrderComputation {
    pub async fn construct_order(
        &self,
        client: &ClientForOms,
        portfolio: &Portfolio,
        portfolio_cash_position: PortfolioCashPosition,
        strategy: &StrategyForOrderComputation,
        dp_id: String,
        created_by: String,
        stock_details: &SecurityDetails,
        broker_trading_acc_number: Option<String>,
        order_source_type: OrderSourceType,
        source_ref: String,
        db_pool: Pool<Manager>,
    ) -> Result<ClientComputedOrders, String> {
        log_info(LogBuilder::system(&format!(
            "Buy-Trade-Idea: Constructing Order For Portfolio-Id: {:?}",
            portfolio.id
        )));

        let portfolio_available_cash = portfolio_cash_position.available_cash_balance;

        let total_portfolio_value = portfolio.market_value + portfolio_cash_position.available_cash_balance;

        let mut buy_trade_amount = total_portfolio_value * (self.change / 100f64);

        if buy_trade_amount > portfolio_cash_position.available_cash_balance {
            buy_trade_amount = 0f64;
        }

        let portfolio = PortfolioForOms {
            account_status: portfolio.account_status.clone(),
            client_strategy_code: portfolio.client_strategy_code.clone(),
            id: portfolio.id.clone(),
            market_value: portfolio.market_value,
            broker_trading_acc_number: broker_trading_acc_number.clone(),
            trading_mode: portfolio.trading_mode.clone(),
            portfolio_type : portfolio.portfolio_type.clone()
        };

        let price_to_consider = if order_source_type == OrderSourceType::RestrictedBuyTradeIdea
            || order_source_type == OrderSourceType::RestrictedSellTradeIdea
        {
            stock_details.get_latest_price()
        } else {
            self.buy_price_to
        };

        match stock_details {
            SecurityDetails::Stocks(stock_details) | SecurityDetails::ETF(stock_details) => {
                let buy_idea_quantity = (buy_trade_amount / price_to_consider).floor();

                buy_trade_amount = ((buy_idea_quantity * price_to_consider) * 100f64).round() / 100f64;

                let mut compute_order = ClientComputedOrders::build(
                    dp_id,
                    &client,
                    &portfolio,
                    &stock_details,
                    stock_details.isin.clone(),
                    self.exchange.clone(),
                    buy_idea_quantity,
                    buy_trade_amount,
                    created_by,
                    &strategy,
                    TransactionType::Buy,
                    0f64,
                    portfolio_available_cash,
                    order_source_type,
                    source_ref
                );

                compute_order.price = price_to_consider;
                compute_order.update_remarks_rationale(
                    String::from("Auto Remark: BuyTradeIdea"),
                    String::from("Buy Trade Idea Order For the Portfolio"),
                );

                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(tac) = broker_trading_acc_number {
                        compute_order.client_trading_account = tac;
                    } else {
                        log_error(LogBuilder::system("Broker Trading Account Number Not Found"));
                        return Err(String::from("Broker Trading Account Number Not Found"));
                    }
                }

                Ok(compute_order)
            }
            SecurityDetails::MutualFund(mf_details) => {
                let buy_idea_quantity = buy_trade_amount / price_to_consider;

                buy_trade_amount = ((buy_idea_quantity * price_to_consider) * 100f64).round() / 100f64;

                let mut conn = db_pool.get().await.map_err(|e| {
                    log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
                    String::from("Failed to get database connection")
                })?;

                let mf_folio = InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mf_details.isin)
                    .await?
                    .unwrap_or_else(|| String::from("NEW"));

                let mf_buy_sell_type = if mf_folio == "NEW" {
                    "FRESH".to_string()
                } else {
                    "ADDITIONAL".to_string()
                };

                let mut compute_order = ClientComputedOrders::build_for_mutual_fund(
                    dp_id,
                    &client,
                    &portfolio,
                    &mf_details,
                    mf_details.isin.clone(),
                    self.exchange.clone(),
                    buy_idea_quantity,
                    buy_trade_amount,
                    created_by,
                    &strategy,
                    TransactionType::Buy,
                    0f64,
                    portfolio_available_cash,
                    order_source_type,
                    source_ref
                );

                compute_order.mf_folio_number = Some(mf_folio);
                compute_order.mf_client_bank = client.bank_account_number.clone();
                compute_order.mf_buy_sell_type = Some(mf_buy_sell_type);

                compute_order.price = price_to_consider;
                compute_order.update_remarks_rationale(
                    String::from("Auto Remark: BuyTradeIdea"),
                    String::from("Buy Trade Idea Order For the Portfolio"),
                );

                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(tac) = broker_trading_acc_number {
                        compute_order.client_trading_account = tac;
                    } else {
                        log_error(LogBuilder::system("Broker Trading Account Number Not Found"));
                        return Err(String::from("Broker Trading Account Number Not Found"));
                    }
                }

                Ok(compute_order)
            }
        }
    }
}

impl SellTradeIdeaOrderComputation {
    pub async fn construct_order(
        &self,
        client: &ClientForOms,
        portfolio: &Portfolio,
        strategy: &StrategyForOrderComputation,
        created_by: String,
        stock_details: &SecurityDetails,
        current_holding: f64,
        dp_id: String,
        cash: PortfolioCashPosition,
        broker_trading_acc_number: Option<String>,
        order_source_type: OrderSourceType,
        source_ref: String,
        db_pool: Pool<Manager>,
    ) -> Result<ClientComputedOrders, String> {
        log_info(LogBuilder::system(&format!(
            "Sell-Trade-Idea: Constructing Order For Portfolio-Id: {:?}",
            portfolio.id
        )));

        let total_portfolio_value = portfolio.market_value + cash.available_cash_balance;

        let mut sell_trade_amount = total_portfolio_value * (self.change / 100f64);

        let price_to_consider = if order_source_type == OrderSourceType::RestrictedBuyTradeIdea
            || order_source_type == OrderSourceType::RestrictedSellTradeIdea
        {
            stock_details.get_latest_price()
        } else {
            self.sell_price_to
        };

        let mut sell_idea_quantity = (sell_trade_amount / price_to_consider).floor();

        if self.is_sell_all {
            sell_idea_quantity = current_holding
        }

        if sell_idea_quantity < 0f64 {
            sell_idea_quantity = 0f64;
        }

        if sell_idea_quantity > current_holding {
            sell_idea_quantity = current_holding;
        }

        sell_trade_amount = ((sell_idea_quantity * price_to_consider) * 100f64).round() / 100f64;

        let portfolio = PortfolioForOms {
            account_status: portfolio.account_status.clone(),
            client_strategy_code: portfolio.client_strategy_code.clone(),
            id: portfolio.id.clone(),
            market_value: portfolio.market_value,
            broker_trading_acc_number: None,
            trading_mode: portfolio.trading_mode.clone(),
            portfolio_type: portfolio.portfolio_type.clone(),
        };

        match stock_details {
            SecurityDetails::Stocks(stock_details) | SecurityDetails::ETF(stock_details) => {
                let mut compute_order = ClientComputedOrders::build(
                    dp_id,
                    &client,
                    &portfolio,
                    &stock_details,
                    stock_details.isin.clone(),
                    self.exchange.clone(),
                    sell_idea_quantity,
                    sell_trade_amount,
                    created_by,
                    &strategy,
                    TransactionType::Sell,
                    0f64,
                    cash.available_cash_balance,
                    order_source_type.clone(),
                    source_ref
                );

                compute_order.price = price_to_consider;

                compute_order.update_remarks_rationale(
                    String::from("Auto Remark: SellTradeIdea"),
                    String::from("Sell Trade Idea Order For the Portfolio"),
                );

                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(tac) = broker_trading_acc_number {
                        compute_order.client_trading_account = tac;
                    } else {
                        log_error(LogBuilder::system("Broker Trading Account Number Not Found"));
                        return Err(String::from("Broker Trading Account Number Not Found"));
                    }
                }

                Ok(compute_order)
            }
            SecurityDetails::MutualFund(mf_details) => {
                let mut conn = db_pool.get().await.map_err(|e| {
                    log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
                    String::from("Failed to get database connection")
                })?;

                let mf_folio = InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mf_details.isin)
                    .await?
                    .unwrap_or_else(|| String::from("NEW"));

                let mf_buy_sell_type = if mf_folio == "NEW" {
                    "FRESH".to_string()
                } else {
                    "REDEEM".to_string()
                };

                let mut compute_order = ClientComputedOrders::build_for_mutual_fund(
                    dp_id,
                    &client,
                    &portfolio,
                    &mf_details,
                    mf_details.isin.clone(),
                    self.exchange.clone(),
                    sell_idea_quantity,
                    sell_trade_amount,
                    created_by,
                    &strategy,
                    TransactionType::Sell,
                    0f64,
                    cash.available_cash_balance,
                    order_source_type.clone(),
                    source_ref
                );

                compute_order.mf_folio_number = Some(mf_folio);
                compute_order.mf_client_bank = client.bank_account_number.clone();
                compute_order.mf_buy_sell_type = Some(mf_buy_sell_type);

                compute_order.price = price_to_consider;
                compute_order.update_remarks_rationale(
                    String::from("Auto Remark: SellTradeIdea"),
                    String::from("Sell Trade Idea Order For the Portfolio"),
                );

                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(tac) = broker_trading_acc_number {
                        compute_order.client_trading_account = tac;
                    } else {
                        log_error(LogBuilder::system("Broker Trading Account Number Not Found"));
                        return Err(String::from("Broker Trading Account Number Not Found"));
                    }
                }

                Ok(compute_order)
            }
        }
    }
}

