use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
};

use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    connection::pool::{deadpool::managed::Pool, Manager},
    schema::{
        client::Client,
        client_order_entry::TransactionType,
        investment::Investments,
        oms::{ClientForOms, PortfolioDetailsForOms, PortfolioForOms},
        portfolio::{portfolio_custodian::PortfolioCustodianForOms, AccountStatus, Portfolio, PortfolioCashPosition},
        restricted_stocks::client_restricted_stocks::RestrictedStockForClients,
        strategy::{
            strategy_model_securities::{StrategyModelSecurities, StrategyModelSecuritiesForOms},
            Strategies,
        },
    },
    types::{compute_order::StrategyForOrderComputation, OrderSourceType},
};
use alpha_utils::{
    constants::RedisKey,
    types::{SecurityDetails, SecurityTypeForPrice},
};
use anyhow::{anyhow, Context};
use futures::{future::join_all, lock::Mutex};
use redis::AsyncCommands;
use tokio::sync::RwLock;
use tracing::{Instrument, Span};
use uuid::Uuid;

pub mod deviation;
pub mod error;
pub mod orders;
mod save_orders;
pub mod trade_compliance;
pub mod trade_idea;
pub mod traits;
use crate::{
    models::trade_idea::BuyTradeIdeaOrderComputation,
    oms::traits::ConstructOrder,
    types::{CustomisedOrder, CustomisedOrderRequest},
};
use crate::{models::trade_idea::SellTradeIdeaOrderComputation, types::ClientComputedOrders};
pub use save_orders::*;

pub struct OrderComputer {
    pool: Pool<Manager>,
    master_pool: Pool<Manager>,
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    created_by: String,
    capital: f64,
}

impl OrderComputer {
    pub const CACHE_EXPIRY: u64 = 5 * 60;

    pub fn new(
        pool: Pool<Manager>,
        master_pool: Pool<Manager>,
        redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
        created_by: String,
        capital: f64,
    ) -> Self {
        Self {
            capital,
            created_by,
            master_pool,
            redis_pool,
            pool,
        }
    }
    pub async fn compute_additional_deployment(
        &self,
        portfolio_id: String,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing additional deployment for portfolioID: {}",
            portfolio_id
        )));
        let (client, portfolio, strategy, custodian, portfolio_cash_position) =
            self.get_essentials(portfolio_id.clone()).await?;

        if portfolio.account_status != AccountStatus::Active {
            return Err(String::from("Portfolio Is Not Active"));
        }

        // Get the Securites in Strategy Model
        let strategy_model_securities = self
            .get_strategy_model_securities(strategy.model_id.clone(), &portfolio_id)
            .await?;

        let client = Arc::new(client);
        let portfolio = Arc::new(portfolio);
        let strategy = Arc::new(strategy);

        let available_cash_balance = portfolio_cash_position.available_cash_balance;

        log_info(
            LogBuilder::system(&format!(
                "Precomputing Details for Client: {} {}",
                client.first_name, client.last_name
            ))
            .add_metadata("client_id", &client.id)
            .add_metadata("portfolio_id", &portfolio.id)
            .add_metadata("strategy_id", &strategy.strategy_id)
            .add_metadata("strategy_name", &strategy.strategy_name)
            .add_metadata(
                "curr_cash_balance",
                &portfolio_cash_position.current_cash_balance.to_string(),
            )
            .add_metadata("available_cash_balance", &available_cash_balance.to_string()),
        );

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let security_cache: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

        let mut handlers = Vec::new();

        let order_id = Arc::new(AtomicU64::new(0));

        for security in strategy_model_securities {
            if security.weight <= 0f64 {
                log_warn(
                    LogBuilder::system(&format!(
                        "Skipping security with non-positive weight, ISIN: {:?}",
                        security.isin
                    ))
                    .add_metadata("portfolio_id", &portfolio_id)
                    .add_metadata("security_exchange", &security.exchange)
                    .add_metadata("security_weight", &security.weight.to_string())
                    .add_metadata("security_isin", &security.isin),
                );
                continue;
            };

            let client = client.clone();
            let portfolio = portfolio.clone();
            let master_pool = self.master_pool.clone();
            let db_pool = self.pool.clone();
            let redis_pool = self.redis_pool.clone();
            let strategy = strategy.clone();
            let created_by = self.created_by.clone();
            let computed_orders = computed_orders.clone();
            let dp_id = custodian.dp_id.clone();
            let capital = self.capital;

            let portfolio_id = portfolio_id.clone();
            let order_id_clone = order_id.clone();

            let source_ref = client.id.clone();

            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let mut redis_conn = match redis_pool.get().await {
                        Ok(conn) => conn,
                        Err(e) => {
                            log_error(LogBuilder::system(&format!("Failed: Redis connection: {}", e)));
                            return Err(String::from("Failed to get Redis connection"));
                        }
                    };
                    let order: Result<ClientComputedOrders, String> = security
                        .construct_order(
                            &mut redis_conn,
                            db_pool,
                            master_pool,
                            &client,
                            &portfolio,
                            &strategy,
                            capital,
                            created_by,
                            0f64,
                            available_cash_balance,
                            dp_id,
                            TransactionType::Buy,
                            OrderSourceType::AdditionalCapitalInPortfolio,
                            source_ref,
                        )
                        .await;

                    match order {
                        Ok(mut order) => {
                            order.id = id;

                            // Log ClientComputedOrders
                            log_contructed_order_details(&order);

                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);

                            Ok(())
                        }
                        Err(err) => {
                            log_warn(
                                LogBuilder::system(&format!("Failed to construct order. Error: {}", err))
                                    .add_metadata("portfolio_id", &portfolio_id)
                                    .add_metadata("security_isin", &security.isin)
                                    .add_metadata("client_id", &client.id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );
            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;

        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::AdditionalCapitalInPortfolio)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }

    pub async fn compute_order_for_capital_withdrawal(
        &self,
        portfolio_id: String,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing order for capital_withdrawl for portfolioID: {}",
            portfolio_id
        )));
        let (client, portfolio, strategy, custodian, portfolio_cash_position) =
            self.get_essentials(portfolio_id.clone()).await?;

        if portfolio.account_status != AccountStatus::Active && portfolio.account_status != AccountStatus::Exiting {
            return Err(String::from("Portfolio Is Not Active"));
        }

        let mut conn = self.pool.get().await.map_err(|_| {
            log_error(LogBuilder::system("Failed: DB connection"));
            format!("Failed to handle DB connection")
        })?;
        let portfolio_investments = Investments::get_by_portfolio_id(&mut conn, &portfolio_id).await?;

        drop(conn);

        let client = Arc::new(client);
        let portfolio = Arc::new(portfolio);
        let strategy = Arc::new(strategy);

        let portfolio_available_cash = portfolio_cash_position.available_cash_balance;

        let total_portfolio_value = portfolio.market_value + portfolio_cash_position.available_cash_balance;

        log_info(
            LogBuilder::system(&format!(
                "Precomputing Details for Client: {} {}",
                client.first_name, client.last_name
            ))
            .add_metadata("client_id", &client.id)
            .add_metadata("portfolio_id", &portfolio.id)
            .add_metadata("strategy_id", &strategy.strategy_id)
            .add_metadata("strategy_name", &strategy.strategy_name)
            .add_metadata(
                "curr_cash_balance",
                &portfolio_cash_position.current_cash_balance.to_string(),
            )
            .add_metadata("available_cash_balance", &portfolio_available_cash.to_string())
            .add_metadata("total_portfolio_value", &total_portfolio_value.to_string()),
        );

        let security_cache: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let mut handlers = Vec::new();

        let order_id = Arc::new(AtomicU64::new(0));
        for mut investment in portfolio_investments {
            if investment.current_holding == 0f64 {
                log_warn(
                    LogBuilder::system(&format!(
                        "Skipping investment with zero current holding, InvestmentId: {:?}",
                        investment.id
                    ))
                    .add_metadata("portfolio_id", &portfolio_id)
                    .add_metadata("investment_market_value", &investment.market_value.to_string())
                    .add_metadata("investment_isin", &investment.isin),
                );
                continue;
            }
            let client = client.clone();
            let portfolio = portfolio.clone();
            let master_pool = self.master_pool.clone();
            let db_pool = self.pool.clone();
            let redis_pool = self.redis_pool.clone();
            let strategy = strategy.clone();
            let created_by = self.created_by.clone();
            let computed_orders = computed_orders.clone();
            let dp_id = custodian.dp_id.clone();
            let capital = self.capital;
            let _security_cache = security_cache.clone();

            //Calculate the weight of each investments
            investment.weight =
                (((investment.market_value / total_portfolio_value) * 100f64) * 100f64).round() / 100f64;

            let portfolio_id = portfolio_id.clone();

            let order_id_clone = order_id.clone();

            let source_ref = client.id.clone();

            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let mut redis_conn = redis_pool
                        .get()
                        .await
                        .map_err(|_e| String::from("Failed to Obtain Connection From DB"))?;

                    let order = investment
                        .construct_order(
                            &mut redis_conn,
                            db_pool,
                            master_pool,
                            &client,
                            &portfolio,
                            &strategy,
                            capital,
                            created_by,
                            0f64,
                            portfolio_available_cash,
                            dp_id,
                            TransactionType::Sell,
                            OrderSourceType::CapitalWithdrawalInPortfolio,
                            source_ref,
                        )
                        .await;

                    match order {
                        Ok(mut order) => {
                            order.id = id;
                            log_contructed_order_details(&order);
                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);
                            Ok(())
                        }
                        Err(err) => {
                            log_warn(
                                LogBuilder::system(&format!("Failed to construct order. Error: {}", err))
                                    .add_metadata("portfolio_id", &portfolio_id)
                                    .add_metadata("client_id", &client.id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );

            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;
        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::CapitalWithdrawalInPortfolio)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }

    pub async fn compute_order_for_custom_trade_for_deviation(
        &self,
        customised_orders: Vec<CustomisedOrder>,
        portfolios: Vec<PortfolioDetailsForOms>,
    ) -> Result<Vec<ClientComputedOrders>, ()> {
        log_info(LogBuilder::system("Starting computation for custom trade deviation"));
        let computed_orders = Arc::new(Mutex::new(Vec::new()));
        let mut handlers = Vec::new();

        let mut orders_by_portfolio = customised_orders.into_iter().fold(HashMap::new(), |mut acc, order| {
            acc.entry(order.portfolio_id.clone())
                .or_insert_with(Vec::new)
                .push(order);
            acc
        });

        for portfolio in portfolios {
            let orders = orders_by_portfolio.remove(&portfolio.portfolio.id);
            if let Some(orders) = orders {
                let client = Arc::new(portfolio.client);
                let capital = self.capital;
                let created_by = self.created_by.clone();
                let client = client.clone();

                let computed_orders = computed_orders.clone();
                let handler = tokio::spawn(
                    async move {
                        for order in orders {
                            log_info(LogBuilder::system(&format!(
                                "Computing for ISIN: {:?}, Portfolio ID: {:?}",
                                order.isin, portfolio.portfolio.id
                            )));
                            let constructed_order = order
                                .construct_order(
                                    &client,
                                    &portfolio.portfolio,
                                    &portfolio.strategy,
                                    capital,
                                    created_by.clone(),
                                    order.current_holding,
                                    portfolio.cash_position.available_cash_balance,
                                    portfolio.custodian.dp_id.clone(),
                                    if order.is_buy {
                                        TransactionType::Buy
                                    } else {
                                        TransactionType::Sell
                                    },
                                    OrderSourceType::CustomClientOrder,
                                )
                                .await;

                            match constructed_order {
                                Ok(order) => {
                                    log_contructed_order_details(&order);
                                    let mut computed_orders_guard = computed_orders.lock().await;
                                    computed_orders_guard.push(order);
                                }
                                Err(err) => {
                                    log_warn(
                                        LogBuilder::system(&format!("Failed to construct order. Error: {}", err))
                                            .add_metadata("portfolio_id", &portfolio.portfolio.id)
                                            .add_metadata("client_id", &client.id),
                                    );
                                }
                            }
                        }
                    }
                    .instrument(Span::current()),
                );
                handlers.push(handler);
            }
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.lock().await;
        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));
        Ok(computed_orders_guard.clone())
    }

    pub async fn compute_order_for_custom_trade(
        &self,
        customised_orders: Vec<CustomisedOrderRequest>,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        /*
         *   The Order will be for different portfolios
         *   But two or more orders can be a part of same portfolio too
         *   Cache the already fetched portfolio in that case
         */

        let computed_orders = Arc::new(Mutex::new(Vec::new()));
        let security_cache: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));
        let mut handlers = Vec::new();

        let orders_by_portfolio = customised_orders.into_iter().fold(HashMap::new(), |mut acc, order| {
            acc.entry(order.portfolio_id.clone())
                .or_insert_with(Vec::new)
                .push(order);
            acc
        });

        let order_id = Arc::new(AtomicU64::new(0));
        for (portfolio_id, customised_orders) in orders_by_portfolio {
            let (client, portfolio, strategy, custodian, portfolio_cash_position) =
                match self.get_essentials(portfolio_id.clone()).await {
                    Ok(result) => result,
                    Err(err) => {
                        return Err(format!(
                            "Failed to get essentials for portfolio {}: {}",
                            portfolio_id, err
                        ));
                    }
                };

            let client = Arc::new(client);
            let db_pool = self.pool.clone();
            let redis_pool = self.redis_pool.clone();
            let master_pool = self.master_pool.clone();
            let capital = self.capital;
            let created_by = self.created_by.clone();
            let client = client.clone();
            let computed_orders = computed_orders.clone();
            let _security_cache = security_cache.clone();
            let source_ref = client.id.clone();

            let order_id_clone = order_id.clone();
            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let mut redis_conn = match redis_pool.get().await {
                        Ok(conn) => conn,
                        Err(_err) => {
                            return Err("Failed to get redis connection");
                        }
                    };

                    let mut conn = match db_pool.get().await {
                        Ok(conn) => conn,
                        Err(_err) => return Err("Failed to get db_pool connection"),
                    };

                    Ok(for order in customised_orders {
                        let db_pool = db_pool.clone();

                        {
                            // logging...
                            let action = if order.is_buy { "Buy" } else { "Sell" };
                            let qty = if order.is_buy {
                                order.buy_quantity.to_string()
                            } else {
                                order.sell_quantity.to_string()
                            };
                            let price = if order.is_buy {
                                order.buy_price.to_string()
                            } else {
                                order.sell_price.to_string()
                            };
                            let rationale = order.rationale.clone().unwrap_or_else(|| "None".to_string());

                            log_info(
                                LogBuilder::system(&format!(
                                    "Computing for PortfolioId: {:?}, ISIN: {:?}",
                                    portfolio_id, order.isin
                                ))
                                .add_metadata("portfolio_id", &portfolio_id)
                                .add_metadata("isin", &order.isin)
                                .add_metadata("action", action)
                                .add_metadata("quantity", &qty)
                                .add_metadata("price", &price)
                                .add_metadata("exchange", &order.exchange)
                                .add_metadata("rationale", &rationale),
                            );
                        }

                        // Get The current Holding of the isin in the portfolio
                        // If Not found then make it as zero
                        let current_holding =
                            match Investments::get_current_holding_of_isin(&mut conn, &order.portfolio_id, &order.isin)
                                .await
                            {
                                Ok(Some(holding)) => holding,
                                Ok(None) => {
                                    log_warn(
                                        LogBuilder::system(&format!(
                                        "Current holding not found for ISIN: {} in portfolio: {}. Defaulting to 0.0",
                                        &order.isin, &order.portfolio_id
                                    ))
                                        .add_metadata("portfolio_id", &order.portfolio_id)
                                        .add_metadata("isin", &order.isin),
                                    );
                                    0.0 // Defaulting current holding to zero
                                }
                                Err(err) => {
                                    log_error(LogBuilder::system(&format!(
                                        "Failed to get current holding for ISIN {}: {}",
                                        &order.isin, err
                                    )));
                                    return Err("Failed to get currect holding");
                                }
                            };

                        let constructed_order = order
                            .construct_order(
                                &mut redis_conn,
                                db_pool,
                                master_pool.clone(),
                                &client,
                                &portfolio,
                                &strategy,
                                capital,
                                created_by.clone(),
                                current_holding,
                                portfolio_cash_position.available_cash_balance,
                                custodian.dp_id.clone(),
                                if order.is_buy {
                                    TransactionType::Buy
                                } else {
                                    TransactionType::Sell
                                },
                                OrderSourceType::CustomClientOrder,
                                source_ref.clone(),
                            )
                            .await;

                        match constructed_order {
                            Ok(mut order) => {
                                order.id = id;
                                log_contructed_order_details(&order);
                                let mut computed_orders_guard = computed_orders.lock().await;
                                computed_orders_guard.push(order);
                            }
                            Err(err) => log_error(LogBuilder::system(&format!(
                                "Failed to construct order. ErrorMsg: {}",
                                err
                            ))),
                        }
                    })
                }
                .instrument(Span::current()),
            );
            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.lock().await;

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::CustomClientOrder)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }

    pub async fn compute_for_buy_trade_idea(
        &self,
        trade_idea_order: BuyTradeIdeaOrderComputation,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing buy trade idea for ISIN: {}, Model ID: {}",
            trade_idea_order.isin, trade_idea_order.model_id
        )));
        //Get All Portfolios In Model
        let mut conn = self.pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
            String::from("Failed to get database connection")
        })?;
        let redis_pool = self.redis_pool.clone();
        let mut redis_conn = redis_pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get Redis connection: {}", e)));
            String::from("Failed to get Redis connection")
        })?;

        let portfolios_in_model =
            Portfolio::get_portfolio_in_model_id_for_trade_idea(&mut conn, trade_idea_order.model_id.clone())
                .await
                .map_err(|e| {
                    log_error(LogBuilder::system(&format!("Failed to get portfolios in model: {}", e)));
                    String::from("Failed to get portfolios in model")
                })?;

        let client_ids: Vec<String> = portfolios_in_model
            .iter()
            .map(|c| c.portfolio.client_id.clone())
            .collect();

        //Get the Clients of these Portfolios
        let clients = Client::get_by_ids_for_oms(&mut conn, &client_ids).await.map_err(|_e| {
            return String::from("Failed to Get Clients");
        })?;

        //Get Stock Details for this ISIN
        let stock_details = SecurityDetails::build(
            &mut redis_conn,
            self.master_pool.clone(),
            trade_idea_order.isin.clone(),
            SecurityTypeForPrice::Equity,
            &trade_idea_order.exchange,
        )
        .await?;

        //Check for Restricted Stock for All Clients
        let restricted_stocks_for_all_clients = RestrictedStockForClients::get_by_client_ids_and_isin(
            &mut conn,
            &client_ids,
            trade_idea_order.isin.clone(),
        )
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get restricted stocks for clients: {}",
                e
            )));
            String::from("Failed to get restricted stocks for clients")
        })?;

        //GET Strategy For This Model
        let strategy =
            Strategies::get_strategy_for_order_computation_by_model_id(&mut conn, trade_idea_order.model_id.clone())
                .await
                .map_err(|e| {
                    log_error(LogBuilder::system(&format!(
                        "Failed to get strategy for order computation: {}",
                        e
                    )));
                    String::from("Failed to get strategy for order computation")
                })?
                .ok_or_else(|| {
                    log_error(LogBuilder::system("Model is Missing in Portfolio"));
                    String::from("Model is Missing in Portfolio")
                })?;

        let strategy = Arc::new(strategy);

        let stock_details = Arc::new(stock_details);

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let mut handlers = Vec::new();
        let trade_idea_order = Arc::new(trade_idea_order);

        log_info(LogBuilder::system(&format!(
            "Creating Order For Trade Idea isin = {:?}, Effecting {:?} Number of clients",
            trade_idea_order.isin,
            portfolios_in_model.len()
        )));

        let order_id = Arc::new(AtomicU64::new(0));
        for portfolio in portfolios_in_model {
            let dp_id = portfolio.dp_id;
            let portfolio_cash_position = portfolio.cash;
            let broker_trading_acc_number = portfolio.broker_trading_acc_number;

            let portfolio = portfolio.portfolio;
            if portfolio.account_status != AccountStatus::Active {
                log_warn(
                    LogBuilder::system(&format!(
                        "Portfolio {} is neither Active nor Frozen to do Buy Trade Idea",
                        portfolio.client_strategy_code
                    ))
                    .add_metadata("portfolio_id", &portfolio.id)
                    .add_metadata("client_id", &portfolio.client_id),
                );
                continue;
            }

            let created_by = self.created_by.clone();
            let pool = self.pool.clone();
            let mut stock_details = stock_details.clone();
            let redis_pool = redis_pool.clone();
            let computed_orders = computed_orders.clone();
            let master_pool = self.master_pool.clone();
            let client = clients.iter().find(|c| c.id == portfolio.client_id).unwrap().clone();
            let trade_idea_order = trade_idea_order.clone();
            let strategy = strategy.clone();
            let source_ref = client.id.clone();

            let restricted_stock_for_the_client = restricted_stocks_for_all_clients
                .iter()
                .find(|r| r.client_id == portfolio.client_id)
                .cloned();

            let order_id_clone = order_id.clone();
            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);

                    let mut order_source_type = OrderSourceType::BuyTradeIdea;
                    //Check if the Client Has Restricted Stocks
                    if let Some(res_stock) = restricted_stock_for_the_client {
                        if let Some(isin_alt) = res_stock.isin_alternative_security {
                            log_info(
                                LogBuilder::system("Finding Restricted Stocks")
                                    .add_metadata("portfolio_id", &portfolio.id)
                                    .add_metadata("client_id", &portfolio.client_id),
                            );
                            let mut redis_conn = redis_pool.get().await.map_err(|e| {
                                log_error(LogBuilder::system(&format!("Failed to get Redis connection: {}", e)));
                                String::from("Failed to get Redis connection")
                            })?;
                            let res_details = SecurityDetails::build(
                                &mut redis_conn,
                                master_pool,
                                isin_alt.clone(),
                                SecurityTypeForPrice::Equity,
                                &trade_idea_order.exchange,
                            )
                            .await?;

                            stock_details = res_details.into();
                            order_source_type = OrderSourceType::RestrictedBuyTradeIdea
                        } else {
                            log_warn(
                                LogBuilder::system("Restricted Stock Alternative Not available. Skipping...")
                                    .add_metadata("portfolio_id", &portfolio.id)
                                    .add_metadata("client_id", &portfolio.client_id),
                            );
                            return Ok(());
                        }
                    }

                    let orders = trade_idea_order
                        .construct_order(
                            &client,
                            &portfolio,
                            portfolio_cash_position,
                            &strategy,
                            dp_id,
                            created_by,
                            &stock_details,
                            broker_trading_acc_number,
                            order_source_type,
                            source_ref,
                            pool,
                        )
                        .await;

                    match orders {
                        Ok(mut order) => {
                            order.id = id;
                            log_contructed_order_details(&order);
                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);
                            Ok(())
                        }
                        Err(err) => {
                            log_error(
                                LogBuilder::system(&format!("Failed to create trade idea orders. Error: {:?}", err))
                                    .add_metadata("portfolio_id", &portfolio.id)
                                    .add_metadata("client_id", &portfolio.client_id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );

            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;
        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::BuyTradeIdea)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }

    pub async fn compute_for_sell_trade_idea(
        &self,
        trade_idea_order: SellTradeIdeaOrderComputation,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing sell trade idea for ISIN: {}, Model ID: {}",
            trade_idea_order.isin, trade_idea_order.model_id
        )));
        //Get All Portfolios In Model
        let mut conn = self.pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
            String::from("Failed to get database connection")
        })?;
        let redis_pool = self.redis_pool.clone();
        let mut redis_conn = redis_pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get Redis connection: {}", e)));
            String::from("Failed to get Redis connection")
        })?;

        let portfolios_in_model = Portfolio::get_by_model_id_for_rebalancing(
            &mut conn,
            trade_idea_order.model_id.clone(),
            trade_idea_order.isin.clone(),
        )
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get portfolios for rebalancing: {}",
                e
            )));
            String::from("Failed to get portfolios for rebalancing")
        })?;

        let client_ids: Vec<String> = portfolios_in_model
            .iter()
            .map(|c| c.portfolio.client_id.clone())
            .collect();

        //Get the Clients of these Portfolios
        let clients = Client::get_by_ids_for_oms(&mut conn, &client_ids).await.map_err(|_e| {
            return String::from("Failed to Get Clients");
        })?;

        //GET Strategy For This Model
        let strategy =
            Strategies::get_strategy_for_order_computation_by_model_id(&mut conn, trade_idea_order.model_id.clone())
                .await
                .map_err(|e| {
                    log_error(LogBuilder::system(&format!(
                        "Failed to get strategy for order computation: {}",
                        e
                    )));
                    String::from("Failed to get strategy for order computation")
                })?
                .ok_or_else(|| {
                    log_error(LogBuilder::system("Model is Missing in Portfolio"));
                    String::from("Model is Missing in Portfolio")
                })?;

        let strategy = Arc::new(strategy);

        //Get Stock Details for this ISIN
        let stock_details = SecurityDetails::build(
            &mut redis_conn,
            self.master_pool.clone(),
            trade_idea_order.isin.clone(),
            SecurityTypeForPrice::Equity,
            &trade_idea_order.exchange,
        )
        .await?;

        //Get Restricted Stock for All Clients
        let restricted_stocks_for_all_clients = RestrictedStockForClients::get_by_client_ids_and_isin(
            &mut conn,
            &client_ids,
            trade_idea_order.isin.clone(),
        )
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get restricted stocks for clients: {}",
                e
            )));
            String::from("Failed to get restricted stocks for clients")
        })?;

        let stock_details = Arc::new(stock_details);

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let mut handlers = Vec::new();
        let trade_idea_order = Arc::new(trade_idea_order);

        log_info(LogBuilder::system(&format!(
            "Creating Order For Trade Idea isin = {:?}, Effecting {:?} Number of Portfolios",
            trade_idea_order.isin,
            portfolios_in_model.len()
        )));

        let order_id = Arc::new(AtomicU64::new(0));
        for portfolio_for_rebalance in portfolios_in_model {
            let portfolio = portfolio_for_rebalance.portfolio;

            if portfolio.account_status != AccountStatus::Active && portfolio.account_status != AccountStatus::Exiting {
                log_warn(
                    LogBuilder::system(&format!(
                        "Portfolio {} is neither Active nor Exiting to do a Sell Trade Idea",
                        portfolio.client_strategy_code
                    ))
                    .add_metadata("portfolio_id", &portfolio.id)
                    .add_metadata("client_id", &portfolio.client_id),
                );
                continue;
            }

            let created_by = self.created_by.clone();
            let stock_details = stock_details.clone();
            let computed_orders = computed_orders.clone();
            let client = clients.iter().find(|c| c.id == portfolio.client_id).unwrap().clone();
            let trade_idea_order = trade_idea_order.clone();
            let strategy = strategy.clone();
            let source_ref = client.id.clone();

            let restricted_stock_for_the_client = restricted_stocks_for_all_clients
                .iter()
                .find(|r| r.client_id == portfolio.client_id)
                .cloned();

            let pool = self.pool.clone();
            let order_id_clone = order_id.clone();
            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let order_source_type = OrderSourceType::SellTradeIdea;

                    //Check if the Client Has Restricted Stocks
                    //If there is restriction then Don't Compute Order for this client
                    if restricted_stock_for_the_client.is_some() {
                        return Ok(());
                    }

                    let orders = trade_idea_order
                        .construct_order(
                            &client,
                            &portfolio,
                            &strategy,
                            created_by,
                            &stock_details,
                            portfolio_for_rebalance.current_holding,
                            portfolio_for_rebalance.dp_id,
                            portfolio_for_rebalance.cash,
                            portfolio_for_rebalance.broker_trading_acc_number,
                            order_source_type,
                            source_ref,
                            pool,
                        )
                        .await;

                    match orders {
                        Ok(mut order) => {
                            order.id = id;
                            log_contructed_order_details(&order);
                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);

                            Ok(())
                        }
                        Err(err) => {
                            log_error(
                                LogBuilder::system(&format!("Failed to create trade idea orders. Error: {:?}", err))
                                    .add_metadata("portfolio_id", &portfolio.id)
                                    .add_metadata("client_id", &portfolio.client_id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );

            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;
        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::SellTradeIdea)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }

    async fn get_essentials(
        &self,
        portfolio_id: String,
    ) -> Result<
        (
            ClientForOms,
            PortfolioForOms,
            StrategyForOrderComputation,
            PortfolioCustodianForOms,
            PortfolioCashPosition,
        ),
        String,
    > {
        let mut conn = self.pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
            String::from("Failed to get database connection")
        })?;

        let details = PortfolioDetailsForOms::get(&mut conn, &portfolio_id)
            .await?
            .ok_or_else(|| String::from("Portfolio Doesn't Exist"))?;

        Ok((
            details.client,
            details.portfolio,
            details.strategy,
            details.custodian,
            details.cash_position,
        ))
    }

    async fn get_strategy_model_securities(
        &self,
        model_id: String,
        portfolio_id: &str,
    ) -> Result<Vec<StrategyModelSecuritiesForOms>, String> {
        let mut conn = self.pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get database connection: {}", e)));
            String::from("Failed to get database connection")
        })?;
        let strategy_model_securites =
            StrategyModelSecurities::get_for_compute_order_by_model_id_and_apply_restricted_stocks(
                &mut conn,
                model_id.clone(),
                portfolio_id,
            )
            .await?;

        Ok(strategy_model_securites)
    }

    /// Save the Computed Orders into redis and generate a unique Id for it with an expiry of 5 minute
    async fn save_computed_orders_to_cache(
        &self,
        orders: &Vec<ClientComputedOrders>,
        order_source: OrderSourceType,
    ) -> anyhow::Result<Uuid> {
        let id = Uuid::new_v4();
        let mut redis_conn = self.redis_pool.get().await?;
        let mut pipe = deadpool_redis::redis::pipe();
        let mut bulk_items: Vec<(u64, Vec<u8>)> = Vec::new();

        for order in orders {
            let serialized = serde_json::to_vec(&order).unwrap();
            bulk_items.push((order.id, serialized));
        }

        let key = format!("{}:{}", RedisKey::ClientComputedOrders.to_string(), id.to_string(),);

        pipe.cmd("ZADD").arg(&key).arg(&bulk_items);

        pipe.expire(&key, Self::CACHE_EXPIRY as i64);

        let key = format!("{}:{}", RedisKey::PreTradeSessionOrderSource, id);
        pipe.set_ex(key, order_source.to_string(), Self::CACHE_EXPIRY);

        // Execute the pipeline atomically
        pipe.query_async::<deadpool_redis::redis::Value>(&mut *redis_conn)
            .await
            .map_err(|_| anyhow!("failed to save orders to cache"))?;

        Ok(id)
    }

    pub async fn get_computed_orders_from_cache(&self, session_id: Uuid) -> anyhow::Result<Vec<ClientComputedOrders>> {
        let mut redis_conn = self.redis_pool.get().await?;
        let key = format!(
            "{}:{}",
            RedisKey::ClientComputedOrders.to_string(),
            session_id.to_string(),
        );

        let order_string: Vec<String> = redis_conn
            .zrange(key, 0, -1)
            .await
            .context("failed to get orders from redis")?;

        let orders: Vec<ClientComputedOrders> = order_string
            .into_iter()
            .map(|s| serde_json::from_str::<ClientComputedOrders>(&s).context("Order deserialisation failed"))
            .collect::<Result<_, _>>()?;

        Ok(orders)
    }

    pub async fn compute_order_for_capital_withdrawal_stp(
        &self,
        portfolio_id: String,
        source_ref: String,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing order for capital_withdrawl for portfolioID: {}",
            portfolio_id
        )));
        let (client, portfolio, strategy, custodian, portfolio_cash_position) =
            self.get_essentials(portfolio_id.clone()).await?;

        if portfolio.account_status != AccountStatus::Active && portfolio.account_status != AccountStatus::Exiting {
            return Err(String::from("Portfolio Is Not Active"));
        }

        let mut conn = self.pool.get().await.map_err(|_| {
            log_error(LogBuilder::system("Failed: DB connection"));
            format!("Failed to handle DB connection")
        })?;
        let portfolio_investments = Investments::get_by_portfolio_id(&mut conn, &portfolio_id).await?;

        drop(conn);

        let client = Arc::new(client);
        let portfolio = Arc::new(portfolio);
        let strategy = Arc::new(strategy);

        let portfolio_available_cash = portfolio_cash_position.available_cash_balance;

        let total_portfolio_value = portfolio.market_value + portfolio_cash_position.available_cash_balance;

        log_info(
            LogBuilder::system(&format!(
                "Precomputing Details for Client: {} {}",
                client.first_name, client.last_name
            ))
            .add_metadata("client_id", &client.id)
            .add_metadata("portfolio_id", &portfolio.id)
            .add_metadata("strategy_id", &strategy.strategy_id)
            .add_metadata("strategy_name", &strategy.strategy_name)
            .add_metadata(
                "curr_cash_balance",
                &portfolio_cash_position.current_cash_balance.to_string(),
            )
            .add_metadata("available_cash_balance", &portfolio_available_cash.to_string())
            .add_metadata("total_portfolio_value", &total_portfolio_value.to_string()),
        );

        let security_cache: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let mut handlers = Vec::new();

        let order_id = Arc::new(AtomicU64::new(0));
        for mut investment in portfolio_investments {
            if investment.current_holding == 0f64 {
                log_warn(
                    LogBuilder::system(&format!(
                        "Skipping investment with zero current holding, InvestmentId: {:?}",
                        investment.id
                    ))
                    .add_metadata("portfolio_id", &portfolio_id)
                    .add_metadata("investment_market_value", &investment.market_value.to_string())
                    .add_metadata("investment_isin", &investment.isin),
                );
                continue;
            }
            let client = client.clone();
            let portfolio = portfolio.clone();
            let master_pool = self.master_pool.clone();
            let db_pool = self.pool.clone();
            let redis_pool = self.redis_pool.clone();
            let strategy = strategy.clone();
            let created_by = self.created_by.clone();
            let computed_orders = computed_orders.clone();
            let dp_id = custodian.dp_id.clone();
            let capital = self.capital;
            let _security_cache = security_cache.clone();
            let source_type = OrderSourceType::SystematicDeploymentRedemption;
            let source_ref = source_ref.clone();

            //Calculate the weight of each investments
            investment.weight =
                (((investment.market_value / total_portfolio_value) * 100f64) * 100f64).round() / 100f64;

            let portfolio_id = portfolio_id.clone();

            let order_id_clone = order_id.clone();
            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let mut redis_conn = redis_pool
                        .get()
                        .await
                        .map_err(|_e| String::from("Failed to Obtain Connection From DB"))?;

                    let order = investment
                        .construct_order(
                            &mut redis_conn,
                            db_pool,
                            master_pool,
                            &client,
                            &portfolio,
                            &strategy,
                            capital,
                            created_by,
                            0f64,
                            portfolio_available_cash,
                            dp_id,
                            TransactionType::Sell,
                            source_type.clone(),
                            source_ref.clone(),
                        )
                        .await;

                    match order {
                        Ok(mut order) => {
                            order.id = id;
                            log_contructed_order_details(&order);
                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);
                            Ok(())
                        }
                        Err(err) => {
                            log_warn(
                                LogBuilder::system(&format!("Failed to construct order. Error: {}", err))
                                    .add_metadata("portfolio_id", &portfolio_id)
                                    .add_metadata("client_id", &client.id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );

            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;
        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::CapitalWithdrawalInPortfolio)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }
    pub async fn compute_additional_deployment_for_stp(
        &self,
        portfolio_id: String,
        source_ref: String,
    ) -> Result<(Uuid, Vec<ClientComputedOrders>), String> {
        log_info(LogBuilder::system(&format!(
            "Computing additional deployment for portfolioID: {}",
            portfolio_id
        )));
        let (client, portfolio, strategy, custodian, portfolio_cash_position) =
            self.get_essentials(portfolio_id.clone()).await?;

        if portfolio.account_status != AccountStatus::Active {
            return Err(String::from("Portfolio Is Not Active"));
        }

        // Get the Securites in Strategy Model
        let strategy_model_securities = self
            .get_strategy_model_securities(strategy.model_id.clone(), &portfolio_id)
            .await?;

        let client = Arc::new(client);
        let portfolio = Arc::new(portfolio);
        let strategy = Arc::new(strategy);

        let available_cash_balance = portfolio_cash_position.available_cash_balance;

        log_info(
            LogBuilder::system(&format!(
                "Precomputing Details for Client: {} {}",
                client.first_name, client.last_name
            ))
            .add_metadata("client_id", &client.id)
            .add_metadata("portfolio_id", &portfolio.id)
            .add_metadata("strategy_id", &strategy.strategy_id)
            .add_metadata("strategy_name", &strategy.strategy_name)
            .add_metadata(
                "curr_cash_balance",
                &portfolio_cash_position.current_cash_balance.to_string(),
            )
            .add_metadata("available_cash_balance", &available_cash_balance.to_string()),
        );

        let computed_orders = Arc::new(RwLock::new(Vec::new()));
        let security_cache: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

        let mut handlers = Vec::new();

        let order_id = Arc::new(AtomicU64::new(0));

        for security in strategy_model_securities {
            if security.weight <= 0f64 {
                log_warn(
                    LogBuilder::system(&format!(
                        "Skipping security with non-positive weight, ISIN: {:?}",
                        security.isin
                    ))
                    .add_metadata("portfolio_id", &portfolio_id)
                    .add_metadata("security_exchange", &security.exchange)
                    .add_metadata("security_weight", &security.weight.to_string())
                    .add_metadata("security_isin", &security.isin),
                );
                continue;
            };

            let client = client.clone();
            let portfolio = portfolio.clone();
            let master_pool = self.master_pool.clone();
            let db_pool = self.pool.clone();
            let redis_pool = self.redis_pool.clone();
            let strategy = strategy.clone();
            let created_by = self.created_by.clone();
            let computed_orders = computed_orders.clone();
            let dp_id = custodian.dp_id.clone();
            let capital = self.capital;

            let portfolio_id = portfolio_id.clone();
            let order_id_clone = order_id.clone();

            let source_type = OrderSourceType::SystematicDeploymentInstallment;
            let source_ref = client.id.clone();

            let handler = tokio::spawn(
                async move {
                    let id = order_id_clone.fetch_add(1, Ordering::SeqCst);
                    let mut redis_conn = match redis_pool.get().await {
                        Ok(conn) => conn,
                        Err(e) => {
                            log_error(LogBuilder::system(&format!("Failed: Redis connection: {}", e)));
                            return Err(String::from("Failed to get Redis connection"));
                        }
                    };
                    let order: Result<ClientComputedOrders, String> = security
                        .construct_order(
                            &mut redis_conn,
                            db_pool,
                            master_pool,
                            &client,
                            &portfolio,
                            &strategy,
                            capital,
                            created_by,
                            0f64,
                            available_cash_balance,
                            dp_id,
                            TransactionType::Buy,
                            source_type.clone(),
                            source_ref,
                        )
                        .await;

                    match order {
                        Ok(mut order) => {
                            order.id = id;

                            // Log ClientComputedOrders
                            log_contructed_order_details(&order);

                            let mut computed_orders_guard = computed_orders.write().await;
                            computed_orders_guard.push(order);

                            Ok(())
                        }
                        Err(err) => {
                            log_warn(
                                LogBuilder::system(&format!("Failed to construct order. Error: {}", err))
                                    .add_metadata("portfolio_id", &portfolio_id)
                                    .add_metadata("security_isin", &security.isin)
                                    .add_metadata("client_id", &client.id),
                            );
                            Err(err)
                        }
                    }
                }
                .instrument(Span::current()),
            );
            handlers.push(handler);
        }

        join_all(handlers).await;

        let computed_orders_guard = computed_orders.read().await;

        log_info(LogBuilder::system(&format!(
            "Total Trade Idea Computed Orders = {}",
            computed_orders_guard.len()
        )));

        let save_orders = self
            .save_computed_orders_to_cache(&computed_orders_guard, OrderSourceType::AdditionalCapitalInPortfolio)
            .await;
        match save_orders {
            Ok(id) => Ok((id, computed_orders_guard.clone())),
            Err(err) => return Err(err.to_string()),
        }
    }
}

/// This function logs the details of a computed client order for auditing and debugging purposes.
fn log_contructed_order_details(order: &ClientComputedOrders) {
    let currency = order.currency.clone().unwrap_or("None".to_string());
    let mf_folio_number = order.mf_folio_number.clone().unwrap_or("None".to_string());
    let mf_buy_sell_type = order.mf_buy_sell_type.clone().unwrap_or("None".to_string());
    let mf_all_units_redemption_flag = order.mf_all_units_redemption_flag.clone().unwrap_or("None".to_string());
    let mf_client_bank = order.mf_client_bank.clone().unwrap_or("None".to_string());
    let mf_oms_client_code = order.mf_oms_client_code.clone().unwrap_or("None".to_string());
    let euin_number = order.euin_number.clone().unwrap_or("None".to_string());
    let series = order.series.clone().unwrap_or("None".to_string());

    log_info(
        LogBuilder::system("Order constructed")
            .add_metadata("client_id", &order.client_id)
            .add_metadata("client_code", &order.client_code)
            .add_metadata("client_strategy_code", &order.client_strategy_code)
            .add_metadata("client_name", &order.client_name)
            .add_metadata("strategy_code", &order.strategy_code)
            .add_metadata("strategy_name", &order.strategy_name)
            .add_metadata("strategy_model_name", &order.strategy_model_name)
            .add_metadata("strategy_custody_code", &order.strategy_custody_code)
            .add_metadata(
                "strategy_trading_account_number",
                &order.strategy_trading_account_number.as_deref().unwrap_or("None"),
            )
            .add_metadata("strategy_model_id", &order.strategy_model_id)
            .add_metadata("identifier", &order.identifier)
            .add_metadata("isin", &order.isin)
            .add_metadata("exchange", &order.exchange)
            .add_metadata("scrip_name", &order.scrip_name)
            .add_metadata("investment_type", &order.investment_type.to_string())
            .add_metadata("transaction_type", &order.transaction_type.to_string())
            .add_metadata("transaction_sub_type", &order.transaction_sub_type.to_string())
            .add_metadata("quantity", &order.quantity.to_string())
            .add_metadata("current_holding", &order.current_holding.to_string())
            .add_metadata("currency", &currency)
            .add_metadata("currency_conversion_rate", &order.currency_conversion_rate.to_string())
            .add_metadata("pending_quantity", &order.pending_quantity.to_string())
            .add_metadata("transaction_quantity", &order.transaction_quantity.to_string())
            .add_metadata("price", &order.price.to_string())
            .add_metadata("order_type", &order.order_type.to_string())
            .add_metadata("order_date", &order.order_date.to_string())
            .add_metadata("settlement_date", &order.settlement_date.to_string())
            .add_metadata("order_status", &order.order_status.to_string())
            .add_metadata("transaction_amount", &order.transaction_amount.to_string())
            .add_metadata(
                "transaction_amount_required",
                &order.transaction_amount_required.to_string(),
            )
            .add_metadata("portfolio_available_cash", &order.portfolio_available_cash.to_string())
            .add_metadata("source_type", &order.source_type.to_string())
            .add_metadata("source_reference", &order.source_reference)
            .add_metadata("mf_folio_number", &mf_folio_number)
            .add_metadata("mf_buy_sell_type", &mf_buy_sell_type)
            .add_metadata("mf_all_units_redemption_flag", &mf_all_units_redemption_flag)
            .add_metadata("mf_client_bank", &mf_client_bank)
            .add_metadata("mf_oms_client_code", &mf_oms_client_code)
            .add_metadata("euin_number", &euin_number)
            .add_metadata("client_domicile", &order.client_domicile.to_string())
            .add_metadata("client_trading_account", &order.client_trading_account)
            .add_metadata("remarks", &order.remarks)
            .add_metadata("order_rationale", &order.order_rationale)
            .add_metadata("portfolio_id", &order.portfolio_id)
            .add_metadata("created_by", &order.created_by)
            .add_metadata("series", &series),
    )
}
