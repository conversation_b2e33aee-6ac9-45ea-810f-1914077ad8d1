use actlogica_logs::{builder::Log<PERSON>uilder, log_error};
use alpha_utils::constants::RedisKey;
use anyhow::Context;
use csv::Writer;
use redis::AsyncCommands;

use crate::types::deviation::ModelDeviationReport;

pub mod deviation;
pub mod deviation_compute_order;

pub mod validate_deviation_orders;

pub struct Deviation {
    deviation_id: String,
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
}

impl Deviation {
    pub fn build(
        deviation_id: String,
        redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    ) -> Self {
        Self {
            deviation_id,
            redis_pool,
        }
    }
    pub async fn get_all_reports(&self) -> anyhow::Result<Vec<ModelDeviationReport>> {
        let mut redis_conn = self.redis_pool.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationReport.to_string(),
            self.deviation_id.clone()
        );

        let reports_string: Vec<String> = redis_conn.zrange(key.clone(), 0, -1).await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: get report - {}", err)));
            anyhow::anyhow!("Failed to get report, please try again")
        })?;

        let mut reports: Vec<ModelDeviationReport> = Vec::new();

        for report in reports_string {
            let report = serde_json::from_str::<ModelDeviationReport>(&report).map_err(|_err| {
                log_error(LogBuilder::system("Failed: deserialize report"));
                anyhow::anyhow!("Report deserialisation failed")
            })?;
            reports.push(report);
        }

        return Ok(reports);
    }

    pub async fn get_reports_in_csv(&self) -> anyhow::Result<Vec<u8>> {
        let reports = self.get_all_reports().await?;

        let mut wtr = Writer::from_writer(vec![]);

        for report in reports {
            wtr.serialize(report).map(|_err| {
                log_error(LogBuilder::system("Failed: contruct report CSV"));
                anyhow::anyhow!("Failed to contruct CSV")
            })?;
        }

        let buff = wtr.into_inner().context("Failed to construct CSV")?;

        Ok(buff)
    }
}
