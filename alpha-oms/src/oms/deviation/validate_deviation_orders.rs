use actlogica_logs::{builder::LogBuilder, log_info};
use alpha_utils::group_by_multiple_fields;
use serde::{Deserialize, Serialize};

use crate::types::CustomisedOrder;

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InvalidDeviationPortfolioOrders {
    client_id: String,
    portfolio_id: String,
    trade_amount: f64,
    portfolio_cash: f64,
    client_name: String,
    portfolio_name: String,
    client_strategy_code: String,
}

pub fn validate_deviation_orders(orders: Vec<CustomisedOrder>) -> Vec<InvalidDeviationPortfolioOrders> {
    log_info(LogBuilder::system("Validating Deviation Orders"));
    let orders_by_portfolio =
        group_by_multiple_fields(orders, |order| (order.client_id.clone(), order.portfolio_id.clone()));
    let mut invalid_orders = Vec::new();

    for ((client_id, portfolio_id), orders) in orders_by_portfolio {
        let cash = orders[0].portfolio_cash.clone();
        let portfolio_name = orders[0].name.clone();
        let client_name = orders[0].client_name.clone();
        let client_strategy_code = orders[0].client_strategy_code.clone();

        let trade_amount: f64 = orders
            .iter()
            .map(|f| {
                if f.is_buy {
                    return f.buy_price * f.buy_quantity;
                }
                0f64
            })
            .sum();

        //If trade amount is zero skip it
        if trade_amount == 0f64 {
            continue;
        }

        if trade_amount > cash {
            invalid_orders.push(InvalidDeviationPortfolioOrders {
                client_name,
                client_id,
                portfolio_id,
                trade_amount,
                portfolio_cash: cash,
                portfolio_name,
                client_strategy_code,
            });
        }
    }

    invalid_orders
}
