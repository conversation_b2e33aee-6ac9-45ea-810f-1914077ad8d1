use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc,
};

use actlogica_logs::{builder::Log<PERSON>uilder, log_error, log_info, log_warn};
use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::client_order_entry::{ClientOrderEntry, SecurityType},
};
use alpha_utils::{
    group_by_multiple_fields,
    types::{SecurityDetails, SecurityTypeForPrice},
};
use tracing::{Instrument, Span};

use crate::{
    oms::error::OmsError,
    types::{
        deviation::{DeviationAction, ModelDeviationReport},
        ClientComputedOrders, CustomisedOrder,
    },
};
use futures::future::join_all;
use tokio::sync::Mutex;

pub struct DeviationOrderComputer {
    pub deviation_report: ModelDeviationReport,
    pub security: SecurityDetails,
}

impl DeviationOrderComputer {
    pub fn calculate_quantity(&self) -> f64 {
        let mut qty = ((self.deviation_report.deviation_pct.abs() / 100.0) * (self.deviation_report.portfolio_mv)
            / self.security.get_latest_price())
        .round();

        if self.deviation_report.action == DeviationAction::Sell {
            //Validate Quantity
            qty = if qty > self.deviation_report.portfolio_holding {
                if self.deviation_report.is_mutual_fund {
                    self.deviation_report.portfolio_holding
                } else {
                    self.deviation_report.portfolio_holding.round()
                }
            } else {
                qty
            };
        }

        qty
    }

    pub fn validate_order(&self, qty: f64) -> bool {
        if qty == 0f64 {
            return false;
        }

        if self.deviation_report.action == DeviationAction::Buy {
            let trade_amount = qty * self.security.get_latest_price();
            if trade_amount > self.deviation_report.portfolio_cash {
                return false;
            }
        }

        true
    }

    pub fn create_customised_order(&self, qty: f64, id: u64) -> CustomisedOrder {
        let mut order = CustomisedOrder {
            id,
            client_id: self.deviation_report.client_id.clone(),
            client_name: self.deviation_report.client_name.clone(),
            client_strategy_code: self.deviation_report.client_strategy_code.clone(),
            isin: self.deviation_report.isin.clone(),
            symbol: self.security.get_symbol().clone(),
            name: self.deviation_report.security_name.clone(),
            exchange: self.deviation_report.exchange.clone(),
            portfolio_id: self.deviation_report.portfolio_id.clone(),
            portfolio_cash: self.deviation_report.portfolio_cash,
            is_buy: self.deviation_report.action == DeviationAction::Buy,
            is_sell: self.deviation_report.action == DeviationAction::Sell,
            current_holding: self.deviation_report.portfolio_holding,
            buy_quantity: qty,
            buy_price: self.security.get_latest_price(),
            sell_quantity: qty,
            sell_price: self.security.get_latest_price(),
            trade_amount: ((qty * self.security.get_latest_price()) * 100f64).round() / 100f64,
            current_price: self.security.get_latest_price(),
            weight: self.deviation_report.portfolio_weight,
            new_weight: ((self.deviation_report.portfolio_holding + qty) * self.security.get_latest_price()
                / self.deviation_report.portfolio_mv)
                .round(),
            industry: self.security.get_industry(),
            rationale: Some(format!("Deviation Trade")),
            is_mf: false,
            identifier: self.security.get_identifier(),
            scrip_name: self.security.get_company_name(),
            security_type: self.security.get_security_type(),
            series: self.security.get_series(),
        };

        if let SecurityDetails::MutualFund(_m) = &self.security {
            order.is_mf = true;
        }
        order
    }
}

/// This function create Customised Order from Deviation Reports
pub async fn prepare_customised_orders_for_deviation(
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    master_pool: Pool<Manager>,
    deviation_reports: Vec<ModelDeviationReport>,
) -> Result<Vec<CustomisedOrder>, OmsError> {
    let computed_orders = Arc::new(Mutex::new(Vec::new()));
    let mut handlers = Vec::new();
    let seq = Arc::new(AtomicU64::new(0));

    let deviation_reports_by_isin =
        group_by_multiple_fields(deviation_reports, |d| (d.isin.clone(), d.exchange.clone()));

    for ((isin, exchange), reports) in deviation_reports_by_isin {
        log_info(
            LogBuilder::system(&format!("Computing Orders for Deviation Reports, for ISIN: {}", isin))
                .add_metadata("isin", &isin)
                .add_metadata("exchange", &exchange)
                .add_metadata("reports_count", &reports.len().to_string()),
        );
        let master_pool = master_pool.clone();
        let computed_orders = computed_orders.clone();
        let redis_pool = redis_pool.clone();
        let seq = seq.clone();

        let is_mutual_fund = reports[0].is_mutual_fund; //WE are sure atleast one element exist
        let handler: tokio::task::JoinHandle<Result<(), OmsError>> = tokio::spawn(
            async move {
                let mut redis_conn = redis_pool.get().await.map_err(|e| {
                    log_error(LogBuilder::system(&format!("Failed to get Redis connection: {}", e)));
                    OmsError::DatabaseCallFail
                })?;

                let security = SecurityDetails::build(
                    redis_conn.as_mut(),
                    master_pool.clone(),
                    isin.clone(),
                    if is_mutual_fund {
                        SecurityTypeForPrice::MutualFund
                    } else {
                        SecurityTypeForPrice::Equity
                    },
                    &exchange,
                )
                .await
                .map_err(|_e| {
                    log_error(LogBuilder::system(
                        "Failed : fetch Security Details. Security Not Found",
                    ));
                    OmsError::SecurityNotFound(SecurityType::MutualFund)
                })?;

                for report in reports {
                    match security {
                        SecurityDetails::MutualFund(ref mf) => {
                            let latest_price = mf.price;

                            if latest_price == 0f64 {
                                log_warn(LogBuilder::system(&format!("Price is Zero for Isin {}", mf.isin)));
                                return Err(OmsError::PriceFetchFailed(SecurityType::MutualFund));
                            }

                            let computer = DeviationOrderComputer {
                                deviation_report: report.clone(),
                                security: SecurityDetails::MutualFund(mf.clone()),
                            };

                            let qty = computer.calculate_quantity();

                            if qty == 0f64 {
                                log_warn(LogBuilder::system(
                                    "Quantity Is Zero So we are not adding in computed orders",
                                ));
                                continue;
                            }
                            // if !computer.validate_order(qty) {
                            //     return Ok(());
                            // }
                            let id = seq.fetch_add(1, Ordering::SeqCst);
                            let order = computer.create_customised_order(qty, id);

                            computed_orders.lock().await.push(order);
                        }
                        SecurityDetails::ETF(ref stock) | SecurityDetails::Stocks(ref stock) => {
                            let latest_price = stock.get_latest_price_by_exchange(&report.exchange);

                            if latest_price == 0f64 {
                                log_warn(LogBuilder::system(&format!("Price is Zero for Isin {}", stock.isin)));
                                return Err(OmsError::PriceFetchFailed(SecurityType::Stocks));
                            }

                            let computer = DeviationOrderComputer {
                                deviation_report: report.clone(),
                                security: SecurityDetails::Stocks(stock.clone()),
                            };

                            let qty = computer.calculate_quantity();
                            if qty == 0f64 {
                                // info!("Quantity Is Zero So we are not adding in computed orders");
                                continue;
                            }
                            // if !computer.validate_order(qty) {
                            //     return Ok(());
                            // }
                            let id = seq.fetch_add(1, Ordering::SeqCst);
                            let order = computer.create_customised_order(qty, id);

                            computed_orders.lock().await.push(order);
                        }
                    }
                }
                Ok(())
            }
            .instrument(Span::current()),
        );

        handlers.push(handler);
    }

    join_all(handlers).await;

    // Lock the mutex
    let guard = computed_orders.lock().await;

    let computed_orders = (*guard).clone();

    Ok(computed_orders)
}

pub async fn save_deviation_orders(
    txn_conn: &mut Object<Manager>,
    client_orders: Vec<ClientComputedOrders>,
    created_by: String,
) -> Result<(), String> {
    log_info(
        LogBuilder::system(&format!(
            "Starting Transaction for Saving Deviation Orders. Created By: {}",
            created_by
        ))
        .add_metadata("created_by", &created_by),
    );
    txn_conn.simple_query("BEGIN TRANSACTION").await.unwrap();

    //Convert to ClientOrderEntry
    for co in client_orders {
        let coe = ClientOrderEntry {
            client_code: co.client_code,
            client_domicile: co.client_domicile,
            client_id: co.client_id,
            client_trading_account: co.client_trading_account,
            created_by: Some(created_by.clone()),
            currency: co.currency,
            currency_conversion_rate: Some(co.currency_conversion_rate),
            euin_number: co.euin_number,
            exchange: co.exchange,
            identifier: co.identifier,
            investment_type: co.investment_type,
            isin: co.isin,
            mf_all_units_redemption_flag: co.mf_all_units_redemption_flag,
            mf_buy_sell_type: co.mf_buy_sell_type,
            mf_client_bank: co.mf_client_bank,
            mf_folio_number: co.mf_folio_number,
            order_date: co.order_date,
            mf_oms_client_code: co.mf_oms_client_code,
            order_rationale: co.order_rationale,
            order_status: co.order_status,
            order_type: co.order_type,
            pending_quantity: co.pending_quantity,
            portfolio_id: co.portfolio_id,
            price: co.price,
            quantity: co.quantity,
            remarks: co.remarks,
            scrip_name: co.scrip_name,
            series: co.series,
            source_reference: co.source_reference,
            source_type: co.source_type.to_string(),
            strategy_code: co.strategy_code,
            strategy_custody_code: co.strategy_custody_code,
            strategy_model_id: co.strategy_model_id,
            strategy_model_name: co.strategy_model_name,
            strategy_name: co.strategy_name,
            strategy_trading_account_number: co.strategy_trading_account_number,
            transaction_amount: co.transaction_amount,
            transaction_sub_type: co.transaction_sub_type,
            transaction_type: co.transaction_type,
            ..Default::default()
        };

        let insert_coe = coe.insert(txn_conn).await;
        if insert_coe.is_err() {
            txn_conn.simple_query("ROLLBACK").await.unwrap();
            return Err(String::from("Failed to Save Orders"));
        }
    }

    txn_conn.simple_query("COMMIT").await.unwrap();

    Ok(())
}
