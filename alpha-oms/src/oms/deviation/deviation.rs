use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
};

use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    connection::pool::{deadpool::managed::Pool, Manager},
    schema::{
        investment::Investments,
        model_portfolio::ModelPortfolio,
        oms::deviation::{PortfolioDetailsForDeviations, PortfolioDetailsForDeviationsForSingleSecurity},
        portfolio::AccountStatus,
    },
};
use bb8_redis::RedisConnectionManager;
use futures::future::join_all;
use tokio::{sync::Mutex, task::<PERSON><PERSON><PERSON><PERSON><PERSON>};
use tracing::{Instrument, Span};

use crate::{
    oms::error::OmsError,
    types::deviation::{DeviationAction, DeviationCalculatedResult, ModelDeviationReport},
};

pub async fn compute_deviation_for_specific_clients_for_all_securites(
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
    client_strategy_codes: &Vec<String>,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;
    let portfolios_in_model = PortfolioDetailsForDeviations::get_by_csc(&mut conn, &client_strategy_codes, &model_id)
        .await
        .map_err(|_| {
            log_error(
                LogBuilder::system("Failed: get portfolio details by client strategy codes for deviations")
                    .add_metadata("model_id", &model_id),
            );
            OmsError::DatabaseCallFail
        })?;
    drop(conn);

    let reports =
        build_deviation_report_for_all_securities(redis_pool.clone(), pool, model_id, portfolios_in_model).await?;

    Ok(reports)
}

pub async fn compute_deviation_for_specific_clients_for_single_security(
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
    client_strategy_codes: &Vec<String>,
    isin: String,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;

    let portfolios_in_model = PortfolioDetailsForDeviations::get_by_csc_for_single_security(
        &mut conn,
        &client_strategy_codes,
        &model_id,
        &isin,
    )
    .await
    .map_err(|_e| {
        log_error(
            LogBuilder::system("Failed to get portfolio details by client strategy codes for single security")
                .add_metadata("model_id", &model_id)
                .add_metadata("isin", &isin),
        );
        OmsError::DatabaseCallFail
    })?;

    drop(conn);

    build_deviation_report_for_a_securities(redis_pool, pool, model_id, isin, portfolios_in_model).await
}

pub async fn compute_deviation_for_all_clients_for_all_securities(
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;

    let portfolios_in_model = PortfolioDetailsForDeviations::get(&mut conn, &model_id)
        .await
        .map_err(|_e| {
            log_error(
                LogBuilder::system("Failed to get portfolio details for deviations for all clients all securities")
                    .add_metadata("model_id", &model_id),
            );
            OmsError::DatabaseCallFail
        })?;

    drop(conn);
    let reports =
        build_deviation_report_for_all_securities(redis_pool.clone(), pool, model_id, portfolios_in_model).await?;

    Ok(reports)
}

pub async fn compute_deviation_for_all_clients_for_single_security(
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
    isin: String,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;
    let portfolios_in_model = PortfolioDetailsForDeviations::get_for_single_security(&mut conn, &model_id, &isin)
        .await
        .map_err(|_e| {
            log_error(
                LogBuilder::system("Failed to get portfolio details for deviations for all clients single security")
                    .add_metadata("model_id", &model_id)
                    .add_metadata("isin", &isin),
            );
            OmsError::DatabaseCallFail
        })?;

    drop(conn);

    build_deviation_report_for_a_securities(redis_pool, pool, model_id, isin, portfolios_in_model).await
}

/// Build the Deviation for All Securities in the Model
async fn build_deviation_report_for_all_securities(
    _redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
    portfolios_in_model: Vec<PortfolioDetailsForDeviations>,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;

    //TODO: Combine Both the Under two function into single sql query
    let model_portfolio = ModelPortfolio::get_by_model_id(&mut conn, &model_id)
        .await
        .map_err(|_e| {
            log_error(
                LogBuilder::system("Failed to get model portfolio by module Id").add_metadata("model_id", &model_id),
            );
            return OmsError::DatabaseCallFail;
        })?
        .ok_or_else(|| {
            return OmsError::EntityNotFound(String::from("Model Portfolio Not Found"));
        })?;

    let investments_in_model = Investments::get_by_model_portfolio_id_for_deviation(&mut conn, model_portfolio.id)
        .await
        .map_err(|_e| {
            log_error(LogBuilder::system(
                "Failed to get investments by model portfolio id for deviation",
            ));
            return OmsError::DatabaseCallFail;
        })?;

    let investments_in_model_grouped = investments_in_model.into_iter().fold(HashMap::new(), |mut acc, txn| {
        acc.entry(txn.isin.clone()).or_insert(txn);
        acc
    });

    let investments_in_model_grouped = Arc::new(investments_in_model_grouped);

    let deviation_reports = Arc::new(Mutex::new(Vec::new()));

    let mut handlers = Vec::new();

    let seq = Arc::new(AtomicU64::new(0));
    // Loop through all the Holdings in Portfolio
    // And Compare with model holdings
    for portfolio in portfolios_in_model {
        let investments_in_portfolio = portfolio.investments;

        let pos = portfolio.cash_balance;
        let deviation_reports = deviation_reports.clone();
        let investments_in_model_grouped = investments_in_model_grouped.clone();
        let seq = seq.clone();

        let handler: JoinHandle<Result<(), OmsError>> = tokio::spawn(
            async move {
                log_info(LogBuilder::system(&format!(
                    "Running Deviation for {}",
                    portfolio.client_strategy_code
                )));
                let portfolio_market_value = portfolio.current_cash_balance + portfolio.market_value;
                if portfolio_market_value == 0f64 {
                    log_warn(LogBuilder::system("Market Value is Zero Skipping"));
                    return Ok(());
                }

                let mut isin_in_portfolio = HashMap::new();

                let mut isin_in_portfolio_not_in_model = HashMap::new();

                //Compare the weight of each isin in investment in model
                for investment in investments_in_portfolio {
                    let portfolio_isin_weight =
                        (((investment.market_value / portfolio_market_value) * 100f64) * 100f64).round() / 100f64;

                    let model_isin = investments_in_model_grouped.get(&investment.isin);
                    let mut model_isin_weight = 0f64;

                    if let Some(model_isin) = model_isin {
                        if model_portfolio.market_value + model_portfolio.current_cash_balance > 0f64 {
                            model_isin_weight = (((model_isin.market_value
                                / (model_portfolio.market_value + model_portfolio.current_cash_balance))
                                * 100f64)
                                * 100f64)
                                .round()
                                / 100f64
                        }

                        isin_in_portfolio.insert(investment.isin.clone(), true);
                    } else {
                        //Security is not in Model
                        //That means sell the security
                        isin_in_portfolio_not_in_model.insert(investment.isin.clone(), investment.clone());
                    }

                    let deviation_cal = DeviationCalculatedResult::calculate_deviation_for_isin(
                        investment.isin,
                        investment.symbol,
                        investment.name,
                        investment.security_type,
                        investment.exchange,
                        model_isin_weight,
                        portfolio_isin_weight,
                        portfolio_market_value,
                        investment.current_holding,
                    );

                    if deviation_cal.action == DeviationAction::Sell {
                        if portfolio.account_status != AccountStatus::Active
                            && portfolio.account_status != AccountStatus::Exiting
                        {
                            continue;
                        }
                    } else {
                        if portfolio.account_status != AccountStatus::Active {
                            continue;
                        }
                    }

                    if deviation_cal.deviation == 0f64 {
                        continue;
                    }

                    let id = seq.fetch_add(1, Ordering::SeqCst);

                    let deviation_report = ModelDeviationReport {
                        id,
                        action: deviation_cal.action,
                        client_id: portfolio.client_id.clone(),
                        client_name: portfolio.client_name.clone(),
                        client_strategy_code: portfolio.client_strategy_code.clone(),
                        deviation_pct: deviation_cal.deviation,
                        exchange: deviation_cal.exchange,
                        is_mutual_fund: deviation_cal.is_mutual_fund,
                        isin: deviation_cal.isin,
                        model_weight: deviation_cal.model_weight,
                        portfolio_cash: pos.available_cash_balance,
                        portfolio_holding: deviation_cal.current_holding,
                        portfolio_id: portfolio.portfolio_id.clone(),
                        portfolio_mv: portfolio_market_value,
                        portfolio_weight: deviation_cal.portfolio_weight,
                        security_name: deviation_cal.security_name,
                        symbol: deviation_cal.symbol,
                        selected: false,
                    };

                    let mut deviation_reports_guard = deviation_reports.lock().await;

                    deviation_reports_guard.push(deviation_report);
                }

                //Loop through investments that exist in model but not in portfolio
                for (isin, investment) in investments_in_model_grouped.as_ref() {
                    //Check if the isin is already in portfolio
                    if isin_in_portfolio.contains_key(isin) {
                        continue;
                    }

                    let mut model_isin_weight = 0f64;
                    if model_portfolio.market_value + model_portfolio.current_cash_balance > 0f64 {
                        model_isin_weight = (((investment.market_value
                            / (model_portfolio.market_value + model_portfolio.current_cash_balance))
                            * 100f64)
                            * 100f64)
                            .round()
                            / 100f64
                    }

                    let deviation_cal = DeviationCalculatedResult::calculate_deviation_for_isin(
                        investment.isin.clone(),
                        investment.symbol.clone(),
                        investment.name.clone(),
                        investment.security_type.clone(),
                        investment.exchange.clone(),
                        model_isin_weight,
                        0f64,
                        portfolio_market_value,
                        0f64,
                    );

                    if deviation_cal.action == DeviationAction::Sell {
                        if portfolio.account_status != AccountStatus::Active
                            && portfolio.account_status != AccountStatus::Exiting
                        {
                            continue;
                        }
                    } else {
                        if portfolio.account_status != AccountStatus::Active {
                            continue;
                        }
                    }

                    let id = seq.fetch_add(1, Ordering::SeqCst);
                    let deviation_report = ModelDeviationReport {
                        id,
                        action: deviation_cal.action,
                        client_id: portfolio.client_id.clone(),
                        client_name: portfolio.client_name.clone(),
                        client_strategy_code: portfolio.client_strategy_code.clone(),
                        deviation_pct: deviation_cal.deviation,
                        exchange: deviation_cal.exchange,
                        is_mutual_fund: deviation_cal.is_mutual_fund,
                        isin: deviation_cal.isin,
                        model_weight: deviation_cal.model_weight,
                        portfolio_cash: pos.available_cash_balance,
                        portfolio_holding: deviation_cal.current_holding,
                        portfolio_id: portfolio.portfolio_id.clone(),
                        portfolio_mv: portfolio_market_value,
                        portfolio_weight: deviation_cal.portfolio_weight,
                        security_name: deviation_cal.security_name,
                        symbol: deviation_cal.symbol,
                        selected: false,
                    };

                    let mut deviation_reports_guard = deviation_reports.lock().await;

                    deviation_reports_guard.push(deviation_report);
                }

                // println!("Finished for {}", portfolio.client_strategy_code);
                Ok(())
            }
            .instrument(Span::current()),
        );

        handlers.push(handler);
    }

    join_all(handlers).await;

    // Lock the mutex
    let guard = deviation_reports.lock().await;

    let dev_reports = (*guard).clone();

    Ok(dev_reports)
}

/// It Builds Deviation Report A single Security for the provided portfolios
async fn build_deviation_report_for_a_securities(
    _redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pool: Pool<Manager>,
    model_id: String,
    isin: String,
    portfolios_in_model: Vec<PortfolioDetailsForDeviationsForSingleSecurity>,
) -> Result<Vec<ModelDeviationReport>, OmsError> {
    let mut conn = pool.get().await.map_err(|_| {
        log_error(LogBuilder::system("Failed to get database connection"));
        OmsError::DatabaseCallFail
    })?;
    let model_portfolio = ModelPortfolio::get_by_model_id(&mut conn, &model_id)
        .await
        .map_err(|_e| {
            return OmsError::DatabaseCallFail;
        })?
        .ok_or_else(|| OmsError::EntityNotFound(format!("Model Portfolio Not Found for the ModelId {}", model_id)))?;

    let isin_investment_in_model = Arc::new(
        Investments::get_by_isin_and_model_portfolio_id(&mut conn, isin.clone(), model_portfolio.id.clone())
            .await
            .map_err(|_e| {
                return OmsError::DatabaseCallFail;
            })?
            .ok_or_else(|| OmsError::EntityNotFound(format!("Isin {} Is not in the Model {}", isin, model_id)))?,
    );

    let mut model_isin_weight = 0f64;

    if model_portfolio.market_value + model_portfolio.current_cash_balance > 0f64 {
        model_isin_weight = (((isin_investment_in_model.market_value
            / (model_portfolio.market_value + model_portfolio.current_cash_balance))
            * 100f64)
            * 100f64)
            .round()
            / 100f64
    }

    let deviation_reports = Arc::new(Mutex::new(Vec::new()));
    let mut handlers = Vec::new();

    drop(conn);

    let _unique_deviation_id = uuid::Uuid::new_v4();
    let seq = Arc::new(AtomicU64::new(0));

    for portfolio in portfolios_in_model {
        let isin_investment_in_model = isin_investment_in_model.clone();
        let deviation_reports = deviation_reports.clone();
        let seq = seq.clone();
        let handler: JoinHandle<Result<(), OmsError>> = tokio::spawn(
            async move {
                log_info(
                    LogBuilder::system("Deviation calculation")
                        .add_metadata("market_value", &portfolio.market_value.to_string())
                        .add_metadata("current_cash_balance", &portfolio.current_cash_balance.to_string()),
                );

                let isin_investment_in_portfolio = portfolio.investments;

                let pos = portfolio.cash_balance;

                let portfolio_market_value = portfolio.current_cash_balance + portfolio.market_value;

                if portfolio_market_value == 0f64 {
                    log_warn(LogBuilder::system("Market Value is Zero Skipping"));
                    return Ok(());
                }

                if let Some(investment) = isin_investment_in_portfolio {
                    let portfolio_isin_weight =
                        (((investment.market_value / portfolio_market_value) * 100f64) * 100f64).round() / 100f64;

                    let deviation_cal = DeviationCalculatedResult::calculate_deviation_for_isin(
                        investment.isin,
                        investment.symbol,
                        investment.name,
                        investment.security_type,
                        investment.exchange,
                        model_isin_weight,
                        portfolio_isin_weight,
                        portfolio_market_value,
                        investment.current_holding,
                    );

                    if deviation_cal.action == DeviationAction::Sell {
                        if portfolio.account_status != AccountStatus::Active
                            && portfolio.account_status != AccountStatus::Exiting
                        {
                            return Ok(());
                        }
                    } else {
                        if portfolio.account_status != AccountStatus::Active {
                            return Ok(());
                        }
                    }

                    if deviation_cal.deviation == 0f64 {
                        return Ok(());
                    }

                    let id = seq.fetch_add(1, Ordering::SeqCst);
                    let deviation_report = ModelDeviationReport {
                        id,
                        action: deviation_cal.action,
                        client_id: portfolio.client_id.clone(),
                        client_name: portfolio.client_name.clone(),
                        client_strategy_code: portfolio.client_strategy_code.clone(),
                        deviation_pct: deviation_cal.deviation,
                        exchange: deviation_cal.exchange,
                        is_mutual_fund: deviation_cal.is_mutual_fund,
                        isin: deviation_cal.isin,
                        model_weight: deviation_cal.model_weight,
                        portfolio_cash: pos.available_cash_balance,
                        portfolio_holding: deviation_cal.current_holding,
                        portfolio_id: portfolio.portfolio_id.clone(),
                        portfolio_mv: portfolio_market_value,
                        portfolio_weight: deviation_cal.portfolio_weight,
                        security_name: deviation_cal.security_name,
                        symbol: deviation_cal.symbol,
                        selected: false,
                    };
                    let mut deviation_reports_guard = deviation_reports.lock().await;

                    deviation_reports_guard.push(deviation_report);
                } else {
                    //Security is in model But Not in Portfolio
                    // So buy the security

                    let deviation_cal = DeviationCalculatedResult::calculate_deviation_for_isin(
                        isin_investment_in_model.isin.clone(),
                        isin_investment_in_model.symbol.clone(),
                        isin_investment_in_model.name.clone(),
                        isin_investment_in_model.security_type.clone(),
                        isin_investment_in_model.exchange.clone(),
                        model_isin_weight,
                        0f64,
                        portfolio_market_value,
                        0f64,
                    );

                    if deviation_cal.action == DeviationAction::Sell {
                        if portfolio.account_status != AccountStatus::Active
                            && portfolio.account_status != AccountStatus::Exiting
                        {
                            return Ok(());
                        }
                    } else {
                        if portfolio.account_status != AccountStatus::Active {
                            return Ok(());
                        }
                    }

                    let id = seq.fetch_add(1, Ordering::SeqCst);
                    let deviation_report = ModelDeviationReport {
                        id,
                        action: deviation_cal.action,
                        client_id: portfolio.client_id.clone(),
                        client_name: portfolio.client_name.clone(),
                        client_strategy_code: portfolio.client_strategy_code.clone(),
                        deviation_pct: deviation_cal.deviation,
                        exchange: deviation_cal.exchange,
                        is_mutual_fund: deviation_cal.is_mutual_fund,
                        isin: deviation_cal.isin,
                        model_weight: deviation_cal.model_weight,
                        portfolio_cash: pos.available_cash_balance,
                        portfolio_holding: deviation_cal.current_holding,
                        portfolio_id: portfolio.portfolio_id.clone(),
                        portfolio_mv: portfolio_market_value,
                        portfolio_weight: deviation_cal.portfolio_weight,
                        security_name: deviation_cal.security_name,
                        symbol: deviation_cal.symbol,
                        selected: false,
                    };

                    let mut deviation_reports_guard = deviation_reports.lock().await;

                    deviation_reports_guard.push(deviation_report);
                }

                Ok(())
            }
            .instrument(Span::current()),
        );

        handlers.push(handler);
    }

    join_all(handlers).await;

    // Lock the mutex
    let guard = deviation_reports.lock().await;

    let dev_reports: Vec<ModelDeviationReport> = (*guard).clone();

    Ok(dev_reports)
}

#[cfg(test)]
mod tests {
    use alpha_core_db::redis::connect_to_redis;
    use tracing_test::traced_test;

    use crate::oms::{
        deviation::deviation_compute_order::{prepare_customised_orders_for_deviation, save_deviation_orders},
        OrderComputer,
    };

    use super::*;

    #[tokio::test]
    #[traced_test]
    async fn test_deviation_report_generation() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let master_pool = alpha_core_db::connection::connect_to_master_data(10).await;
        let redis_pool = connect_to_redis().await;
        let portfolio_id = String::from("01be4bf2b9644cd9b6bd67e3c68e51b7");
        let model_id = String::from("9927dad8a31f43a591199732db5abb65");
        let capital = 43534f64;
        let isin = String::from("INE062A01020");
        let created_by = String::from("Athul");

        let deviation_reports =
            compute_deviation_for_all_clients_for_all_securities(redis_pool.clone(), pool.clone(), model_id)
                .await
                .unwrap();

        println!("LEN = {}", deviation_reports.len());
    }
}
