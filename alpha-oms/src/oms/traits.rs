use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::{
        client_order_entry::{SecurityType, TransactionType},
        investment::Investments,
        investment_transaction::InvestmentTransaction,
        oms::{ClientForOms, PortfolioForOms},
        portfolio::{AccountStatus, TradingMode},
        restricted_stocks::client_restricted_stocks::RestrictedStockForClients,
        security_master::{
            company_master::{CompanyMaster, EquityCompanyMaster},
            SchemeMasterDetails, SchemeMasterDetailsNew,
        },
        strategy::strategy_model_securities::StrategyModelSecuritiesForOms,
    },
    types::{compute_order::StrategyForOrderComputation, OrderSourceType},
};
use actlogica_logs::{builder::LogBuilder, log_error, log_warn};
use alpha_utils::types::{SecurityDetails, SecurityTypeForPrice};
use redis::aio::MultiplexedConnection;

use crate::types::{ClientComputedOrders, CustomisedOrder, CustomisedOrderRequest};

use super::error::OmsError;

/// All the Securities Type should implement this trait
/// Struct that Implements this can be used to construct any order for the OMS
pub trait ConstructOrder {
    fn construct_order(
        &self,
        redis_conn: &mut MultiplexedConnection,
        db_pool: Pool<Manager>,
        master_pool: Pool<Manager>,
        client: &ClientForOms,
        portfolio: &PortfolioForOms,
        strategy: &StrategyForOrderComputation,
        capital: f64,
        created_by: String,
        current_holding: f64,
        portfolio_available_cash: f64,
        dp_id: String,
        transaction_type: TransactionType,
        order_source: OrderSourceType,
        source_ref: String,
    ) -> impl std::future::Future<Output = Result<ClientComputedOrders, String>> + Send;

    fn get_mf_details(
        &self,
        conn: &mut Object<Manager>,
        client_id: String,
        isin: String,
        master_conn: &mut Object<Manager>,
    ) -> impl std::future::Future<Output = Result<SchemeMasterDetails, OmsError>> + Send {
        async {
            //Check For Restricted stocks
            let restricted_stocks = RestrictedStockForClients::get_by_client_id_and_isin(conn, client_id, isin.clone())
                .await
                .unwrap();

            if let Some(_res_stock) = restricted_stocks {
                let mf_details = SchemeMasterDetailsNew::get_mf_details_by_isin(master_conn, isin, true, true)
                    .await
                    .map_err(|_e| OmsError::DatabaseCallFail)?
                    .ok_or_else(|| OmsError::SecurityNotFound(SecurityType::MutualFund))?;

                return Ok(mf_details);
            }

            let mf_details = SchemeMasterDetailsNew::get_mf_details_by_isin(master_conn, isin, true, true)
                .await
                .map_err(|_e| OmsError::DatabaseCallFail)?
                .ok_or_else(|| OmsError::SecurityNotFound(SecurityType::MutualFund))?;

            Ok(mf_details)
        }
    }

    fn get_stock_details(
        &self,
        conn: &mut Object<Manager>,
        client_id: String,
        master_conn: &mut Object<Manager>,
        isin: String,
    ) -> impl std::future::Future<Output = Result<EquityCompanyMaster, OmsError>> + Send {
        async {
            //Check For Restricted stocks
            let restricted_stocks = RestrictedStockForClients::get_by_client_id_and_isin(conn, client_id, isin.clone())
                .await
                .unwrap();

            if let Some(res_stock) = restricted_stocks {
                if let Some(isin_alt) = res_stock.isin_alternative_security {
                    let res_details = CompanyMaster::get_by_isin(master_conn, isin_alt.clone())
                        .await
                        .map_err(|_e| {
                            return OmsError::DatabaseCallFail;
                        })?
                        .ok_or_else(|| return OmsError::SecurityNotFound(SecurityType::Stocks))?;

                    return Ok(res_details);
                }
            }

            let stock_details = CompanyMaster::get_by_isin(master_conn, isin)
                .await
                .map_err(|_e| {
                    return OmsError::DatabaseCallFail;
                })?
                .ok_or_else(|| return OmsError::SecurityNotFound(SecurityType::Stocks))?;

            Ok(stock_details)
        }
    }
}

/// Order Construction for Securities in Model
impl ConstructOrder for StrategyModelSecuritiesForOms {
    async fn construct_order(
        &self,
        redis_conn: &mut MultiplexedConnection,
        db_pool: Pool<Manager>,
        master_pool: Pool<Manager>,
        client: &ClientForOms,
        portfolio: &PortfolioForOms,
        strategy: &StrategyForOrderComputation,
        capital: f64,
        created_by: String,
        current_holding: f64,
        portfolio_available_cash: f64,
        dp_id: String,
        transaction_type: TransactionType,
        order_source: OrderSourceType,
        source_ref: String,
    ) -> Result<ClientComputedOrders, String> {
        // Get the Security details After Apply the Restriction
        let mut conn = db_pool.get().await.unwrap();
        let security = SecurityDetails::build_with_restricted_stocks(
            redis_conn,
            &mut conn,
            master_pool,
            self.isin.clone(),
            client.id.clone(),
            if self.is_mutual_fund {
                SecurityTypeForPrice::MutualFund
            } else {
                SecurityTypeForPrice::Equity
            },
            &self.exchange,
        )
        .await?;

        match security {
            SecurityDetails::MutualFund(mutual_fund_details) => {
                let weighted_investment_amount = (((capital * self.weight) / 100f64) * 100f64).round() / 100f64;

                if mutual_fund_details.price == 0f64 {
                    return Err(String::from("MF Price not found"));
                }

                let investment_quantity = (weighted_investment_amount / mutual_fund_details.price).floor();
                let actual_investment_amount =
                    ((investment_quantity * mutual_fund_details.price) * 100f64).round() / 100f64;

                let mf_folio = InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mutual_fund_details.isin)
                    .await?
                    .unwrap_or_else(|| String::from("NEW"));

                let mf_buy_sell_type = if mf_folio == "NEW" {
                    "FRESH".to_string()
                } else {
                    if transaction_type == TransactionType::Buy {
                        "ADDITIONAL".to_string()
                    } else {
                        "REDEEM".to_string()
                    }
                };

                let mut computed_order_entry = ClientComputedOrders::build_for_mutual_fund(
                    dp_id,
                    &client,
                    &portfolio,
                    &mutual_fund_details,
                    mutual_fund_details.isin.clone(),
                    self.exchange.clone(),
                    investment_quantity,
                    actual_investment_amount,
                    created_by,
                    strategy,
                    transaction_type,
                    current_holding,
                    portfolio_available_cash,
                    order_source,
                    source_ref
                );

                computed_order_entry.mf_folio_number = Some(mf_folio);
                computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                computed_order_entry.update_remarks_rationale(
                    String::from("Auto Remark: AdditionalCapitalInPortfolio"),
                    String::from("Additional Capital deployment into an existing portfolio for client."),
                );

                if mutual_fund_details.isin != self.isin {
                    computed_order_entry.remarks.push_str(" RestrictedStock");
                }

                //If Portfolio Is Individual Update portfolio broker Trading acc number
                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(broker) = &portfolio.broker_trading_acc_number {
                        computed_order_entry.client_trading_account = broker.clone()
                    } else {
                        return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                    }
                }

                return Ok(computed_order_entry);
            }
            SecurityDetails::Stocks(stock_details) | SecurityDetails::ETF(stock_details) => {
                let price = stock_details.get_latest_price_by_exchange(&self.exchange);

                if price == 0f64 {
                    return Err(format!(
                        "Failed Price Of Stock {} Is zero For Exchange {} ",
                        stock_details.isin, self.exchange
                    ));
                }

                let weighted_investment_amount = (((capital * self.weight) / 100f64) * 100f64).round() / 100f64;

                let investment_quantity = (weighted_investment_amount / price).floor();
                let actual_investment_amount = ((investment_quantity * price) * 100f64).round() / 100f64;

                let mut computed_order_entry = ClientComputedOrders::build(
                    dp_id,
                    &client,
                    &portfolio,
                    &stock_details,
                    stock_details.isin.clone(),
                    self.exchange.clone(),
                    investment_quantity,
                    actual_investment_amount,
                    created_by.clone(),
                    &strategy,
                    transaction_type,
                    current_holding,
                    portfolio_available_cash,
                    order_source,
                    source_ref
                );

                computed_order_entry.update_remarks_rationale(
                    String::from("Auto Remark: AdditionalCapitalInPortfolio"),
                    String::from("Additional Capital deployment into an existing portfolio for client."),
                );

                if stock_details.isin != self.isin {
                    computed_order_entry.remarks.push_str(" RestrictedStock");
                }

                //If Portfolio Is Individual Update portfolio broker Trading acc number
                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(broker) = &portfolio.broker_trading_acc_number {
                        computed_order_entry.client_trading_account = broker.clone()
                    } else {
                        return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                    }
                }

                return Ok(computed_order_entry);
            }
        }
    }
}

/// Order Construction for Securities in Portfolios
impl ConstructOrder for Investments {
    async fn construct_order(
        &self,
        redis_conn: &mut MultiplexedConnection,
        db_pool: Pool<Manager>,
        master_pool: Pool<Manager>,
        client: &ClientForOms,
        portfolio: &PortfolioForOms,
        strategy: &StrategyForOrderComputation,
        capital: f64,
        created_by: String,
        _current_holding: f64,
        portfolio_available_cash: f64,
        dp_id: String,
        transaction_type: TransactionType,
        order_source: OrderSourceType,
        source_ref: String,
    ) -> Result<ClientComputedOrders, String> {
        let mut conn = db_pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get database connection while constructing orders: {}", e)));
            String::from("Failed to get database connection")
        })?;

        let security = SecurityDetails::build_with_restricted_stocks(
            redis_conn,
            &mut conn,
            master_pool,
            self.isin.clone(),
            client.id.clone(),
            if self.security_type == SecurityType::MutualFund {
                SecurityTypeForPrice::MutualFund
            } else {
                SecurityTypeForPrice::Equity
            },
            &self.exchange,
        )
        .await?;

        //Dont allow restricted stock on withdrawal
        if security.get_isin() != self.isin {
            return Err(format!("Isin {} is Restricted", self.isin));
        }

        match security {
            SecurityDetails::MutualFund(mutual_fund_details) => {
                if mutual_fund_details.price == 0f64 {
                    log_error(LogBuilder::system("Mutual Fund price Not Found"));
                    return Err(String::from("Mutual Fund price Not Found"));
                }

                let weighted_investment_amount = (((capital * self.weight) / 100f64) * 100f64).round() / 100f64;

                let mut investment_quantity = (weighted_investment_amount / mutual_fund_details.price).floor();

                investment_quantity = if investment_quantity > self.current_holding {
                    self.current_holding
                } else {
                    investment_quantity
                };

                //This means Take full Quantity
                if capital == 0f64 {
                    investment_quantity = self.current_holding;
                }

                let actual_investment_amount =
                    ((investment_quantity * mutual_fund_details.price) * 100f64).round() / 100f64;

                let mut conn = db_pool.get().await.map_err(|e| {
                    log_error(LogBuilder::system("Failed to get database connection").add_metadata("error", &e.to_string()));
                    String::from("Failed to get database connection")
                })?;

                let mf_folio = InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mutual_fund_details.isin)
                    .await?
                    .unwrap_or_else(|| String::from("NEW"));

                let mf_buy_sell_type = if mf_folio == "NEW" {
                    "FRESH".to_string()
                } else {
                    if transaction_type == TransactionType::Buy {
                        "ADDITIONAL".to_string()
                    } else {
                        "REDEEM".to_string()
                    }
                };

                let mut computed_order_entry = ClientComputedOrders::build_for_mutual_fund(
                    dp_id,
                    &client,
                    &portfolio,
                    &mutual_fund_details,
                    mutual_fund_details.isin.clone(),
                    self.exchange.clone(),
                    investment_quantity,
                    actual_investment_amount,
                    created_by,
                    strategy,
                    transaction_type,
                    self.current_holding,
                    portfolio_available_cash,
                    order_source,
                    source_ref
                );

                computed_order_entry.mf_folio_number = Some(mf_folio);
                computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                computed_order_entry.update_remarks_rationale(
                    String::from("Auto Remark: CapitalWithdrawalInPortfolio"),
                    String::from("Capital Withdrawal from Portfolio."),
                );

                if mutual_fund_details.isin != self.isin {
                    computed_order_entry.remarks.push_str(" RestrictedStock");
                }

                //If Portfolio Is Individual Update portfolio broker Trading acc number
                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(broker) = &portfolio.broker_trading_acc_number {
                        computed_order_entry.client_trading_account = broker.clone()
                    } else {
                        log_error(LogBuilder::system(&format!("Broker: {} Not Found for the Portfolio.", portfolio.id.clone())));
                        return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                    }
                }

                Ok(computed_order_entry)
            }
            SecurityDetails::Stocks(stock_details) | SecurityDetails::ETF(stock_details) => {
                let latest_price = stock_details.get_latest_price_by_exchange(&self.exchange);

                if latest_price == 0f64 {
                    log_warn(LogBuilder::system(&format!("Stock Price Not Found for the ISIN = {:?}", stock_details.isin)));
                    return Err(format!(
                        "Stock Price Not Found For ISIN = {:?} Exchange ={:?}",
                        stock_details.isin, self.exchange
                    ));
                }

                let weighted_investment_amount = (((capital * self.weight) / 100f64) * 100f64).round() / 100f64;

                let mut investment_quantity = (weighted_investment_amount / latest_price).floor();

                investment_quantity = if investment_quantity > self.current_holding {
                    self.current_holding
                } else {
                    investment_quantity
                };

                //This means Take full Quantity
                if capital == 0f64 {
                    investment_quantity = self.current_holding;
                }

                let actual_investment_amount = ((investment_quantity * latest_price) * 100f64).round() / 100f64;

                let mut computed_order_entry = ClientComputedOrders::build(
                    dp_id,
                    &client,
                    &portfolio,
                    &stock_details,
                    stock_details.isin.clone(),
                    self.exchange.clone(),
                    investment_quantity,
                    actual_investment_amount,
                    created_by.clone(),
                    &strategy,
                    transaction_type,
                    self.current_holding,
                    portfolio_available_cash,
                    order_source,
                    source_ref
                );

                computed_order_entry.update_remarks_rationale(
                    String::from("Auto Remark: CapitalWithdrawalInPortfolio"),
                    String::from("Capital Withdrawal from Portfolio."),
                );

                if stock_details.isin != self.isin {
                    computed_order_entry.remarks.push_str(" RestrictedStock");
                }

                //If Portfolio Is Individual Update portfolio broker Trading acc number
                if portfolio.trading_mode == TradingMode::Individual {
                    if let Some(broker) = &portfolio.broker_trading_acc_number {
                        computed_order_entry.client_trading_account = broker.clone()
                    } else {
                        log_error(LogBuilder::system(&format!("Broker: {} Not Found for the Portfolio.", portfolio.id.clone())));
                        return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                    }
                }

                Ok(computed_order_entry)
            }
        }
    }
}

/// Order Construction for Customised Orders
impl ConstructOrder for CustomisedOrderRequest {
    async fn construct_order(
        &self,
        redis_conn: &mut MultiplexedConnection,
        db_pool: Pool<Manager>,
        master_pool: Pool<Manager>,
        client: &ClientForOms,
        portfolio: &PortfolioForOms,
        strategy: &StrategyForOrderComputation,
        _capital: f64,
        created_by: String,
        current_holding: f64,
        portfolio_available_cash: f64,
        dp_id: String,
        transaction_type: TransactionType,
        order_source: OrderSourceType,
        source_ref: String,
    ) -> Result<ClientComputedOrders, String> {
        let mut conn = db_pool.get().await.unwrap();

        //Check restricted security only for Buy Trade
        let security = SecurityDetails::build_with_restricted_stocks(
            redis_conn,
            &mut conn,
            master_pool,
            self.isin.clone(),
            client.id.clone(),
            if self.is_mf {
                SecurityTypeForPrice::MutualFund
            } else {
                SecurityTypeForPrice::Equity
            },
            &self.exchange,
        )
        .await?;

        if security.get_isin() != self.isin && self.is_sell {
            return Err(format!("Isin {} is Restricted", self.isin));
        }

        match security {
            SecurityDetails::MutualFund(mut mutual_fund_details) => {
                if self.is_buy {
                    if portfolio.account_status != AccountStatus::Active {
                        return Err(String::from("Portfolio Is In Not Active"));
                    }

                    mutual_fund_details.set_price(self.buy_price);

                    let mut investment_amount = ((self.buy_quantity * self.buy_price) * 100f64).round() / 100f64;

                    let mut buy_quantity = self.buy_quantity;

                    if investment_amount > portfolio_available_cash {
                        investment_amount = 0f64;
                        buy_quantity = 0f64;
                    }

                    let mf_folio =
                        InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mutual_fund_details.isin)
                            .await?
                            .unwrap_or_else(|| String::from("NEW"));

                    let mf_buy_sell_type = if mf_folio == "NEW" {
                        "FRESH".to_string()
                    } else {
                        if transaction_type == TransactionType::Buy {
                            "ADDITIONAL".to_string()
                        } else {
                            "REDEEM".to_string()
                        }
                    };

                    let mut computed_order_entry = ClientComputedOrders::build_for_mutual_fund(
                        dp_id,
                        &client,
                        &portfolio,
                        &mutual_fund_details,
                        mutual_fund_details.isin.clone(),
                        self.exchange.clone(),
                        buy_quantity,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        transaction_type,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                        source_ref
                    );

                    computed_order_entry.mf_folio_number = Some(mf_folio);
                    computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                    computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Buy Client Order"), rationale);

                    if mutual_fund_details.isin != self.isin {
                        computed_order_entry.remarks.push_str(" RestrictedStock");
                    }

                    return Ok(computed_order_entry);
                } else {
                    if portfolio.account_status != AccountStatus::Active
                        && portfolio.account_status != AccountStatus::Exiting
                    {
                        return Err(String::from("Portfolio Is Not Active"));
                    }
                    mutual_fund_details.set_price(self.sell_price);

                    let quantity_to_sell = if self.sell_quantity > current_holding {
                        current_holding
                    } else {
                        self.sell_quantity
                    };

                    let investment_amount = ((quantity_to_sell * self.sell_price) * 100f64).round() / 100f64;

                    let mf_folio =
                        InvestmentTransaction::get_mf_folio(&mut conn, &portfolio.id, &mutual_fund_details.isin)
                            .await?
                            .unwrap_or_else(|| String::from("NEW"));

                    let mf_buy_sell_type = if mf_folio == "NEW" {
                        "FRESH".to_string()
                    } else {
                        if transaction_type == TransactionType::Buy {
                            "ADDITIONAL".to_string()
                        } else {
                            "REDEEM".to_string()
                        }
                    };

                    let mut computed_order_entry = ClientComputedOrders::build_for_mutual_fund(
                        dp_id,
                        &client,
                        &portfolio,
                        &mutual_fund_details,
                        mutual_fund_details.isin.clone(),
                        self.exchange.clone(),
                        quantity_to_sell,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        TransactionType::Sell,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                        source_ref
                    );

                    computed_order_entry.mf_folio_number = Some(mf_folio);
                    computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                    computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Sell Client Order"), rationale);

                    if mutual_fund_details.isin != self.isin {
                        computed_order_entry.remarks.push_str(" RestrictedStock");
                    }

                    return Ok(computed_order_entry);
                }
            }
            SecurityDetails::Stocks(mut stock_details) | SecurityDetails::ETF(mut stock_details) => {
                let latest_price = stock_details.get_latest_price();

                if latest_price == 0f64 {
                    return Err(format!("Stock Price Not Found"));
                }

                if self.is_buy {
                    if portfolio.account_status != AccountStatus::Active {
                        return Err(String::from("Portfolio Is In Not Active"));
                    }

                    stock_details.set_price(self.buy_price);

                    let mut investment_amount = ((self.buy_quantity * self.buy_price) * 100f64).round() / 100f64;

                    let mut buy_quantity = self.buy_quantity;

                    if investment_amount > portfolio_available_cash {
                        investment_amount = 0f64;
                        buy_quantity = 0f64;
                    }

                    let mut computed_order_entry = ClientComputedOrders::build(
                        dp_id,
                        &client,
                        &portfolio,
                        &stock_details,
                        stock_details.isin.clone(),
                        self.exchange.clone(),
                        buy_quantity,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        transaction_type,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                        source_ref
                    );

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Buy Client Order"), rationale);

                    if stock_details.isin != self.isin {
                        computed_order_entry.remarks.push_str(" RestrictedStock");
                    }

                    return Ok(computed_order_entry);
                } else {
                    if portfolio.account_status != AccountStatus::Active
                        && portfolio.account_status != AccountStatus::Exiting
                    {
                        return Err(String::from("Portfolio Is Not Active"));
                    }
                    stock_details.set_price(self.sell_price);

                    let quantity_to_sell = if self.sell_quantity > current_holding {
                        current_holding
                    } else {
                        self.sell_quantity
                    };

                    let investment_amount = ((quantity_to_sell * self.sell_price) * 100f64).round() / 100f64;

                    let mut computed_order_entry = ClientComputedOrders::build(
                        dp_id,
                        &client,
                        &portfolio,
                        &stock_details,
                        stock_details.isin.clone(),
                        self.exchange.clone(),
                        quantity_to_sell,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        TransactionType::Sell,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                        source_ref
                    );

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Sell Client Order"), rationale);

                    if stock_details.isin != self.isin {
                        computed_order_entry.remarks.push_str(" RestrictedStock");
                    }

                    return Ok(computed_order_entry);
                }
            }
        }
    }
}

/// Order Construction for Customised Orders
impl CustomisedOrder {
    pub async fn construct_order(
        &self,
        client: &ClientForOms,
        portfolio: &PortfolioForOms,
        strategy: &StrategyForOrderComputation,
        _capital: f64,
        created_by: String,
        current_holding: f64,
        portfolio_available_cash: f64,
        dp_id: String,
        transaction_type: TransactionType,
        order_source: OrderSourceType,
    ) -> Result<ClientComputedOrders, String> {
        match self.is_mf {
            true => {
                if self.is_buy {
                    if portfolio.account_status != AccountStatus::Active {
                        return Err(String::from("Portfolio Is In Not Active"));
                    }

                    let mut investment_amount = ((self.buy_quantity * self.buy_price) * 100f64).round() / 100f64;

                    let mut buy_quantity = self.buy_quantity;

                    if investment_amount > portfolio_available_cash {
                        investment_amount = 0f64;
                        buy_quantity = 0f64;
                    }

                    let mf_folio = "NEW".to_string();

                    let mf_buy_sell_type = if mf_folio == "NEW" {
                        "FRESH".to_string()
                    } else {
                        if transaction_type == TransactionType::Buy {
                            "ADDITIONAL".to_string()
                        } else {
                            "REDEEM".to_string()
                        }
                    };

                    let mut computed_order_entry = ClientComputedOrders::build_for_deviation_mutual_fund(
                        dp_id,
                        &client,
                        &portfolio,
                        self.isin.clone(),
                        self.identifier.clone(),
                        self.buy_price,
                        self.scrip_name.clone(),
                        self.exchange.clone(),
                        buy_quantity,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        transaction_type,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                    );

                    computed_order_entry.mf_folio_number = Some(mf_folio);
                    computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                    computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Buy Client Order"), rationale);

                    return Ok(computed_order_entry);
                } else {
                    if portfolio.account_status != AccountStatus::Active
                        && portfolio.account_status != AccountStatus::Exiting
                    {
                        return Err(String::from("Portfolio Is Not Active"));
                    }

                    let quantity_to_sell = if self.sell_quantity > current_holding {
                        current_holding
                    } else {
                        self.sell_quantity
                    };

                    let investment_amount = ((quantity_to_sell * self.sell_price) * 100f64).round() / 100f64;

                    let mf_folio = String::from("NEW");

                    let mf_buy_sell_type = if mf_folio == "NEW" {
                        "FRESH".to_string()
                    } else {
                        if transaction_type == TransactionType::Buy {
                            "ADDITIONAL".to_string()
                        } else {
                            "REDEEM".to_string()
                        }
                    };

                    let mut computed_order_entry = ClientComputedOrders::build_for_deviation_mutual_fund(
                        dp_id,
                        &client,
                        &portfolio,
                        self.isin.clone(),
                        self.identifier.clone(),
                        self.sell_price,
                        self.scrip_name.clone(),
                        self.exchange.clone(),
                        quantity_to_sell,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        TransactionType::Sell,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                    );

                    computed_order_entry.mf_folio_number = Some(mf_folio);
                    computed_order_entry.mf_client_bank = client.bank_account_number.clone();
                    computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Sell Client Order"), rationale);

                    return Ok(computed_order_entry);
                }
            }
            false => {
                if self.is_buy {
                    if portfolio.account_status != AccountStatus::Active {
                        return Err(String::from("Portfolio Is In Not Active"));
                    }
                    let mut investment_amount = ((self.buy_quantity * self.buy_price) * 100f64).round() / 100f64;

                    let mut buy_quantity = self.buy_quantity;

                    if investment_amount > portfolio_available_cash {
                        investment_amount = 0f64;
                        buy_quantity = 0f64;
                    }

                    let mut computed_order_entry = ClientComputedOrders::build_stocks_for_deviation(
                        dp_id,
                        &client,
                        &portfolio,
                        self.identifier.clone(),
                        self.isin.clone(),
                        self.security_type.clone(),
                        self.exchange.clone(),
                        self.series.clone(),
                        self.scrip_name.clone(),
                        buy_quantity,
                        self.buy_price,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        transaction_type,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                    );

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Buy Client Order"), rationale);

                    return Ok(computed_order_entry);
                } else {
                    if portfolio.account_status != AccountStatus::Active
                        && portfolio.account_status != AccountStatus::Exiting
                    {
                        return Err(String::from("Portfolio Is Not Active"));
                    }

                    let quantity_to_sell = if self.sell_quantity > current_holding {
                        current_holding
                    } else {
                        self.sell_quantity
                    };

                    let investment_amount = ((quantity_to_sell * self.sell_price) * 100f64).round() / 100f64;

                    let mut computed_order_entry = ClientComputedOrders::build_stocks_for_deviation(
                        dp_id,
                        &client,
                        &portfolio,
                        self.identifier.clone(),
                        self.isin.clone(),
                        self.security_type.clone(),
                        self.exchange.clone(),
                        self.series.clone(),
                        self.scrip_name.clone(),
                        quantity_to_sell,
                        self.sell_price,
                        investment_amount,
                        created_by.clone(),
                        &strategy,
                        transaction_type,
                        current_holding,
                        portfolio_available_cash,
                        order_source,
                    );

                    //If Portfolio Is Individual Update portfolio broker Trading acc number
                    if portfolio.trading_mode == TradingMode::Individual {
                        if let Some(broker) = &portfolio.broker_trading_acc_number {
                            computed_order_entry.client_trading_account = broker.clone()
                        } else {
                            return Err(format!("Broker Not Found for the Portfolio {}", portfolio.id.clone()));
                        }
                    }

                    let rationale = if let Some(rationale) = &self.rationale {
                        format!("CUSTOMCLIENTORDER-{}", rationale)
                    } else {
                        format!("CUSTOMCLIENTORDER")
                    };

                    computed_order_entry.update_remarks_rationale(String::from("Custom Sell Client Order"), rationale);

                    return Ok(computed_order_entry);
                }
            }
        }
    }
}
