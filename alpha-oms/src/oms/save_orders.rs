use alpha_core_db::{connection::pool::Manager, schema::client_order_entry::ClientOrderEntry};
use deadpool::managed::Object;
use actlogica_logs::{builder::LogBuilder,log_error, log_info};

use crate::types::ClientComputedOrders;

pub async fn save_orders(
    txn_conn: &mut Object<Manager>,
    client_orders: Vec<ClientComputedOrders>,
    created_by: String,
) -> Result<(), String> {
    log_info(
        LogBuilder::system(&format!(
            "Starting Transaction for Saving Trade Idea Orders. Created By: {}",
            created_by
        ))
        .add_metadata("created_by", &created_by),
    );

    //Convert to ClientOrderEntry
    for co in client_orders {
        log_info(LogBuilder::system(&format!("Inserting Client-Order-Entry with ISIN : {:?}", co.isin)));

        let coe = ClientOrderEntry {
            client_code: co.client_code,
            client_domicile: co.client_domicile,
            client_id: co.client_id,
            client_trading_account: co.client_trading_account,
            created_by: Some(created_by.clone()),
            currency: co.currency,
            currency_conversion_rate: Some(co.currency_conversion_rate),
            euin_number: co.euin_number,
            exchange: co.exchange,
            identifier: co.identifier,
            investment_type: co.investment_type,
            isin: co.isin,
            mf_all_units_redemption_flag: co.mf_all_units_redemption_flag,
            mf_buy_sell_type: co.mf_buy_sell_type,
            mf_client_bank: co.mf_client_bank,
            mf_folio_number: co.mf_folio_number,
            order_date: co.order_date,
            mf_oms_client_code: co.mf_oms_client_code,
            order_rationale: co.order_rationale,
            order_status: co.order_status,
            order_type: co.order_type,
            pending_quantity: co.pending_quantity,
            portfolio_id: co.portfolio_id,
            price: co.price,
            quantity: co.quantity,
            remarks: co.remarks,
            scrip_name: co.scrip_name,
            series: co.series,
            source_reference: co.source_reference,
            source_type: co.source_type.to_string(),
            strategy_code: co.strategy_code,
            strategy_custody_code: co.strategy_custody_code,
            strategy_model_id: co.strategy_model_id,
            strategy_model_name: co.strategy_model_name,
            strategy_name: co.strategy_name,
            strategy_trading_account_number: co.strategy_trading_account_number,
            transaction_amount: co.transaction_amount,
            transaction_sub_type: co.transaction_sub_type,
            transaction_type: co.transaction_type,
            ..Default::default()
        };

        let insert_coe = coe.insert(txn_conn).await;
        if insert_coe.is_err() {
            txn_conn.simple_query("ROLLBACK").await.unwrap();
            log_error(LogBuilder::system("Failed to Save Client-Order-Entry"));
            return Err(String::from("Failed to Save Orders"));
        }
    }

    log_info(LogBuilder::system("Completed! Inserting Client-Order-Entry. Commiting Transaction..."));

    Ok(())
}
