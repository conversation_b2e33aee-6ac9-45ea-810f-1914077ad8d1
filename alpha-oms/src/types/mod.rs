use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    redis::schema::{MutualFundData, StockData},
    schema::{
        client::{Client, DomicileType},
        client_order_entry::{OrderStatus, SecurityType, TransactionSubType, TransactionType},
        oms::{ClientForOms, PortfolioForOms},
        portfolio::{portfolio_broker::PortfolioBroker, PortfolioType},
        security_master::company_master::EquityCompanyMaster,
    },
    types::{compute_order::StrategyForOrderComputation, OrderSourceType, OrderType},
};
use chrono::{DateTime, NaiveDateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

pub mod deviation;
pub mod paginated;
#[derive(Serialize, Deserialize, Clone, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct ClientComputedOrders {
    pub id: u64,
    pub created_date: DateTime<Utc>,
    pub client_id: String,
    pub client_code: String,
    pub client_strategy_code: String,
    pub client_name: String,
    pub strategy_code: String,
    pub strategy_name: String,
    pub strategy_model_name: String,
    pub strategy_custody_code: String,
    pub strategy_trading_account_number: Option<String>,
    pub strategy_model_id: String,
    pub identifier: String,
    pub isin: String,
    pub exchange: String,
    pub scrip_name: String,
    pub industry: String,
    pub market_cap: String,
    pub investment_type: SecurityType,
    pub transaction_type: TransactionType,
    pub transaction_sub_type: TransactionSubType,
    pub quantity: f64,
    pub current_holding: f64,
    pub currency: Option<String>,
    pub currency_conversion_rate: f64,
    pub pending_quantity: f64,
    pub transaction_quantity: f64,
    pub price: f64,
    pub order_type: OrderType,
    pub order_date: NaiveDateTime,
    pub settlement_date: DateTime<Utc>,
    pub order_status: OrderStatus,
    pub transaction_amount: f64,
    pub transaction_amount_required: f64,
    pub portfolio_available_cash: f64,
    pub source_type: OrderSourceType,
    pub source_reference: String,
    pub mf_folio_number: Option<String>,
    pub mf_buy_sell_type: Option<String>,
    pub mf_all_units_redemption_flag: Option<String>,
    pub mf_client_bank: Option<String>,
    pub mf_oms_client_code: Option<String>,
    pub euin_number: Option<String>,
    pub client_domicile: DomicileType,
    pub client_trading_account: String,
    pub remarks: String,
    pub order_rationale: String,
    pub portfolio_id: String,
    pub created_by: String,
    pub series: Option<String>,
}

impl ClientComputedOrders {
    pub fn build_for_deviation_mutual_fund(
        dp_id: String,
        client_details: &ClientForOms,
        portfolio_details: &PortfolioForOms,
        isin: String,
        rta_code: String,
        price: f64,
        scheme_name: String,
        exchange: String,
        quantity: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
        order_source: OrderSourceType,
    ) -> Self {
        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Purchase
        } else {
            TransactionSubType::Redemption
        };

        Self {
            portfolio_id: portfolio_details.id.clone(),
            order_date: Utc::now().naive_utc(),
            investment_type: SecurityType::MutualFund,
            order_status: OrderStatus::Draft,
            isin,
            exchange,
            identifier: rta_code,
            scrip_name: scheme_name,
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price: price,
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_custody_code: dp_id,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            client_strategy_code: portfolio_details.client_strategy_code.clone(),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            client_trading_account: format!("PoolAccount"),
            mf_oms_client_code: client_details.bse_star_ucc.clone(),
            mf_buy_sell_type: format!("Additional").into(),
            created_date: Utc::now(),
            source_type: order_source,
            source_reference: client_details.id.clone(),
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub fn build_for_mutual_fund(
        dp_id: String,
        client_details: &ClientForOms,
        portfolio_details: &PortfolioForOms,
        scheme_details: &MutualFundData,
        isin: String,
        exchange: String,
        quantity: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
        order_source: OrderSourceType,
        source_reference: String,
    ) -> Self {
        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Purchase
        } else {
            TransactionSubType::Redemption
        };

        let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
            OrderStatus::AwaitingClientApproval
        } else {
            OrderStatus::Draft
        };

        Self {
            portfolio_id: portfolio_details.id.clone(),
            order_date: Utc::now().naive_utc(),
            investment_type: SecurityType::MutualFund,
            order_status,
            isin,
            exchange,
            identifier: scheme_details.rta_code.clone(),
            scrip_name: scheme_details.scheme_name.clone(),
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price: scheme_details.price,
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_custody_code: dp_id,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            client_strategy_code: portfolio_details.client_strategy_code.clone(),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            client_trading_account: format!("PoolAccount"),
            mf_oms_client_code: client_details.bse_star_ucc.clone(),
            mf_buy_sell_type: format!("Additional").into(),
            created_date: Utc::now(),
            source_type: order_source,
            source_reference,
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub fn build_for_stocks(
        dp_id: String,
        client_details: &ClientForOms,
        portfolio_details: &PortfolioForOms,
        stock_details: &EquityCompanyMaster,
        isin: String,
        exchange: String,
        quantity: f64,
        price: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
    ) -> Self {
        let security_type = if stock_details.status == "ETF" {
            SecurityType::ETF
        } else {
            SecurityType::Stocks
        };

        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Buy
        } else {
            TransactionSubType::Sell
        };

        let identifier = if exchange.to_lowercase() == "nse" {
            stock_details.symbol.clone().unwrap()
        } else {
            stock_details.scripcode.clone().unwrap()
        };

        let series = if exchange.to_lowercase() == "nse" {
            stock_details.series.clone()
        } else {
            stock_details.scrip_group.clone()
        };

        let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
            OrderStatus::AwaitingClientApproval
        } else {
            OrderStatus::Draft
        };

        Self {
            portfolio_id: portfolio_details.id.clone(),
            order_date: Utc::now().naive_utc(),
            investment_type: security_type,
            order_status,
            isin,
            exchange,
            identifier,
            series: series,
            industry: stock_details.industry.clone().unwrap_or("Unknown".to_string()),
            market_cap: stock_details.market_cap.clone().unwrap_or("Unknown".to_string()),
            scrip_name: stock_details.comp_name.clone(),
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price,
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_custody_code: dp_id,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            client_strategy_code: portfolio_details.client_strategy_code.clone(),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            pending_quantity: quantity,
            client_domicile: client_details.domicile.clone(),
            order_rationale: format!("Additional capital deployment into an existing portfolio for client."),
            remarks: format!("Auto Remark: AdditionalCapitalInPortfolio"),
            client_trading_account: format!("PoolAccount"),
            ..Default::default()
        }
    }

    pub fn build_stocks_for_deviation(
        dp_id: String,
        client_details: &ClientForOms,
        portfolio_details: &PortfolioForOms,
        identifier: String,
        isin: String,
        security_type: SecurityType,
        exchange: String,
        series: Option<String>,
        scrip_name: String,
        quantity: f64,
        price: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
        order_source: OrderSourceType,
    ) -> Self {
        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Buy
        } else {
            TransactionSubType::Sell
        };

        let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
            OrderStatus::AwaitingClientApproval
        } else {
            OrderStatus::Draft
        };

        Self {
            portfolio_id: portfolio_details.id.clone(),
            order_date: Utc::now().naive_utc(),
            investment_type: security_type,
            order_status,
            isin,
            identifier,
            exchange,
            series: series,
            scrip_name,
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price,
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_custody_code: dp_id,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            client_strategy_code: portfolio_details.client_strategy_code.clone(),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            client_trading_account: format!("PoolAccount"),
            created_date: Utc::now(),
            source_type: order_source,
            source_reference: client_details.id.clone(),
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub fn build(
        dp_id: String,
        client_details: &ClientForOms,
        portfolio_details: &PortfolioForOms,
        stock_details: &StockData,
        isin: String,
        exchange: String,
        quantity: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
        order_source: OrderSourceType,
        source_reference: String,
    ) -> Self {
        let security_type = if stock_details.status == "ETF" {
            SecurityType::ETF
        } else {
            SecurityType::Stocks
        };

        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Buy
        } else {
            TransactionSubType::Sell
        };

        let series = if exchange.to_lowercase() == "nse" {
            stock_details.series.clone()
        } else {
            stock_details.scrip_group.clone()
        };

        let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
            OrderStatus::AwaitingClientApproval
        } else {
            OrderStatus::Draft
        };

        Self {
            portfolio_id: portfolio_details.id.clone(),
            order_date: Utc::now().naive_utc(),
            investment_type: security_type,
            order_status,
            isin,
            identifier: stock_details.get_identifier_by_exchange(&exchange),
            industry: stock_details.industry.clone().unwrap_or("Unknown".to_string()),
            market_cap: stock_details.market_cap.clone().unwrap_or("Unknown".to_string()),
            exchange,
            series: series,
            scrip_name: stock_details.company_name.clone(),
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price: stock_details.get_latest_price(),
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_custody_code: dp_id,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            client_strategy_code: portfolio_details.client_strategy_code.clone(),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            client_trading_account: format!("PoolAccount"),
            created_date: Utc::now(),
            source_type: order_source,
            source_reference,
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub fn build_for_new_portfolio(
        dp_id: String,
        client_details: &Client,
        stock_details: &StockData,
        isin: String,
        exchange: String,
        quantity: f64,
        _price: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
    ) -> Self {
        let security_type = if stock_details.status == "ETF" {
            SecurityType::ETF
        } else {
            SecurityType::Stocks
        };

        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Buy
        } else {
            TransactionSubType::Sell
        };

        let series = if exchange.to_lowercase() == "nse" {
            stock_details.series.clone()
        } else {
            stock_details.scrip_group.clone()
        };

        // let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
        //     OrderStatus::AwaitingClientApproval
        // } else {
        //     OrderStatus::Draft
        // };

        Self {
            order_date: Utc::now().naive_utc(),
            investment_type: security_type,
            order_status: OrderStatus::Draft,
            isin,
            identifier: stock_details.get_identifier_by_exchange(&exchange),
            industry: stock_details.industry.clone().unwrap_or("Unknown".to_string()),
            market_cap: stock_details.market_cap.clone().unwrap_or("Unknown".to_string()),
            exchange,
            series,
            scrip_name: stock_details.company_name.clone(),
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price: stock_details.get_latest_price(),
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            order_rationale: format!("Fresh capital deployment into a new portfolio."),
            remarks: format!("Auto Remark: NewCapitalInPortfolio"),
            client_trading_account: format!("PoolAccount"),
            strategy_custody_code: dp_id,
            source_reference: client_details.id.clone(),
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub fn build_for_new_portfolio_mutual_fund(
        dp_id: String,
        client_details: &Client,
        scheme_details: &MutualFundData,
        isin: String,
        exchange: String,
        quantity: f64,
        price: f64,
        trade_amount: f64,
        created_by: String,
        strategy: &StrategyForOrderComputation,
        transaction_type: TransactionType,
        current_holding: f64,
        portfolio_available_cash: f64,
    ) -> Self {
        let transaction_sub_type = if transaction_type == TransactionType::Buy {
            TransactionSubType::Buy
        } else {
            TransactionSubType::Sell
        };

        // let order_status = if portfolio_details.portfolio_type == PortfolioType::NonDiscretionary {
        //     OrderStatus::AwaitingClientApproval
        // } else {
        //     OrderStatus::Draft
        // };

        Self {
            order_date: Utc::now().naive_utc(),
            investment_type: SecurityType::MutualFund,
            order_status: OrderStatus::Draft,
            isin,
            exchange,
            identifier: scheme_details.rta_code.clone(),
            scrip_name: scheme_details.scheme_name.clone(),
            order_type: OrderType::LimitOrder,
            transaction_type,
            transaction_sub_type,
            quantity,
            current_holding,
            transaction_quantity: quantity,
            price,
            transaction_amount: trade_amount,
            transaction_amount_required: trade_amount,
            portfolio_available_cash: portfolio_available_cash,
            strategy_model_id: strategy.model_id.clone(),
            client_id: client_details.id.clone(),
            client_code: client_details.client_code.clone(),
            client_name: format!("{} {}", client_details.first_name, client_details.last_name),
            strategy_code: strategy.strategy_code.clone(),
            strategy_name: strategy.strategy_name.clone(),
            strategy_model_name: strategy.model_name.clone(),
            created_by,
            client_domicile: client_details.domicile.clone(),
            order_rationale: format!("Fresh capital deployment into a new portfolio."),
            remarks: format!("Auto Remark: NewCapitalInPortfolio"),
            client_trading_account: format!("PoolAccount"),
            strategy_custody_code: dp_id,
            source_reference: client_details.id.clone(),
            pending_quantity: quantity,
            ..Default::default()
        }
    }

    pub async fn update_nri_trading_account_number(&mut self, conn: &mut Object<Manager>) -> Result<(), String> {
        //Get the Broker of the Portfolio
        let broker = PortfolioBroker::get_by_portfolio_id(conn, self.portfolio_id.clone()).await?;

        if let Some(broker) = broker {
            self.client_trading_account = broker.trading_account_number;
            Ok(())
        } else {
            return Err(format!(
                "Broker Not Found for the Portfolio {}",
                self.portfolio_id.clone()
            ));
        }
    }

    pub fn update_remarks_rationale(&mut self, remarks: String, rationale: String) {
        self.remarks = remarks;
        self.order_rationale = rationale;
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CustomisedOrder {
    pub id: u64,
    pub is_mf: bool,
    pub isin: String,
    pub portfolio_id: String,
    pub client_id: String,
    pub client_name: String,
    pub portfolio_cash: f64,
    pub client_strategy_code: String,
    pub name: String,
    pub industry: Option<String>,
    pub exchange: String,
    pub is_buy: bool,
    pub is_sell: bool,
    pub symbol: String,
    pub current_holding: f64,
    pub buy_quantity: f64,
    pub buy_price: f64,
    pub sell_quantity: f64,
    pub sell_price: f64,
    pub weight: f64,
    pub new_weight: f64,
    pub trade_amount: f64,
    pub current_price: f64,
    pub rationale: Option<String>,
    pub security_type: SecurityType,
    pub series: Option<String>,
    pub scrip_name: String,
    pub identifier: String,
}

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CustomisedOrderRequest {
    pub is_mf: bool,
    pub isin: String,
    pub portfolio_id: String,
    pub exchange: String,
    pub is_buy: bool,
    pub is_sell: bool,
    pub buy_quantity: f64,
    pub buy_price: f64,
    pub sell_quantity: f64,
    pub sell_price: f64,
    pub rationale: Option<String>,
}

// fn get_bse_appropriate_scheme_code(
//     transaction_type: TransactionType,
//     scheme_details: &MutualFundData,
//     order_amt: f64,
// ) -> String {
// if transaction_type == TransactionType::Buy
//     && scheme_details.scheme_type.to_uppercase() == "LIQUID"
// {
//     format!("{}-L0", scheme_details.bse_star_scheme_code)
// } else if transaction_type == TransactionType::Buy
//     && scheme_details.scheme_type.to_uppercase() != "LIQUID"
//     && order_amt >= 200000.0
// {
//     format!("{}-L1", scheme_details.bse_star_scheme_code)
// } else {
//     scheme_details.bse_star_scheme_code.clone()
// }

// String::from("Empty")
// }

#[derive(Serialize)]
pub struct ComputeOrderRsp {
    pub session_id: Uuid,
    pub orders: Vec<ClientComputedOrders>,
}
