use alpha_core_db::schema::client_order_entry::SecurityType;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, Serialize, Deserialize, PartialEq)]
pub enum DeviationAction {
    Buy,
    Sell,
}

pub struct DeviationCalculatedResult {
    pub isin: String,
    pub symbol: String,
    pub security_name: String,
    pub is_mutual_fund: bool,
    pub exchange: String,
    pub model_weight: f64,
    pub portfolio_weight: f64,
    pub portfolio_mv: f64,
    pub current_holding: f64,
    pub deviation: f64,
    pub action: DeviationAction,
}

impl DeviationCalculatedResult {
    pub fn calculate_deviation_for_isin(
        isin: String,
        symbol: String,
        security_name: String,
        security_type: SecurityType,
        exchange: String,
        model_weight: f64,
        portfolio_weight: f64,
        portfolio_mv: f64,
        portfolio_current_holding: f64,
    ) -> DeviationCalculatedResult {
        let deviation_action = if portfolio_weight > model_weight {
            DeviationAction::Sell
        } else {
            DeviationAction::Buy
        };

        DeviationCalculatedResult {
            isin,
            symbol,
            security_name,
            is_mutual_fund: matches!(security_type, SecurityType::MutualFund),
            exchange,
            model_weight,
            portfolio_weight,
            portfolio_mv,
            current_holding: portfolio_current_holding,
            deviation: portfolio_weight - model_weight,
            action: deviation_action,
        }
    }
}

#[derive(Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ModelDeviationReport {
    pub id: u64,
    pub client_id: String,
    pub portfolio_id: String,
    pub client_name: String,
    pub client_strategy_code: String,
    pub isin: String,
    pub security_name: String,
    pub is_mutual_fund: bool,
    pub exchange: String,
    pub portfolio_weight: f64,
    pub portfolio_holding: f64,
    pub portfolio_mv: f64,
    pub portfolio_cash: f64,
    pub model_weight: f64,
    pub deviation_pct: f64,
    pub action: DeviationAction,
    pub symbol : String,
    pub selected:bool
}
