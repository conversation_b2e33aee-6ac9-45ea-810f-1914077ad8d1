use std::collections::HashMap;

use alpha_core_db::{
    schema::{client_order_entry::TransactionType, compliance_pre_trade::rule_type::PreTradeRuleType},
    tenant_clickhouse::pre_trade_compliance_report::PreTradeComplianceReportDb,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PreTradeComplianceReport {
    pub portfolio_reports: Vec<PreTradeComplianceRulesSummary>,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct PreTradeComplianceRulesReport {
    pub id: u64,
    pub isin: String,
    pub security_name: String,
    pub market_cap: String,
    pub sector: String,
    pub portfolio: String,
    pub symbol: String,
    pub amount: f64,
    pub transaction_type: TransactionType,
    pub quantity: f64,
    pub rule: String,
    pub violate: bool,
}

#[derive(Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PreTradeComplianceRulesSummary {
    pub id: u64,
    pub isin: String,
    pub security_name: String,
    pub market_cap: String,
    pub sector: String,
    pub portfolio_name: String,
    pub portfolio_id: String,
    pub symbol: String,
    pub amount: f64,
    pub transaction_type: TransactionType,
    pub quantity: f64,
    pub rules: HashMap<String, bool>,
}

impl PreTradeComplianceRulesSummary {
    pub fn flatten(&self, session_id: Uuid) -> Vec<PreTradeComplianceReportDb> {
        self.rules
            .iter()
            .map(|(rule, &violate)| PreTradeComplianceReportDb {
                id: self.id,
                session_id: session_id,
                isin: self.isin.clone(),
                security_name: self.security_name.clone(),
                market_cap: self.market_cap.clone(),
                sector: self.sector.clone(),
                portfolio_name: self.portfolio_name.clone(),
                portfolio_id: self.portfolio_id.clone(),
                symbol: self.symbol.clone(),
                amount: self.amount,
                transaction_type: self.transaction_type.to_string(),
                quantity: self.quantity,
                rule: rule.clone(),
                violate,
            })
            .collect()
    }
}
