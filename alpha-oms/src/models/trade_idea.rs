use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
pub struct BuyTradeIdeaOrderComputation {
    pub isin: String,
    pub exchange: String,
    pub buy_price_from: f64,
    pub buy_price_to: f64,
    pub exit_price: Option<f64>,
    pub change: f64,
    pub model_id: String,
}

#[derive(Serialize, Deserialize)]
pub struct SellTradeIdeaOrderComputation {
    pub security_name: String,
    pub symbol: String,
    pub isin: String,
    pub exchange: String,
    pub security_type: String,
    pub sell_net_change: f64,
    pub sell_price_from: f64,
    pub sell_price_to: f64,
    pub change: f64,
    pub is_sell_all: bool,
    pub description: String,
    pub status: String,
    pub strategy_id: String,
    pub model_id: String,
}
