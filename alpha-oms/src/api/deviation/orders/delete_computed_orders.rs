use crate::log_actions::Action;
use crate::models::{AppState, TokenClaims};
use actlogica_logs::{builder::LogBuilder, log_error, log_info, generator::generate_event_id};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

#[derive(Serialize, Deserialize)]
pub struct DeleteDeviationOrdersPayload {
    /// Id of the deviation
    id: String,

    /// Receives the seq for which all orders should be deleted for deviation
    seq: Vec<u64>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn delete_deviation_computed_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): <PERSON><PERSON><DeleteDeviationOrdersPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    let seq = &payload.seq.iter().map(|s| s.to_string()).collect::<Vec<_>>().join(",");
    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Delete Deviation Computed Orders",
            Action::DeleteDeviationComputedOrders,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id)
        .add_metadata("seq", &seq),
    );

    let redis_pool = state.tenant_redis_url.clone();
    let mut redis_conn = match redis_pool.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to connect to database",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), payload.id.clone());

    let mut pipe = deadpool_redis::redis::pipe();
    pipe.atomic();
    for score in payload.seq {
        pipe.zrembyscore(&key, score, score);
    }

    let delete_orders: Result<Vec<i64>, deadpool_redis::redis::RedisError> = pipe.query_async(&mut redis_conn).await;

    match delete_orders {
        Ok(count) => {
            if count.len() == 0 {
                let key_exist: Result<bool, deadpool_redis::redis::RedisError> = redis_conn.exists(&key).await;
                match key_exist {
                    Ok(exist) => {
                        if !exist {
                            log_error(
                                LogBuilder::system("Session Expired: Key does not exist in Redis")
                                    .add_metadata("key", &key),
                            );
                            let error_response = serde_json::json!({
                                "status": "error",
                                "message": "Session Expired",
                                "code":401
                            });
                            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                        }
                    }
                    Err(err) => {
                        log_error(
                            LogBuilder::system(format!("Failed to verify order existence: {:?}", err).as_str())
                                .add_metadata("key", &key),
                        );
                        let error_response = serde_json::json!({
                            "status": "error",
                            "message": "Failed to verify order existence",
                        });
                        return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                    }
                }
            }

            log_info(
                LogBuilder::system("RES: Successfully Deleted Deviation Computed Orders")
                    .add_metadata("deviation_id", &payload.id)
                    .add_metadata("seq", &seq)
                    .add_metadata("deleted_count", &count.len().to_string()),
            );

            let response = serde_json::json!({
                "status": "success",
                "message": format!("Successfully Deleted the order"),
            });

            Ok((StatusCode::ACCEPTED, Json(response)))
        }
        Err(_err) => {
            log_error(
                LogBuilder::system(format!("Failed to Delete Order in Redis: {:?}", _err).as_str())
                    .add_metadata("deviation_id", &payload.id)
                    .add_metadata("seq", &seq),
            );
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Failed to Delete Order"),
            });

            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)))
        }
    }
}
