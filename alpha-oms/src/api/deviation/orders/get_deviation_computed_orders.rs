use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    types::CustomisedOrder,
};

#[derive(Serialize, Deserialize)]
pub struct GetDeviationComputedOrdersQuery {
    /// Unique Deviation Id
    id: String,

    /// Seq from which next entries are required
    seq: u64,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_deviation_computed_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    <PERSON><PERSON>(query): <PERSON><PERSON><GetDeviationComputedOrdersQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Get Deviation Computed Order",
            Action::GetDeviationComputedOrder,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id)
        .add_metadata("seq", &query.seq.to_string()),
    );

    //Get the next 100 from the seq id provided
    let redis_pool = state.tenant_redis_url.clone();
    let mut redis_conn = match redis_pool.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to get Redis connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), query.id.clone());

    let start_seq = query.seq;
    let end_seq = start_seq + 99;

    let items_str: Vec<String> = match redis_conn
        .zrangebyscore::<String, u64, u64, Vec<String>>(key.clone(), start_seq, end_seq)
        .await
    {
        Ok(items) => items,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Fetch orders from Redis: {:?}", err).as_str(),
            ));
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to fetch orders from Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let mut reports: Vec<CustomisedOrder> = Vec::new();

    for item in items_str {
        match serde_json::from_str(&item) {
            Ok(report) => reports.push(report),
            Err(_) => {
                log_error(
                    LogBuilder::system(format!("Failed: deserialize data").as_str()).add_metadata("str_item", &item),
                );
                let error_response = serde_json::json!({
                    "status": "error",
                    "message": "Failed to deserialize order data",
                });
                return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
            }
        }
    }
    log_info(
        LogBuilder::system("RES: Successfully fetched and deserialized orders")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("total_orders", &reports.len().to_string()),
    );

    Ok::<(StatusCode, axum::Json<Vec<CustomisedOrder>>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(reports),
    ))
}
