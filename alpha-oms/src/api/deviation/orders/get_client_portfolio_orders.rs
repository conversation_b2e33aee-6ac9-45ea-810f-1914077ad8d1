use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    types::CustomisedOrder,
};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ClientPortfolioOrdersRequest {
    /// Id of the deviation
    id: String,

    /// Portfolio id
    portfolio_id: String,
}

#[derive(Serialize, Deserialize)]
pub struct ClientPortfoliosOrdersResponse {
    /// Id of the deviation
    id: String,

    /// Client Details of the invalid orders
    data: Vec<CustomisedOrder>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_client_portfolio_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): Json<ClientPortfolioOrdersRequest>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Get Client Portfolio Orders",
            Action::GetClientPortfolioOrders,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id)
        .add_metadata("portfolio_id", &payload.portfolio_id),
    );
    let mut redis_conn = match state.tenant_redis_url.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to Get connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), payload.id.clone());

    let all_orders: Result<Vec<Vec<u8>>, deadpool_redis::redis::RedisError> = redis_conn.zrange(key, 0, -1).await;

    let mut customised_orders = Vec::new();
    match all_orders {
        Ok(orders) => {
            for order in orders {
                let des_order: Result<CustomisedOrder, serde_json::Error> = serde_json::from_slice(&order);

                match des_order {
                    Ok(order) => {
                        if order.portfolio_id == payload.portfolio_id {
                            customised_orders.push(order);
                        }
                    }
                    Err(_) => {
                        log_error(LogBuilder::system(format!("Failed: deserialize data").as_str()));
                        let error_response = serde_json::json!({
                            "status": "error",
                            "message": "Failed to Deserialise the Data",
                        });
                        return Err((StatusCode::OK, Json(error_response)));
                    }
                }
            }
        }
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to Get connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }

    log_info(
        LogBuilder::system("RES: Successfully fetched Client portfolio orders")
            .add_metadata("deviation_id", &payload.id)
            .add_metadata("portfolio_id", &payload.portfolio_id)
            .add_metadata("total_orders", &customised_orders.len().to_string()),
    );

    Ok((
        StatusCode::OK,
        Json(ClientPortfoliosOrdersResponse {
            id: payload.id,
            data: customised_orders,
        }),
    ))
}
