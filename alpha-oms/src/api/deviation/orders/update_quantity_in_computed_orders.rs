use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, log_error, log_info, generator::generate_event_id};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    types::CustomisedOrder,
};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OrderToChange {
    /// The id of Order which needs to be updated
    id: u64,

    /// Quantity to be updated
    quantity: f64,

    price: f64,
}

#[derive(Serialize, Deserialize)]
pub struct UpdateComputedOrderPayload {
    /// Deviation Reports Unique Id in cache
    id: String,

    changes: Vec<OrderToChange>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn update_quantity_in_computed_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): Json<UpdateComputedOrderPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Update Quantity in Computed Orders",
            Action::UpdateQuantityInComputedOrders,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id),
    );

    let redis_pool = state.tenant_redis_url.clone();
    let mut redis_conn = match redis_pool.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to get Redis connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), payload.id.clone());

    if payload.changes.is_empty() {
        log_error(LogBuilder::system("Failed: Changes array is empty"));
        let error_response = json!({
            "status": "error",
            "message": "Changes array should have at least one element",
        });
        return Err((StatusCode::BAD_REQUEST, Json(error_response)));
    }

    //Update quantities
    // First, collect all the IDs we need to fetch
    let change_ids: Vec<u64> = payload.changes.iter().map(|change| change.id).collect();

    // Create a pipeline for the bulk fetch
    let mut fetch_pipe = deadpool_redis::redis::pipe();
    for &id in &change_ids {
        fetch_pipe.zrangebyscore::<String, u64, u64>(key.clone(), id, id);
    }

    // Execute the pipeline to get all orders in one network call
    let fetch_results: Vec<Option<Vec<String>>> = match fetch_pipe.query_async(&mut *redis_conn).await {
        Ok(results) => results,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Bulk fetch orders: {:?}", err).as_str(),
            ));
            let error_response = json!({
            "status": "error",
            "message": "Failed to fetch orders in bulk",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    // Create a pipeline for the bulk update
    let mut update_pipe = deadpool_redis::redis::pipe();
    update_pipe.atomic();

    // Process all orders and add update commands to the pipeline
    for (i, change) in payload.changes.iter().enumerate() {
        let result = &fetch_results[i];

        if let Some(order_data) = result {
            if order_data.is_empty() {
                log_error(LogBuilder::system(
                    format!("Order with ID {} not found", change.id).as_str(),
                ));
                let error_response = json!({
                    "status": "error",
                    "message": format!("Order with ID {} not found", change.id),
                });
                return Err((StatusCode::NOT_FOUND, Json(error_response)));
            }

            let order: CustomisedOrder = match serde_json::from_str(&order_data[0]) {
                Ok(order) => order,
                Err(_) => {
                    log_error(
                        LogBuilder::system(format!("Failed to deserialize order with ID {}", change.id).as_str())
                            .add_metadata("order_data", &order_data[0]),
                    );
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to deserialize order with ID {}", change.id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let mut updated_order = order;
            updated_order.buy_quantity = change.quantity;
            updated_order.sell_quantity = change.quantity;
            updated_order.buy_price = change.price;
            updated_order.sell_price = change.price;

            let serialized_report = match serde_json::to_vec(&updated_order) {
                Ok(serialized) => serialized,
                Err(_) => {
                    log_error(
                        LogBuilder::system(format!("Failed to deserialize order with ID {}", change.id).as_str())
                            .add_metadata("updated_order_id", &updated_order.id.to_string()),
                    );
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to serialize updated order with ID {}", change.id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            // Add commands to remove old order and add updated one
            update_pipe.zrembyscore(&key, updated_order.id, updated_order.id).zadd(
                &key,
                serialized_report,
                updated_order.id,
            );
        } else {
            let error_response = json!({
                "status": "error",
                "message": format!("Order with ID {} not found", change.id),
            });
            return Err((StatusCode::NOT_FOUND, Json(error_response)));
        }
    }

    // Execute the update pipeline in a single network call
    match update_pipe
        .query_async::<deadpool_redis::redis::Value>(&mut redis_conn)
        .await
    {
        Ok(_) => (),
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Update orders in Redis: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to update orders in Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    log_info(
        LogBuilder::system("RES: Successfully updated quantities in computed orders")
            .add_metadata("user_id", user_id)
            .add_metadata("user_name", user_name)
            .add_metadata("user_role", &tenant.role.to_string())
            .add_metadata("deviation_id", &payload.id),
    );
    let response = json!({
        "status": "success",
        "message": "Updated Successfully",
    });

    Ok((StatusCode::ACCEPTED, Json(response)))
}
