use crate::log_actions::Action;
use crate::{
    models::{AppState, TokenClaims},
    oms::deviation::validate_deviation_orders::InvalidDeviationPortfolioOrders,
    types::CustomisedOrder,
};
use actlogica_logs::{builder::Log<PERSON>uilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::sync::Arc;

#[derive(Serialize, Deserialize)]
pub struct ValidateOrdersPayload {
    /// Id of the deviation
    id: String,
}

#[derive(Serialize, Deserialize)]
pub struct ValidateOrdersResponsePayload {
    /// Id of the deviation
    id: String,

    // Client Details of the invalid orders
    data: Vec<InvalidDeviationPortfolioOrders>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn validate_deviation_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): Json<ValidateOrdersPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Validate Deviation Orders",
            Action::ValidateDeviationOrders,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id),
    );
    let redis_conn = state.tenant_redis_url.get().await;

    let mut redis_conn = match redis_conn {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to Get connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), payload.id.clone());

    let all_orders: Result<Vec<Vec<u8>>, deadpool_redis::redis::RedisError> = redis_conn.zrange(key, 0, -1).await;

    let mut customised_orders = Vec::new();

    match all_orders {
        Ok(orders) => {
            for order in orders {
                let des_order: Result<CustomisedOrder, serde_json::Error> = serde_json::from_slice(&order);

                match des_order {
                    Ok(order) => {
                        customised_orders.push(order);
                    }
                    Err(_) => {
                        log_error(LogBuilder::system(format!("Failed: deserialize order_data").as_str()));
                        let error_response = json!({
                            "status": "error",
                            "message": "Failed to Deserialise the Data",
                        });
                        return Err((StatusCode::OK, Json(error_response)));
                    }
                }
            }
        }
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Fetch orders from Redis: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to Get connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }
    log_info(LogBuilder::system(
        format!("Total Orders found {}", customised_orders.len()).as_str(),
    ));

    let invalid_orders = crate::oms::deviation::validate_deviation_orders::validate_deviation_orders(customised_orders);
    log_info(
        LogBuilder::system("RES: Successfully validated orders")
            .add_metadata("total_invalid_orders", &invalid_orders.len().to_string()),
    );
    Ok((
        StatusCode::OK,
        Json(ValidateOrdersResponsePayload {
            id: payload.id,
            data: invalid_orders,
        }),
    ))
}
