use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info, log_warn};
use alpha_core_db::schema::oms::PortfolioDetailsForOms;
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{collections::HashSet, sync::Arc};

use crate::{
    api::deviation::fetch_reports_in_bulk,
    models::{AppState, TokenClaims},
    types::deviation::ModelDeviationReport,
};

use super::update_reports_in_bulk_and_add_portfolio_details;

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportToChange {
    report_id: u64,
    select: bool,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveDeviationReportsQuery {
    id: String,
    changes: Vec<ReportToChange>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveDeviationReportsResponse {
    pub status: String,
}

/// This save the Deviation Report
/// Report id to save should be included in the Json Body
#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn save_deviation_reports(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    query: Json<SaveDeviationReportsQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Save Deviation report",
            Action::SaveDeviationReports,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id),
    );

    let redis_pool = state.tenant_redis_url.clone();
    let mut redis_conn = match redis_pool.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to get Redis connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationReport.to_string(), query.id.clone());

    // Extract report IDs from the query
    let report_ids: Vec<u64> = query.changes.iter().map(|change| change.report_id).collect();

    // Fetch all reports in bulk
    let reports_data = fetch_reports_in_bulk(&mut redis_conn, &key, &report_ids).await?;

    // Process reports and prepare updates
    let mut updates_to_make = Vec::new();
    let mut portfolios_to_fetch = HashSet::new();

    for (i, change) in query.changes.iter().enumerate() {
        if let Some(result) = &reports_data[i] {
            if result.is_empty() {
                log_warn(
                    LogBuilder::system("Report not found")
                        .add_metadata("id_of_report_to_change", &change.report_id.to_string()),
                );
                continue; // Skip if report not found
            }

            log_info(LogBuilder::system(&format!(
                "Processing report with _id: {}",
                change.report_id
            )));

            let report: ModelDeviationReport = match serde_json::from_str(&result[0]) {
                Ok(report) => report,
                Err(_) => {
                    log_error(LogBuilder::system(
                        format!("Failed to deserialize report with ID {}", change.report_id).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to deserialize report with ID {}", change.report_id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let mut updated_report = report;
            updated_report.selected = change.select;

            if change.select == true {
                portfolios_to_fetch.insert(updated_report.portfolio_id.clone());
            }

            match serde_json::to_vec(&updated_report) {
                Ok(serialized) => {
                    updates_to_make.push((serialized, updated_report.id));
                }
                Err(_) => {
                    log_error(LogBuilder::system(
                        format!("Failed to deserialize report with ID {}", change.report_id).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to serialize updated report with ID {}", change.report_id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };
        }
    }

    //Fetch the portfolio details and store in the cache
    let mut db_conn = match state.db.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(format!("Failed: DB connection: {:?}", err).as_str()));
            let error_response = json!({
                "status": "error",
                "message": "Failed to get Db connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let portfolios =
        match PortfolioDetailsForOms::get_bulk(&mut db_conn, &portfolios_to_fetch.into_iter().collect::<Vec<String>>())
            .await
        {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: Get portfolio info: {:?}", err).as_str(),
                ));
                let error_response = json!({
                    "status": "error",
                    "message": "Failed to get portfolio info",
                });
                return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
            }
        };

    let ser_portfolios = portfolios
        .iter()
        .filter_map(|p| match serde_json::to_vec(p) {
            Ok(serialized) => Some((serialized, p.portfolio.id.clone())),
            Err(_) => {
                log_error(LogBuilder::system("Failed to serialize portfolio"));
                None
            }
        })
        .collect::<Vec<(Vec<u8>, String)>>();

    // Update all reports in bulk
    if !updates_to_make.is_empty() {
        update_reports_in_bulk_and_add_portfolio_details(&mut redis_conn, &key, updates_to_make, &ser_portfolios)
            .await?;
    }
    log_info(
        LogBuilder::system("RES: Successfully updated deviation reports and portfolio details")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("user_id", &user_id)
            .add_metadata("user_name", &user_name),
    );

    Ok::<(StatusCode, axum::Json<SaveDeviationReportsResponse>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(SaveDeviationReportsResponse {
            status: "success".to_owned(),
        }),
    ))
}
