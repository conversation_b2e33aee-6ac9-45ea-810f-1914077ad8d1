use actlogica_logs::{builder::Log<PERSON><PERSON><PERSON>, log_error};
use alpha_utils::constants::RedisKey;
use axum::{http::StatusCode, Json};
use deadpool_redis::Connection;
use serde_json::json;

pub mod download_full_report;
pub mod get_deviation_reports;
pub mod get_reports_by_filter;
pub mod save_deviation_report;

pub async fn update_reports_in_bulk_and_add_portfolio_details(
    redis_conn: &mut Connection,
    key: &str,
    updated_reports: Vec<(Vec<u8>, u64)>,
    portfolios: &Vec<(Vec<u8>, String)>,
) -> Result<(), (StatusCode, Json<serde_json::Value>)> {
    let mut pipeline = deadpool_redis::redis::pipe();
    pipeline.atomic();

    for (serialized_report, report_id) in updated_reports {
        //First Remove the entry to avoid duplication
        pipeline.zrembyscore(key, report_id, report_id);
        pipeline.zadd(key, serialized_report, report_id);
    }

    pipeline.expire(key, 5 * 60);

    for (portfolio, portfolio_id) in portfolios {
        let key = format!("{}:{}", RedisKey::DeviationPortfolios, portfolio_id);
        pipeline.set_ex(key, portfolio, 5 * 60);
    }

    pipeline
        .query_async::<deadpool_redis::redis::Value>(redis_conn)
        .await
        .map_err(|err| {
            log_error(
                LogBuilder::system("Failed: fetch reports in bulk from redis pipeline")
                    .add_metadata("error", err.to_string().as_str()),
            );
            let error_response = json!({
                "status": "error",
                "message": "Failed to update reports in bulk",
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

    Ok(())
}
