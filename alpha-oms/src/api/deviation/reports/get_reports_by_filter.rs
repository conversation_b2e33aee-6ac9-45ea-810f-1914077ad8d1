use std::sync::Arc;

use actlogica_logs::{builder::Log<PERSON><PERSON><PERSON>, generator::generate_event_id, log_error, log_info, log_warn};
use alpha_utils::constants::RedisKey;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use serde_json::json;

use crate::{
    log_actions::Action,
    models::{AppState, TokenClaims},
    types::deviation::{DeviationAction, ModelDeviationReport},
};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportToChange {
    report_id: u64,
    select: bool,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetDeviationReportsPayload {
    id: String,
    changes: Vec<ReportToChange>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetDeviationReportsQuery {
    filter_buy: Option<bool>,
    #[serde(default)]
    filter_sell: Option<bool>,
    #[serde(default)]
    portfolio_id: Option<String>,
    #[serde(default = "default_offset")]
    offset: i64,
    #[serde(default = "default_limit")]
    limit: i64,
}

fn default_offset() -> i64 {
    0
}

fn default_limit() -> i64 {
    100
}

#[derive(Serialize)]
struct PaginatedResponse {
    reports: Vec<ModelDeviationReport>,
    total_count: usize,
    next_offset: Option<i64>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_deviation_reports_by_filters(
    Extension(_tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    query: Query<GetDeviationReportsQuery>,
    Json(payload): Json<GetDeviationReportsPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &_tenant.name;
    let user_id = &_tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Get Deviation reports by filters",
            Action::GetDeviationReportsByFilters,
        )
        .add_metadata("user_role", &_tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id)
        .add_metadata("total_count", &payload.changes.len().to_string()),
    );
    //Get the next 100 from the seq id provided
    let redis_pool = state.tenant_redis_url.clone();
    let mut redis_conn = match redis_pool.get().await {
        Ok(conn) => conn,
        Err(_) => {
            log_error(LogBuilder::system("Failed to get Redis connection"));

            let error_response = json!({
                "status": "error",
                "message": "Failed to get Redis connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::DeviationReport.to_string(), payload.id.clone());

    //Update if there is any selected or unselected one
    for change in &payload.changes {
        let result: Option<Vec<String>> = match redis_conn
            .zrangebyscore::<String, u64, u64, Option<Vec<String>>>(key.clone(), change.report_id, change.report_id)
            .await
        {
            Ok(result) => result,
            Err(_) => {
                log_error(LogBuilder::system(
                    format!("Failed to fetch report with ID {}", change.report_id).as_str(),
                ));
                let error_response = json!({
                    "status": "error",
                    "message": format!("Failed to fetch report with ID {}", change.report_id),
                });
                return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
            }
        };

        if let Some(result) = result {
            if result.is_empty() {
                continue; // Skip if no results found
            }

            let report: ModelDeviationReport = match serde_json::from_str(&result[0]) {
                Ok(report) => report,
                Err(_) => {
                    log_error(LogBuilder::system(
                        format!("Failed to deserialize report with ID {}", change.report_id).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to deserialize report with ID {}", change.report_id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let mut updated_report = report;
            updated_report.selected = change.select;

            let serialized_report = match serde_json::to_vec(&updated_report) {
                Ok(serialized) => serialized,
                Err(_) => {
                    log_error(LogBuilder::system(
                        format!("Failed to serialize updated report with ID {}", change.report_id).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to serialize updated report with ID {}", change.report_id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            match redis_conn
                .zadd::<String, u64, Vec<u8>, u64>(key.clone(), serialized_report, updated_report.id)
                .await
            {
                Ok(_) => (),
                Err(_) => {
                    log_error(LogBuilder::system(
                        format!("Failed to update report with ID {} in Redis", change.report_id).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": format!("Failed to update report with ID {} in Redis", change.report_id),
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };
        }
    }

    // Apply pagination with offset and limit (max 100)
    let limit = if query.limit > 100 { 100 } else { query.limit };

    let reports_bytes: Vec<Vec<u8>> = match redis_conn
        .zrange(key.clone(), query.offset as isize, (query.offset + limit - 1) as isize)
        .await
    {
        Ok(bytes) => bytes,
        Err(_) => {
            log_error(LogBuilder::system("Failed to fetch reports from Redis"));
            let error_response = json!({
                "status": "error",
                "message": "Failed to fetch reports from Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let mut reports: Vec<ModelDeviationReport> = Vec::new();

    for item in reports_bytes {
        let report: ModelDeviationReport = match serde_json::from_slice(&item) {
            Ok(report) => report,
            Err(_) => {
                log_error(LogBuilder::system("Failed to deserialize report data"));
                let error_response = json!({
                    "status": "error",
                    "message": "Failed to deserialize report data",
                });
                return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
            }
        };

        // Apply buy/sell filters
        let pass_buy_sell_filter = match (query.filter_buy, query.filter_sell) {
            // If both filters are None or both are Some(true), include all
            (None, None) => true,
            (Some(true), Some(true)) => true,

            // If only buy filter is provided
            (Some(true), None) | (Some(true), Some(false)) => report.action == DeviationAction::Buy,

            // If only sell filter is provided
            (None, Some(true)) | (Some(false), Some(true)) => report.action == DeviationAction::Sell,

            // If "not buy" filter is provided without sell filter
            (Some(false), None) => report.action == DeviationAction::Sell,

            // If "not sell" filter is provided without buy filter
            (None, Some(false)) => report.action == DeviationAction::Buy,

            // If both are explicitly false, include nothing (edge case)
            (Some(false), Some(false)) => false,
        };

        // Apply portfolio filter if provided
        let pass_portfolio_filter = match &query.portfolio_id {
            Some(portfolio_id) => report.portfolio_id == *portfolio_id,
            None => true,
        };

        // Only include if report passes all filters
        let should_include = pass_buy_sell_filter && pass_portfolio_filter;

        if should_include {
            reports.push(report);
        }
    }

    // Get total count for pagination info
    let total_count: usize = match redis_conn.zcard(key.clone()).await {
        Ok(count) => count,
        Err(_) => {
            log_warn(LogBuilder::system(
                "Failed to get total count of reports from Redis. Using current report length as total count.",
            ));
            // If we can't get the count, use the current reports length as a fallback
            // This is not ideal but allows the API to continue working
            reports.len()
        }
    };

    // Calculate next offset (only if there are more results)
    let next_offset = if ((query.offset + limit) as usize) < total_count {
        Some(query.offset + limit)
    } else {
        None
    };

    let response = PaginatedResponse {
        reports,
        total_count,
        next_offset,
    };

    Ok::<(StatusCode, axum::Json<PaginatedResponse>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(response),
    ))
}
