use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use axum::{
    extract::{Query, State},
    http::{header, HeaderValue, StatusCode},
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    oms::deviation::Deviation,
};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportToChange {
    report_id: u64,
    select: bool,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetDeviationReportsQuery {
    id: String,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn download_full_report(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Query(query): Query<GetDeviationReportsQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Download Full Deviation Report",
            Action::DownloadFullReport,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id),
    );
    let deviation = Deviation::build(query.id.clone(), state.tenant_redis_url.clone());
    let reports = match deviation.get_reports_in_csv().await {
        Ok(reports) => reports,
        Err(err) => {
            log_error(
                LogBuilder::system(format!("Failed: get reports in CSV. Reason: {:?}", err).as_str())
                    .add_metadata("deviation_id", &query.id)
                    .add_metadata("error", &err.to_string()),
            );
            let error_response = json!({
                "status": "error",
                "message": format!("{}",err)
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    // Get a filename based on the deviation ID
    let filename = format!("deviation_report_{}.csv", query.id);

    // Create content disposition header value
    let content_disposition = format!("attachment; filename=\"{}\"", filename);

    log_info(
        LogBuilder::system("Successfully generated and prepared the full deviation report.")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("filename", &filename),
    );

    // Create response with appropriate headers for CSV download
    let response = (
        StatusCode::OK,
        [
            (header::CONTENT_TYPE, HeaderValue::from_static("text/csv")),
            (
                header::CONTENT_DISPOSITION,
                HeaderValue::from_str(&content_disposition).unwrap(),
            ),
        ],
        reports,
    );

    log_info(
        LogBuilder::system("RES: Full deviation is ready to download")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("filename", &filename),
    );

    Ok::<_, (StatusCode, axum::Json<serde_json::Value>)>(response)
}
