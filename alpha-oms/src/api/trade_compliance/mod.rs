use std::sync::Arc;

use axum::{routing::get, Router};

use crate::models::AppState;

mod compute_pre_trade;
mod get_compliance_reports;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/pre_trade_compliance", get(compute_pre_trade::trade_compliance))
        .route(
            "/get_pre_trade_compliance_report/:session_id",
            get(get_compliance_reports::get_compliance_reports),
        )
}
