use actlogica_logs::{builder::Log<PERSON>uilder, generator::generate_event_id, log_error, log_info, log_warn};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

use crate::{
    log_actions::Action,
    models::{AppState, TokenClaims},
    oms::trade_compliance::pre_trade::PreTradeCompliance,
};

#[derive(Serialize, Deserialize)]
pub struct GetComplianceReportQuery {
    limit: u64,
    offset: u64,
}

#[tracing::instrument(fields(module = "Trade-Order-Compliance", event_id = %generate_event_id()), skip_all)]
pub async fn get_compliance_reports(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(session_id): Path<Uuid>,
    Query(query): Query<GetComplianceReportQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(LogBuilder::user(
        user_id,
        user_name,
        "REQ: Get trade orders compliance report",
        Action::GetComplianceReport,
    ));

    let compliance_computer = PreTradeCompliance {
        app_state: state.clone(),
        session_id,
    };

    if query.limit > 100 {
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("You can only get 100 reports at a time reduce the limit"),
        });
        log_error(
            LogBuilder::system("You can only get 100 reports at a time reduce the limit")
                .add_metadata("qeury_limit", &query.limit.to_string())
                .add_metadata("query_offset", &query.offset.to_string()),
        );
        return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
    }

    match compliance_computer
        .get_compliance_report(query.offset, query.limit)
        .await
    {
        Ok(reports) => {
            log_info(LogBuilder::system("RES: trade order report fetched successfuly"));
            return Ok((StatusCode::OK, Json(reports)))},
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("{}",err),
            });
            log_error(
                LogBuilder::system("Failed: get compliance report")
                    .add_metadata("qeury_limit", &query.limit.to_string())
                    .add_metadata("query_offset", &query.offset.to_string())
                    .add_metadata("error", &err.to_string()),
            );
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };
}
