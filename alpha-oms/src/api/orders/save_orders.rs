use actlogica_logs::{builder::Log<PERSON>uild<PERSON>, generator::generate_event_id, log_error, log_info};
use std::sync::Arc;

use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use uuid::Uuid;

use crate::{
    log_actions::Action,
    models::{AppState, TokenClaims},
    oms::orders::save_order_from_session,
};

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn save_orders_handler(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(session_id): Path<Uuid>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(user_id, user_name, "REQ: Save orders", Action::SaveOrders)
            .add_metadata("session_id", &session_id.to_string()),
    );

    match save_order_from_session(session_id, state.clone(), tenant.sub.clone()).await {
        Ok(save) => {
            let success_response = serde_json::json!({
                "status": "success",
                "message": format!("Successfully saved the orders from the session id {}",session_id),
            });
            log_info(
                LogBuilder::system("RES: Orders saved successfully")
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("requested_by", user_name),
            );
            Ok((StatusCode::CREATED, Json(success_response)))
        }
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": err.to_string(),
            });
            log_error(
                LogBuilder::system("Failed to save orders")
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("requested_by", user_name)
                    .add_metadata("error", &err.to_string()),
            );
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)))
        }
    }
}
