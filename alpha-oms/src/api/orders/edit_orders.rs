use actlogica_logs::{builder::Log<PERSON><PERSON><PERSON>, generator::generate_event_id, log_error, log_info};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use std::sync::Arc;

use crate::{
    log_actions::Action,
    models::{update_order::UpdateOrder, AppState, TokenClaims},
    oms::orders::edit_order,
};
use serde::Deserialize;
use uuid::Uuid;

#[derive(Deserialize)]
pub struct EditOrderQuantityPayload {
    updates: Vec<UpdateOrder>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn edit_order_handler(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(session_id): Path<Uuid>,
    Json(payload): Json<EditOrderQuantityPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(user_id, user_name, "REQ: Edit orders", Action::EditOrders)
            .add_metadata("session_id", &session_id.to_string()),
    );

    match edit_order(session_id, state.tenant_redis_url.clone(), payload.updates).await {
        Ok(save) => {
            let success_response = serde_json::json!({
                "status": "success",
                "message": format!("Successfully updated the orders from the session id {}",session_id),
            });
            log_info(
                LogBuilder::system("RES: Order updated successfully")
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("created_by", user_name),
            );
            return Ok((StatusCode::CREATED, Json(success_response)));
        }
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": err.to_string(),
            });
            log_error(
                LogBuilder::system(format!("Failed to edit orders").as_str())
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("created_by", user_name)
                    .add_metadata("error", &err.to_string()),
            );
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }
}
