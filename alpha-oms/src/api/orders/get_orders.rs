use actlogica_logs::{builder::Log<PERSON><PERSON><PERSON>, generator::generate_event_id, log_error, log_info};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use std::sync::Arc;
use uuid::Uuid;

use crate::{
    log_actions::Action,
    models::{AppState, TokenClaims},
    oms::orders::get_orders_in_session,
};

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_orders_handler(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(session_id): Path<Uuid>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(user_id, user_name, "REQ: Get orders", Action::GetOrders)
            .add_metadata("session_id", &session_id.to_string()),
    );

    match get_orders_in_session(session_id, state.tenant_redis_url.clone()).await {
        Ok(orders) => {
            let success_response = serde_json::json!({
                    "orders":orders
            });
            log_info(
                LogBuilder::system("RES: Order updated successfully")
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("requested_by", user_name),
            );
            return Ok((StatusCode::CREATED, Json(success_response)));
        }
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": err.to_string(),
            });
            log_error(
                LogBuilder::system("Failed to get orders")
                    .add_metadata("session_id", &session_id.to_string())
                    .add_metadata("requested_by", user_name)
                    .add_metadata("error", &err.to_string()),
            );
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }
}
