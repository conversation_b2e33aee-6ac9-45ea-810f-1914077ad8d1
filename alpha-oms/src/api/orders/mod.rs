use std::sync::Arc;

use axum::{
    routing::{get, post, put},
    Router,
};
use edit_orders::edit_order_handler;
use get_orders::get_orders_handler;
use save_orders::save_orders_handler;

use crate::models::AppState;

pub mod edit_orders;
pub mod get_orders;
pub mod save_orders;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/get_orders/:session_id", get(get_orders_handler))
        .route("/save_orders/:session_id", get(save_orders_handler))
        .route("/edit_orders/:session_id", put(edit_order_handler))
}
