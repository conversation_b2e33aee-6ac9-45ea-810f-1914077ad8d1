use std::sync::Arc;

use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};

use alpha_core_db::schema::restricted_stocks::{
    client_restricted_stocks::RestrictedStockForClients, org_restricted_stocks::RestrictedStockForOrganisation,
};
use alpha_utils::{
    constants::RedisKey,
    group_by_multiple_fields,
    types::{SecurityDetails, SecurityTypeForPrice},
};
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};

use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use serde_json::json;

use crate::{
    models::{AppState, TokenClaims},
    oms::deviation::{
        deviation::{
            compute_deviation_for_all_clients_for_all_securities,
            compute_deviation_for_all_clients_for_single_security,
            compute_deviation_for_specific_clients_for_all_securites,
            compute_deviation_for_specific_clients_for_single_security,
        },
        deviation_compute_order::prepare_customised_orders_for_deviation,
    },
    types::{
        deviation::{DeviationAction, ModelDeviationReport},
        CustomisedOrder,
    },
};

#[derive(Serialize, Deserialize)]
pub struct DeviationReportQuery {
    pub all_clients: bool,
    pub all_securities: bool,
    pub non_model: Option<bool>,
    pub deviation_action: Option<DeviationAction>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviationReportPayload {
    pub model_id: String,
    pub client_strategy_codes: Vec<String>,
    pub isin: Option<String>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviationReportResponse {
    pub session_id: String,
    pub reports: Vec<ModelDeviationReport>,
    pub total: usize,
}

#[tracing::instrument(fields(module = "Deviation-trade", event_id = %generate_event_id()), skip_all)]
pub async fn build_deviation_reports(
    State(state): State<Arc<AppState>>,
    Extension(tenant): Extension<TokenClaims>,
    Query(query): Query<DeviationReportQuery>,
    Json(payload): Json<DeviationReportPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;
    let model_id = payload.model_id.to_string();

    let action = match query.deviation_action {
        Some(DeviationAction::Buy) => "Buy".to_string(),
        Some(DeviationAction::Sell) => "Sell".to_string(),
        None => "None".to_string(),
    };

    let client_strategy_codes_string = format!(
        "[ {} ]",
        payload
            .client_strategy_codes
            .iter()
            .map(|s| format!("\'{}\'", s))
            .collect::<Vec<String>>()
            .join(", ")
    );
    let isin = if let Some(i) = payload.isin.clone() {
        i
    } else {
        "None".to_string()
    };

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Build Deviation report",
            Action::BuildDeviationReport,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("model_id", &model_id)
        .add_metadata("deviaction_action", &action)
        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
        .add_metadata("isin", &isin),
    );

    if !query.all_securities && payload.isin.is_none() {
        log_error(
            LogBuilder::system("ISIN is required when not querying all securities")
                .add_metadata("user_role", &tenant.role.to_string())
                .add_metadata("model_id", &model_id)
                .add_metadata("deviaction_action", &action)
                .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                .add_metadata("isin", &isin),
        );
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({
                "status": "error",
                "message": "ISIN is required when not querying all securities"
            })),
        ));
    }

    if !query.all_clients && payload.client_strategy_codes.len() == 0 {
        log_error(
            LogBuilder::system("No client_strategy_code provided")
                .add_metadata("user_role", &tenant.role.to_string())
                .add_metadata("model_id", &model_id)
                .add_metadata("deviaction_action", &action)
                .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                .add_metadata("isin", &isin),
        );
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({
                "status": "error",
                "message": "Client Strategy Code is required when not querying all clients"
            })),
        ));
    }

    let result = match (query.all_clients, query.all_securities) {
        (true, true) => compute_deviation_for_all_clients_for_all_securities(
            state.master_redis_url.clone(),
            state.db.clone(),
            payload.model_id,
        )
        .await
        .map(|model_deviation_reports| {
            log_info(
                LogBuilder::system("Success: Computed deviation for all clients and all securities")
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
            );
            model_deviation_reports
        })
        .map_err(|e| {
            log_error(
                LogBuilder::system(
                    format!("Failed: compute deviation, all clients for all securities {:?}", e).as_str(),
                )
                .add_metadata("user_role", &tenant.role.to_string())
                .add_metadata("model_id", &model_id)
                .add_metadata("deviaction_action", &action)
                .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                .add_metadata("isin", &isin),
            );
            (
                StatusCode::BAD_REQUEST,
                Json(json!({
                    "status": "error",
                    "message": format!("Failed to Compute" )
                })),
            )
        }),
        (true, false) => {
            compute_deviation_for_all_clients_for_single_security(
                state.master_redis_url.clone(),
                state.db.clone(),
                payload.model_id,
                payload.isin.unwrap(), // Safe due to early validation
            )
            .await
            .map(|model_deviation_reports| {
                log_info(
                    LogBuilder::system("Success: Computed deviation for all clients and single security")
                        .add_metadata("user_role", &tenant.role.to_string())
                        .add_metadata("model_id", &model_id)
                        .add_metadata("deviaction_action", &action)
                        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                        .add_metadata("isin", &isin),
                );
                model_deviation_reports
            })
            .map_err(|e| {
                log_error(
                    LogBuilder::system(
                        format!("Failed: compute deviation, all clients for single security {:?}", e).as_str(),
                    )
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
                );
                (
                    StatusCode::BAD_REQUEST,
                    Json(json!({
                        "status": "error",
                        "message": format!("Failed to Compute" )
                    })),
                )
            })
        }
        (false, true) => compute_deviation_for_specific_clients_for_all_securites(
            state.master_redis_url.clone(),
            state.db.clone(),
            payload.model_id,
            &payload.client_strategy_codes,
        )
        .await
        .map(|model_deviation_reports| {
            log_info(
                LogBuilder::system("Success: Computed deviation for specific clients and all securities")
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
            );
            model_deviation_reports
        })
        .map_err(|e| {
            log_error(
                LogBuilder::system(
                    format!("Failed: compute deviation, specific client all securities {:?}", e).as_str(),
                )
                .add_metadata("user_role", &tenant.role.to_string())
                .add_metadata("model_id", &model_id)
                .add_metadata("deviaction_action", &action)
                .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                .add_metadata("isin", &isin),
            );
            (
                StatusCode::BAD_REQUEST,
                Json(json!({
                    "status": "error",
                    "message": format!("Failed to Compute" )
                })),
            )
        }),
        (false, false) => {
            compute_deviation_for_specific_clients_for_single_security(
                state.master_redis_url.clone(),
                state.db.clone(),
                payload.model_id,
                &payload.client_strategy_codes,
                payload.isin.unwrap(), // Safe due to early validation
            )
            .await
            .map(|model_deviation_reports| {
                log_info(
                    LogBuilder::system("Success: Computed deviation for specific clients and single security")
                        .add_metadata("user_role", &tenant.role.to_string())
                        .add_metadata("model_id", &model_id)
                        .add_metadata("deviaction_action", &action)
                        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                        .add_metadata("isin", &isin),
                );
                model_deviation_reports
            })
            .map_err(|e| {
                log_error(
                    LogBuilder::system(
                        format!("Failed: compute deviation, single client single security {:?}", e).as_str(),
                    )
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
                );

                (
                    StatusCode::BAD_REQUEST,
                    Json(json!({
                        "status": "error",
                        "message": format!("Failed to Compute" )
                    })),
                )
            })
        }
    };

    match result {
        Ok(deviation_reports) => {
            let unique_deviation_id = uuid::Uuid::new_v4();

            // Get Redis connection
            let mut redis_conn = match state.tenant_redis_url.get().await {
                Ok(conn) => conn,
                Err(err) => {
                    log_error(LogBuilder::system(
                        format!("Failed: Redis connection: {:?}", err).as_str(),
                    ));

                    let error_response = json!({
                        "status": "error",
                        "message": "Failed to get Redis connection",
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            // Format the key
            let key = format!(
                "{}:{}",
                RedisKey::DeviationReport.to_string(),
                unique_deviation_id.to_string(),
            );

            let mut deviation_reports: Vec<ModelDeviationReport> = deviation_reports
                .into_iter()
                .filter(|f| {
                    if f.deviation_pct == 0f64 {
                        return false;
                    }

                    if let Some(deviation) = &query.deviation_action {
                        if &f.action == deviation {
                            return true;
                        } else {
                            return false;
                        }
                    }

                    if let Some(non_model) = query.non_model {
                        if non_model {
                            if f.model_weight == 0f64 {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            if f.portfolio_weight == 0f64 {
                                return true;
                            } else {
                                return false;
                            }
                        }
                    }

                    return true;
                })
                .collect();

            if deviation_reports.is_empty() {
                log_info(
                    LogBuilder::system("RES: Deviation computed empty result")
                        .add_metadata("user_role", &tenant.role.to_string())
                        .add_metadata("model_id", &model_id)
                        .add_metadata("deviaction_action", &action)
                        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                        .add_metadata("isin", &isin),
                );
                return Ok((
                    StatusCode::ACCEPTED,
                    Json(DeviationReportResponse {
                        reports: vec![],
                        session_id: unique_deviation_id.to_string(),
                        total: 0,
                    }),
                ));
            }

            let client_ids = deviation_reports
                .iter()
                .map(|f| f.client_id.clone())
                .collect::<Vec<String>>();

            let mut conn = match state.db.get().await {
                Ok(conn) => conn,
                Err(err) => {
                    log_error(LogBuilder::system(format!("Failed: db connection: {:?}", err).as_str()));
                    let error_response = json!({
                        "status": "error",
                        "message": "Failed to get DB connection",
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let res_stocks_org = match RestrictedStockForOrganisation::get_all(&mut conn).await {
                Ok(conn) => conn,
                Err(err) => {
                    log_error(
                        LogBuilder::system(
                            format!("Failed: Get restricted stocks for organisation: {:?}", err).as_str(),
                        )
                        .add_metadata("user_role", &tenant.role.to_string())
                        .add_metadata("model_id", &model_id)
                        .add_metadata("deviaction_action", &action)
                        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                        .add_metadata("isin", &isin),
                    );
                    let error_response = json!({
                        "status": "error",
                        "message": "Failed to get Restricted Stocks Details",
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let res_stocks_clients = match RestrictedStockForClients::get_all_by_client_ids(&mut conn, &client_ids)
                .await
            {
                Ok(conn) => conn,
                Err(err) => {
                    log_error(
                        LogBuilder::system(format!("Failed: Get restricted stocks by clients ids: {:?}", err).as_str())
                            .add_metadata("user_role", &tenant.role.to_string())
                            .add_metadata("model_id", &model_id)
                            .add_metadata("deviaction_action", &action)
                            .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                            .add_metadata("isin", &isin),
                    );
                    let error_response = json!({
                        "status": "error",
                        "message": "Failed to get Restricted Stocks Details",
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };

            let res_stock_for_clients_grouped = group_by_multiple_fields(res_stocks_clients, |res| {
                (
                    res.client_id.clone(),
                    res.isin_restricted_security.clone(),
                    res.exchange_restricted_security.clone(),
                )
            });

            //Apply Restricted stocks to all the entries
            for report in &mut deviation_reports {
                //Check if res stock is in client level
                report.selected = true;
                if let Some(res_stock) = res_stock_for_clients_grouped.get(&(
                    report.client_id.clone(),
                    report.isin.clone(),
                    report.exchange.clone(),
                )) {
                    let restricted_stock = res_stock[0].clone();
                    //Check if alternative exist
                    if let (Some(alt_isin), Some(name), Some(exchange)) = (
                        restricted_stock.isin_alternative_security,
                        restricted_stock.alternative_security_identifier,
                        restricted_stock.exchange_alternative_security,
                    ) {
                        //Check if the alternative is restricted at org level
                        for res_stock in &res_stocks_org {
                            if alt_isin == res_stock.isin_restricted_security
                                && exchange == res_stock.exchange_restricted_security
                            {
                                report.selected = false;
                                break;
                            } else {
                                if report.action == DeviationAction::Sell {
                                    report.selected = false;
                                } else {
                                    let alt_stock_details = SecurityDetails::build(
                                        &mut redis_conn,
                                        state.master_db.clone(),
                                        alt_isin.clone(),
                                        SecurityTypeForPrice::Equity,
                                        &exchange,
                                    )
                                    .await;

                                    match alt_stock_details {
                                        Ok(sec) => {
                                            report.isin = alt_isin.clone();
                                            report.exchange = exchange.clone();
                                            report.security_name = name.clone();
                                            report.symbol = sec.get_symbol();
                                            report.selected = true;

                                            log_info(
                                                LogBuilder::system("Success: Retrieve Alternative Stock Details")
                                                    .add_metadata("user_role", &tenant.role.to_string())
                                                    .add_metadata("model_id", &model_id)
                                                    .add_metadata("deviaction_action", &action)
                                                    .add_metadata(
                                                        "client_strategy_codes",
                                                        &client_strategy_codes_string,
                                                    )
                                                    .add_metadata("isin", &isin)
                                                    .add_metadata("alt_isin", &alt_isin)
                                                    .add_metadata("exchange", &exchange),
                                            );
                                        }
                                        Err(err) => {
                                            log_error(
                                                LogBuilder::system(
                                                    format!("Failed: Get Alternative Stock Details: {:?}", err)
                                                        .as_str(),
                                                )
                                                .add_metadata("user_role", &tenant.role.to_string())
                                                .add_metadata("model_id", &model_id)
                                                .add_metadata("deviaction_action", &action)
                                                .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                                                .add_metadata("isin", &isin),
                                            );
                                            let error_response = json!({
                                                "status": "error",
                                                "message": "Failed to get Alternative Stock Details",
                                            });
                                            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                                        }
                                    }
                                }
                            }
                        }

                        if report.selected == true {
                            //Check if the alt is again restricted at client lvl
                            if let Some(_res_stock) = res_stock_for_clients_grouped.get(&(
                                report.client_id.clone(),
                                alt_isin.clone(),
                                exchange.clone(),
                            )) {
                                report.selected = false;
                            }
                        }
                    } else {
                        report.selected = false;
                    }
                } else {
                    //Check if its in restricted stocks for Org
                    for res_org in &res_stocks_org {
                        if res_org.isin_restricted_security == report.isin
                            && res_org.exchange_restricted_security == report.exchange
                        {
                            //Check if alternative exist
                            if let (Some(alt_isin), Some(name), Some(exchange)) = (
                                res_org.isin_alternative_security.clone(),
                                res_org.alternative_security_identifier.clone(),
                                res_org.exchange_alternative_security.clone(),
                            ) {
                                //Check if alternative is restricted at client level
                                if res_stock_for_clients_grouped.contains_key(&(
                                    report.client_id.clone(),
                                    alt_isin.clone(),
                                    exchange.clone(),
                                )) {
                                    report.selected = false;
                                    break;
                                } else {
                                    if report.action == DeviationAction::Sell {
                                        report.selected = false;
                                    } else {
                                        let alt_stock_details = SecurityDetails::build(
                                            &mut redis_conn,
                                            state.master_db.clone(),
                                            alt_isin.clone(),
                                            SecurityTypeForPrice::Equity,
                                            &exchange,
                                        )
                                        .await;

                                        match alt_stock_details {
                                            Ok(sec) => {
                                                report.isin = alt_isin.clone();
                                                report.exchange = exchange.clone();
                                                report.security_name = name.clone();
                                                report.symbol = sec.get_symbol();
                                                report.selected = true;
                                                log_info(
                                                    LogBuilder::system("Success: Fetch Alternate Stock Details")
                                                        .add_metadata("user_role", &tenant.role.to_string())
                                                        .add_metadata("model_id", &model_id)
                                                        .add_metadata("deviaction_action", &action)
                                                        .add_metadata(
                                                            "client_strategy_codes",
                                                            &client_strategy_codes_string,
                                                        )
                                                        .add_metadata("isin", &isin)
                                                        .add_metadata("alt_isin", &alt_isin)
                                                        .add_metadata("exchange", &exchange),
                                                );
                                            }
                                            Err(err) => {
                                                log_error(
                                                    LogBuilder::system(
                                                        format!("Failed: Get Alternative Stock Details: {:?}", err)
                                                            .as_str(),
                                                    )
                                                    .add_metadata("user_role", &tenant.role.to_string())
                                                    .add_metadata("model_id", &model_id)
                                                    .add_metadata("deviaction_action", &action)
                                                    .add_metadata(
                                                        "client_strategy_codes",
                                                        &client_strategy_codes_string,
                                                    )
                                                    .add_metadata("isin", &isin),
                                                );
                                                let error_response = json!({
                                                    "status": "error",
                                                    "message": "Failed to get Alternative Stock Details",
                                                });
                                                return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                                            }
                                        }
                                    }
                                }

                                if report.selected == true {
                                    //Check if the alternative is restricted is org again
                                    for res_org in &res_stocks_org {
                                        if res_org.isin_restricted_security == report.isin
                                            && res_org.exchange_restricted_security == report.exchange
                                        {
                                            report.selected = false;
                                            break;
                                        }
                                    }
                                }
                            } else {
                                //No alternative found
                                report.selected = false;
                            }
                        }
                    }
                }
            }

            log_info(
                LogBuilder::system("Saving the Reports to Cache")
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
            );

            // Create a pipeline
            let mut pipe = deadpool_redis::redis::pipe();
            let mut total_items = 0;
            let mut index = 0;

            let mut bulk_items: Vec<(u64, Vec<u8>)> = Vec::new();

            // Prepare bulk data for zadd
            if !deviation_reports.is_empty() {
                for report in &mut deviation_reports {
                    if report.selected {
                        report.id = index;
                        //Make selected false here
                        report.selected = false;
                        let serialized = serde_json::to_vec(&report).unwrap();
                        bulk_items.push((report.id, serialized));
                        index += 1;
                    }
                }
                total_items = bulk_items.len();
            }

            if bulk_items.is_empty() {
                log_info(
                    LogBuilder::system("RES: No selected deviation reports to save in cache")
                        .add_metadata("user_role", &tenant.role.to_string())
                        .add_metadata("model_id", &model_id)
                        .add_metadata("deviaction_action", &action)
                        .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                        .add_metadata("isin", &isin),
                );
                return Ok((
                    StatusCode::ACCEPTED,
                    Json(DeviationReportResponse {
                        reports: vec![],
                        session_id: unique_deviation_id.to_string(),
                        total: 0,
                    }),
                ));
            }

            pipe.cmd("ZADD").arg(&key).arg(&bulk_items);
            // Set expiration in the same pipeline
            pipe.expire(&key, 5 * 60);

            // Execute the pipeline atomically
            pipe.query_async::<deadpool_redis::redis::Value>(&mut *redis_conn)
                .await
                .map_err(|_| {
                    log_error(LogBuilder::system(
                        "Failed: Save the report to cache. Redis-Connection error",
                    ));
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(json!({
                            "status": "error",
                            "message": format!("Failed to Save into Cache" )
                        })),
                    )
                })?;

            // Get first 100 reports
            let start_seq = 0;
            let end_seq = (start_seq + 100) - 1;

            log_info(
                LogBuilder::system("Getting First 100 Reports from the Cache")
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
            );
            let items_str: Vec<String> = redis_conn
                .zrangebyscore::<String, u64, u64, Vec<String>>(key.clone(), start_seq, end_seq)
                .await
                .map_err(|_| {
                    log_error(LogBuilder::system("Failed: Redis Connection"));
                    return (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(json!({
                            "status": "error",
                            "message": format!("Failed to Save into Cache" )
                        })),
                    );
                })?;

            let mut reports: Vec<ModelDeviationReport> = Vec::new();

            for item in items_str {
                let report: ModelDeviationReport = serde_json::from_str(&item).map_err(|err| {
                    log_error(
                        LogBuilder::system(format!("Failed: deserialize deviation report: {:?}", err).as_str())
                            .add_metadata("item", &item),
                    );
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(json!({
                            "status": "error",
                            "message": "Failed to deserialize deviation report",
                        })),
                    )
                })?;
                reports.push(report);
            }

            log_info(
                LogBuilder::system(format!("RES: Total Reports Fetched = {}", reports.len()).as_str())
                    .add_metadata("total_reports", &reports.len().to_string())
                    .add_metadata("user_role", &tenant.role.to_string())
                    .add_metadata("model_id", &model_id)
                    .add_metadata("deviaction_action", &action)
                    .add_metadata("client_strategy_codes", &client_strategy_codes_string)
                    .add_metadata("isin", &isin),
            );
            Ok((
                StatusCode::ACCEPTED,
                Json(DeviationReportResponse {
                    reports,
                    session_id: unique_deviation_id.to_string(),
                    total: total_items,
                }),
            ))
        }
        Err(_error_response) => Err(_error_response),
    }
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviationOrdersResponse {
    pub orders: Vec<CustomisedOrder>,
    pub total: usize,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DeviationComputeOrderPayload {
    pub session_id: String,
    pub compute_all: bool,
}

/// Compute The Orders from Deviation Reports
/// Deiation Reports should be taken from Redis cache
#[tracing::instrument(fields(module = "Deviation-trade", event_id = %generate_event_id()), skip_all)]
pub async fn compute_orders_for_deviation_reports(
    State(state): State<Arc<AppState>>,
    Extension(tenant): Extension<TokenClaims>,
    Json(payload): Json<DeviationComputeOrderPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            &user_id,
            &user_name,
            "REQ: Compute orders for Deviation reports",
            Action::ComputeOrderForDeviationReport,
        )
        .add_metadata("session_id", &payload.session_id),
    );

    let key = format!(
        "{}:{}",
        RedisKey::DeviationReport.to_string(),
        payload.session_id.clone()
    );

    let mut redis_conn = match state.tenant_redis_url.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            let error_response = json!({
                "status": "error",
                "message": "Failed to get Redis connection",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    // Get all elements from the sorted set (0 to -1 means all elements)
    let results: Vec<String> = match redis_conn.zrange(key.clone(), 0, -1).await {
        Ok(res) => res,
        Err(err) => {
            log_error(
                LogBuilder::system(format!("Failed: Fetching deviation reports from Redis: {:?}", err).as_str())
                    .add_metadata("session_id", &payload.session_id),
            );
            let error_response = json!({
                "status": "error",
                "message": "Failed to fetch deviation reports from Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };
    let mut reports: Vec<ModelDeviationReport> = Vec::new();

    for res in results {
        let report: ModelDeviationReport = match serde_json::from_str(&res) {
            Ok(report) => report,
            Err(err) => {
                log_error(
                    LogBuilder::system(format!("Failed: deserialize deviation report: {:?}", err).as_str())
                        .add_metadata("session_id", &payload.session_id),
                );
                continue;
            }
        };

        if payload.compute_all || report.selected {
            reports.push(report);
        }
    }

    // Deserialize each item back to DeviationReport
    let result =
        prepare_customised_orders_for_deviation(state.tenant_redis_url.clone(), state.master_db.clone(), reports)
            .await
            .map(|orders| {
                log_info(
                    LogBuilder::system(format!("Success: Prepare customised orders for deviation").as_str())
                        .add_metadata("session_id", &payload.session_id),
                );
                orders
            })
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed: prepare customised orders for deviation: {:?}", e).as_str())
                        .add_metadata("session_id", &payload.session_id),
                );
                (
                    StatusCode::BAD_REQUEST,
                    Json(json!({
                        "status": "error",
                        "message": format!("Failed to Compute: {:?}", e)
                    })),
                )
            });

    match result {
        Ok(orders) => {
            log_info(
                LogBuilder::system(format!("Computed Orders Count = {}", orders.len()).as_str())
                    .add_metadata("session_id", &payload.session_id),
            );
            let mut redis_conn = match state.tenant_redis_url.get().await {
                Ok(conn) => conn,
                Err(err) => {
                    log_error(LogBuilder::system(
                        format!("Failed: Redis connection: {:?}", err).as_str(),
                    ));
                    let error_response = json!({
                        "status": "error",
                        "message": "Failed to get Redis connection",
                    });
                    return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
                }
            };
            let key = format!(
                "{}:{}",
                RedisKey::DeviationOrders.to_string(),
                payload.session_id.to_string(),
            );

            // Create a pipeline
            let mut pipe = deadpool_redis::redis::pipe();

            // Delete existing key and add all orders in pipeline
            pipe.del(&key);

            for order in &orders {
                pipe.zadd(&key, serde_json::to_vec(&order).unwrap(), order.id);
            }

            // Set expiration time in the same pipeline
            pipe.expire(&key, 5 * 60);

            // Execute the pipeline atomically
            pipe.query_async::<deadpool_redis::redis::Value>(&mut redis_conn)
                .await
                .map_err(|e| {
                    log_error(
                        LogBuilder::system(format!("Failed: Save report to cache. REASON :{}", e).as_str())
                            .add_metadata("session_id", &payload.session_id),
                    );
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(json!({
                            "status": "error",
                            "message": format!("Failed to Save into Cache" )
                        })),
                    )
                })?;

            // Get first 100 reports - this can be in a separate call
            let start_seq = 0;
            let end_seq = start_seq + 99;

            log_info(
                LogBuilder::system("Getting First 100 Orders from the Cache")
                    .add_metadata("session_id", &payload.session_id),
            );
            let items_str: Vec<String> = match redis_conn
                .zrangebyscore::<String, u64, u64, Vec<String>>(key.clone(), start_seq, end_seq)
                .await
            {
                Ok(items) => items,
                Err(err) => {
                    log_error(
                        LogBuilder::system(format!("Failed: Fetching orders from Redis: {:?}", err).as_str())
                            .add_metadata("session_id", &payload.session_id),
                    );
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(json!({
                            "status": "error",
                            "message": "Failed to fetch orders from Redis",
                        })),
                    ));
                }
            };

            let mut required_orders: Vec<CustomisedOrder> = Vec::new();

            for item in items_str {
                let report: CustomisedOrder = match serde_json::from_str(&item) {
                    Ok(report) => report,
                    Err(_err) => {
                        log_error(
                            LogBuilder::system(format!("Failed: deserialize customised order").as_str())
                                .add_metadata("session_id", &payload.session_id)
                                .add_metadata("str_item", &item),
                        );
                        continue;
                    }
                };
                required_orders.push(report);
            }

            log_info(
                LogBuilder::system(format!("RES: Total Orders Fetched = {}", orders.len()).as_str())
                    .add_metadata("total_orders", &required_orders.len().to_string())
                    .add_metadata("session_id", &payload.session_id),
            );
            Ok((
                StatusCode::ACCEPTED,
                Json(DeviationOrdersResponse {
                    orders: required_orders,
                    total: orders.len(),
                }),
            ))
        }
        Err(_error_response) => Err(_error_response),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::oms::deviation::deviation::compute_deviation_for_all_clients_for_all_securities;
    use alpha_core_db::redis::connect_to_redis;

    #[tokio::test]
    async fn test_all_deviation() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let matser_pool = alpha_core_db::connection::connect_to_master_data(10).await;
        let redis_pool = connect_to_redis().await;
        let model_id = String::from("9927dad8a31f43a591199732db5abb65");
        let capital = 43534f64;
        let created_by = String::from("Athul");

        let foo = compute_deviation_for_all_clients_for_all_securities(redis_pool, pool, model_id)
            .await
            .unwrap();

        println!("{:?}", foo);
    }
}
