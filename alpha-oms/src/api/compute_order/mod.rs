use std::sync::Arc;

use axum::{
    routing::{get, post},
    Router,
};

use crate::models::AppState;

pub mod additional_deployment;
pub mod capital_withdrawal;
pub mod customised_trade;
pub mod deviation_trade;
pub mod new_portfolio;
pub mod trade_idea;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route(
            "/compute_order_for_additional_deployment",
            get(additional_deployment::compute_orders_for_additional_capital_deployments),
        )
        .route(
            "/compute_order_for_capital_withdrawal",
            get(capital_withdrawal::compute_orders_for_capital_withdrawals_route),
        )
        .route(
            "/compute_order_for_new_portfolio",
            get(new_portfolio::compute_orders_for_new_portfolio_route),
        )
        .route(
            "/compute_order_for_buy_trade_idea/:id",
            get(trade_idea::compute_orders_for_buy_trade_idea),
        )
        .route(
            "/compute_order_for_sell_trade_idea/:id",
            get(trade_idea::compute_orders_for_sell_trade_idea),
        )
        .route("/save_trade_idea/:id", post(trade_idea::save_trade_idea))
        .route(
            "/compute_orders_for_customised_trade",
            post(customised_trade::compute_orders_for_customised_trade),
        )
}
