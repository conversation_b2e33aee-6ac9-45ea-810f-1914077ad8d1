use std::sync::Arc;

use crate::{log_actions::Action, types::ComputeOrderRsp};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};

use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};

use crate::{
    models::{AppState, TokenClaims},
    oms::OrderComputer,
    types::{ClientComputedOrders, CustomisedOrderRequest},
};
use serde::Deserialize;
use serde_json::Value;

#[derive(Deserialize)]
pub struct CustomisedOrderCompute {
    pub orders: Vec<CustomisedOrderRequest>,
}

#[tracing::instrument(fields(module = "Customised-trade", event_id = %generate_event_id()), skip_all)]
pub async fn compute_orders_for_customised_trade(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): Json<CustomisedOrderCompute>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Compute orders for customised trade",
            Action::ComputeOrderForCustomisedTrade,
        )
        .add_metadata("user_role", &tenant.role.to_string()),
    );
    let order = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        tenant.name.clone(),
        0f64,
    );

    let (session_id, orders) = order
        .compute_order_for_custom_trade(payload.orders)
        .await
        .map_err(|_e| {
            log_error(
                LogBuilder::system("Failed to compute order for capital withdrawl routes")
                    .add_metadata("tenant", &tenant.tenant)
                    .add_metadata("user_role", &tenant.role.to_string()),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Database error:"),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;
    log_info(
        LogBuilder::system("RES: Success. compute orders for customised trade")
            .add_metadata("tenant", &tenant.tenant)
            .add_metadata("user_role", &tenant.role.to_string()),
    );
    Ok::<(StatusCode, Json<ComputeOrderRsp>), (StatusCode, Json<Value>)>((
        StatusCode::CREATED,
        Json(ComputeOrderRsp { session_id, orders }),
    ))
}
