use actlogica_logs::builder::LogAction;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub(crate) enum Action {
    ComputeOrderForAdditionalCapital,
    ComputeOrderForCapitalWithdrawalRoute,
    ComputeOrderForCustomisedTrade,
    ComputeOrderForNewPortfolioRoute,
    ComputeOrderForBuyTradeIdea,
    ComputeOrderForSellTradeIdea,
    SaveTradeIdea,
    BuildDeviationReport,
    ComputeOrderForDeviationReport,
    SaveDeviationOrder,
    SaveDeviationReports,
    GetDeviationComputedOrder, 
    GetDeviationReports,
    UpdateQuantityInComputedOrders,
    DeleteDeviationComputedOrders,
    ValidateDeviationOrders,
    GetClientPortfolioOrders,
    DownloadFullReport,
    GetDeviationReportsByFilters,
    EditOrders,
    GetOrders,
    SaveOrders,
    ComputePreTrade,
    GetComplianceReport
}

impl LogAction for Action {}

impl ToString for Action {
    fn to_string(&self) -> String {
        match self {
            Action::ComputeOrderForAdditionalCapital => "ComputeOrderForAdditionalCapital".to_string(),
            Action::ComputeOrderForCapitalWithdrawalRoute => "ComputeOrderForCapitalWithdrawalRoute".to_string(),
            Action::ComputeOrderForCustomisedTrade => "ComputeOrderForCustomisedTrade".to_string(),
            Action::ComputeOrderForNewPortfolioRoute => "ComputeOrderForNewPortfolioRoute".to_string(),
            Action::ComputeOrderForBuyTradeIdea => "ComputeOrderForBuyTradeIdea".to_string(),
            Action::ComputeOrderForSellTradeIdea => "ComputeOrderForSellTradeIdea".to_string(),
            Action::SaveTradeIdea => "SaveTradeIdea".to_string(),
            Action::BuildDeviationReport => "BuildDeviationReport".to_string(),
            Action::ComputeOrderForDeviationReport => "ComputeOrderForDeviationReport".to_string(),
            Action::SaveDeviationOrder => "SaveDeviationOrder".to_string(),
            Action::SaveDeviationReports => "SaveDeviationReports".to_string(),
            Action::GetDeviationComputedOrder => "GetDeviationComputedOrder".to_string(),
            Action::GetDeviationReports => "GetDeviationReports".to_string(),
            Action::UpdateQuantityInComputedOrders => "UpdateQuantityInComputedOrders".to_string(),
            Action::DeleteDeviationComputedOrders => "DeleteDeviationComputedOrders".to_string(),
            Action::ValidateDeviationOrders => "ValidateDeviationOrders".to_string(),
            Action::GetClientPortfolioOrders => "GetClientPortfolioOrders".to_string(),
            Action::DownloadFullReport => "DownloadFullReport".to_string(),
            Action::GetDeviationReportsByFilters => "GetDeviationReportsByFilters".to_string(),
            Action::EditOrders => "EditOrders".to_string(),
            Action::GetOrders => "GetOrders".to_string(),
            Action::SaveOrders => "SaveOrders".to_string(),
            Action::ComputePreTrade => "ComputePreTrade".to_string(),
            Action::GetComplianceReport => "GetComplianceReport".to_string()
        }
    }
}
