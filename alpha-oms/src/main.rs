use actlogica_logs::{
    builder::LogBuilder,
    log_error, log_info,
    setting::{init_logger, LogOutput},
};
use alpha_oms::{
    api::{
        self,
        compute_order::deviation_trade::{build_deviation_reports, compute_orders_for_deviation_reports},
        deviation::{
            orders::{
                delete_computed_orders, get_deviation_computed_orders::get_deviation_computed_orders,
                save_deviation_orders::save_deviation_orders,
                update_quantity_in_computed_orders::update_quantity_in_computed_orders, validate_orders,
            },
            reports::{get_deviation_reports::get_deviation_reports, save_deviation_report::save_deviation_reports},
        },
    },
    middleware,
    models::AppState,
};
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tower_http::cors::{Any, CorsLayer};
use tracing_subscriber::filter::LevelFilter;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    if let Err(err) = init_logger("Order-Management-Service", LevelFilter::INFO, LogOutput::StdOut).await {
        log_error(LogBuilder::system("Failed to initialize logger").add_metadata("error", &err.to_string()));
    }

    log_info(LogBuilder::system("Logger service initialized in OMS"));

    let app_state = Arc::new(AppState::new().await);

    let cors = CorsLayer::new().allow_origin(Any).allow_headers(Any).allow_methods(Any);

    let app = Router::new()
        .nest("/order", api::compute_order::router())
        .nest("/orders", api::orders::router())
        .nest("/compliance", api::trade_compliance::router())
        .layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            middleware::auth,
        ))
        .route(
            "/order/deviation/build_deviation_report",
            post(build_deviation_reports).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviation/compute_orders_for_deviation_reports",
            post(compute_orders_for_deviation_reports).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviation/save_orders",
            post(save_deviation_orders).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/get_deviation_reports",
            post(get_deviation_reports).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/save_deviation_reports",
            post(save_deviation_reports).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/get_deviation_computed_orders",
            post(get_deviation_computed_orders).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/update_quantity_in_computed_orders",
            post(update_quantity_in_computed_orders).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/delete_computer_orders",
            post(delete_computed_orders::delete_deviation_computed_orders).route_layer(
                axum::middleware::from_fn_with_state(app_state.clone(), middleware::auth),
            ),
        )
        .route(
            "/order/deviations/validate_deviation_orders",
            post(validate_orders::validate_deviation_orders).route_layer(axum::middleware::from_fn_with_state(
                app_state.clone(),
                middleware::auth,
            )),
        )
        .route(
            "/order/deviations/get_client_portfolio_orders",
            post(alpha_oms::api::deviation::orders::get_client_portfolio_orders::get_client_portfolio_orders)
                .route_layer(axum::middleware::from_fn_with_state(
                    app_state.clone(),
                    middleware::auth,
                )),
        )
        .route(
            "/order/deviations/download_full_report",
            get(alpha_oms::api::deviation::reports::download_full_report::download_full_report).route_layer(
                axum::middleware::from_fn_with_state(app_state.clone(), middleware::auth),
            ),
        )
        .route("/", get(|| async { "Hey Its OMS, Say Hello!" }))
        .layer(cors)
        .with_state(app_state.clone());
    let listener = tokio::net::TcpListener::bind("0.0.0.0:4010").await.unwrap();
    axum::serve(listener, app).await.unwrap();
}
