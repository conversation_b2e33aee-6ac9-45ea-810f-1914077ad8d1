# =============================================================================
# ClickHouse Database Management
# =============================================================================

# Start ClickHouse server and create database
.PHONY: clickhouse-up
clickhouse-up:
	@echo "Starting ClickHouse server..."
	docker run -d --name clickhouse-server \
		-p 8123:8123 -p 9000:9000 \
		-v clickhouse_data:/var/lib/clickhouse \
		-e CLICKHOUSE_USER=default \
		-e CLICKHOUSE_PASSWORD=password \
		-e CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1 \
		clickhouse/clickhouse-server:latest
	@echo "Waiting for ClickHouse to be ready..."
	@sleep 3
	@echo "Creating development database..."
	docker run --rm \
		--network="host" \
		clickhouse/clickhouse-client \
		--host 127.0.0.1 \
		--user default \
		--password password \
		--query "CREATE DATABASE IF NOT EXISTS dev"
	@echo "ClickHouse setup complete!"

# Stop and remove ClickHouse container
.PHONY: clickhouse-down
clickhouse-down:
	@echo "Stopping ClickHouse server..."
	docker rm -f clickhouse-server || true
	@echo "ClickHouse server stopped."

# Run database migrations
.PHONY: migrate
migrate:
	@echo "Running ClickHouse migrations..."
	CH_MIGRATIONS_HOST=http://localhost:8123 \
	CH_MIGRATIONS_USER=default \
	CH_MIGRATIONS_PASSWORD=password \
	CH_MIGRATIONS_DB=default \
	CH_MIGRATIONS_HOME=./alpha-recons/alpha-recon-api/migrations \
	npx clickhouse-migrations migrate
	@echo "Migrations completed."

# Run the alpha-recon API
.PHONY: alpha-recon-api
alpha-recon-api:
	@echo "Starting alpha-recon API..."
	cargo run --bin alpha-recon-api

# =============================================================================
# RabbitMQ Management
# =============================================================================

# Variables
PROJECT_NAME = rabbitmq_example
RABBITMQ_CONTAINER = rabbitmq
RABBITMQ_IMAGE = rabbitmq:3-management
CARGO = cargo

# Default target
.PHONY: all
all: build

# Build the Rust project
.PHONY: build
build:
	@echo "Building Rust project..."
	$(CARGO) build --release
	@echo "Build completed."

# Run the Rust application
.PHONY: run
run:
	@echo "Running Rust application..."
	$(CARGO) run

# Start RabbitMQ container
.PHONY: start-rabbitmq
start-rabbitmq:
	@echo "Starting RabbitMQ container..."
	docker run -d --name $(RABBITMQ_CONTAINER) -p 5672:5672 -p 15672:15672 $(RABBITMQ_IMAGE)
	@echo "RabbitMQ container started. Management UI available at http://localhost:15672"

# Stop and remove RabbitMQ container
.PHONY: stop-rabbitmq
stop-rabbitmq:
	@echo "Stopping RabbitMQ container..."
	docker stop $(RABBITMQ_CONTAINER) || true
	docker rm $(RABBITMQ_CONTAINER) || true
	@echo "RabbitMQ container stopped and removed."

# Create a queue in RabbitMQ
.PHONY: create-queue
create-queue:
	@echo "Creating RabbitMQ queue..."
	docker exec $(RABBITMQ_CONTAINER) rabbitmqadmin declare queue name=my_queue durable=true
	@echo "Queue 'my_queue' created successfully."

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	$(CARGO) clean
	rm -f target/$(PROJECT_NAME)
	@echo "Cleanup completed."

# Install dependencies
.PHONY: install
install:
	@echo "Installing dependencies..."
	$(CARGO) fetch
	@echo "Dependencies installed."

# =============================================================================
# Development Workflow
# =============================================================================

# Setup development environment
.PHONY: dev-setup
dev-setup: clickhouse-up start-rabbitmq
	@echo "Development environment setup complete!"

# Teardown development environment
.PHONY: dev-teardown
dev-teardown: clickhouse-down stop-rabbitmq
	@echo "Development environment teardown complete!"

# Full development workflow
.PHONY: dev
dev: dev-setup migrate alpha-recon-api
	@echo "Development environment ready!"