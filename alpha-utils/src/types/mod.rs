#![allow(unused)]
use crate::price::{fetch_mutual_fund_price_latest, fetch_price};
use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    clickhouse::{
        bse_stock_price::bse_stock_price::BseSecurityPrice, nse_stock_price::nse_stock_price::NseSecurityPrice,
    },
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    redis::schema::{MutualFundData, StockData},
    schema::{
        client_order_entry::SecurityType,
        restricted_stocks::{
            client_restricted_stocks::RestrictedStockForClients, org_restricted_stocks::RestrictedStockForOrganisation,
        },
        security_master::{company_master::CompanyMaster, security_masters::SecurityMasters, SchemeMasterDetailsNew},
    },
};
use chrono::{DateTime, Utc};
use redis::{aio::MultiplexedConnection, AsyncCommands};
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Deserializer, Serialize};

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct StockPrice {
    #[serde(rename = "Symbol")]
    pub symbol: String,

    #[serde(rename = "Exchange")]
    pub exchange: String,

    #[serde(rename = "Price")]
    pub price: f64,

    #[serde(rename = "Timestamp")]
    pub timetamp: DateTime<Utc>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct MutualFundPrice {
    #[serde(rename = "Nav")]
    #[serde(deserialize_with = "string_to_f64")]
    pub price: f64,

    #[serde(rename = "Timestamp")]
    pub timetamp: DateTime<Utc>,
}

fn string_to_f64<'de, D>(deserializer: D) -> Result<f64, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    s.parse::<f64>().map_err(serde::de::Error::custom)
}

#[derive(Deserialize, Serialize, Debug)]
pub struct QueueMessage {
    #[serde(rename = "messageType")]
    pub message_type: MessageContent,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(tag = "messageType")]
pub enum MessageContent {
    Settlement(SettlementMessageMeta),
    HoldingRecon(HoldingReconMessageMeta),
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SettlementMessageMeta {
    pub message: SettlementMessage,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SettlementMessage {
    pub id: String,
    #[serde(rename = "releasePayout")]
    pub release_payout: bool,
    #[serde(rename = "cancelPendingOrders")]
    pub cancel_pending_orders: bool,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct HoldingReconMessageMeta {
    pub message: HoldingReconMessage,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct HoldingReconMessage {
    pub id: String,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(tag = "SecurityType")]
pub enum SecurityDetails {
    Stocks(StockData),
    MutualFund(MutualFundData),
    ETF(StockData),
}

pub enum SecurityTypeForPrice {
    Equity,
    MutualFund,
}

impl SecurityDetails {
    pub async fn build_with_restricted_stocks(
        redis_conn: &mut MultiplexedConnection,
        db_conn: &mut Object<Manager>,
        master_pool: Pool<Manager>,
        isin: String,
        client_id: String,
        security_type: SecurityTypeForPrice,
        exchange: &str,
    ) -> Result<Self, String> {
        log_info(LogBuilder::system(&format!(
            "Building restricted stocks with ISIN: {}",
            isin.clone()
        )));

        let mut isin_to_be_computed = isin.clone();
        let mut exchange = exchange.to_owned();
        //Client Restriction
        let client_restricted_stocks = RestrictedStockForClients::get_by_client_id_and_isin(
            db_conn,
            client_id.clone(),
            isin_to_be_computed.clone(),
        )
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get restricted stocks for client: {}",
                e
            )));
            format!("Failed to get restricted stocks for client: {}", e)
        })?;

        //If Client Has restriction check if the alternative has restriction at org level
        //If yes don't compute
        if let Some(res_stock) = client_restricted_stocks {
            //Organisation Restriction
            if let Some(isin_alt) = res_stock.isin_alternative_security {
                let org_restricted_stocks = RestrictedStockForOrganisation::get_by_and_isin(db_conn, &isin_alt).await?;

                if let Some(_) = &org_restricted_stocks {
                    let err =
                        String::from("Client Alternative Stock has Restriction In Organisation. So Not Tradabale");
                    log_error(LogBuilder::system(&err));
                    return Err(err);
                }

                isin_to_be_computed = isin_alt.clone();
                if let Some(alt_exchange) = res_stock.exchange_alternative_security {
                    exchange = alt_exchange.clone();
                }
            } else {
                let err = format!(
                    "Alternative Stock Not available For Restricted Stock {} skipping...",
                    isin_to_be_computed
                );
                log_error(LogBuilder::system(&err));

                return Err(err);
            }
        } else {
            //Organisation Restriction
            let org_restricted_stocks =
                RestrictedStockForOrganisation::get_by_and_isin(db_conn, &isin_to_be_computed).await?;

            if let Some(org_restricted_stocks) = &org_restricted_stocks {
                //Check if the alternative is restricted in client
                if let Some(isin_alt) = &org_restricted_stocks.isin_alternative_security {
                    let client_restricted_stocks =
                        RestrictedStockForClients::get_by_client_id_and_isin(db_conn, client_id, isin_alt.clone())
                            .await
                            .map_err(|e| {
                                log_error(LogBuilder::system(&format!(
                                    "Failed to get restricted stocks for client: {}",
                                    e
                                )));
                                format!("Failed to get client restricted stocks: {}", e)
                            })?;

                    if client_restricted_stocks.is_some() {
                        let err = String::from("Org Alternative Stock has Restriction In Client. So Not Tradabale");
                        log_error(LogBuilder::system(&err));
                        return Err(err);
                    }

                    isin_to_be_computed = isin_alt.clone();
                    if let Some(alt_exchange) = &org_restricted_stocks.exchange_alternative_security {
                        exchange = alt_exchange.clone();
                    }
                } else {
                    let err = format!(
                        "Alternative Stock Not available For Restricted Stock {} skipping...",
                        isin_to_be_computed
                    );
                    log_error(LogBuilder::system(&err));
                    return Err(err);
                }
            }
        }

        Self::build(redis_conn, master_pool, isin_to_be_computed, security_type, &exchange).await
    }

    pub async fn build(
        redis_conn: &mut redis::aio::MultiplexedConnection,
        master_pool: Pool<Manager>,
        isin: String,
        security_type: SecurityTypeForPrice,
        exchange: &str,
    ) -> Result<Self, String> {
        let exchange = exchange.to_uppercase();

        // This will be read from Redis Cache
        // So Differentiate MF and Stock
        log_info(
            LogBuilder::system(&format!("Searching Security isin = {} from Redis", &isin))
                .add_metadata("exchange", &exchange),
        );

        //The security Can be either Mutual fund / Stocks / ETF / Bond
        // Parse for everything
        match security_type {
            SecurityTypeForPrice::MutualFund => {
                let mf_isin_format = format!("{}-MutualFund", isin.clone());
                let security_json = redis_conn.get::<String, String>(mf_isin_format).await;
                if let Ok(security_json) = security_json {
                    let stock_details: Result<SecurityDetails, serde_json::Error> =
                        serde_json::from_str(&security_json);

                    if let Ok(security) = stock_details {
                        log_info(
                            LogBuilder::system(&format!("Found Security {}, In Redis Cache", &isin))
                                .add_metadata("exchange", &exchange),
                        );
                        match security {
                            SecurityDetails::ETF(etf_sec) => {
                                return Ok(Self::ETF(etf_sec));
                            }
                            SecurityDetails::MutualFund(mf_sec) => {
                                return Ok(Self::MutualFund(mf_sec));
                            }
                            SecurityDetails::Stocks(stock_sec) => {
                                return Ok(Self::Stocks(stock_sec));
                            }
                        }
                    } else {
                        log_warn(
                            LogBuilder::system(&format!("Security {} Is Not In Redis Cache", &isin))
                                .add_metadata("exchange", &exchange),
                        );
                    }
                }
            }
            SecurityTypeForPrice::Equity => {
                let security_json = redis_conn.get::<String, String>(isin.clone()).await;
                if let Ok(security_json) = security_json {
                    let stock_details: Result<SecurityDetails, serde_json::Error> =
                        serde_json::from_str(&security_json);

                    if let Ok(security) = stock_details {
                        log_info(
                            LogBuilder::system(&format!("Found Security {} In Redis Cache", &isin))
                                .add_metadata("exchange", &exchange),
                        );
                        match security {
                            SecurityDetails::ETF(mut etf_sec) => {
                                if exchange == "BSE" {
                                    if etf_sec.bse_price == 0f64 {
                                        let identifier = etf_sec.scrip_code.clone().unwrap();

                                        let price = fetch_price(identifier, exchange).await;
                                        etf_sec.bse_price = price;
                                        etf_sec.nse_price = price;
                                    }
                                } else {
                                    if etf_sec.nse_price == 0f64 {
                                        let identifier = etf_sec.symbol.clone().unwrap();

                                        let price = fetch_price(identifier, exchange).await;
                                        etf_sec.bse_price = price;
                                        etf_sec.nse_price = price;
                                    }
                                }
                                return Ok(Self::ETF(etf_sec));
                            }
                            SecurityDetails::MutualFund(mf_sec) => {
                                return Ok(Self::MutualFund(mf_sec));
                            }
                            SecurityDetails::Stocks(mut stock_sec) => {
                                if exchange == "BSE" {
                                    if stock_sec.bse_price == 0f64 {
                                        let identifier = stock_sec.scrip_code.clone().unwrap();

                                        let price = fetch_price(identifier, exchange).await;
                                        stock_sec.bse_price = price;
                                        stock_sec.nse_price = price;
                                    }
                                } else {
                                    if stock_sec.nse_price == 0f64 {
                                        let identifier = stock_sec.symbol.clone().unwrap();

                                        let price = fetch_price(identifier, exchange).await;
                                        stock_sec.bse_price = price;
                                        stock_sec.nse_price = price;
                                    }
                                }
                                return Ok(Self::Stocks(stock_sec));
                            }
                        }
                    } else {
                        log_warn(
                            LogBuilder::system(&format!("Security {} Is Not In Redis Cache", &isin))
                                .add_metadata("exchange", &exchange),
                        );
                    }
                }
            }
        }
        let mut master_conn = master_pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get master pool connection: {}",
                e
            )));
            format!("Failed to get master pool connection: {}", e)
        })?;

        log_info(
            LogBuilder::system("Getting Security from Company Master")
                .add_metadata("isin", &isin)
                .add_metadata("exchange", &exchange),
        );
        let stock_details_from_table = CompanyMaster::get_by_isin(&mut master_conn, isin.clone()).await?;

        if let Some(stock_details_from_table) = stock_details_from_table {
            log_info(LogBuilder::system(&format!(
                "Security Found In Company Master, with ISIN: {}",
                &stock_details_from_table.isin
            )));
            let mut stock: StockData = StockData {
                bse_price: stock_details_from_table.latest_price,
                // bse_price_date: Utc::now(),
                bse_sublisting: stock_details_from_table.sublisting.clone(),
                nse_sublisting: stock_details_from_table.sublisting.clone(),
                company_name: stock_details_from_table.comp_name,
                fin_code: stock_details_from_table.fincode,
                house_code: stock_details_from_table.hse_code,
                id: 3,
                industry: stock_details_from_table.industry,
                industry_code: stock_details_from_table.ind_code.clone(),
                industry_shortname: stock_details_from_table.ind_code,
                isin: stock_details_from_table.isin,
                market_cap: stock_details_from_table.market_cap,
                nse_price: stock_details_from_table.latest_price,
                // nse_price_date: Utc::now(),
                scrip_code: stock_details_from_table.scripcode,
                scrip_group: stock_details_from_table.scrip_group,
                scrip_name: stock_details_from_table.s_name.unwrap(),
                series: stock_details_from_table.series,
                status: stock_details_from_table.status,
                sublisting: stock_details_from_table.sublisting,
                symbol: stock_details_from_table.symbol,
            };

            let identifier = if exchange == "BSE" {
                stock.scrip_code.clone().unwrap()
            } else {
                stock.symbol.clone().unwrap()
            };

            let price = fetch_price(identifier, exchange).await;
            stock.bse_price = price;
            stock.nse_price = price;
            // stock.nse_price_date = Utc::now();
            // stock.bse_price_date = Utc::now();

            log_info(LogBuilder::system("Returning Stock..."));
            return Ok(Self::Stocks(stock));
        } else {
            log_warn(LogBuilder::system(&format!(
                "Security Not Found In Company Master, ISIN: {}",
                &isin
            )));
            log_info(LogBuilder::system("Checking Security in SchemeMaster..."));

            let mf_details_from_table =
                SchemeMasterDetailsNew::get_mf_details_by_isin(&mut master_conn, isin.clone(), true, true)
                    .await
                    .map_err(|e| e)?
                    .ok_or_else(|| {
                        log_error(LogBuilder::system(&format!(
                            "Security {:?} Not Found In Scheme Master",
                            isin
                        )));

                        format!("Security {:?} Not Found In Scheme Master", isin)
                    })?;

            log_warn(LogBuilder::system("Security Found In Scheme Master"));

            let mut mf = MutualFundData {
                amc: mf_details_from_table.amc,
                amc_code: mf_details_from_table.amc_code,
                amfi_code: mf_details_from_table.amfi_code.clone(),
                amfi_type: mf_details_from_table.amfi_code,
                asset_class: mf_details_from_table.asset_class,
                benchmark: mf_details_from_table.benchmark,
                cash_allocation: mf_details_from_table.cash_allocation,
                category_name: mf_details_from_table.category_name,
                company_city: None,
                company_id: None,
                company_name: None,
                company_state: None,
                debt_allocation: mf_details_from_table.debt_allocation,
                distribution_status: mf_details_from_table.distribution_status,
                equity_allocation: mf_details_from_table.equity_allocation,
                exchange_traded_share: mf_details_from_table.exchange_traded_share,
                fund_class: mf_details_from_table.fund_class,
                fund_legal_name: mf_details_from_table.fund_legal_name,
                fund_manager_name: mf_details_from_table.fund_manager_name,
                fund_manager_start_date: mf_details_from_table.fund_manager_start_date,
                fund_name: mf_details_from_table.fund_name,
                fund_status: mf_details_from_table.fund_status,
                inception_date: mf_details_from_table.inception_date,
                investment_plan: mf_details_from_table.investment_plan,
                isin: mf_details_from_table.isin,
                morning_star_benchmark_id: mf_details_from_table.morningstar_benchmark_id,
                morning_star_benchmark_name: mf_details_from_table.morningstar_benchmark_name,
                net_expense_ratio: mf_details_from_table.net_expense_ratio.to_f64().unwrap(),
                price_date: Utc::now().naive_utc(),
                primary_fd_code: None,
                purchase_mode: mf_details_from_table.purchase_mode,
                registrar: None,
                risk_category: None,
                rta_code: mf_details_from_table.rta_code,
                scheme_code: mf_details_from_table.scheme_type,
                scheme_name: mf_details_from_table.scheme_name,
                ..Default::default()
            };

            let price = fetch_mutual_fund_price_latest(mf.amfi_code.clone()).await;
            mf.price = price;

            log_info(LogBuilder::system("Returning Mutual Fund..."));
            Ok(Self::MutualFund(mf))
        }
    }

    /// Build the security from Security Master
    pub async fn build_from_security_master(db_conn: Pool<Manager>, isin: &str) -> Result<Self, String> {
        let mut db_conn = db_conn
            .get()
            .await
            .map_err(|_e| format!("Failed to Get Db conenction"))?;

        let security = SecurityMasters::get_by_isin(&mut db_conn, isin)
            .await
            .map_err(|_e| return format!("Failed to Get Security Details from Db"))?
            .unwrap(); //WE ARE SURE SECURITY EXIST

        Ok(Self::Stocks(StockData {
            bse_price: 0f64,
            nse_price: 0f64,
            bse_sublisting: None,
            house_code: None,
            industry: security.industry,
            company_name: "Master".to_string(),
            fin_code: "N/A".to_string(),
            id: 0,
            industry_code: None,
            industry_shortname: None,
            isin: security.isin,
            market_cap: security.market_cap,
            nse_sublisting: security.sublisting.clone(),
            scrip_code: None,
            scrip_group: None,
            scrip_name: "N/A".to_string(),
            series: security.nse_series,
            symbol: security.nse_symbol,
            status: security.status,
            sublisting: security.sublisting,
        }))
    }

    /// Build the Security and the Price at the given Date
    pub async fn build_as_at(
        _clickhouse_client: clickhouse::Client,
        redis_conn: &mut MultiplexedConnection,
        master_pool: Pool<Manager>,
        isin: String,
        _date: time::Date,
        security_type: SecurityTypeForPrice,
    ) -> Result<Self, String> {
        log_info(LogBuilder::system(
            "Building the Security and the Price at the given Date",
        ));
        //Get the Security
        log_info(LogBuilder::system("Getting Security From Cache"));
        let security = Self::get_security(redis_conn, master_pool, isin.clone(), security_type).await?;

        Ok(security)
    }

    /// Fetch The Security From Redis, If Not found, FallBack to Master Data
    async fn get_security(
        redis_conn: &mut MultiplexedConnection,
        master_pool: Pool<Manager>,
        mut isin: String,
        security_type: SecurityTypeForPrice,
    ) -> Result<SecurityDetails, String> {
        // This will be read from Redis Cache
        // So Differentiate MF and Stock
        log_info(LogBuilder::system(&format!(
            "Searching Security with isin = {} from Redis",
            isin
        )));

        if let SecurityTypeForPrice::MutualFund = security_type {
            isin = isin + "MutualFund"
        }
        let security_json = redis_conn.get::<String, String>(isin.clone()).await;

        //The security Can be either Mutual fund / Stocks / ETF / Bond
        // Parse for everything
        if let Ok(security_json) = security_json {
            let stock_details: Result<SecurityDetails, serde_json::Error> = serde_json::from_str(&security_json);

            if let Ok(security) = stock_details {
                log_info(LogBuilder::system(&format!("Found Security {} In Redis Cache", isin)));
                match security {
                    SecurityDetails::ETF(etf_sec) => {
                        return Ok(Self::ETF(etf_sec));
                    }
                    SecurityDetails::MutualFund(mf_sec) => {
                        return Ok(Self::MutualFund(mf_sec));
                    }
                    SecurityDetails::Stocks(stock_sec) => {
                        return Ok(Self::Stocks(stock_sec));
                    }
                }
            } else {
                log_warn(LogBuilder::system(&format!(
                    "Security {} Is Not In Redis Cache",
                    isin.clone()
                )));
            }
        }

        log_warn(LogBuilder::system(&format!(
            "Security {} Is Not In Redis Cache",
            isin.clone()
        )));

        let mut master_conn = master_pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get master pool connection: {}",
                e
            )));
            format!("Failed to get master pool connection: {}", e)
        })?;

        log_info(LogBuilder::system("Getting Security from Company Master"));
        let stock_details_from_table = CompanyMaster::get_by_isin(&mut master_conn, isin.clone()).await?;

        if let Some(stock_details_from_table) = stock_details_from_table {
            log_info(LogBuilder::system(&format!(
                "Security {} Found In Company Master",
                isin.clone()
            )));
            let stock = StockData {
                bse_price: stock_details_from_table.latest_price,
                // bse_price_date: Utc::now(),
                bse_sublisting: stock_details_from_table.sublisting.clone(),
                nse_sublisting: stock_details_from_table.sublisting.clone(),
                company_name: stock_details_from_table.comp_name,
                fin_code: stock_details_from_table.fincode,
                house_code: stock_details_from_table.hse_code,
                id: 3,
                industry: stock_details_from_table.industry,
                industry_code: stock_details_from_table.ind_code.clone(),
                industry_shortname: stock_details_from_table.ind_code,
                isin: stock_details_from_table.isin,
                market_cap: stock_details_from_table.market_cap,
                nse_price: stock_details_from_table.latest_price,
                // nse_price_date: Utc::now(),
                scrip_code: stock_details_from_table.scripcode,
                scrip_group: stock_details_from_table.scrip_group,
                scrip_name: stock_details_from_table.s_name.unwrap(),
                series: stock_details_from_table.series,
                status: stock_details_from_table.status,
                sublisting: stock_details_from_table.sublisting,
                symbol: stock_details_from_table.symbol,
            };

            return Ok(Self::Stocks(stock));
        } else {
            log_warn(LogBuilder::system(&format!(
                "Security {} not found in Company Master",
                isin.clone()
            )));

            log_info(LogBuilder::system("Checking Security in SchemeMaster"));

            let mf_details_from_table =
                SchemeMasterDetailsNew::get_mf_details_by_isin(&mut master_conn, isin.clone(), true, true)
                    .await
                    .map_err(|e| e)?
                    .ok_or_else(|| {
                        log_error(LogBuilder::system(&format!(
                            "Security {} Not Found In Scheme Master",
                            isin.clone()
                        )));

                        String::from("Failed")
                    })?;

            log_info(LogBuilder::system(&format!(
                "Security {} Found In Scheme Master",
                isin.clone()
            )));

            let mf = MutualFundData {
                amc: mf_details_from_table.amc,
                amc_code: mf_details_from_table.amc_code,
                amfi_code: mf_details_from_table.amfi_code.clone(),
                amfi_type: mf_details_from_table.amfi_code,
                asset_class: mf_details_from_table.asset_class,
                benchmark: mf_details_from_table.benchmark,
                cash_allocation: mf_details_from_table.cash_allocation,
                category_name: mf_details_from_table.category_name,
                company_city: None,
                company_id: None,
                company_name: None,
                company_state: None,
                debt_allocation: mf_details_from_table.debt_allocation,
                distribution_status: mf_details_from_table.distribution_status,
                equity_allocation: mf_details_from_table.equity_allocation,
                exchange_traded_share: mf_details_from_table.exchange_traded_share,
                fund_class: mf_details_from_table.fund_class,
                fund_legal_name: mf_details_from_table.fund_legal_name,
                fund_manager_name: mf_details_from_table.fund_manager_name,
                fund_manager_start_date: mf_details_from_table.fund_manager_start_date,
                fund_name: mf_details_from_table.fund_name,
                fund_status: mf_details_from_table.fund_status,
                inception_date: mf_details_from_table.inception_date,
                investment_plan: mf_details_from_table.investment_plan,
                isin: mf_details_from_table.isin,
                morning_star_benchmark_id: mf_details_from_table.morningstar_benchmark_id,
                morning_star_benchmark_name: mf_details_from_table.morningstar_benchmark_name,
                net_expense_ratio: mf_details_from_table.net_expense_ratio.to_f64().unwrap(),
                price_date: Utc::now().naive_utc(),
                primary_fd_code: None,
                purchase_mode: mf_details_from_table.purchase_mode,
                registrar: None,
                risk_category: None,
                rta_code: mf_details_from_table.rta_code,
                scheme_code: mf_details_from_table.scheme_type,
                scheme_name: mf_details_from_table.scheme_name,
                ..Default::default()
            };

            log_info(LogBuilder::system("Returning Mutual Fund..."));
            Ok(Self::MutualFund(mf))
        }
    }

    /// Sets the Security Price By Fetching from the Clickhouse
    async fn set_security_price_as_at(
        &mut self,
        clickhouse_client: clickhouse::Client,
        date: time::Date,
    ) -> Result<(), String> {
        log_info(LogBuilder::system(&format!("Setting security price at Date: {}", date)));

        let isin = self.get_isin();
        let nse_price = NseSecurityPrice::get_by_date(clickhouse_client.clone(), date, &isin)
            .await
            .map_err(|e| {
                log_error(LogBuilder::system(&format!("Failed To Get NSE Price {:?}", e)));
                format!("Failed To Get NSE Price {:?}", e)
            })?;

        if let Some(nse_price) = nse_price {
            self.set_price(nse_price.price as f64 / 10000f64);
        } else {
            let bse_price = BseSecurityPrice::get_by_date(clickhouse_client.clone(), date, &isin)
                .await
                .map_err(|e| {
                    log_error(LogBuilder::system(&format!("Failed To Get BSE Price {:?}", e)));

                    format!("Failed To Get BSE Price {:?}", e)
                })?;

            if let Some(bse_price) = bse_price {
                self.set_price(bse_price.price as f64 / 10000f64);
            } else {
                log_warn(LogBuilder::system(&format!(
                    "Price Not Found for the Security = {}",
                    isin
                )));
                self.set_price(0f64);
            }
        }

        Ok(())
    }

    pub async fn get_price_as_at(
        clickhouse_client: clickhouse::Client,
        date: time::Date,
        isin: String,
    ) -> Result<f64, String> {
        log_info(LogBuilder::system(&format!("Getting security price at Date: {}", date)));

        let nse_price = NseSecurityPrice::get_by_date(clickhouse_client.clone(), date, &isin)
            .await
            .map_err(|e| format!("Failed To Get NSE Price {:?}", e))?;

        if let Some(nse_price) = nse_price {
            Ok(nse_price.price as f64 / 10000f64)
        } else {
            let bse_price = BseSecurityPrice::get_by_date(clickhouse_client.clone(), date, &isin)
                .await
                .map_err(|e| format!("Failed To Get BSE Price {:?}", e))?;

            if let Some(bse_price) = bse_price {
                Ok(bse_price.price as f64 / 10000f64)
            } else {
                log_warn(LogBuilder::system(&format!(
                    "Price Not Found for the Security = {}",
                    isin
                )));
                Ok(0f64)
            }
        }
    }
}

impl SecurityDetails {
    pub fn get_mutual_fund_details(&self) -> Option<&MutualFundData> {
        if let SecurityDetails::MutualFund(mf_details) = self {
            return Some(mf_details);
        }

        return None;
    }

    pub fn get_stock_details(&self) -> Option<&StockData> {
        if let SecurityDetails::Stocks(stocks) | SecurityDetails::ETF(stocks) = self {
            return Some(stocks);
        }

        None
    }

    pub fn get_latest_price(&self) -> f64 {
        match self {
            SecurityDetails::MutualFund(m) => m.price,
            SecurityDetails::Stocks(s) => s.get_latest_price(),
            SecurityDetails::ETF(etf) => etf.get_latest_price(),
        }
    }

    pub fn get_industry(&self) -> Option<String> {
        match self {
            SecurityDetails::MutualFund(m) => Some(m.category_name.clone()),
            SecurityDetails::Stocks(s) => s.industry.clone(),
            SecurityDetails::ETF(etf) => etf.industry.clone(),
        }
    }

    pub fn get_company_name(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.scheme_name.clone(),
            SecurityDetails::Stocks(s) => s.company_name.clone(),
            SecurityDetails::ETF(etf) => etf.company_name.clone(),
        }
    }

    pub fn get_scrip_name(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.scheme_name.clone(),
            SecurityDetails::Stocks(s) => s.scrip_name.clone(),
            SecurityDetails::ETF(etf) => etf.scrip_name.clone(),
        }
    }

    pub fn get_asset_class(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.asset_class.clone(),
            SecurityDetails::Stocks(s) => "Equity".to_string(),
            SecurityDetails::ETF(etf) => "Equity".to_string(),
        }
    }

    pub fn get_security_type(&self) -> SecurityType {
        match self {
            SecurityDetails::MutualFund(m) => SecurityType::MutualFund,
            SecurityDetails::Stocks(s) => SecurityType::Stocks,
            SecurityDetails::ETF(etf) => SecurityType::ETF,
        }
    }

    pub fn get_symbol(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.rta_code.clone(),
            SecurityDetails::Stocks(s) => s
                .symbol
                .clone()
                .unwrap_or_else(|| s.scrip_code.clone().unwrap_or("UKW".to_string())),
            SecurityDetails::ETF(etf) => etf
                .symbol
                .clone()
                .unwrap_or_else(|| etf.scrip_code.clone().unwrap_or("UKW".to_string())),
        }
    }

    pub fn get_identifier(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.rta_code.clone(),
            SecurityDetails::Stocks(s) => s.get_identifier(),
            SecurityDetails::ETF(etf) => etf.get_identifier(),
        }
    }

    pub fn get_name(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.scheme_name.clone(),
            SecurityDetails::Stocks(s) => s.scrip_name.clone(),
            SecurityDetails::ETF(etf) => etf.scrip_name.clone(),
        }
    }

    pub fn get_series(&self) -> Option<String> {
        match self {
            SecurityDetails::MutualFund(m) => None,
            SecurityDetails::Stocks(s) => s.series.clone(),
            SecurityDetails::ETF(etf) => etf.series.clone(),
        }
    }

    pub fn get_isin(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => m.isin.clone(),
            SecurityDetails::Stocks(s) => s.isin.clone(),
            SecurityDetails::ETF(etf) => etf.isin.clone(),
        }
    }

    pub fn set_price(&mut self, price: f64) {
        match self {
            SecurityDetails::MutualFund(m) => {
                m.set_price(price);
            }
            SecurityDetails::Stocks(s) => {
                s.set_price(price);
            }
            SecurityDetails::ETF(s) => {
                s.set_price(price);
            }
        }
    }

    pub fn get_exchange(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => String::from("DIR"),
            SecurityDetails::Stocks(s) => s.get_exchange(),
            SecurityDetails::ETF(s) => s.get_exchange(),
        }
    }

    pub fn get_marketcap(&self) -> String {
        match self {
            SecurityDetails::MutualFund(m) => String::from("Unknown"),
            SecurityDetails::Stocks(s) => s.market_cap.clone().unwrap_or(String::from("Not Available")),
            SecurityDetails::ETF(s) => s.market_cap.clone().unwrap_or(String::from("Not Available")),
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::types::{SecurityDetails, SecurityTypeForPrice};
    use alpha_core_db::{
        connection::{connect_to_master_data, connect_to_mssql},
        redis::connect_to_redis,
    };
    use tracing_test::traced_test;

    #[tokio::test]
    #[traced_test]
    async fn test_redis_kv() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();
        let master_data_pool = connect_to_master_data(2).await;
        let mut master_data_pool_conn = master_data_pool.get().await.unwrap();
        let redis_pool = connect_to_redis().await;
        let mut redis_conn = redis_pool.get().await.unwrap();
        println!("Connected to reids");
        let isin = String::from("INE0A1101019");
        let foo = SecurityDetails::build(
            &mut redis_conn,
            master_data_pool,
            isin,
            crate::types::SecurityTypeForPrice::Equity,
            "nse",
        )
        .await
        .unwrap();
    }

    #[tokio::test]
    #[traced_test]
    async fn test_all_kvs() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();
        let master_data_pool = connect_to_master_data(2).await;
        let mut master_data_pool_conn = master_data_pool.get().await.unwrap();
        let redis_pool = connect_to_redis().await;
        let mut redis_conn = redis_pool.get().await.unwrap();
        println!("Connected to reids");
        let isin = String::from("INE0A1101019");
        let mut redis_conn = redis_pool.get().await.unwrap();
        let foo = SecurityDetails::build(
            &mut redis_conn,
            master_data_pool,
            isin,
            SecurityTypeForPrice::MutualFund,
            "nse",
        )
        .await
        .unwrap();
    }
}
