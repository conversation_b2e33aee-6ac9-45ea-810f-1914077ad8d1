use csv::Writer;
use actlogica_logs::{builder::LogBuilder, log_error};

pub async fn create_csv_for_holding_recon_result(rows: Vec<super::excel::ReconExcelEntry>) -> Result<Vec<u8>, String> {
    let mut wtr = Writer::from_writer(vec![]);

    for row in rows {
        wtr.serialize(row).map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to Serialise Row into Bytes : {:?}", e)));
            return format!("Failed to Serialise Row into Bytes");
        })?;
    }

    let buff = wtr.into_inner().map_err(|e| {
        log_error(LogBuilder::system(&format!("Failed to Serialise Row into Bytes : {:?}", e)));
        return format!("Failed to Serialise Row into Bytes");
    })?;

    Ok(buff)
}
