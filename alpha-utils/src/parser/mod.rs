pub mod csv_parser;
pub mod excel;
use std::{collections::HashMap, io::Cursor, path::Path};

use actlogica_logs::{builder::LogBuilder, log_error};

use csv::{Reader, ReaderBuilder};

/// Schema For Custody File
#[derive(Debug, serde::Deserialize, Clone)]
#[serde(rename_all = "lowercase")]
pub struct HoldingReconCustodyFile {
    #[serde(alias = "Client Code")]
    pub custodian_portfolio_code: String,

    #[serde(alias = "ISIN Code")]
    pub isin: String,

    #[serde(alias = "Client Name")]
    pub client_name: String,

    #[serde(alias = "Scrip Name")]
    pub security_name: String,

    #[serde(alias = "Total Quantity")]
    pub holdings: f64,
}

///  Schema for Fund Accountant File
#[derive(Debug, serde::Deserialize, Clone)]
#[serde(rename_all = "lowercase")]
pub struct HoldingReconFundAccountantFile {
    #[serde(rename = "CODE")]
    pub fund_accountant_code: String,

    #[serde(rename = "ISIN")]
    pub isin: String,

    #[serde(rename = "NAME")]
    pub client_name: String,

    #[serde(rename = "HOLDINGS")]
    pub holdings: f64,

    #[serde(rename = "SECURITY")]
    pub security_name: String,
}

impl HoldingReconFundAccountantFile {
    /// It Parses the Csv File from the Stream to the Fund Accountant type
    pub fn parse_holding_recon_fund_accountant_csv_file(stream: Vec<u8>) -> Result<HashMap<String, Vec<Self>>, String> {
        let cursor = Cursor::new(stream);
        let mut reader = ReaderBuilder::new().has_headers(true).from_reader(cursor);

        let result: Result<Vec<Self>, csv::Error> = reader.deserialize().collect::<Result<_, _>>();

        match result {
            Ok(result) => Ok(result.into_iter().fold(HashMap::new(), |mut acc, recon| {
                acc.entry(recon.fund_accountant_code.clone())
                    .or_insert_with(Vec::new)
                    .push(recon);
                acc
            })),
            Err(err) => {
                log_error(LogBuilder::system("Failed to parse Fund Accountant File").add_metadata("error", &err.to_string()));
                return Err(format!(
                    "Parsing Failed For Fund Accountant File  {:?}",
                    err.to_string()
                ));
            }
        }
    }
}

impl HoldingReconCustodyFile {
    /// It Parses the Csv File from the Stream to the Custody type
    pub fn parse_holding_recon_custody_csv_file(stream: Vec<u8>) -> Result<HashMap<String, Vec<Self>>, String> {
        let cursor = Cursor::new(stream);
        let mut reader = ReaderBuilder::new().has_headers(true).from_reader(cursor);

        let result: Result<Vec<Self>, csv::Error> = reader.deserialize().collect::<Result<_, _>>();

        match result {
            Ok(result) => Ok(result.into_iter().fold(HashMap::new(), |mut acc, recon| {
                acc.entry(recon.custodian_portfolio_code.clone())
                    .or_insert_with(Vec::new)
                    .push(recon);
                acc
            })),
            Err(err) => {
                log_error(LogBuilder::system("Failed to parse Custody File").add_metadata("error", &err.to_string()));
                return Err(format!("Parsing Failed For Custody File  {:?}", err.to_string()));
            }
        }
    }
}
