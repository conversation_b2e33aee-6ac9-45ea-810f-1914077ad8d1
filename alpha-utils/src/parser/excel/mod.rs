use std::time::{SystemTime, UNIX_EPOCH};

use alpha_core_db::storage::blob::connection::AzureBlob;
use rust_xlsxwriter::{Format, Workbook};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Excel Headers for Holding Recon Result
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct ReconExcelEntry {
    #[serde(rename = "Client Name")]
    pub client_name: String,
    #[serde(rename = "ClientStrategyCode")]
    pub client_strategy_code: String,
    #[serde(rename = "ClientCustodianCode")]
    pub client_custodian_code: String,
    #[serde(rename = "ClientFACode")]
    pub client_fa_code: Option<String>,
    #[serde(rename = "Custodian")]
    pub custodian: String,
    #[serde(rename = "Scrip Name")]
    pub scrip_name: Option<String>,
    #[serde(rename = "ISIN")]
    pub isin: String,
    #[serde(rename = "Symbol")]
    pub symbol: Option<String>,
    #[serde(rename = "AlphaHoldings")]
    pub alpha_holdings: f64,
    #[serde(rename = "CustodyHoldings")]
    pub custody_holdings: f64,
    #[serde(rename = "FundAccountantHoldigs")]
    pub fund_accountant_holdings: f64,
    #[serde(rename = "AlphaVsCustody")]
    pub alpha_vs_custody: f64,
    #[serde(rename = "AlphaVsFA")]
    pub alpha_vs_fa: f64,
    #[serde(rename = "CustodyVsFA")]
    pub custody_vs_fa: f64,
    #[serde(rename = "Action")]
    pub remarks: String,
}

impl ReconExcelEntry {
    pub fn add_alpha_holdings(&mut self, holdings: f64) {
        self.alpha_holdings = holdings;
    }

    pub fn add_fund_accountant_alpha_holdings(&mut self, holdings: f64) {
        self.fund_accountant_holdings = holdings;
    }

    pub fn add_custody_holdings(&mut self, holdings: f64) {
        self.custody_holdings = holdings;
    }

    pub fn append_remarks(&mut self, remarks: &str) {
        self.remarks = self.remarks.clone() + remarks;
    }

    pub fn add_alpha_vs_custody(&mut self, holdings: f64) {
        self.alpha_vs_custody = holdings;
    }

    pub fn add_alpha_vs_fund_accountant(&mut self, holdings: f64) {
        self.alpha_vs_fa = holdings;
    }

    pub fn add_custody_vs_fund_accountant(&mut self, holdings: f64) {
        self.custody_vs_fa = holdings;
    }
}

/// Returns a Buffer which holds the excel content of Holding Recon Result
pub async fn create_excel_for_holding_recon(rows: Vec<ReconExcelEntry>) -> Vec<u8> {
    // Create a new Excel file object.
    let mut workbook = Workbook::new();

    // Add a worksheet to the workbook.
    let worksheet = workbook.add_worksheet();

    // Add a simple format for the headers.
    let format = Format::new().set_bold();

    // Set up the start location and headers of the data to be serialized.
    worksheet
        .deserialize_headers_with_format::<ReconExcelEntry>(0, 0, &format)
        .unwrap();
    for row in rows {
        worksheet.serialize(&row).unwrap();
    }

    let excel_buffer = workbook.save_to_buffer().expect("Failed to Save excel to Buffer");
    workbook.save("demo.xlsx").unwrap();

    excel_buffer
}
