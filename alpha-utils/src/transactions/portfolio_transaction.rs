use std::collections::HashMap;

use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    redis::schema::{MutualFundData, StockData},
    schema::{
        client_order_entry::{SecuritySubType, TransactionSubType, TransactionType},
        investment::{self, Investments},
        investment_transaction::InvestmentTransaction,
        portfolio_receivables::{PortfolioReceivables, ReceivableStatus},
        security_master::{SchemeMasterDetails, SchemeMasterDetailsNew},
    },
    storage::table::historical_mutual_fund_nav::HistoricalMutualFundNav,
};
use chrono::{Duration, Utc};
use actlogica_logs::{builder::LogBuilder, log_info};

use crate::{settlement::InvestmentTransactionForSettlement, types::SecurityDetails};

use super::find_min_max_dates;

/// This Add Transaction to a Equity Investment
pub async fn add_transactions_to_equity_investment(
    equity_details: &HashMap<String, SecurityDetails>,
    conn: &mut Object<Manager>,
    client_id: String,
    portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
    release_payout: bool,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    //Group all the transaction By Isin
    let transactions_by_isin: HashMap<String, Vec<InvestmentTransactionForSettlement>> =
        transactions.into_iter().fold(HashMap::new(), |mut acc, transaction| {
            acc.entry(transaction.transaction_details.isin.clone())
                .or_insert_with(Vec::new)
                .push(transaction);
            acc
        });

    let receivable_status = if release_payout {
        ReceivableStatus::Received
    } else {
        ReceivableStatus::Hold
    };
    // Loop through each security transactions
    for (isin, transactions) in transactions_by_isin {
        log_info(LogBuilder::system(&format!("Inserting Transaction for ISIN : {:?}", isin)));
        let portfolio_id = portfolio_id.clone();
        let client_id = client_id.clone();

        let isin_details = equity_details.get(&isin).unwrap().get_stock_details().clone().unwrap(); //WE ARE SURE THAT IT WILL HAVE THE DETAILS

        let investments = Investments::get_by_isin_and_portfolio_id(conn, isin.clone(), portfolio_id.clone())
            .await
            .unwrap();

        let price = isin_details.get_latest_price();

        //Check whether Investment Exist for this ISIN in Portfolio Investments
        if let Some(investments) = investments {
            log_info(LogBuilder::system(&format!("ISIN {:?} Is In Investment Just inserting transaction", isin)));
            add_transactions_to_existing_equity_investment(
                price,
                isin_details,
                conn,
                investments,
                transactions,
                receivable_status.clone(),
                is_stt_included_in_cost,
            )
            .await?;
        } else {
            log_info(LogBuilder::system(&format!(
                "ISIN {:?} Is Not IN Investment Creating Investment and  inserting transaction",
                isin
            )));
            //Create a New Investment and put transactions into It
            add_transactions_to_new_equity_investment(
                price,
                isin_details,
                conn,
                isin,
                client_id,
                portfolio_id.clone(),
                transactions,
                receivable_status.clone(),
                is_stt_included_in_cost,
            )
            .await?;
        }
    }

    Ok(())
}

/// This create a new investment and add transaction to investment by ISIN
/// The transaction given to the function argument should belongs to same ISIN
/// Transaction list length should be > 0
async fn add_transactions_to_new_equity_investment(
    price: f64,
    isin_details: &StockData,
    conn: &mut Object<Manager>,
    isin: String,
    client_id: String,
    portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
    receivable_status: ReceivableStatus,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    let current_holding = calculate_current_holding_from_transaction(&transactions);
    let single_transaction = transactions[0].clone(); // Check Is Done Before Passing into this function

    let (min_date, max_date) = find_min_max_dates(&transactions[..]);

    let investment: Investments = Investments {
        client_id: Some(client_id.clone()),
        portfolio_id: Some(portfolio_id.clone()),
        current_holding,
        current_price_date: Utc::now().naive_utc() - Duration::days(1),
        isin,
        symbol: if single_transaction.transaction_details.exchange == "NSE" {
            isin_details.symbol.clone().unwrap()
        } else {
            isin_details.scrip_code.clone().unwrap()
        },
        exchange: single_transaction.transaction_details.exchange,
        current_price: price,
        market_value: ((price * current_holding) * 100.0).round() / 100.0,
        first_transaction_date: min_date,
        last_transaction_date: max_date,
        name: single_transaction.transaction_details.name,
        sector: isin_details.industry.clone(),
        market_cap: isin_details.market_cap.clone(),
        security_type: single_transaction.security_type,
        asset_class: String::from("Equity"),
        ..Default::default()
    };

    // Insert Into Investment Table
    let new_investment_id = investment.insert(conn).await.map_err(|e| {
        return format!("Failed to Insert Investment {:?}", e);
    })?;

    //Insert into Investment Transaction Table
    // let bulk_insert = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|t| t.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|e| {
    //     return format!("Failed to Update Investment");
    // })?;

    //FIXME: Move to Bulk Update
    for mut txn in transactions {
        txn.transaction_details.investment_id = new_investment_id.clone();
        txn.transaction_details
            .insert(conn)
            .await
            .map_err(|e| format!("{:?}", e))?;

        //Add receivables
        if txn.transaction_details.transaction_type == TransactionType::Sell {
            let portfolio_receivables = PortfolioReceivables {
                amount: if !is_stt_included_in_cost {
                    txn.transaction_details.amount - txn.transaction_details.stt_amount
                } else {
                    txn.transaction_details.amount
                },
                price: txn.net_rate,
                client_id: client_id.clone(),
                portfolio_id: portfolio_id.clone(),
                cgt_date: txn.transaction_details.cgt_date,
                corporate_action_type: None,
                exchange: txn.transaction_details.exchange.clone(),
                investment_id: new_investment_id.clone(),
                investment_name: investment.name.clone(),
                isin: investment.isin.clone(),
                quantity: txn.transaction_details.quantity,
                settlement_date: txn.transaction_details.settlement_date,
                symbol: investment.symbol.clone(),
                transaction_date: txn.transaction_details.transaction_date,
                transaction_type: TransactionType::Credit,
                transaction_sub_type: TransactionSubType::Sell,
                remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                receivable_status: receivable_status.clone(),
                ..Default::default()
            };

            portfolio_receivables.insert(conn).await.map_err(|e| {
                return format!("Failed to Insert into receivables {:?}", e);
            })?;

            if !is_stt_included_in_cost {
                let stt_status = if receivable_status == ReceivableStatus::Received {
                    ReceivableStatus::Paid
                } else {
                    receivable_status.clone()
                };
                let portfolio_receivables = PortfolioReceivables {
                    amount: txn.transaction_details.stt_amount,
                    price: txn.transaction_details.stt_amount / txn.transaction_details.quantity,
                    client_id: investment.client_id.clone().unwrap(),
                    portfolio_id: investment.portfolio_id.clone().unwrap(),
                    cgt_date: txn.transaction_details.cgt_date,
                    corporate_action_type: None,
                    exchange: txn.transaction_details.exchange.clone(),
                    investment_id: investment.id.clone(),
                    investment_name: investment.name.clone(),
                    isin: investment.isin.clone(),
                    quantity: txn.transaction_details.quantity,
                    settlement_date: txn.transaction_details.settlement_date,
                    symbol: investment.symbol.clone(),
                    transaction_date: txn.transaction_details.transaction_date,
                    transaction_type: TransactionType::Debit,
                    transaction_sub_type: TransactionSubType::Stt,
                    remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                    receivable_status: stt_status,
                    ..Default::default()
                };

                portfolio_receivables.insert(conn).await.map_err(|e| {
                    return format!("Failed to Insert into receivables {:?}", e);
                })?;
            }
        }
    }

    Ok(())
}

/// This add transaction to existing equity investment by ISIN
/// The transaction given to the function argument should belongs to same ISIN
async fn add_transactions_to_existing_equity_investment(
    price: f64,
    isin_details: &StockData,
    conn: &mut Object<Manager>,
    mut investment: Investments,
    transactions: Vec<InvestmentTransactionForSettlement>,
    receivable_status: ReceivableStatus,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    let new_holdings_to_add: f64 = transactions
        .iter()
        .map(|t| match t.transaction_details.transaction_type {
            TransactionType::Sell => -t.transaction_details.quantity,
            _ => t.transaction_details.quantity,
        })
        .sum();

    let (min_date, max_date) = find_min_max_dates(&transactions);

    // Update investment's current_holdings , transaction in Database
    investment.current_holding += new_holdings_to_add;
    investment.market_value = ((investment.current_holding * price) * 100.0).round() / 100.0;
    investment.first_transaction_date = min_date;
    investment.last_transaction_date = max_date;

    let _ = investment.update_on_settlement(conn).await.map_err(|e| {
        return format!("Failed to Update Investment {:?}", e);
    })?;

    //Insert transaction in Bulk
    // let _ = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|t| t.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|_e| {
    //     return format!("Failed to Insert Investment");
    // })?;
    //FIXME: Move to Bulk Update
    for mut txn in transactions {
        txn.transaction_details.investment_id = investment.id.clone();
        txn.transaction_details
            .insert(conn)
            .await
            .map_err(|e| format!("{:?}", e))?;

        //Add receivables
        if txn.transaction_details.transaction_type == TransactionType::Sell {
            let portfolio_receivables = PortfolioReceivables {
                amount: if !is_stt_included_in_cost {
                    txn.transaction_details.amount - txn.transaction_details.stt_amount
                } else {
                    txn.transaction_details.amount
                },
                price: txn.net_rate,
                client_id: investment.client_id.clone().unwrap(),
                portfolio_id: investment.portfolio_id.clone().unwrap(),
                cgt_date: txn.transaction_details.cgt_date,
                corporate_action_type: None,
                exchange: txn.transaction_details.exchange.clone(),
                investment_id: investment.id.clone(),
                investment_name: investment.name.clone(),
                isin: investment.isin.clone(),
                quantity: txn.transaction_details.quantity,
                settlement_date: txn.transaction_details.settlement_date,
                symbol: investment.symbol.clone(),
                transaction_date: txn.transaction_details.transaction_date,
                transaction_type: TransactionType::Credit,
                transaction_sub_type: TransactionSubType::Sell,
                remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                receivable_status: receivable_status.clone(),
                ..Default::default()
            };

            portfolio_receivables.insert(conn).await.map_err(|e| {
                return format!("Failed to Insert into receivables {:?}", e);
            })?;

            if !is_stt_included_in_cost {
                let stt_status = if receivable_status == ReceivableStatus::Received {
                    ReceivableStatus::Paid
                } else {
                    receivable_status.clone()
                };

                let portfolio_receivables = PortfolioReceivables {
                    amount: txn.transaction_details.stt_amount,
                    price: txn.transaction_details.stt_amount / txn.transaction_details.quantity,
                    client_id: investment.client_id.clone().unwrap(),
                    portfolio_id: investment.portfolio_id.clone().unwrap(),
                    cgt_date: txn.transaction_details.cgt_date,
                    corporate_action_type: None,
                    exchange: txn.transaction_details.exchange.clone(),
                    investment_id: investment.id.clone(),
                    investment_name: investment.name.clone(),
                    isin: investment.isin.clone(),
                    quantity: txn.transaction_details.quantity,
                    settlement_date: txn.transaction_details.settlement_date,
                    symbol: investment.symbol.clone(),
                    transaction_date: txn.transaction_details.transaction_date,
                    transaction_type: TransactionType::Debit,
                    transaction_sub_type: TransactionSubType::Stt,
                    remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                    receivable_status: stt_status,
                    ..Default::default()
                };

                portfolio_receivables.insert(conn).await.map_err(|e| {
                    return format!("Failed to Insert into receivables {:?}", e);
                })?;
            }
        }
    }

    Ok(())
}

/// This Add Transaction to a Mutual Fund
pub async fn add_transactions_to_mutual_fund_investment(
    mf_details: &HashMap<String, SecurityDetails>,
    conn: &mut Object<Manager>,
    client_id: String,
    portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
    release_payout: bool,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    //Group the Transaction By Isin

    //Get the ISIN investment for this portfolio from Investment Table

    let transactions_by_isin: HashMap<String, Vec<InvestmentTransactionForSettlement>> =
        transactions.into_iter().fold(HashMap::new(), |mut acc, client| {
            acc.entry(client.transaction_details.isin.clone())
                .or_insert_with(Vec::new)
                .push(client);
            acc
        });

    let receivable_status = if release_payout {
        ReceivableStatus::Received
    } else {
        ReceivableStatus::Hold
    };

    for (isin, transactions) in transactions_by_isin {
        let isin_details = mf_details.get(&isin).unwrap().get_mutual_fund_details().unwrap(); //WE ARE SURE THAT IT WILL HAVE THE DETAILS
        let investments = Investments::get_by_isin_and_portfolio_id(conn, isin.clone(), portfolio_id.clone())
            .await
            .unwrap();

        let price = isin_details.price;

        //Check whether Investment Exist for this ISIN in Portfolio Investments
        if let Some(investments) = investments {
            add_transactions_to_existing_mutual_fund_investment(
                price,
                isin_details,
                conn,
                investments,
                transactions,
                receivable_status.clone(),
                is_stt_included_in_cost,
            )
            .await?;
        } else {
            //Create a New Investment and put transactions into It
            add_transactions_to_new_mutual_fund_investment(
                price,
                isin_details,
                conn,
                isin,
                client_id.clone(),
                portfolio_id.clone(),
                transactions,
                receivable_status.clone(),
                is_stt_included_in_cost,
            )
            .await?;
        }
    }

    Ok(())
}

///  This Creates a New Mutual Fund Investment and Add Transaction to it
pub async fn add_transactions_to_new_mutual_fund_investment(
    price: f64,
    isin_details: &MutualFundData,
    conn: &mut Object<Manager>,
    isin: String,
    client_id: String,
    portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
    receivable_status: ReceivableStatus,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    let current_holding = calculate_current_holding_from_transaction(&transactions);
    let single_transaction = transactions[0].clone(); // Check Is Done Before Passing into this function

    let (min_date, max_date) = find_min_max_dates(&transactions[..]);

    let investment: Investments = Investments {
        client_id: Some(client_id.clone()),
        portfolio_id: Some(portfolio_id.clone()),
        current_holding,
        current_price_date: Utc::now().naive_utc() - Duration::days(1),
        isin,
        exchange: String::from("DIR"),
        current_price: price,
        market_value: ((price * current_holding) * 100.0).round() / 100.0,
        first_transaction_date: min_date,
        last_transaction_date: max_date,
        name: single_transaction.transaction_details.name,
        amfi_code: Some(isin_details.amfi_code.clone()),
        asset_class: isin_details.asset_class.clone(),
        category: Some(isin_details.category_name.clone()),
        fund_class: Some(isin_details.fund_class.clone()),
        security_type: single_transaction.security_type,
        symbol: isin_details.rta_code.clone(),
        ..Default::default()
    };

    //Insert Investment
    let new_investment_id = investment.insert(conn).await.map_err(|e| {
        return format!("Failed to Insert into investment {:?}", e);
    })?;

    //Insert into Investment Transaction Table
    // let _ = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|t| t.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|e| {
    //     return format!("Failed to Update Investment");
    // })?;

    //FIXME: Move to Bulk Update
    for mut txn in transactions {
        txn.transaction_details.investment_id = new_investment_id.clone();
        txn.transaction_details
            .insert(conn)
            .await
            .map_err(|e| format!("{:?}", e))?;

        if txn.transaction_details.transaction_type == TransactionType::Sell {
            let portfolio_receivables = PortfolioReceivables {
                amount: if !is_stt_included_in_cost {
                    txn.transaction_details.amount - txn.transaction_details.stt_amount
                } else {
                    txn.transaction_details.amount
                },
                price: txn.net_rate,
                client_id: client_id.clone(),
                portfolio_id: portfolio_id.clone(),
                cgt_date: txn.transaction_details.cgt_date,
                corporate_action_type: None,
                exchange: txn.transaction_details.exchange.clone(),
                investment_id: investment.id.clone(),
                investment_name: investment.name.clone(),
                isin: investment.isin.clone(),
                quantity: txn.transaction_details.quantity,
                settlement_date: txn.transaction_details.settlement_date,
                symbol: investment.symbol.clone(),
                transaction_date: txn.transaction_details.transaction_date,
                transaction_type: TransactionType::Credit,
                transaction_sub_type: TransactionSubType::Sell,
                remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                receivable_status: receivable_status.clone(),
                ..Default::default()
            };

            portfolio_receivables.insert(conn).await.map_err(|e| {
                return format!("Failed to Insert into receivables {:?}", e);
            })?;

            if !is_stt_included_in_cost {
                let stt_status = if receivable_status == ReceivableStatus::Received {
                    ReceivableStatus::Paid
                } else {
                    receivable_status.clone()
                };

                let portfolio_receivables = PortfolioReceivables {
                    amount: txn.transaction_details.stt_amount,
                    price: txn.transaction_details.stt_amount / txn.transaction_details.quantity,
                    client_id: investment.client_id.clone().unwrap(),
                    portfolio_id: investment.portfolio_id.clone().unwrap(),
                    cgt_date: txn.transaction_details.cgt_date,
                    corporate_action_type: None,
                    exchange: txn.transaction_details.exchange.clone(),
                    investment_id: investment.id.clone(),
                    investment_name: investment.name.clone(),
                    isin: investment.isin.clone(),
                    quantity: txn.transaction_details.quantity,
                    settlement_date: txn.transaction_details.settlement_date,
                    symbol: investment.symbol.clone(),
                    transaction_date: txn.transaction_details.transaction_date,
                    transaction_type: TransactionType::Debit,
                    transaction_sub_type: TransactionSubType::Stt,
                    remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                    receivable_status: stt_status,
                    ..Default::default()
                };

                portfolio_receivables.insert(conn).await.map_err(|e| {
                    return format!("Failed to Insert into receivables {:?}", e);
                })?;
            }
        }
    }

    Ok(())
}

/// This add transaction to an existing mutual Fund Investment
pub async fn add_transactions_to_existing_mutual_fund_investment(
    price: f64,
    isin_details: &MutualFundData,
    conn: &mut Object<Manager>,
    mut investment: Investments,
    transactions: Vec<InvestmentTransactionForSettlement>,
    receivable_status: ReceivableStatus,
    is_stt_included_in_cost: bool,
) -> Result<(), String> {
    let new_holdings_to_add: f64 = transactions.iter().map(|t| t.transaction_details.quantity).sum();
    let (min_date, max_date) = find_min_max_dates(&transactions);

    // Update investment's current_holdings , transaction in Database
    investment.current_holding += new_holdings_to_add;
    investment.market_value = ((investment.current_holding * price) * 100.0).round() / 100.0;
    investment.first_transaction_date = min_date;
    investment.last_transaction_date = max_date;

    let _ = investment.update_on_settlement(conn).await.map_err(|_e| {
        return format!("Failed to Update Investment");
    })?;

    //Insert transaction in Bulk
    // let _ = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|t| t.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|_e| {
    //     return format!("Failed to Insert Investment");
    // })?;

    //FIXME: Move to Bulk Update
    for mut txn in transactions {
        txn.transaction_details.investment_id = investment.id.clone();
        txn.transaction_details
            .insert(conn)
            .await
            .map_err(|e| format!("{:?}", e))?;

        if txn.transaction_details.transaction_type == TransactionType::Sell {
            let portfolio_receivables = PortfolioReceivables {
                amount: if !is_stt_included_in_cost {
                    txn.transaction_details.amount - txn.transaction_details.stt_amount
                } else {
                    txn.transaction_details.amount
                },
                price: txn.net_rate,
                client_id: investment.client_id.clone().unwrap(),
                portfolio_id: investment.portfolio_id.clone().unwrap(),
                cgt_date: txn.transaction_details.cgt_date,
                corporate_action_type: None,
                exchange: txn.transaction_details.exchange.clone(),
                investment_id: investment.id.clone(),
                investment_name: investment.name.clone(),
                isin: investment.isin.clone(),
                quantity: txn.transaction_details.quantity,
                settlement_date: txn.transaction_details.settlement_date,
                symbol: investment.symbol.clone(),
                transaction_date: txn.transaction_details.transaction_date,
                transaction_type: TransactionType::Credit,
                transaction_sub_type: TransactionSubType::Sell,
                remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                receivable_status: receivable_status.clone(),
                ..Default::default()
            };

            portfolio_receivables.insert(conn).await.map_err(|e| {
                return format!("Failed to Insert into receivables {:?}", e);
            })?;

            if !is_stt_included_in_cost {
                let stt_status = if receivable_status == ReceivableStatus::Received {
                    ReceivableStatus::Paid
                } else {
                    receivable_status.clone()
                };
                let portfolio_receivables = PortfolioReceivables {
                    amount: txn.transaction_details.stt_amount,
                    price: txn.transaction_details.stt_amount / txn.transaction_details.quantity,
                    client_id: investment.client_id.clone().unwrap(),
                    portfolio_id: investment.portfolio_id.clone().unwrap(),
                    cgt_date: txn.transaction_details.cgt_date,
                    corporate_action_type: None,
                    exchange: txn.transaction_details.exchange.clone(),
                    investment_id: investment.id.clone(),
                    investment_name: investment.name.clone(),
                    isin: investment.isin.clone(),
                    quantity: txn.transaction_details.quantity,
                    settlement_date: txn.transaction_details.settlement_date,
                    symbol: investment.symbol.clone(),
                    transaction_date: txn.transaction_details.transaction_date,
                    transaction_type: TransactionType::Debit,
                    transaction_sub_type: TransactionSubType::Stt,
                    remarks: format!("Credit|Sell|{}|{}", investment.symbol.clone(), investment.isin.clone()),
                    receivable_status: stt_status,
                    ..Default::default()
                };

                portfolio_receivables.insert(conn).await.map_err(|e| {
                    return format!("Failed to Insert into receivables {:?}", e);
                })?;
            }
        }
    }

    Ok(())
}

/// Calculate the balance (Holdings) from the provided transaction for the ISIN
pub fn calculate_balance_from_transactions(
    isin: String,
    mut transactions: Vec<InvestmentTransaction>,
) -> Vec<InvestmentTransaction> {
    for mut transaction in &mut transactions {
        let mut current_holding = 0.00;

        if transaction.transaction_type == TransactionType::Buy {
            transaction.unrealised_holding = transaction.quantity;
        } else if transaction.transaction_type == TransactionType::Sell {
            let quantity_to_sell = transaction.quantity;

            // Loop through all the Buy Transaction
        }
    }

    transactions
}

/// Get the Details of Mutual Fund By ISIN
pub async fn get_mutual_fund_details_by_isin(
    conn: &mut Object<Manager>,
    isin: String,
    purchase_allowed: bool,
    redemption_allowed: bool,
) -> Result<Option<SchemeMasterDetails>, String> {
    let mf_detail =
        SchemeMasterDetailsNew::get_mf_details_by_isin(conn, isin, purchase_allowed, redemption_allowed).await?;

    if mf_detail.is_none() {
        return Ok(None);
    }

    let mut mf_detail = mf_detail.unwrap();

    let yesterday = Utc::now().date_naive() - Duration::days(1);
    let price_as_at = HistoricalMutualFundNav::get_price_on_as_at_date(mf_detail.amfi_code.clone(), yesterday)
        .await?
        .unwrap();

    mf_detail.latest_price = price_as_at.nav.parse().unwrap();

    Ok(Some(mf_detail))
}

pub fn calculate_current_holding_from_transaction(transactions: &Vec<InvestmentTransactionForSettlement>) -> f64 {
    let mut holdings = 0f64;

    for transaction in transactions {
        if transaction.transaction_details.transaction_type == TransactionType::Buy {
            holdings += transaction.transaction_details.quantity;
        } else if transaction.transaction_details.transaction_type == TransactionType::Sell {
            holdings -= transaction.transaction_details.quantity;
        }
    }

    holdings
}
