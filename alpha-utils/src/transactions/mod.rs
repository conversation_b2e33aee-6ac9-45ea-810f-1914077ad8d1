use crate::settlement::InvestmentTransactionForSettlement;

pub mod portfolio_transaction;

pub fn find_min_max_dates(
    objects: &[InvestmentTransactionForSettlement],
) -> (chrono::NaiveDateTime, chrono::NaiveDateTime) {
    let (min_date, max_date) = objects.iter().fold(
        (
            objects[0].transaction_details.transaction_date,
            objects[0].transaction_details.transaction_date,
        ),
        |(min, max), obj| {
            let new_min = min.min(obj.transaction_details.transaction_date);
            let new_max = max.max(obj.transaction_details.transaction_date);
            (new_min, new_max)
        },
    );

    (min_date, max_date)
}
