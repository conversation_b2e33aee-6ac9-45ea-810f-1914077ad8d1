use alpha_core_db::storage::blob::connection::AzureBlob;

pub async fn upload_offsite_report_to_blob(request_id: &str, buffer: Vec<u8>) -> Result<String, String> {
    let mut azure_blob = AzureBlob::new(String::from("alphap-pdf-reports"));
    let file_path = generate_unique_blob_name("offsite-reports.zip", request_id);
    let blob_client = azure_blob.get_blob(&file_path);

    let result = blob_client.put_block_blob(buffer).content_type("application/zip").await;

    if result.is_ok() {
        Ok(file_path)
    } else {
        Err(String::from("Failed to Upload to Storage"))
    }
}

pub async fn upload_report_to_blob(request_id: &str, buffer: Vec<u8>, name: &str) -> Result<String, String> {
    let mut azure_blob = AzureBlob::new(String::from("alphap-pdf-reports"));
    let file_path = generate_unique_blob_name(&format!("{}.xml", name), request_id);
    let blob_client = azure_blob.get_blob(&file_path);

    let result = blob_client.put_block_blob(buffer).content_type("application/xml").await;

    if result.is_ok() {
        Ok(file_path)
    } else {
        Err(String::from("Failed to Upload to Storage"))
    }
}

/// Generates an Unique Name for the Blob in azure storage table
fn generate_unique_blob_name(original_name: &str, unique_id: &str) -> String {
    let extension = std::path::Path::new(original_name)
        .extension()
        .and_then(std::ffi::OsStr::to_str)
        .unwrap_or("");

    format!(
        "{}_{}.{}",
        std::path::Path::new(original_name)
            .file_stem()
            .and_then(std::ffi::OsStr::to_str)
            .unwrap_or(original_name),
        unique_id,
        extension
    )
}
