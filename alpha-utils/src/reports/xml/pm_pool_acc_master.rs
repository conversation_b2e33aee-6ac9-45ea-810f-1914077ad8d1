use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use quick_xml::se::to_string;
use csv::Writer;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::portfolio::Portfolio;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:PMPoolAccountMasterReport")]
#[serde(rename_all = "PascalCase")]
pub struct PMPoolAccMasterReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "PM_PoolAcc")]
    pub pms_pool_accounts: Vec<PMPoolAcc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct PMPoolAcc {
    pub pool_dp_id: String,
    pub pool_dp_name: String,
    pub pool_name: String,
    pub pool_boid: String,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_PMPA: i32 = 4;

// Function to generate XML and CSV reports
pub async fn generate_xml_pm_pool_acc_master(conn: &mut Object<Manager>) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let pm_pool_acc_master_rows: Vec<Row> = match Portfolio::get_pm_pool_acc_master_details(conn).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetch PM Pool Acc Master details: {}", err);
            return Err(anyhow::anyhow!("Error fetch PM Pool Acc Master details"));
        }
    };

    // Iterate through pm_pool_acc_master_rows and map to PMPoolAcc
    let pm_pool_acc_master_list: Vec<PMPoolAcc> = pm_pool_acc_master_rows
    .iter()
    .map(|row| PMPoolAcc {
        pool_dp_id: row
            .get::<&str, _>("POOL_DP_ID")
            .map(String::from)
            .unwrap_or("N/A".to_string()),  
        pool_dp_name: row
            .get::<&str, _>("POOL_DP_NAME")
            .map(String::from)
            .unwrap_or("N/A".to_string()),
        pool_name: row
            .get::<&str, _>("POOL_NAME")
            .map(String::from)
            .unwrap_or("N/A".to_string()),   
        pool_boid: row
            .get::<&str, _>("POOL_BOID")
            .map(String::from)
            .unwrap_or("N/A".to_string()),   
    })
    .collect();

    // Prepare CSV
    let csv_header = ["PM Pool Account Master", "Test123"];
    let csv_string = export_to_csv(&pm_pool_acc_master_list.clone(), &csv_header).unwrap();

    // Prepare the struct
    let pm_pool_acc_master_report = PMPoolAccMasterReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: "N/A".to_string(),
            },
            month: Month {
                month: "N/A".to_string(),
            },
        },
        pms_pool_accounts: pm_pool_acc_master_list,
    };

    let xml_string = to_string(&pm_pool_acc_master_report).unwrap();

    /* Create XML and CSV files on disc (for testing) 
    let mut filexml = File::create("pm_pool_acc_master.xml").unwrap();
    filexml.write_all(xml_string.as_bytes()).unwrap();
    std::fs::write("pm_pool_acc_master.csv", &csv_string).unwrap();
    */

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[PMPoolAcc],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header

        let header_size = header.len();
        let num_empty_strings = (LEN_PMPA - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_pmpoolaccmaster() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        generate_xml_pm_pool_acc_master(&mut pool_conn).await;
    }
}