use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::{NaiveDateTime, NaiveDate};
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::client::Client;

use crate::reports::serializer::serialize_option_datetime;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientFolioMasterReport")]
#[serde(rename_all = "PascalCase")]
pub struct ClientFolioMasterReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Folio")]
    pub client_folios: Vec<ClientFolioMaster>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientFolioMaster {
    pub unique_client_code: String,
    pub client_folio_no: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub agreement_date: Option<NaiveDateTime>,
    pub investment_approach: String,
    pub investment_strategy: String,
    pub benchmark: String,
    pub client_boid: String,
    pub custodian_reg_no: String,
    pub custodian_name: String,
    pub is_power_of_attorney_executed: String,
    pub is_permission_invst_in_associates: String,
    pub percentage_individual_equity_consent: String,
    pub percentage_total_equity_consent: String,
    pub percentage_individual_debt_consent: String,
    pub percentage_total_debt_consent: String,
    pub percentage_total_limit_consent: String,
    pub is_consent_rebalance_passive: String,
    pub is_consent_invst_in_equity_derivative: String,
    pub is_consent_invst_in_commodity_derivative: String,
    pub percentage_derivative_consent: String,
    pub is_consent_lending: String,
    pub client_custodian_code: String,
    pub pm_distributor_name: String,
    pub pm_distributor_pan: String,
    pub performance_fee_description: String,
    pub percentage_performance_fee: String,
}

// Function to generate XML report
pub async fn generate_xml_client_folio_master(conn: &mut Object<Manager>, from_date: NaiveDate, to_date: NaiveDate) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let client_folio_master_rows: Vec<Row> = match Client::get_client_folio_master_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching client folio master details: {}", err);
            return Err(anyhow::anyhow!("Error fetching client folio master details"));
        }
    };

    // todo: iterate through client_folio_master_rows and map to ClientFolioMaster
    // let _default_date = NaiveDateTime::from_ymd_opt(1, 1, 1).expect("Invalid date");
    let client_folio_master_list: Vec<ClientFolioMaster> = client_folio_master_rows
        .iter()
        .map(|row| ClientFolioMaster {
            unique_client_code: row
                .get::<&str, _>("UNIQUE_CLIENT_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            client_folio_no: row
                .get::<&str, _>("CLIENT_FOLIO_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            agreement_date: row
                .get::<NaiveDateTime, _>("AGREEMENT_DATE"),
            investment_approach: row
                .get::<&str, _>("INVESTMENT_APPROACH")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            investment_strategy: row
                .get::<&str, _>("INVESTMENT_STRATEGY")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            benchmark: row
                .get::<&str, _>("BENCHMARK")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            client_boid: row
                .get::<&str, _>("CLIENT_BOID")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            custodian_reg_no: row
                .get::<&str, _>("CUSTODIAN_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            custodian_name: row
                .get::<&str, _>("CUSTODIAN_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            is_power_of_attorney_executed: row
                .get::<bool, _>("IS_POWER_OF_ATTORNEY_EXECUTED")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_permission_invst_in_associates: row
                .get::<bool, _>("IS_PERMISSION_INVST_IN_ASSOCIATES")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_individual_equity_consent: row
                .get::<f64, _>("PERCENTAGE_INDIVIDUAL_EQUITY_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_total_equity_consent: row
                .get::<f64, _>("PERCENTAGE_TOTAL_EQUITY_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_individual_debt_consent: row
                .get::<f64, _>("PERCENTAGE_INDIVIDUAL_DEBT_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_total_debt_consent: row
                .get::<f64, _>("PERCENTAGE_TOTAL_DEBT_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_total_limit_consent: row
                .get::<f64, _>("PERCENTAGE_TOTAL_LIMIT_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_consent_rebalance_passive: row
                .get::<bool, _>("IS_CONSENT_REBALANCE_PASSIVE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_consent_invst_in_equity_derivative: row
                .get::<bool, _>("IS_CONSENT_INVST_IN_EQUITY_DERIVATIVE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_consent_invst_in_commodity_derivative: row
                .get::<bool, _>("IS_CONSENT_INVST_IN_COMMODITY_DERIVATIVE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            percentage_derivative_consent: row
                .get::<f64, _>("PERCENTAGE_DERIVATIVE_CONSENT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_consent_lending: row
                .get::<bool, _>("IS_CONSENT_LENDING")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            client_custodian_code: row
                .get::<&str, _>("CLIENT_CUSTODIAN_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_distributor_name: row
                .get::<&str, _>("PM_DISTRIBUTOR_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_distributor_pan: row
                .get::<&str, _>("PM_DISTRIBUTOR_PAN")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            performance_fee_description: row
                .get::<&str, _>("PERFORMANCE_FEE_DESCRIPTION")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            percentage_performance_fee: row
                .get::<f64, _>("PERCENTAGE_PERFORMANCE_FEE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
        })
        .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();

    // Prepare the struct
    let client_folio_master_report = ClientFolioMasterReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: year_str,
            },
            month: Month {
                month: month_str,
            },
        },
        client_folios: client_folio_master_list,
    };

    // Serialize to XML
    let xml = to_string(&client_folio_master_report).unwrap();
    let mut file = File::create("client_folio_master_report.xml").unwrap();
    file.write_all(xml.as_bytes()).unwrap();
    /* 
    */
    Ok(xml)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_clientfoliomaster() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();

        let test_from_date = NaiveDate::from_ymd_opt(2024, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2025, 1, 31).expect("Invalid Date");
        
        generate_xml_client_folio_master(&mut pool_conn, test_from_date, test_to_date).await;
    }
}
