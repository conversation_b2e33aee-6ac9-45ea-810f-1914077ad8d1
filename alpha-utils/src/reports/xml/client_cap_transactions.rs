use std::{any, fs::{self, File}, i32, io::Write, path::Path};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::{NaiveDateTime, NaiveDate};
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;
use csv::Writer;
use tracing_subscriber::fmt::format;
use crate::reports::serializer::serialize_option_datetime;

use alpha_core_db::schema::client::Client;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientCapTransactionsReport")]
#[serde(rename_all = "PascalCase")]
pub struct ClientCapTransactionsReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Cap_Transactions")]
    pub client_cap_transactions: Vec<ClientCapTransactions>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientCapTransactions {
    pub unique_client_code: String,
    pub client_folio_no: String,
    pub transaction_type: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub transaction_date: Option<NaiveDateTime>,
    pub transaction_amount: String,
    pub exit_load: String,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_CCT: i32 = 6;

#[derive(Debug)]
pub enum ReportOutputType {
    Xml,
    Csv,
}

// Function to generate XML and CSV reports
pub async fn generate_xml_client_cap_transactions(
    conn: &mut Object<Manager>, 
    from_date: NaiveDate, 
    to_date: NaiveDate,
    output_type: ReportOutputType
) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let client_cap_transactions_rows: Vec<Row> = match Client::get_client_cap_transactions_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching client cap details: {}", err);
            return Err(anyhow::anyhow!("Failed to Fetch Data From DB"));
        }
    };

    // Iterate through client_cap_transactions_rows here and map results to ClientCapTransactions
    let client_cap_transactions_list: Vec<ClientCapTransactions> = client_cap_transactions_rows
        .iter()
        .map(|row| ClientCapTransactions {
            unique_client_code: row
                .get::<&str, _>("UNIQUE_CLIENT_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            client_folio_no: row
                .get::<&str, _>("CLIENT_FOLIO_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            transaction_type: row
                .get::<&str, _>("TRANSACTION_TYPE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            transaction_date: row
                .get::<NaiveDateTime, _>("TRANSACTION_DATE"),
            transaction_amount: row
                .get::<f64, _>("TRANSACTION_AMOUNT")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            exit_load: row
                .get::<f64, _>("EXIT_LOAD")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
        })
        .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();
    
    // XML function
    fn generate_xml(
        list: Vec<ClientCapTransactions>,
        year_str: String,
        month_str: String,
    ) -> anyhow::Result<String> {
        // Prepare the struct
        let client_cap_transactions_report = ClientCapTransactionsReport {
            xmlns_ns1: "http://example.com/namespace".to_string(),
            header: Header {
                login_id: "Test123".to_string(),
                year: Year {
                    year: year_str,
                },
                month: Month {
                    month: month_str,
                },
            },
            client_cap_transactions: list,
        };
        // Serialize to XML
        let xml_string = to_string(&client_cap_transactions_report).unwrap();
        
        /* File on disk for testing */
        let mut filexml = File::create("client_cap_transactions.xml").unwrap();
        filexml.write_all(xml_string.as_bytes()).unwrap();
        
        Ok(xml_string)
    }

    fn generate_csv(
        list: Vec<ClientCapTransactions>,
        year_str: String,
        month_str: String,
    ) -> anyhow::Result<String> {
        // Prepare CSV
        let csv_header = ["Client Cap Transactions", "Test123", year_str.as_str(), month_str.as_str()];
        let csv_string = export_to_csv(list, &csv_header).unwrap();
        
        /* File on disk for testing */
        std::fs::write("client_cap_transactions.csv", &csv_string).unwrap();

        Ok(csv_string)
    }

    // Return XML or CSV based on option
    match output_type {
        ReportOutputType::Xml => generate_xml(client_cap_transactions_list, year_str, month_str),
        ReportOutputType::Csv => generate_csv(client_cap_transactions_list, year_str, month_str),
    }
}

pub fn export_to_csv(
    transactions: Vec<ClientCapTransactions>,
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header
        header.push(format!("{} {}", header_data.get(2).unwrap_or(&""), header_data.get(3).unwrap_or(&"")));

        let header_size = header.len();
        let num_empty_strings = (LEN_CCT - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_clientcaptransactions() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let test_from_date = NaiveDate::from_ymd_opt(2025, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2025, 1, 31).expect("Invalid Date");
        generate_xml_client_cap_transactions(&mut pool_conn, test_from_date, test_to_date, ReportOutputType::Xml).await;
    }
}
