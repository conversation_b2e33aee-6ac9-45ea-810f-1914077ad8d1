use alpha_core_db::schema::capital_register::PortfolioCapitalRegister;
use alpha_core_db::schema::client::Client;
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::investment_transaction::InvestmentTransaction,
};
use chrono::{Datelike, NaiveDate, NaiveDateTime};
use quick_xml::se::to_string;
use reqwest::Client as ReqwestClient;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{fs::File, io::Write};
use tiberius::Row;
use tokio; // Ensure tokio is imported for async operations

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:SebiMonthly")]
#[serde(rename_all = "PascalCase")]
pub struct SebiMonthly {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Discretionary_Service")]
    pub discretionary_service: Vec<DiscretionaryServices>,
    #[serde(rename = "Non_Discretionary_Service")]
    pub non_discretionary_service: Vec<DiscretionaryServices>,
    #[serde(rename = "Advisory_Service")]
    pub advisory_services: Vec<DiscretionaryServices>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Strategies {
    #[serde(rename = "Strategy")]
    pub strategies: Vec<Strategy>,
    pub investment_approach: InvestmentApproach,
    pub benchmark: Benchmark,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Strategy {
    pub strategy_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct InvestmentApproach {
    pub approach_name: String,
    #[serde(rename = "AUMInCrore")]
    pub aum_in_crore: f64,
    #[serde(rename = "ReturnPercentage")]
    pub return_percentage: ReturnPercentage,
    #[serde(rename = "PortfolioTurnoverratio")]
    pub portfolio_turnover_ratio: PortfolioTurnoverRatio,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Benchmark {
    pub benchmark_name: String,
    #[serde(rename = "ReturnPercentage")]
    pub return_percentage: ReturnPercentage,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ReturnPercentage {
    pub one_month: f64,
    pub three_month: f64,
    pub six_month: f64,
    pub one_year: f64,
    pub two_year: f64,
    pub three_year: f64,
    pub four_year: f64,
    pub five_year: f64,
    pub since_inception: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct PortfolioTurnoverRatio {
    pub one_month: f64,
    pub one_year: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ClientBreakUp {
    #[serde(rename = "Domestic_PF_EPFO")]
    pub domestic_pf_epfo: i32,
    #[serde(rename = "Domestic_Corporate")]
    pub domestic_corporate: i32,
    #[serde(rename = "Domestic_Non_Corporate")]
    pub domestic_non_corporate: i32,
    #[serde(rename = "Domestic_Non_Resident")]
    pub foreign_non_resident: i32,
    #[serde(rename = "Foreign_Fpi")]
    pub foreign_fpi: i32,
    #[serde(rename = "Foreign_Others")]
    pub foreign_others: i32,
    #[serde(rename = "Total")]
    pub total: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApproachIO {
    #[serde(rename = "ApproachName_io")]
    pub name: String,
    #[serde(rename = "Inflow_during_month")]
    pub inflow_month: String,
    #[serde(rename = "Outflow_during_month")]
    pub outflow_month: String,
    #[serde(rename = "Netflow_Month")]
    pub netflow_month: String,
    #[serde(rename = "Inflow_FY")]
    pub inflow_fy: String,
    #[serde(rename = "Outflow_FY")]
    pub outflow_fy: String,
    #[serde(rename = "Netflow_FY")]
    pub netflow_fy: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InflowOutflowData {
    #[serde(rename = "Approach_IO")]
    pub approach_io: ApproachIO,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TransactionsData {
    #[serde(rename = "Sales_In_Month_In_Crores")]
    pub sales_in_month: String,
    #[serde(rename = "Purchase_In_Month_In_Crores")]
    pub purchase_in_month: String,
    #[serde(rename = "Portfolio_Turnover_Ratio")]
    pub turn_over: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AssetClassBreakdown {
    #[serde(rename = "Listed")]
    pub listed: f64,
    #[serde(rename = "UnListed")]
    pub unlisted: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AssetClassMarketValue {
    pub asset_class: String,
    #[serde(flatten)]
    pub breakdown: AssetClassBreakdown,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TotalAUMonLastDayInCrore {
    pub approach_name: String,
    pub equity: AssetClassBreakdown,
    pub plain_debt: AssetClassBreakdown,
    pub structured_debt: AssetClassBreakdown,
    pub derivatives: DerivativeBreakdown,
    pub mutual_funds: f64,
    pub others: f64,
    pub total: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DerivativeBreakdown {
    pub equity: f64,
    pub commodity: f64,
    pub others: f64,
}
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct DiscretionaryServices {
    pub client_breakup: ClientBreakUp,
    #[serde(rename = "Asset_Class_Market_Value")]
    pub total_au_on_last_day: Vec<TotalAUMonLastDayInCrore>,
    pub inflow_outflow_data: Vec<InflowOutflowData>,
    #[serde(rename = "Transactions_Data")]
    pub transactions_data: Vec<TransactionsData>,
    pub performance_data: Vec<Strategies>,
}

async fn get_service_counts(
    conn: &mut Object<Manager>,
    portfolio_type: &str,
    from_date: NaiveDateTime,
    to_date: NaiveDateTime,
    fy_from: NaiveDate,
    fy_to: NaiveDate,
) -> DiscretionaryServices {
    // CLIENT BREAKUP
    let domestic_pf_epfo_count =
        Client::get_count_by_type_and_domicile(conn, portfolio_type, "Resident", "PF", to_date)
            .await
            .unwrap_or(0);
    let domestic_corporate_count =
        Client::get_count_by_type_and_domicile(conn, portfolio_type, "Resident", "Corporate", to_date)
            .await
            .unwrap_or(0);
    let domestic_non_corporate_count =
        Client::get_count_by_type_and_domicile(conn, portfolio_type, "Resident", "Individual", to_date)
            .await
            .unwrap_or(0);
    let foreign_non_resident_count =
        Client::get_count_by_type_and_domicile(conn, portfolio_type, "NonResident", "Individual", to_date)
            .await
            .unwrap_or(0);
    let foreign_fpi_count = Client::get_count_by_type_and_domicile(conn, portfolio_type, "NonResident", "FPI", to_date)
        .await
        .unwrap_or(0);
    let foreign_others_count =
        Client::get_count_by_type_and_domicile(conn, portfolio_type, "NonResident", "NonIndividual", to_date)
            .await
            .unwrap_or(0);

    let total_count = domestic_pf_epfo_count
        + domestic_corporate_count
        + domestic_non_corporate_count
        + foreign_non_resident_count
        + foreign_fpi_count
        + foreign_others_count;

    let client_breakup = ClientBreakUp {
        domestic_pf_epfo: domestic_pf_epfo_count,
        domestic_corporate: domestic_corporate_count,
        domestic_non_corporate: domestic_non_corporate_count,
        foreign_non_resident: foreign_non_resident_count,
        foreign_fpi: foreign_fpi_count,
        foreign_others: foreign_others_count,
        total: total_count,
    };

    // INFLOW/OUTFLOW
    let inflows_outflows: Vec<Row> = PortfolioCapitalRegister::get_entries_for_sebi_monthly(
        conn,
        from_date,
        to_date,
        fy_from,
        fy_to,
        portfolio_type.to_string(),
    )
    .await
    .unwrap_or_default();

    let inflow_outflow_list = inflows_outflows
        .iter()
        .map(|row| InflowOutflowData {
            approach_io: ApproachIO {
                name: row
                    .get::<&str, _>("Name")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                inflow_month: row
                    .get::<f64, _>("Inflow_Amount_Month")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
                outflow_month: row
                    .get::<f64, _>("Outflow_Amount_Month")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
                netflow_month: row
                    .get::<f64, _>("Net_Inflow_Outflow_Month")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
                inflow_fy: row
                    .get::<f64, _>("Inflow_Amount_Year")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
                outflow_fy: row
                    .get::<f64, _>("Outflow_Amount_Year")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
                netflow_fy: row
                    .get::<f64, _>("Net_Inflow_Outflow_Year")
                    .map(|v| v.to_string())
                    .unwrap_or("0".to_string()),
            },
        })
        .collect();

    // TRANSACTIONS DATA
    let purchase_transaction_data =
        InvestmentTransaction::get_transactions_data(conn, portfolio_type.to_string(), "Buy", from_date, to_date)
            .await
            .unwrap_or(0);
    let sell_transaction_data =
        InvestmentTransaction::get_transactions_data(conn, portfolio_type.to_string(), "Sell", from_date, to_date)
            .await
            .unwrap_or(0);
    let portfolio_turn_over_ratio = purchase_transaction_data.max(sell_transaction_data) / 10; // TODO: Use actual denominator

    let transactions_data = vec![TransactionsData {
        sales_in_month: sell_transaction_data.to_string(),
        purchase_in_month: purchase_transaction_data.to_string(),
        turn_over: portfolio_turn_over_ratio.to_string(),
    }];

    // COMMON API CALL
    let client = ReqwestClient::new();
    let body = serde_json::json!({
        "fromDate": from_date.to_string(),
        "toDate": to_date.to_string()
    });

    let response = client
        .post("https://storage.demo.alphap.actlogica.com/strategy/get_strategy_details_for_sebi_monthly")
        .json(&body)
        .send()
        .await;

    let response_text = match response {
        Ok(res) => res.text().await.unwrap_or_else(|_| "{\"data\":[]}".to_string()),
        Err(_) => "{\"data\":[]}".to_string(),
    };

    let json_value: Value = serde_json::from_str(&response_text).unwrap_or_else(|_| Value::Null);
    let data = json_value.get("data");

    // HELPER MACRO
    macro_rules! val {
        ($obj:expr, $key:expr) => {
            $obj.get($key).and_then(|v| v.as_f64()).unwrap_or(0.0)
        };
    }

    // TOTAL AUM
    let mut total_au_on_last_day = match data {
        Some(Value::Object(map)) => map
            .iter()
            .map(|(approach_name, value)| TotalAUMonLastDayInCrore {
                approach_name: approach_name.clone(),
                equity: AssetClassBreakdown {
                    listed: val!(&value["Equity"], "Listed"),
                    unlisted: val!(&value["Equity"], "UnListed"),
                },
                plain_debt: AssetClassBreakdown {
                    listed: val!(&value["PlainDebt"], "Listed"),
                    unlisted: val!(&value["PlainDebt"], "UnListed"),
                },
                structured_debt: AssetClassBreakdown {
                    listed: val!(&value["StructuredDebt"], "Listed"),
                    unlisted: val!(&value["StructuredDebt"], "UnListed"),
                },
                derivatives: DerivativeBreakdown {
                    equity: val!(&value["Derivatives"], "Equity"),
                    commodity: val!(&value["Derivatives"], "Commodity"),
                    others: val!(&value["Derivatives"], "Others"),
                },
                mutual_funds: val!(value, "MutualFunds"),
                others: val!(value, "Others"),
                total: val!(value, "Total"),
            })
            .collect(),
        _ => vec![],
    };

    if total_au_on_last_day.is_empty() {
        total_au_on_last_day.push(TotalAUMonLastDayInCrore {
            approach_name: "No Data".to_string(),
            equity: AssetClassBreakdown {
                listed: 0.0,
                unlisted: 0.0,
            },
            plain_debt: AssetClassBreakdown {
                listed: 0.0,
                unlisted: 0.0,
            },
            structured_debt: AssetClassBreakdown {
                listed: 0.0,
                unlisted: 0.0,
            },
            derivatives: DerivativeBreakdown {
                equity: 0.0,
                commodity: 0.0,
                others: 0.0,
            },
            mutual_funds: 0.0,
            others: 0.0,
            total: 0.0,
        });
    }

    // PERFORMANCE DATA
    let strategies_data: Vec<Strategies> = match data {
        Some(Value::Array(array)) if !array.is_empty() => array
            .iter()
            .map(|entry| {
                let strategy_name = entry
                    .get("strategyName")
                    .and_then(|v| v.as_str())
                    .unwrap_or("Unknown")
                    .to_string();
                let approach = entry.get("investmentApproach").unwrap_or(&Value::Null);
                let benchmark = entry.get("benchmark").unwrap_or(&Value::Null);

                Strategies {
                    strategies: vec![Strategy { strategy_name }],
                    investment_approach: InvestmentApproach {
                        approach_name: approach
                            .get("approachName")
                            .and_then(|v| v.as_str())
                            .unwrap_or("N/A")
                            .to_string(),
                        aum_in_crore: val!(approach, "aum"),
                        return_percentage: ReturnPercentage {
                            one_month: val!(&approach["return"], "1Month"),
                            three_month: val!(&approach["return"], "3Month"),
                            six_month: val!(&approach["return"], "6Month"),
                            one_year: val!(&approach["return"], "1Year"),
                            two_year: 0.0,
                            three_year: 0.0,
                            four_year: 0.0,
                            five_year: 0.0,
                            since_inception: val!(&approach["return"], "SinceInception"),
                        },
                        portfolio_turnover_ratio: PortfolioTurnoverRatio {
                            one_month: val!(&approach["turnoverRatio"], "1Month"),
                            one_year: val!(&approach["turnoverRatio"], "1Year"),
                        },
                    },
                    benchmark: Benchmark {
                        benchmark_name: benchmark
                            .get("benchmarkName")
                            .and_then(|v| v.as_str())
                            .unwrap_or("N/A")
                            .to_string(),
                        return_percentage: ReturnPercentage {
                            one_month: val!(&benchmark["return"], "1Month"),
                            three_month: val!(&benchmark["return"], "3Month"),
                            six_month: val!(&benchmark["return"], "6Month"),
                            one_year: val!(&benchmark["return"], "1Year"),
                            two_year: 0.0,
                            three_year: 0.0,
                            four_year: 0.0,
                            five_year: 0.0,
                            since_inception: val!(&benchmark["return"], "SinceInception"),
                        },
                    },
                }
            })
            .collect(),
        _ => vec![],
    };

    DiscretionaryServices {
        client_breakup,
        total_au_on_last_day,
        inflow_outflow_data: inflow_outflow_list,
        transactions_data,
        performance_data: strategies_data,
    }
}

pub async fn generate_sebi_monthly(
    conn: &mut Object<Manager>,
    from_date: NaiveDate,
    to_date: NaiveDate,
) -> anyhow::Result<String> {
    let fy_from = if from_date.month() >= 4 {
        NaiveDate::from_ymd_opt(from_date.year(), 4, 1).unwrap()
    } else {
        NaiveDate::from_ymd_opt(from_date.year() - 1, 4, 1).unwrap()
    };

    let fy_to = if to_date.month() >= 4 {
        NaiveDate::from_ymd_opt(to_date.year() + 1, 3, 31).unwrap()
    } else {
        NaiveDate::from_ymd_opt(to_date.year(), 3, 31).unwrap()
    };

    let discretionary_total =
        get_service_counts(conn, "Discretionary", from_date.into(), to_date.into(), fy_from, fy_to).await;
    let non_discretionary_total = get_service_counts(
        conn,
        "NonDiscretionary",
        from_date.into(),
        to_date.into(),
        fy_from,
        fy_to,
    )
    .await;
    let advisory_total = get_service_counts(conn, "Advisory", from_date.into(), to_date.into(), fy_from, fy_to).await;

    let sebi_monthly = SebiMonthly {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: "2024".to_string(),
            },
            month: Month {
                month: "September".to_string(),
            },
        },
        discretionary_service: vec![discretionary_total],
        non_discretionary_service: vec![non_discretionary_total],
        advisory_services: vec![advisory_total],
    };

    let xml = to_string(&sebi_monthly).unwrap();

    // // Save the XML to a file asynchronously
    tokio::fs::write("sebi_monthly.xml", xml.as_bytes()).await.unwrap();
    let xml = to_string(&sebi_monthly).unwrap();

    Ok(xml)
}

#[cfg(test)]
mod tests {
    use chrono::{NaiveTime, Utc};

    use super::*;

    #[tokio::test]
    async fn test_generate_xml_sebi_monthly() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let from_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap().and_time(NaiveTime::MIN);
        let to_date = NaiveDate::from_ymd_opt(2025, 2, 28).unwrap().and_time(NaiveTime::MIN);
        let result = generate_sebi_monthly(&mut pool_conn, from_date.into(), to_date.into()).await;
    }
}
