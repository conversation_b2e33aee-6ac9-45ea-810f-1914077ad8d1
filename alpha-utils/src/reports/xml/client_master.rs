use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::{NaiveDateTime, NaiveDate};
use csv::Writer;
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use crate::reports::serializer::serialize_option_datetime;

use alpha_core_db::schema::client::Client;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientMasterReport")]
#[serde(rename_all = "PascalCase")]
pub struct ClientMasterReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Master")]
    pub client_master: Vec<ClientMaster>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientMaster {
    pub unique_client_code: String,
    pub client_folio_no: String,
    pub client_pan: String,
    pub client_boid: String,
    pub client_category: String,
    pub client_sub_category: String,
    pub service_category: String,
    pub client_first_name: String,
    pub client_middle_name: String,
    pub client_last_name: String,
    pub client_address: String,
    pub client_city: String,
    pub client_state: String,
    pub client_pincode: String,
    pub client_country: String,
    pub client_primary_mobile_no: String,
    pub client_email: String,
    pub joint_holder_1_name: String,
    pub joint_holder_1_pan: String,
    pub joint_holder_2_name: String,
    pub joint_holder_2_pan: String,
    pub holding_nature: String,
    pub nominee_1_name: String,
    pub nominee_1_pan_no: String,
    pub first_holder_gender: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub first_holder_dob: Option<NaiveDateTime>,
    pub first_holder_nationality: String,
    pub first_holder_occupation: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub date_of_pms_account_activation: Option<NaiveDateTime>,
    pub is_account_active: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub inactive_since: Option<NaiveDateTime>,
    pub account_inactivity_desc: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub date_of_pms_account_closure: Option<NaiveDateTime>,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_CM: i32 = 33; 

// Function to generate XML and CSV reports
pub async fn generate_xml_client_master(conn: &mut Object<Manager>, from_date: NaiveDate, to_date: NaiveDate) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let client_master_rows: Vec<Row> = match Client::get_client_master_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching Client Master details: {}", err);
            return Err(anyhow::anyhow!("Failed to Fetch Data From DB"));
        }
    };

    // Iterate through client_master_rows here and map results to ClientMaster
    let client_master_list: Vec<ClientMaster> = client_master_rows
        .iter()
        .map(|row| {
            ClientMaster {
                unique_client_code: row
                    .get::<&str, _>("UNIQUE_CLIENT_CODE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_folio_no: row
                    .get::<&str, _>("CLIENT_FOLIO_NO")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_pan: row
                    .get::<&str, _>("CLIENT_PAN")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_boid: row
                    .get::<&str, _>("CLIENT_BOID")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_category: row
                    .get::<&str, _>("CLIENT_CATEGORY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_sub_category: row
                    .get::<&str, _>("CLIENT_SUB_CATEGORY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                service_category: row
                    .get::<&str, _>("SERVICE_CATEGORY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_first_name: row
                    .get::<&str, _>("CLIENT_FIRST_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_middle_name: row
                    .get::<&str, _>("CLIENT_MIDDLE_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_last_name: row
                    .get::<&str, _>("CLIENT_LAST_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_address: row
                    .get::<&str, _>("CLIENT_ADDRESS")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_city: row
                    .get::<&str, _>("CLIENT_CITY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_state: row
                    .get::<&str, _>("CLIENT_STATE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_pincode: row
                    .get::<&str, _>("CLIENT_PINCODE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_country: row
                    .get::<&str, _>("CLIENT_COUNTRY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_primary_mobile_no: row
                    .get::<&str, _>("CLIENT_PRIMARY_MOBILE_NO")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_email: row
                    .get::<&str, _>("CLIENT_EMAIL")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                joint_holder_1_name: row
                    .get::<&str, _>("JOINT_HOLDER_1_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                joint_holder_1_pan: row
                    .get::<&str, _>("JOINT_HOLDER_1_PAN")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                joint_holder_2_name: row
                    .get::<&str, _>("JOINT_HOLDER_2_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                joint_holder_2_pan: row
                    .get::<&str, _>("JOINT_HOLDER_2_PAN")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                holding_nature: row
                    .get::<&str, _>("HOLDING_NATURE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                nominee_1_name: row
                    .get::<&str, _>("NOMINEE_1_NAME")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                nominee_1_pan_no: row
                    .get::<&str, _>("NOMINEE_1_PAN_NO")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                first_holder_gender: row
                    .get::<&str, _>("FIRST_HOLDER_GENDER")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                first_holder_dob: row
                    .get::<NaiveDateTime, _>("FIRST_HOLDER_DOB"),
                first_holder_nationality: row
                    .get::<&str, _>("FIRST_HOLDER_NATIONALITY")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                first_holder_occupation: row
                    .get::<&str, _>("FIRST_HOLDER_OCCUPATION")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                date_of_pms_account_activation: row
                    .get::<NaiveDateTime, _>("DATE_OF_PMS_ACCOUNT_ACTIVATION"),
                is_account_active: row
                    .get::<&str, _>("IS_ACCOUNT_ACTIVE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                inactive_since: row
                    .get::<NaiveDateTime, _>("INACTIVE_SINCE"),
                account_inactivity_desc: row
                    .get::<&str, _>("ACCOUNT_INACTIVITY_DESC")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                date_of_pms_account_closure: row   
                    .get::<NaiveDateTime, _>("DATE_OF_PMS_ACCOUNT_CLOSURE"),
            }
        })
        .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();

    // Prepare CSV
    let csv_header = ["Client Master", "Test123", year_str.as_str(), month_str.as_str()];
    let csv_string = export_to_csv(&client_master_list.clone(), &csv_header).unwrap();

    // Prepare the struct
    let client_master_report = ClientMasterReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: year_str,
            },
            month: Month {
                month: month_str,
            },
        },
        client_master: client_master_list,
    };

    // Serialize to XML
    let xml_string = to_string(&client_master_report).unwrap();

    /* Create XML and CSV files on disk (for testing) 
    let mut filexml = File::create("client_master.xml").unwrap();
    filexml.write_all(xml.as_bytes()).unwrap();
    std::fs::write("client_master.csv", &csv_string).unwrap();
    */

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[ClientMaster],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header
        header.push(format!("{} {}", header_data.get(2).unwrap_or(&""), header_data.get(3).unwrap_or(&"")));

        let header_size = header.len();
        let num_empty_strings = (LEN_CM - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_clientmaster() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();

        let test_from_date = NaiveDate::from_ymd_opt(2024, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2026, 1, 31).expect("Invalid Date");

        generate_xml_client_master(&mut pool_conn, test_from_date, test_to_date).await;
    }
}
