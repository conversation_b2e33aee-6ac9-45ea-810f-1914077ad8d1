use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::{NaiveDateTime, NaiveDate};
use csv::Writer;
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::client::Client;

use crate::reports::serializer::serialize_option_datetime;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientHoldingReport")]
#[serde(rename_all = "PascalCase")]
pub struct ClientHoldingReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Holding")]
    pub client_holdings: Vec<ClientHolding>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientHolding {
    #[serde(serialize_with = "serialize_option_datetime")]
    pub holding_date: Option<NaiveDateTime>,
    pub unique_client_code: String,
    pub client_folio_no: String,
    pub investment_type: String,
    pub asset_type: String,
    pub issuer_name: String,
    pub security_name: String,
    pub security_isin: String,
    pub security_code: String,
    pub is_security_associated: String,
    pub is_security_listed: String,
    pub security_rating: String,
    pub rating_agency: String,
    pub quantity: String,
    pub unit_price: String,
    pub market_value: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub maturity_date: Option<NaiveDateTime>,
    pub option_type: String,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_CH: i32 = 18; 

// Function to generate XML and CSV reports
pub async fn generate_xml_client_holding(conn: &mut Object<Manager>, from_date: NaiveDate, to_date: NaiveDate)->anyhow::Result<String> {
    // Fetch details that have been queried
    let client_holding_rows: Vec<Row> = match Client::get_client_holding_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching client holding details: {}", err);
            return Err(anyhow::anyhow!("Error fetching client holding details"));
        }
    };

    // Iterate through client_holding_rows and map to ClientHolding
    let client_holding_list: Vec<ClientHolding> = client_holding_rows
        .iter()
        .map(|row| ClientHolding {
            holding_date: row
                .get::<NaiveDateTime, _>("HOLDING_DATE"),
            unique_client_code: row
                .get::<&str, _>("UNIQUE_CLIENT_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            client_folio_no: row
                .get::<&str, _>("CLIENT_FOLIO_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            investment_type: row
                .get::<&str, _>("INVESTMENT_TYPE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            asset_type: row
                .get::<&str, _>("ASSET_TYPE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            issuer_name: row
                .get::<&str, _>("ISSUER_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            security_name: row
                .get::<&str, _>("SECURITY_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            security_isin: row
                .get::<&str, _>("SECURITY_ISIN")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            security_code: row
                .get::<&str, _>("SECURITY_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            is_security_associated: row
                .get::<bool, _>("IS_SECURITY_ASSOCIATED")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            is_security_listed: row
                .get::<&str, _>("IS_SECURITY_LISTED")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            security_rating: row
                .get::<&str, _>("SECURITY_RATING")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            rating_agency: row
                .get::<&str, _>("RATING_AGENCY")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            quantity: row
                .get::<f64, _>("QUANTITY")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            unit_price: row
                .get::<f64, _>("UNIT_PRICE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            market_value: row
                .get::<f64, _>("MARKET_VALUE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            maturity_date: row
                .get::<NaiveDateTime, _>("MATURITY_DATE"),
            option_type: row
                .get::<&str, _>("OPTION_TYPE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
        })
        .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();

    // Prepare CSV
    let csv_header = ["Client Holding", "Test123", year_str.as_str(), month_str.as_str()];
    let csv_string = export_to_csv(&client_holding_list.clone(), &csv_header).unwrap();

    // Prepare the struct
    let client_holding_report = ClientHoldingReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: year_str,
            },
            month: Month {
                month: month_str,
            },
        },
        client_holdings: client_holding_list,
    };

    // Serialize to XML
    let xml_string = to_string(&client_holding_report).unwrap();

    /* Create XML and CSV files on disk (for testing) 
    let mut filexml = File::create("client_holding.xml").unwrap();
    filexml.write_all(xml.as_bytes()).unwrap();
    std::fs::write("client_holding.csv", &csv_string).unwrap();
    */

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[ClientHolding],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header
        header.push(format!("{} {}", header_data.get(2).unwrap_or(&""), header_data.get(3).unwrap_or(&"")));

        let header_size = header.len();
        let num_empty_strings = (LEN_CH - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_clientholding() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();

        let test_from_date = NaiveDate::from_ymd_opt(2024, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2026, 1, 31).expect("Invalid Date");

        generate_xml_client_holding(&mut pool_conn, test_from_date, test_to_date).await;
    }
}
