use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::NaiveDate;
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::portfolio::Portfolio;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:PMOperatingExpenseReport")]
#[serde(rename_all = "PascalCase")]
pub struct PMOperatingExpenseReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "PM_Operating_Expense")]
    pub pm_operating_expenses: Vec<PMOperatingExpense>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct PMOperatingExpense {
    pub accrual_date: NaiveDate,
    pub vendor_pan: VendorPAN,
    pub vendor_name: String,
    pub nature_of_service: String,
    pub is_vendor_associate_of_pms: u8,
    pub amount_paid: f64,
    pub frequency: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VendorPAN {
    #[serde(rename = "Is_Valid_PAN")]
    pub is_valid_pan: String,
}

// Function to generate XML report
pub async fn generate_xml_pm_operating_expense(conn: &mut Object<Manager>)->anyhow::Result<String>{
    // Fetch details that have been queried
    let pm_operating_expense_rows: Vec<Row> =
        match Portfolio::get_pm_operating_expense_details(conn).await {
            Ok(details) => details,
            Err(err) => {
                println!("Error fetching PM Operating Expense details: {}", err);
                return Err(anyhow::anyhow!("Error fetching PM Operating Expense details"));
            }
        };

    // todo: iterate through pm_operating_expense_rows and map to PMOperatingExpense
    let default_date = NaiveDate::from_ymd_opt(1, 1, 1).expect("Invalid date");
    let pm_operating_expense_list: Vec<PMOperatingExpense> = pm_operating_expense_rows
        .iter()
        .map(|row| PMOperatingExpense {
            accrual_date: default_date,                         // todo
            vendor_pan: VendorPAN {
                is_valid_pan: "mapping_required".to_string(),   // todo
            },
            vendor_name: "mapping_required".to_string(),        // todo
            nature_of_service: "mapping_required".to_string(),  // todo
            is_vendor_associate_of_pms: 0,                      // todo
            amount_paid: -1.0,                                  // todo
            frequency: "mapping_required".to_string(),          // todo
        })
        .collect();

    // Prepare the struct
    let pm_operating_expense_report = PMOperatingExpenseReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: "2024".to_string(),
            },
            month: Month {
                month: "September".to_string(),
            },
        },
        pm_operating_expenses: pm_operating_expense_list,
    };

    // Serialize to XML
    let xml = to_string(&pm_operating_expense_report).unwrap();
    let mut file = File::create("pm_op_test.xml").unwrap();
    file.write_all(xml.as_bytes()).unwrap(); 
    Ok(xml)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_pmoperatingexpense() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        generate_xml_pm_operating_expense(&mut pool_conn).await;
    }
}
