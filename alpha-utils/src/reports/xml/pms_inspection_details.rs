use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use csv::Writer;
use tiberius::Row;
use chrono::{NaiveDateTime, NaiveDate};

use alpha_core_db::schema::portfolio::Portfolio;

use crate::reports::serializer::serialize_option_datetime;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:PMSInspectionDetailsReport")]
#[serde(rename_all = "PascalCase")]
pub struct PMSInspectionDetailsReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "PMS_InspectionDetails")]
    pub pms_inspection_details: Vec<PMSInspectionDetails>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct PMSInspectionDetails {
    pub isin: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub transaction_date: Option<NaiveDateTime>,
    pub asset_class: String,
    pub security_type: String,
    pub symbol: String,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_PID: i32 = 5;

// Function to generate XML and CSV reports
pub async fn generate_xml_pms_inspection_details(conn: &mut Object<Manager>, from_date: NaiveDate, to_date: NaiveDate) ->anyhow::Result<String> {
    // Fetch details that have been queried
    let pms_inspection_details_rows: Vec<Row> = match Portfolio::get_pms_inspection_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching PMS Inspection Details details: {}", err);
            return Err(anyhow::anyhow!("Error fetching PMS Inspection Details details"));
        }
    };

    // Iterate through pms_inspection_details_rows and map to PMSInspectionDetails
    let pms_inspection_details_list: Vec<PMSInspectionDetails> = pms_inspection_details_rows
    .iter()
    .map(|row| PMSInspectionDetails {
        isin: row
            .get::<&str, _>("ISIN")
            .map(String::from)
            .unwrap_or("N/A".to_string()),
        transaction_date: row
            .get::<NaiveDateTime, _>("TRANSACTION_DATE"),
        asset_class: row
            .get::<&str, _>("ASSET_CLASS")
            .map(String::from)
            .unwrap_or("N/A".to_string()),
        security_type: row
            .get::<&str, _>("SECURITY_TYPE")
            .map(String::from)
            .unwrap_or("N/A".to_string()),  
        symbol: row
            .get::<&str, _>("SYMBOL")
            .map(String::from)
            .unwrap_or("N/A".to_string()),  
    })
    .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();

    // Preapre CSV
    let csv_header = ["PMS Inspection Details", "Test123", year_str.as_str(), month_str.as_str()];
    let csv_string = export_to_csv(&pms_inspection_details_list.clone(), &csv_header).unwrap();

    // Prepare the struct
    let pms_inspection_details_report = PMSInspectionDetailsReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: year_str,
            },
            month: Month {
                month: month_str,
            },
        },
        pms_inspection_details: pms_inspection_details_list,
    };

    let xml_string = to_string(&pms_inspection_details_report).unwrap();

    /* Create XML and CSV files on disc (for testing) 
    let mut filexml = File::create("pms_inspection_details.xml").unwrap();
    filexml.write_all(xml.as_bytes()).unwrap();
    std::fs::write("pms_inspection_details.csv", &csv_string).unwrap();
    */ 

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[PMSInspectionDetails],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header
        header.push(format!("{} {}", header_data.get(2).unwrap_or(&""), header_data.get(3).unwrap_or(&"")));

        let header_size = header.len();
        let num_empty_strings = (LEN_PID - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_pmsinspectiondetails() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();

        let test_from_date = NaiveDate::from_ymd_opt(2025, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2025, 1, 31).expect("Invalid Date");
        
        generate_xml_pms_inspection_details(&mut pool_conn, test_from_date, test_to_date).await;
    }
}