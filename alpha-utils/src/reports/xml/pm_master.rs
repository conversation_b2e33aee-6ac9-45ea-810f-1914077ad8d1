use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::NaiveDateTime;
use csv::Writer;
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::portfolio::Portfolio;

use crate::reports::serializer::serialize_option_datetime;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:PMMasterReport")]
#[serde(rename_all = "PascalCase")]
pub struct PMMasterReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "PM_Master")]
    pub pm_master: Vec<PMMaster>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct PMMaster {
    pub pm_name: String,
    pub pm_pan: String,
    pub pm_po_pan: String,
    pub pm_po_name: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub pm_po_doj: Option<NaiveDateTime>,
    pub pm_sebi_reg_no: String,
    pub pm_co_pan: String,
    pub pm_co_name: String,
    pub po_nism_certificate_no: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub po_nism_certific_date: Option<NaiveDateTime>,
    pub fiu_reg_no: String,
    pub kra_reg_no: String,
    pub cersai_reg_no: String,
    pub score_reg_no: String,
    pub weblink_for_direct_client_onboarding: String,
    pub fo_system_name: String,
    pub bo_system_name: String,
    #[serde(serialize_with = "serialize_option_datetime")]
    pub pm_surrender_date: Option<NaiveDateTime>,
    pub pm_net_worth: String,    
    #[serde(serialize_with = "serialize_option_datetime")]
    pub pm_net_worth_date: Option<NaiveDateTime>,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_PMM: i32 = 20; 

// Function to generate XML and CSV reports
pub async fn generate_xml_pm_master(conn: &mut Object<Manager>) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let pm_master_rows: Vec<Row> = match Portfolio::get_pm_master_details(conn).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching PM Master details: {}", err);
            return Err(anyhow::anyhow!("Error fetching PM Master details:"));
        }
    };

    // Iterate through pm_master_rows and map to PMMaster
    let pm_master_list: Vec<PMMaster> = pm_master_rows
        .iter()
        .map(|row| PMMaster {
            pm_name: row
                .get::<&str, _>("PM_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_pan: row
                .get::<&str, _>("PM_PAN")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_po_pan: row
                .get::<&str, _>("PM_PO_PAN")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_po_name: row
                .get::<&str, _>("PM_PO_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_po_doj: row
                .get::<NaiveDateTime, _>("PM_PO_DOJ"),
            pm_sebi_reg_no: row
                .get::<&str, _>("PM_SEBI_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_co_pan: row
                .get::<&str, _>("PM_CO_PAN")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_co_name: row
                .get::<&str, _>("PM_CO_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            po_nism_certificate_no: row
                .get::<&str, _>("PO_NISM_CERTIFICATE_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            po_nism_certific_date: row
                .get::<NaiveDateTime, _>("PO_NISM_CERTIFIC_DATE"),
            fiu_reg_no: row
                .get::<&str, _>("FIU_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            kra_reg_no: row
                .get::<&str, _>("KRA_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            cersai_reg_no: row
                .get::<&str, _>("CERSAI_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            score_reg_no: row
                .get::<&str, _>("SCORE_REG_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            weblink_for_direct_client_onboarding: row
                .get::<&str, _>("WEBLINK")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            fo_system_name: row
                .get::<&str, _>("FO_SYSTEM_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            bo_system_name: row
                .get::<&str, _>("BO_SYSTEM_NAME")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            pm_surrender_date: row
                .get::<NaiveDateTime, _>("PM_SURRENDER_DATE"),
            pm_net_worth: row
                .get::<f64, _>("PM_NET_WORTH")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
            pm_net_worth_date: row
                .get::<NaiveDateTime, _>("PM_NET_WORTH_DATE")
        })
        .collect();

    // Prepare CSV
    let csv_header = ["PM Master", "Test123"];
    let csv_string = export_to_csv(&pm_master_list.clone(), &csv_header).unwrap();
    
    // Prepare the struct
    let pm_master_report = PMMasterReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: "N/A".to_string(),
            },
            month: Month {
                month: "N/A".to_string(),
            },
        },
        pm_master: pm_master_list,
    };

    let xml_string = to_string(&pm_master_report).unwrap();

    /* Create XML and CSV files on disk (for testing) 
    let mut filexml = File::create("pm_master.xml").unwrap();
    filexml.write_all(xml_string.as_bytes()).unwrap();
    std::fs::write("pm_master.csv", &csv_string).unwrap();
    */

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[PMMaster],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header

        let header_size = header.len();
        let num_empty_strings = (LEN_PMM - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_pmmaster() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        generate_xml_pm_master(&mut pool_conn).await;
    }
}
