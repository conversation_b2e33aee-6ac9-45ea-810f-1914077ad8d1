use std::{fs::File, io::Write};

use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    storage::table::{
        portfolio_aum_daily::{self, PortfolioAumDaily},
        AzureStorageTable,
    },
};
use chrono::{DateTime, NaiveDate, NaiveDateTime};
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;

use alpha_core_db::schema::client::Client;
use tracing::info;

use super::client_folio_aum;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientFolioAUM")]
#[serde(rename_all = "PascalCase")]
pub struct ClientFolioAUMReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Folio_AUM")]
    pub client_folio_aum: Vec<ClientFolioAUM>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientFolioAUM {
    pub aum_date: String,
    pub unique_client_code: String,
    pub client_folio_no: String,
    pub client_folio_aum: String,
}

// Function to generate XML report
// pub async fn generate_xml_client_folio_aum(conn: &mut Object<Manager>,from_Date: NaiveDate,to_date: NaiveDate) {
pub async fn generate_xml_client_folio_aum(
    conn: &mut Object<Manager>,
    from_date: NaiveDate,
    to_date: NaiveDate,
) -> anyhow::Result<String> {
    // Fetch details that have been queried

    let client_folio_aum_rows: Vec<Row> =
        match Client::get_client_expense_master_details(conn, from_date, to_date).await {
            Ok(details) => details,
            Err(err) => {
                println!("Error fetching Client Folio AUM details: {}", err);
                return Err(anyhow::anyhow!("Error fetching Client Folio AUM details"));
            }
        };

    let from_date = "2024-05-02 12:00:00";
    let parsed_datetime_f = NaiveDateTime::parse_from_str(from_date, "%Y-%m-%d %H:%M:%S").unwrap();
    let to_date = "2025-05-02 12:00:00";
    let parsed_datetime_t = NaiveDateTime::parse_from_str(to_date, "%Y-%m-%d %H:%M:%S").unwrap();

    let mut client_folio_aum_list = Vec::new();

    for client in &client_folio_aum_rows {
        let portfolio_id = client
            .get::<&str, _>("PORTFOLIO_ID")
            .map(String::from)
            .unwrap_or("N/A".to_string());

        let holdings = PortfolioAumDaily::get_holdings(parsed_datetime_f, parsed_datetime_t, portfolio_id).await;
        for holding in holdings.iter() {
            let market_value = holding.market_value.to_string();

            let aum_date = holding.as_at_date.to_string();

            client_folio_aum_list.push(ClientFolioAUM {
                aum_date,
                unique_client_code: client
                    .get::<&str, _>("UNIQUE_CLIENT_CODE")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_folio_no: client
                    .get::<&str, _>("CLIENT_FOLIO_NO")
                    .map(String::from)
                    .unwrap_or("N/A".to_string()),
                client_folio_aum: market_value,
            });
        }
    }

    let client_folio_aum_report = ClientFolioAUMReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: "2024".to_string(),
            },
            month: Month {
                month: "September".to_string(),
            },
        },
        client_folio_aum: client_folio_aum_list,
    };

    // Serialize to XML
    let xml = to_string(&client_folio_aum_report).unwrap();

    return Ok(xml);
}

#[cfg(test)]
mod tests {
    use crate::reports::xml::client_folio_aum::generate_xml_client_folio_aum;

    use super::*;

    #[tokio::test]
    async fn test_generate_xml_client_folio_aum() {
        dotenv::dotenv().ok();
        println!("TESTING");
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        //generate_xml_client_folio_aum(&mut pool_conn).await.unwrap();
    }
}
