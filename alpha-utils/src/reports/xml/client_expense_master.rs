use std::{fs::File, io::Write};

use alpha_core_db::connection::pool::{deadpool::managed::Object, Manager};
use chrono::{NaiveDate, NaiveDateTime};
use csv::Writer;
use quick_xml::se::to_string;
use serde::{Deserialize, Serialize};
use tiberius::Row;
use crate::reports::serializer::serialize_option_datetime;

use alpha_core_db::schema::client::Client;

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "ns1:ClientExpenseMasterReport")]
#[serde(rename_all = "PascalCase")]
pub struct ClientExpenseMasterReport {
    #[serde(rename = "@xmlns:ns1")]
    pub xmlns_ns1: String,
    pub header: Header,
    #[serde(rename = "Client_Expense")]
    pub client_expenses: Vec<ClientExpenseMaster>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Header {
    #[serde(rename = "LOGIN_ID")]
    pub login_id: String,
    #[serde(rename = "YEAR")]
    pub year: Year,
    #[serde(rename = "MONTH")]
    pub month: Month,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Year {
    #[serde(rename = "year")]
    pub year: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Month {
    #[serde(rename = "month")]
    pub month: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub struct ClientExpenseMaster {
    #[serde(serialize_with = "serialize_option_datetime")]
    pub accrual_date: Option<NaiveDateTime>,
    pub unique_client_code: String,
    pub client_folio_no: String,
    pub expense_type: String,
    pub expense_sub_type: String,
    pub expense_value: String,
}
// NOTE: change this number when the number of fields in the report is changed
const LEN_CEM: i32 = 6; 

// Function to generate XML and CSV reports
pub async fn generate_xml_client_expense_master(conn: &mut Object<Manager>, from_date: NaiveDate, to_date: NaiveDate) -> anyhow::Result<String> {
    // Fetch details that have been queried
    let client_expense_master_rows: Vec<Row> = match Client::get_client_expense_master_details(conn, from_date, to_date).await {
        Ok(details) => details,
        Err(err) => {
            println!("Error fetching Client Expense Master details: {}", err);
            return Err(anyhow::anyhow!("Error fetching Client Expense Master details"));
        }
    };

    // Iterate through client_expense_master_rows and map to ClientExpenseMaster
    let _default_date = NaiveDate::from_ymd_opt(1, 1, 1).expect("Invalid date");
    let client_expense_master_list: Vec<ClientExpenseMaster> = client_expense_master_rows
        .iter()
        .map(|row| ClientExpenseMaster {
            accrual_date: row
                .get::<NaiveDateTime, _>("TRANSACTION_DATE"),
            unique_client_code: row
                .get::<&str, _>("UNIQUE_CLIENT_CODE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            client_folio_no: row
                .get::<&str, _>("CLIENT_FOLIO_NO")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            expense_type: row
                .get::<&str, _>("EXPENSE_TYPE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            expense_sub_type: row
                .get::<&str, _>("EXPENSE_SUB_TYPE")
                .map(String::from)
                .unwrap_or("N/A".to_string()),
            expense_value: row
                .get::<f64, _>("EXPENSE_VALUE")
                .map(|v| v.to_string())
                .unwrap_or("N/A".to_string()),
        })
        .collect();

    let year_str = from_date.format("%Y").to_string();
    let month_str = from_date.format("%B").to_string();

    // Prepare CSV
    let csv_header = ["Client Expense Master", "Test123", year_str.as_str(), month_str.as_str()];
    let csv_string = export_to_csv(&client_expense_master_list.clone(), &csv_header).unwrap();

    // Prepare the struct
    let client_expense_master_report = ClientExpenseMasterReport {
        xmlns_ns1: "http://example.com/namespace".to_string(),
        header: Header {
            login_id: "Test123".to_string(),
            year: Year {
                year: year_str,
            },
            month: Month {
                month: month_str,
            },
        },
        client_expenses: client_expense_master_list,
    };

    // Serialize to XML
    let xml_string = to_string(&client_expense_master_report).unwrap();

    /* Create XML and CSV files on disk (for testing) 
    let mut filexml = File::create("client_expense_master.xml").unwrap();
    filexml.write_all(xml.as_bytes()).unwrap();
    std::fs::write("client_expense_master.csv", &csv_string).unwrap();
    */

    // Return XML and CSV as tuple
    Ok(xml_string)
}

pub fn export_to_csv(
    transactions: &[ClientExpenseMaster],
    header_data: &[&str],
) -> anyhow::Result<String> {
    let mut wtr = Writer::from_writer(vec![]);

    // Formatting the first row and adding to csv
    {
        let mut header: Vec<String> = Vec::new();
        header.push(header_data.get(0).unwrap_or(&"").to_string());
        header.push(format!("Login ID: {}", header_data.get(1).unwrap_or(&""))); // Static part of the header
        header.push(format!("{} {}", header_data.get(2).unwrap_or(&""), header_data.get(3).unwrap_or(&"")));

        let header_size = header.len();
        let num_empty_strings = (LEN_CEM - header_size as i32) as usize;
        for _ in 0..num_empty_strings {
            header.push("".to_string());
        }
        wtr.write_record(header)?;
    }

    for record in transactions {
        wtr.serialize(record)?;
    }

    let data = wtr.into_inner()?;
    let csv = String::from_utf8(data)?;

    Ok(csv)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_xml_clientexpensemaster() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let test_from_date = NaiveDate::from_ymd_opt(2025, 1, 1).expect("Invalid Date");
        let test_to_date = NaiveDate::from_ymd_opt(2025, 1, 30).expect("Invalid Date");
        generate_xml_client_expense_master(&mut pool_conn, test_from_date, test_to_date).await;
    }
}
