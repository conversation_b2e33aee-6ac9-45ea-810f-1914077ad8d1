use crate::reports::mis_reports::serialize_primitive_datetime;
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::portfolio_cash_ledger::get_income_expense_details_for_mis,
};
use rust_xlsxwriter::{Format, Workbook};
use serde::{Deserialize, Serialize};
use tiberius::{time::time::PrimitiveDateTime, Row};

pub struct MisIncomeExpense;

#[derive(Serialize, Deserialize, Debug)]
pub struct MisIncomeExpenseDetails {
    portfolio_cash_ledger_id: String,
    client_code: String,
    client_name: String,
    strategy_name: String,
    client_strategy_code: String,
    custodian_portfolio_code: String,
    #[serde(serialize_with = "serialize_primitive_datetime")]
    transaction_date: PrimitiveDateTime,
    transaction_type: String,
    transaction_sub_type: String,
    amount: f64,
    #[serde(serialize_with = "serialize_primitive_datetime")]
    as_at_date: PrimitiveDateTime,
    description: String,
}

impl MisIncomeExpenseDetails {
    pub fn from_row(row: &Row) -> Self {
        Self {
            portfolio_cash_ledger_id: row.get::<&str, _>("PortfolioCashLedgerId").unwrap().to_string(),
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            client_name: row.get::<&str, _>("ClientName").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            custodian_portfolio_code: row.get::<&str, _>("CustodianPortfolioCode").unwrap().to_string(),
            transaction_date: row.get::<PrimitiveDateTime, _>("TransactionDate").unwrap(),
            transaction_type: row.get::<&str, _>("TransactionType").unwrap().to_string(),
            transaction_sub_type: row.get::<&str, _>("TransactionSubType").unwrap().to_string(),
            amount: row.get("Amount").unwrap(),
            as_at_date: row.get::<PrimitiveDateTime, _>("AsAtDate").unwrap(),
            description: row.get::<&str, _>("Description").unwrap().to_string(),
        }
    }
}

impl MisIncomeExpense {
    pub async fn create_mis_income_expense_details(&self, conn: &mut Object<Manager>) -> Result<(), String> {
        let rows = get_income_expense_details_for_mis(conn).await?;

        let rows: Vec<MisIncomeExpenseDetails> = rows.iter().map(MisIncomeExpenseDetails::from_row).collect();

        self.create_excel(rows);

        Ok(())
    }

    fn create_excel(&self, rows: Vec<MisIncomeExpenseDetails>) {
        // Create a new Excel file object.
        let mut workbook = Workbook::new();

        // Add a worksheet to the workbook.
        let worksheet = workbook.add_worksheet();

        // Add a simple format for the headers.
        let format = Format::new().set_bold();

        let date_format = Format::new().set_num_format("yyyy-mm-dd");
        worksheet.set_column_format(6, &date_format).unwrap();
        worksheet.set_column_format(10, &date_format).unwrap();

        // Set up the start location and headers of the data to be serialized.
        worksheet
            .deserialize_headers_with_format::<MisIncomeExpenseDetails>(0, 0, &format)
            .unwrap();

        for row in rows {
            worksheet.serialize(&row).unwrap();
        }

        let excel_buffer = workbook.save_to_buffer().expect("Failed to Save excel to Buffer");
        workbook.save("income_expense.xlsx").unwrap();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn mis_income_expense() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();

        let mis = MisIncomeExpense {};
        let _ = mis.create_mis_income_expense_details(&mut pool_conn).await;
    }
}
