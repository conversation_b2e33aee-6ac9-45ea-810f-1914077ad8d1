use crate::reports::mis_reports::serialize_primitive_datetime;
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::portfolio_cash_ledger::get_dividends_details_for_mis,
};
use rust_xlsxwriter::{ExcelDateTime, Format, Workbook};
use serde::{ser::SerializeStruct, Deserialize, Serialize, Serializer};
use tiberius::{
    time::{
        chrono::{NaiveDate, NaiveDateTime},
        time::PrimitiveDateTime,
    },
    Row,
};

pub struct MisDividends;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct MisDividendsDetails {
    pub client_code: String,
    pub client_name: String,
    pub strategy_name: String,
    pub client_strategy_code: String,
    pub custodian_portfolio_code: String,
    #[serde(serialize_with = "serialize_primitive_datetime")]
    pub transaction_date: PrimitiveDateTime,
    pub transaction_type: String,
    pub transaction_sub_type: String,
    pub amount: f64,
    pub description: String,
    #[serde(serialize_with = "serialize_primitive_datetime")]
    pub as_at_date: PrimitiveDateTime,
}

impl MisDividendsDetails {
    pub fn from_row(row: &Row) -> Self {
        Self {
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            client_name: row.get::<&str, _>("ClientName").unwrap().to_string(),
            strategy_name: row.get::<&str, _>("StrategyName").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            custodian_portfolio_code: row.get::<&str, _>("CustodianPortfolioCode").unwrap().to_string(),
            transaction_date: row.get::<PrimitiveDateTime, _>("TransactionDate").unwrap(),
            transaction_type: row.get::<&str, _>("TransactionType").unwrap().to_string(),
            transaction_sub_type: row.get::<&str, _>("TransactionSubType").unwrap().to_string(),
            amount: row.get("Amount").unwrap(),
            description: row.get::<&str, _>("Description").unwrap().to_string(),
            as_at_date: row.get::<PrimitiveDateTime, _>("AsAtDate").unwrap(),
        }
    }
}

impl MisDividends {
    async fn create_mis_dividends(&self, conn: &mut Object<Manager>) -> Result<(), String> {
        let rows = get_dividends_details_for_mis(conn).await?;

        let rows: Vec<MisDividendsDetails> = rows.iter().map(MisDividendsDetails::from_row).collect();
        self.create_excel(rows);
        Ok(())
    }

    fn create_excel(&self, rows: Vec<MisDividendsDetails>) {
        // Create a new Excel file object.
        let mut workbook = Workbook::new();

        // Add a worksheet to the workbook.
        let worksheet = workbook.add_worksheet();

        // Add a simple format for the headers.
        let format = Format::new().set_bold();

        let date_format = Format::new().set_num_format("yyyy-mm-dd");

        worksheet.set_column_format(5, &date_format).unwrap();
        worksheet.set_column_format(10, &date_format).unwrap();

        // Set up the start location and headers of the data to be serialized.
        worksheet
            .deserialize_headers_with_format::<MisDividendsDetails>(0, 0, &format)
            .unwrap();

        for row in rows {
            worksheet.serialize(&row).unwrap();
        }

        let excel_buffer = workbook.save_to_buffer().expect("Failed to Save excel to Buffer");
        workbook.save("dividends.xlsx").unwrap();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn mis_dividends() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();

        let mis = MisDividends {};
        mis.create_mis_dividends(&mut pool_conn).await.unwrap();
    }
}
