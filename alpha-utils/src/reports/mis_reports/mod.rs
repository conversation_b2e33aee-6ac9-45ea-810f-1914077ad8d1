use chrono::{Datelike, NaiveDate};
use rust_xlsxwriter::ExcelDateTime;
use tiberius::time::time::PrimitiveDateTime;
use time::{Date, Month};

fn serialize_primitive_datetime<S>(date: &PrimitiveDateTime, serializer: S) -> Result<S::Ok, S::Error>
where
    S: serde::Serializer,
{
    let offset_dt = date.assume_utc();
    let excel_dt = ExcelDateTime::from_timestamp(offset_dt.unix_timestamp() as i64).unwrap();
    serializer.serialize_f64(excel_dt.to_excel())
}

fn chrono_to_time_date(naive: NaiveDate) -> Date {
    Date::from_calendar_date(
        naive.year(),
        // Convert month number to time::Month enum
        match naive.month() {
            1 => Month::January,
            2 => Month::February,
            3 => Month::March,
            4 => Month::April,
            5 => Month::May,
            6 => Month::June,
            7 => Month::July,
            8 => Month::August,
            9 => Month::September,
            10 => Month::October,
            11 => Month::November,
            12 => Month::December,
            _ => unreachable!(),
        },
        naive.day() as u8,
    )
    .unwrap()
}

pub mod capital_register;
pub mod dividends;
pub mod holdings;
pub mod holdings_as_at;
pub mod income_expense;
