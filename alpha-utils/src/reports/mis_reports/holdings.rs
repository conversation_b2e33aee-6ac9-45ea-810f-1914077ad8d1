use crate::reports::mis_reports::serialize_primitive_datetime;
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    storage::table::portfolio_analytics::PortfolioAnalytics,
};
use chrono::NaiveDateTime;
use rust_xlsxwriter::{Format, Workbook};
use serde::{Deserialize, Serialize};
use tiberius::time::time::PrimitiveDateTime;

pub struct MisHoldings;

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct MisHoldingsDetails {
    client_name: String,
    strategy_name: String,
    client_strategy_code: String,
    isin: String,
    holding_name: String,
    symbol: String,
    holdings: f64,
    cost: f64,
    market_value: f64,
    sector: String,
    market_cap: String,
    #[serde(serialize_with = "serialize_primitive_datetime")]
    as_at_date: PrimitiveDateTime,
}

impl MisHoldings {
    pub async fn create_mis_holdings(&self, conn: &mut Object<Manager>) -> Result<(), String> {
        let datetime_str = "2024-05-02 12:00:00";
        let parsed_datetime = NaiveDateTime::parse_from_str(datetime_str, "%Y-%m-%d %H:%M:%S").unwrap();

        let holdings =
            PortfolioAnalytics::get_holdings(parsed_datetime, String::from("ab34541ff1ac4bd194d82903acd468ee")).await;

        Ok(())
    }

    fn create_excel(&self, rows: Vec<MisHoldingsDetails>) {
        // Create a new Excel file object.
        let mut workbook = Workbook::new();

        // Add a worksheet to the workbook.
        let worksheet = workbook.add_worksheet();

        // Add a simple format for the headers.
        let format = Format::new().set_bold();

        let date_format = Format::new().set_num_format("yyyy-mm-dd");
        worksheet.set_column_format(6, &date_format).unwrap();
        worksheet.set_column_format(10, &date_format).unwrap();

        // Set up the start location and headers of the data to be serialized.
        worksheet
            .deserialize_headers_with_format::<MisHoldingsDetails>(0, 0, &format)
            .unwrap();

        for row in rows {
            worksheet.serialize(&row).unwrap();
        }

        let excel_buffer = workbook.save_to_buffer().expect("Failed to Save excel to Buffer");
        workbook.save("holdings.xlsx").unwrap();
    }
}

#[cfg(test)]
mod tests {
    use alpha_core_db::storage::table::portfolio_analytics::PortfolioAnalytics;
    use chrono::NaiveDateTime;
    use rust_xlsxwriter::{Format, Workbook};

    use crate::reports::mis_reports::holdings::MisHoldings;

    use super::MisHoldingsDetails;

    #[tokio::test]
    async fn mis_holdings() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(2).await;
        let mut pool_conn = pool.get().await.unwrap();
    }
}
