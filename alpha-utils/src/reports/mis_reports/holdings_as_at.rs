use std::sync::Arc;

use crate::reports::mis_reports::{chrono_to_time_date, serialize_primitive_datetime};
use alpha_core_db::{
    clickhouse::{
        bse_stock_price::bse_stock_price::BseSecurityPrice,
        mutual_fund_price::mutual_fund_price::MutualFundSecurityPrice,
        nse_stock_price::nse_stock_price::NseSecurityPrice,
    },
    connection::pool::{deadpool::managed::Manager, Pool},
    schema::{
        client_order_entry::{SecuritySubType, SecurityType, TransactionType},
        investment::mis_holdings::RptHoldingDetailsForMis,
        portfolio::Portfolio,
        portfolio_receivables::receivables_report::RptPortfolioReceivables,
        security_master::security_price::TenantSecurityPrice,
    },
};
use anyhow::Context;
use chrono::NaiveDate;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use time::PrimitiveDateTime;

pub struct MisReports {
    pub db: Pool,
    pub master_client: clickhouse::Client,
}

impl MisReports {
    pub async fn generate_holdings_as_at(
        &self,
        as_at: NaiveDate,
        with_acquisition_rate: bool,
    ) -> anyhow::Result<Vec<RptHoldingDetailsForMis>> {
        let mut db_conn = self.db.get().await.unwrap();

        let mut mis_holdings = RptHoldingDetailsForMis::get(&mut db_conn, as_at, with_acquisition_rate).await?;
        let receivables_payables = RptPortfolioReceivables::get_as_at(&mut db_conn, as_at).await?;
        let tenant_security_prices = TenantSecurityPrice::get_all(&mut db_conn).await?;

        let price_cache: DashMap<String, f64> = DashMap::new(); // Using dashmap here so we can move to multi thread if required
        println!("loop");
        for holding in mis_holdings.iter_mut() {
            holding.unrealised_qty = (holding.unrealised_qty as f64 * 10000.0).round() / 10000.0;
            holding.total_cost = (holding.total_cost as f64 * 10000.0).round() / 10000.0;

            holding.average_price = ((holding.total_cost / holding.unrealised_qty) * 10000.0).round() / 10000.0;

            //Process cash
            if holding.symbol.to_lowercase() == "cash" {
                let cash_receivables = receivables_payables.iter().find(|rec| {
                    rec.transaction_type == TransactionType::Credit
                        && rec.portfolio_id == holding.portfolio_id
                        && rec.investment_id.to_lowercase() == "cash"
                });

                let cash_amount_receivables = if let Some(rec) = cash_receivables {
                    rec.amount
                } else {
                    0f64
                };

                let cash_payables = receivables_payables.iter().find(|rec| {
                    rec.transaction_type == TransactionType::Debit
                        && rec.portfolio_id == holding.portfolio_id
                        && rec.investment_id.to_lowercase() == "cash"
                });

                let cash_amount_payables = if let Some(rec) = cash_payables {
                    rec.amount
                } else {
                    0f64
                };

                holding.price = 1f64;
                holding.average_price = 1f64;
                holding.market_value =
                    (holding.unrealised_qty * holding.price) + (cash_amount_receivables - cash_amount_payables);

                holding.accrued_income = 0f64;
                holding.receivable = cash_amount_receivables;
                holding.payable = cash_amount_payables;

                continue;
            }

            //Check if the price of this isin is in cache
            let get_price_in_cache = price_cache.get(&holding.isin);

            if let Some(price) = get_price_in_cache {
                holding.price = *price.value();
            } else {
                if holding.security_type == SecurityType::ETF || holding.security_type == SecurityType::Stocks {
                    if holding.security_sub_type != SecuritySubType::Unlisted {
                        let date = chrono_to_time_date(as_at);
                        println!("Getting price for isin {}", holding.isin);
                        async fn get_price(
                            client: clickhouse::Client,
                            date: time::Date,
                            isin: &str,
                        ) -> Result<Option<f64>, anyhow::Error> {
                            if let Some(price) = NseSecurityPrice::get_by_date(client.clone(), date, isin).await? {
                                return Ok(Some(price.price as f64));
                            }
                            if let Some(price) = BseSecurityPrice::get_by_date(client.clone(), date, isin).await? {
                                return Ok(Some(price.price as f64));
                            }
                            Ok(None)
                        }

                        match get_price(self.master_client.clone(), date, &holding.isin)
                            .await
                            .context(format!("failed to get price for {}", holding.isin))?
                        {
                            Some(price) => {
                                holding.price = price;
                                price_cache.insert(holding.isin.clone(), holding.price);
                            }
                            None => {
                                println!("❌ price for isin {} not found in either nse or bse", holding.isin);
                                continue;
                                // TODO: Remove this once we have a way to get price for all isins
                                // return Err(anyhow::anyhow!(
                                //     "price for isin {} not found in either nse or bse",
                                //     holding.isin
                                // ));
                            }
                        }
                    } else if holding.security_sub_type == SecuritySubType::Unlisted {
                        let unlisted_sec_price = tenant_security_prices.iter().find(|sec| sec.isin == holding.isin);
                        if let Some(sec_price) = unlisted_sec_price {
                            holding.price = sec_price.price;
                            price_cache.insert(holding.isin.clone(), holding.price);
                        }
                    }
                } else if holding.security_type == SecurityType::MutualFund {
                    let date = chrono_to_time_date(as_at);
                    let mf_price =
                        MutualFundSecurityPrice::get_by_date(self.master_client.clone(), date, &holding.isin)
                            .await
                            .context(format!("failed to get price for the isin {}", holding.isin))?;
                    if let Some(sec_price) = mf_price {
                        holding.price = sec_price.price as f64;
                        price_cache.insert(holding.isin.clone(), holding.price);
                    } else {
                        return Err(anyhow::anyhow!(
                            "price for isin {} not found in either nse or bse",
                            holding.isin
                        ));
                    }
                } else {
                    //Its a bond
                    let unlisted_sec_price = tenant_security_prices.iter().find(|sec| sec.isin == holding.isin);
                    if let Some(sec_price) = unlisted_sec_price {
                        holding.price = sec_price.price;
                        price_cache.insert(holding.isin.clone(), holding.price);
                    }
                }
            }

            // Update holdings with the calculated values
            let security_accruable = receivables_payables.iter().find(|rec| {
                rec.transaction_type == TransactionType::Credit
                    && rec.portfolio_id == holding.portfolio_id
                    && &rec.investment_id == holding.investment_id.as_ref().unwrap()
                // At this point investment_id will be available
            });

            let security_payables = receivables_payables.iter().find(|rec| {
                rec.transaction_type == TransactionType::Debit
                    && rec.portfolio_id == holding.portfolio_id
                    && &rec.investment_id == holding.investment_id.as_ref().unwrap()
                // At this point investment_id will be available
            });

            let accrued_amount_receivables = if let Some(accrue) = security_accruable {
                accrue.amount
            } else {
                0f64
            };

            let accrued_amount_payables = if let Some(accrue) = security_payables {
                accrue.amount
            } else {
                0f64
            };
            
            holding.price = holding.price / 10000.0;
            holding.market_value =
                (holding.unrealised_qty * holding.price) + accrued_amount_receivables - accrued_amount_payables;

            holding.accrued_income = accrued_amount_receivables;

            holding.receivable = 0f64;
            holding.payable = accrued_amount_payables;

            if holding.unrealised_qty <= 0f64 {
                holding.price = 0f64;
                holding.average_price = 0f64;
            }
        }

        Ok(mis_holdings
            .into_iter()
            .filter(|h| h.market_value > 0f64 && h.unrealised_qty > 0f64)
            .collect())
    }
}

mod test {
    use alpha_core_db::{clickhouse::create_clickhouse_client, connection::connect_to_mssql};
    use chrono::NaiveDate;

    use crate::reports::mis_reports::holdings_as_at::MisReports;

    #[tokio::test]
    async fn test_mis_latest_holdings() {
        dotenv::dotenv().ok();
        let db = connect_to_mssql(2).await;
        let clickhouse = create_clickhouse_client();

        let report = MisReports {
            db,
            master_client: clickhouse,
        };
        let date = NaiveDate::from_ymd_opt(2025, 06, 01).unwrap();
        let report = report.generate_holdings_as_at(date, true).await;
        assert!(report.is_ok());
    }
}
