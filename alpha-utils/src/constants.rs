use std::{fmt, str::FromStr};

pub enum RedisKey {
    DeviationReport,
    DeviationOrders,
    DeviationPortfolios,
    ClientComputedOrders,
    PreTradeSessionOrderSource,
    PreTradeComplianceSummary
}

impl fmt::Display for RedisKey {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            RedisKey::DeviationReport => write!(f, "DEVIATION_REPORT"),
            RedisKey::DeviationOrders => write!(f, "DEVIATION_ORDERS"),
            RedisKey::DeviationPortfolios => write!(f, "DEVIATION_PORTFOLIOS"),
            RedisKey::ClientComputedOrders => write!(f, "CLIENT_COMPUTED_ORDERS"),
            RedisKey::PreTradeSessionOrderSource => write!(f, "PRE_TRADE_SESSION_ORDER_SOURCE"),
            RedisKey::PreTradeComplianceSummary => write!(f, "PRE_TRADE_COMPLIANCE_SUMMARY"),
        }
    }
}

