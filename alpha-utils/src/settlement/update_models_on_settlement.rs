use actlogica_logs::{builder::Log<PERSON>uilder, log_error, log_info};
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::{
        cash_ledger::PortfolioCashLedger,
        client_order_entry::{ClientOrderEntry, SecuritySubType, SecurityType, TransactionSubType, TransactionType},
        investment::Investments,
        investment_transaction::InvestmentTransaction,
        model_portfolio::ModelPortfolio,
        trade_idea::{buy_trade_idea::BuyTradeIdea, sell_trade_idea::SellTradeIdeas},
    },
};
use chrono::Duration;
use std::collections::HashMap;
use tiberius::time::chrono::Utc;

use crate::{
    transactions::{find_min_max_dates, portfolio_transaction::calculate_current_holding_from_transaction},
    types::SecurityDetails,
};

use super::InvestmentTransactionForSettlement;

pub async fn update_models_on_settlement(
    security_details_map: &HashMap<String, SecurityDetails>,
    conn: &mut Object<Manager>,
    updated_client_order_entries: Vec<ClientOrderEntry>,
) -> Result<(), String> {
    log_info(LogBuilder::system("Starting Model Portfolio Updation"));

    let entries_for_buy_transactions: HashMap<String, Vec<ClientOrderEntry>> = updated_client_order_entries
        .clone()
        .into_iter()
        .filter(|cord| {
            cord.source_type.to_lowercase().contains("buy") && !cord.source_type.to_lowercase().contains("restricted")
        })
        .fold(HashMap::new(), |mut acc, txn| {
            acc.entry(txn.source_reference.clone())
                .or_insert_with(Vec::new)
                .push(txn);
            acc
        });

    log_info(LogBuilder::system(&format!(
        "Buy Orders Count = {}",
        entries_for_buy_transactions.len()
    )));

    let entries_for_sell_transactions = updated_client_order_entries
        .clone()
        .into_iter()
        .filter(|cord| {
            cord.source_type.to_lowercase().contains("sell") && !cord.source_type.to_lowercase().contains("restricted")
        })
        .fold(HashMap::new(), |mut acc, txn| {
            acc.entry(txn.source_reference.clone())
                .or_insert_with(Vec::new)
                .push(txn);
            acc
        });

    log_info(LogBuilder::system(&format!(
        "Sell Orders Count = {}",
        entries_for_sell_transactions.len()
    )));

    if entries_for_buy_transactions.len() > 0 {
        for (source_ref, filtered_orders) in entries_for_buy_transactions {
            let total_qty_requested: f64 = filtered_orders.iter().map(|f| f.quantity).sum();
            let total_qty_settled: f64 = filtered_orders.iter().map(|f| f.settlement_quantity).sum();
            let total_brokerage: f64 = filtered_orders.iter().map(|f| f.settlement_brokerage_amount).sum();

            let total_service_tax: f64 = filtered_orders.iter().map(|f| f.settlement_service_tax).sum();
            let total_stt_amount: f64 = filtered_orders.iter().map(|f| f.settlement_stt_amount).sum();
            let total_turn_tax: f64 = filtered_orders.iter().map(|f| f.settlement_turn_tax).sum();
            let total_stamp_duty: f64 = filtered_orders.iter().map(|f| f.settlement_other_tax).sum();

            let single_transaction = filtered_orders[0].clone();
            let is_mutual_fund = if single_transaction.investment_type == SecurityType::MutualFund {
                true
            } else {
                false
            };

            log_info(
                LogBuilder::system(&format!("Processing Buy Orders for source reference: {}", &source_ref))
                    .add_metadata("total_qty_requested", &total_qty_requested.to_string())
                    .add_metadata("total_qty_settled", &total_qty_settled.to_string())
                    .add_metadata("total_brokerage", &total_brokerage.to_string())
                    .add_metadata("total_service_tax", &total_service_tax.to_string())
                    .add_metadata("total_stt_amount", &total_stt_amount.to_string())
                    .add_metadata("total_turn_tax", &total_turn_tax.to_string())
                    .add_metadata("total_stamp_duty", &total_stamp_duty.to_string()),
            );

            //Get Trade Idea
            let trade_idea = BuyTradeIdea::get(conn, source_ref).await?;

            if trade_idea.is_none() {
                return Err(format!("Trade Idea Not Found"));
            }

            let trade_idea = if let Some(idea) = trade_idea {
                idea
            } else {
                log_error(LogBuilder::system("NotFound: trade-idea"));
                return Err(format!("NotFound: trade-idea"));
            };

            let model_adjustment_pct = total_qty_settled / total_qty_requested;

            //Fetch The Model Portfolios
            let model_portfolio = match ModelPortfolio::get(conn, single_transaction.strategy_model_id).await {
                Ok(Some(portfolio)) => portfolio,
                Ok(None) => {
                    log_error(LogBuilder::system("Model Portfolio Not Found"));
                    return Err(format!("Model Portfolio Not Found"));
                }
                Err(e) => {
                    log_error(LogBuilder::system(&format!("Failed to fetch Model Portfolio: {:?}", e)));
                    return Err(format!("Failed to fetch Model Portfolio"));
                }
            };

            let amount_to_buy = (model_portfolio.market_value + model_portfolio.current_cash_balance)
                * model_adjustment_pct
                * (trade_idea.change / 100f64);

            let quantity_to_settle_in_model = if is_mutual_fund {
                (((amount_to_buy / single_transaction.settlement_price) * 100f64).round()) / 100f64
            } else {
                (amount_to_buy / single_transaction.settlement_price).floor()
            };

            let service_tax = (total_service_tax / total_qty_settled) * quantity_to_settle_in_model;
            let stt_amount = (total_stt_amount / total_qty_settled) * quantity_to_settle_in_model;

            let model_transaction = InvestmentTransaction {
                isin: single_transaction.isin,
                symbol: single_transaction.identifier,
                name: single_transaction.scrip_name,
                exchange: single_transaction.exchange,
                transaction_type: single_transaction.transaction_type,
                transaction_sub_type: single_transaction.transaction_sub_type,
                transaction_date: single_transaction.order_date,
                cgt_date: single_transaction.order_date,
                settlement_date: single_transaction.settlement_date,
                quantity: quantity_to_settle_in_model,
                price: single_transaction.settlement_net_rate,
                market_rate: single_transaction.settlement_price,
                service_tax,
                brokerage: (((total_brokerage / total_qty_settled) * quantity_to_settle_in_model) * 100.0).round()
                    / 100.0,
                stt_amount,
                turn_tax: (((total_turn_tax / total_qty_settled) * quantity_to_settle_in_model) * 100.0).round()
                    / 100.0,
                other_tax: (total_stamp_duty / total_qty_settled) * quantity_to_settle_in_model,
                amount: (((quantity_to_settle_in_model * single_transaction.settlement_net_rate) * 100f64).round()
                    / 100f64)
                    + service_tax
                    + stt_amount,
                ..Default::default()
            };

            let security_sub_type = if single_transaction.investment_type == SecurityType::Stocks
                || single_transaction.investment_type == SecurityType::ETF
            {
                Some(SecuritySubType::Listed)
            } else if single_transaction.investment_type == SecurityType::MutualFund {
                Some(SecuritySubType::MutualFund)
            } else if single_transaction.investment_type == SecurityType::FixedIncome {
                Some(SecuritySubType::Bond)
            } else {
                None
            };

            let transaction = InvestmentTransactionForSettlement {
                net_rate: model_transaction.price,
                transaction_details: model_transaction,
                security_type: single_transaction.investment_type.clone(),
                security_sub_type,
            };

            log_info(LogBuilder::system(&format!(
                "Inserting Buy Txn Into Model Portfolio {:?}",
                model_portfolio.id
            )));

            add_transactions_to_model_portfolio(security_details_map, conn, model_portfolio.id, vec![transaction])
                .await?;
        }
    }

    if entries_for_sell_transactions.len() > 0 {
        for (source_ref, filtered_orders) in entries_for_sell_transactions {
            let total_qty_requested: f64 = filtered_orders.iter().map(|f| f.quantity).sum();
            let total_qty_settled: f64 = filtered_orders.iter().map(|f| f.settlement_quantity).sum();
            let total_brokerage: f64 = filtered_orders.iter().map(|f| f.settlement_brokerage_amount).sum();

            let total_service_tax: f64 = filtered_orders.iter().map(|f| f.settlement_service_tax).sum();
            let total_stt_amount: f64 = filtered_orders.iter().map(|f| f.settlement_stt_amount).sum();
            let total_turn_tax: f64 = filtered_orders.iter().map(|f| f.settlement_turn_tax).sum();
            let total_stamp_duty: f64 = filtered_orders.iter().map(|f| f.settlement_other_tax).sum();

            let single_transaction = filtered_orders[0].clone();

            log_info(
                LogBuilder::system(&format!("Processing Sell Orders for source reference: {}", &source_ref))
                    .add_metadata("total_qty_requested", &total_qty_requested.to_string())
                    .add_metadata("total_qty_settled", &total_qty_settled.to_string())
                    .add_metadata("total_brokerage", &total_brokerage.to_string())
                    .add_metadata("total_service_tax", &total_service_tax.to_string())
                    .add_metadata("total_stt_amount", &total_stt_amount.to_string())
                    .add_metadata("total_turn_tax", &total_turn_tax.to_string())
                    .add_metadata("total_stamp_duty", &total_stamp_duty.to_string()),
            );

            //Get Trade Idea
            let trade_idea = SellTradeIdeas::get(conn, source_ref).await.unwrap().unwrap();

            let model_adjustment_pct = total_qty_settled / total_qty_requested;

            let is_mutual_fund = if single_transaction.investment_type == SecurityType::MutualFund {
                true
            } else {
                false
            };

            //Fetch The Model Portfolios
            let model_portfolio = ModelPortfolio::get(conn, single_transaction.strategy_model_id).await?;

            if model_portfolio.is_none() {
                return Err(format!("Model Portfolio Not Found"));
            }

            let model_portfolio = model_portfolio.unwrap();

            let amount_to_sell = (model_portfolio.market_value + model_portfolio.current_cash_balance)
                * (model_adjustment_pct * trade_idea.change / 100f64);

            let quantity_to_settle_in_model = if is_mutual_fund {
                (((amount_to_sell / single_transaction.settlement_price) * 100f64).round()) / 100f64
            } else {
                (amount_to_sell / single_transaction.settlement_price).floor()
            };

            let service_tax = (total_service_tax / total_qty_settled) * quantity_to_settle_in_model;
            let stt_amount = (total_stt_amount / total_qty_settled) * quantity_to_settle_in_model;

            let model_transaction = InvestmentTransaction {
                isin: single_transaction.isin,
                symbol: single_transaction.identifier,
                name: single_transaction.scrip_name,
                exchange: single_transaction.exchange,
                transaction_type: single_transaction.transaction_type,
                transaction_sub_type: single_transaction.transaction_sub_type,
                transaction_date: single_transaction.order_date,
                cgt_date: single_transaction.order_date,
                settlement_date: single_transaction.settlement_date,
                quantity: quantity_to_settle_in_model,
                price: single_transaction.settlement_net_rate,
                market_rate: single_transaction.settlement_price,
                service_tax,
                brokerage: (total_brokerage / total_qty_settled) * quantity_to_settle_in_model,
                stt_amount,
                turn_tax: (total_turn_tax / total_qty_settled) * quantity_to_settle_in_model,
                other_tax: (total_stamp_duty / total_qty_settled) * quantity_to_settle_in_model,
                amount: (((quantity_to_settle_in_model * single_transaction.settlement_net_rate) * 100f64).round()
                    / 100f64)
                    + service_tax
                    + stt_amount,
                ..Default::default()
            };

            let security_sub_type = if single_transaction.investment_type == SecurityType::Stocks
                || single_transaction.investment_type == SecurityType::ETF
            {
                Some(SecuritySubType::Listed)
            } else if single_transaction.investment_type == SecurityType::MutualFund {
                Some(SecuritySubType::MutualFund)
            } else if single_transaction.investment_type == SecurityType::FixedIncome {
                Some(SecuritySubType::Bond)
            } else {
                None
            };

            let transaction = InvestmentTransactionForSettlement {
                net_rate: single_transaction.price,
                transaction_details: model_transaction,
                security_type: single_transaction.investment_type.clone(),
                security_sub_type,
            };

            log_info(LogBuilder::system(&format!(
                "Inserting Sell Txn Into Model Portfolio {:?}",
                model_portfolio.id,
            )));

            add_transactions_to_model_portfolio(security_details_map, conn, model_portfolio.id, vec![transaction])
                .await?;
        }
    }

    Ok(())
}

/// TODO: ADD Transactions based on different security type later
pub async fn add_transactions_to_model_portfolio(
    security_details_map: &HashMap<String, SecurityDetails>,
    conn: &mut Object<Manager>,
    model_portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
) -> Result<(), String> {
    log_info(LogBuilder::system(&format!(
        "Adding Transactions to Model Portfolio {:?}",
        &model_portfolio_id
    )));
    //Group all the transaction By Isin
    let total_debit: f64 = transactions
        .iter()
        .filter(|t| t.transaction_details.transaction_type == TransactionType::Buy)
        .map(|t| t.transaction_details.amount)
        .sum();

    let total_credit: f64 = transactions
        .iter()
        .filter(|t| t.transaction_details.transaction_type == TransactionType::Sell)
        .map(|t| t.transaction_details.amount)
        .sum();

    let transactions_by_isin: HashMap<String, Vec<InvestmentTransactionForSettlement>> =
        transactions.into_iter().fold(HashMap::new(), |mut acc, transaction| {
            acc.entry(transaction.transaction_details.isin.clone())
                .or_insert_with(Vec::new)
                .push(transaction);
            acc
        });

    for (isin, transactions) in transactions_by_isin {
        log_info(LogBuilder::system(&format!(
            "Adding Transactions to Model Portfolio {:?} for ISIN {:?}",
            model_portfolio_id, isin
        )));
        let investments =
            Investments::get_by_isin_and_model_portfolio_id(conn, isin.clone(), model_portfolio_id.clone())
                .await
                .unwrap();

        let security = security_details_map.get(&isin).unwrap(); //WE ARE SURE THAT IT WILL HAVE THE DETAILS

        let price = security.get_latest_price();

        //Check whether Investment Exist for this ISIN in Portfolio Investments
        if let Some(investments) = investments {
            add_transactions_to_existing_investment(price, conn, investments, transactions).await?;
        } else {
            //Create a New Investment and put transactions into It
            add_transactions_to_new_investment(price, security, conn, isin, model_portfolio_id.clone(), transactions)
                .await?;
        }
    }

    if total_debit > 0f64 {
        let current_running_balance =
            PortfolioCashLedger::get_running_balance_of_a_model(conn, model_portfolio_id.clone())
                .await
                .map_err(|_e| return String::from("Failed To Get Running Balance"))?
                .unwrap_or(0f64);

        let ledger_entry = PortfolioCashLedger {
            amount: total_debit,
            is_model_portfolio: true,
            model_portfolio_id: Some(model_portfolio_id.clone()),
            settlement_date: Utc::now().naive_utc(),
            transaction_date: Utc::now().naive_utc(),
            transaction_type: TransactionType::Debit,
            transaction_sub_type: TransactionSubType::Buy,
            description: Some(format!("Securities bought Amount reflects net")),
            running_balance: current_running_balance - total_debit,
            ..Default::default()
        };

        ledger_entry.insert(conn).await.map_err(|e| {
            log_error(
                LogBuilder::system(&format!(
                    "Failed to Insert Ledger Entry For Model Portfolio {}",
                    &model_portfolio_id
                ))
                .add_metadata("error", &e.to_string()),
            );
            return String::from("Failed to Insert Ledger Entry For Model Portfolio ");
        })?;
    }

    if total_credit > 0f64 {
        let current_running_balance =
            PortfolioCashLedger::get_running_balance_of_a_model(conn, model_portfolio_id.clone())
                .await
                .map_err(|_e| return String::from("Failed To Get Runnign Balance"))?
                .unwrap_or(0f64);

        let ledger_entry = PortfolioCashLedger {
            amount: total_credit,
            is_model_portfolio: true,
            model_portfolio_id: Some(model_portfolio_id.clone()),
            settlement_date: Utc::now().naive_utc(),
            transaction_date: Utc::now().naive_utc(),
            transaction_type: TransactionType::Credit,
            transaction_sub_type: TransactionSubType::Sell,
            description: Some(format!("Securities bought Amount reflects net")),
            running_balance: current_running_balance + total_credit,
            ..Default::default()
        };

        ledger_entry.insert(conn).await.map_err(|e| {
            log_error(
                LogBuilder::system(&format!(
                    "Failed to Insert Ledger Entry For Model Portfolio {}",
                    &model_portfolio_id
                ))
                .add_metadata("error", &e.to_string()),
            );
            return String::from("Failed to Insert Ledger Entry For Model Portfolio ");
        })?;
    }

    Ok(())
}

async fn add_transactions_to_new_investment(
    price: f64,
    security: &SecurityDetails,
    conn: &mut Object<Manager>,
    isin: String,
    model_portfolio_id: String,
    transactions: Vec<InvestmentTransactionForSettlement>,
) -> Result<(), String> {
    log_info(LogBuilder::system(&format!(
        "Adding Transactions to New Investment for Model Portfolio {:?} for ISIN {:?}",
        model_portfolio_id, isin
    )));
    let current_holding = calculate_current_holding_from_transaction(&transactions);
    let single_transaction = transactions[0].clone(); // Check Is Done Before Passing into this function

    let (min_date, max_date) = find_min_max_dates(&transactions[..]);

    //TODO: Compare for Mutual Fund ALSO
    let isin_details = match security.get_stock_details() {
        Some(details) => details,
        None => {
            log_error(LogBuilder::system("Failed to get stock details"));
            return Err(String::from("Failed to get stock details"));
        }
    };

    let investment: Investments = Investments {
        modelportfolio_id: Some(model_portfolio_id),
        current_holding,
        current_price_date: Utc::now().naive_utc() - Duration::days(1),
        isin,
        symbol: if single_transaction.transaction_details.exchange == "NSE" {
            isin_details.symbol.clone().unwrap()
        } else {
            isin_details.scrip_code.clone().unwrap()
        },
        exchange: single_transaction.transaction_details.exchange,
        current_price: single_transaction.transaction_details.price,
        market_value: price * current_holding,
        first_transaction_date: min_date,
        last_transaction_date: max_date,
        name: single_transaction.transaction_details.name,
        sector: isin_details.industry.clone(),
        market_cap: None, //FIXME:
        security_type: single_transaction.security_type,
        client_id: None,
        portfolio_id: None,
        ..Default::default()
    };

    // Insert Into Investment Table
    let investment_id = investment.insert(conn).await.map_err(|e| {
        log_error(LogBuilder::system("Failed to Insert Into Investment").add_metadata("error", &e.to_string()));
        return format!("Failed to Insert Into Investment");
    })?;

    // //Insert into Investment Transaction Table
    // let _ = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|tx| tx.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|_e| {
    //     return format!("Failed to Insert Into Investment");
    // })?;

    for mut txn in transactions {
        txn.transaction_details.investment_id = investment_id.clone();

        txn.transaction_details.insert(conn).await.map_err(|e| {
            log_error(
                LogBuilder::system("Failed : Investment Transaction Insertion").add_metadata("error", &e.to_string()),
            );
            return format!("Transaction Insertion Failed {:?}", e);
        })?;
    }
    Ok(())
}

async fn add_transactions_to_existing_investment(
    price: f64,
    conn: &mut Object<Manager>,
    mut investment: Investments,
    transactions: Vec<InvestmentTransactionForSettlement>,
) -> Result<(), String> {
    log_info(LogBuilder::system(&format!(
        "Adding Transactions to Existing Investment for Model Portfolio {:?}",
        &investment.modelportfolio_id
    )));
    let new_holdings_to_add: f64 = transactions
        .iter()
        .map(|t| match t.transaction_details.transaction_type {
            TransactionType::Sell => -t.transaction_details.quantity,
            _ => t.transaction_details.quantity,
        })
        .sum();

    let (min_date, max_date) = find_min_max_dates(&transactions);

    // Update investment's current_holdings , transaction in Database
    investment.current_holding += new_holdings_to_add;
    investment.market_value = investment.current_holding * price;
    investment.first_transaction_date = min_date;
    investment.last_transaction_date = max_date;

    let _ = investment.update_on_settlement(conn).await.map_err(|e| {
        log_error(LogBuilder::system("Failed : Update investment").add_metadata("error", &e.to_string()));
        return format!("Fialed to Update Investment");
    })?;

    //Insert transaction in Bulk
    // let _ = InvestmentTransaction::bulk_insert(
    //     conn,
    //     &transactions
    //         .iter()
    //         .map(|tx| tx.transaction_details.clone())
    //         .collect(),
    // )
    // .await
    // .map_err(|_e| {
    //     return format!("Fialed to Insert Transactions");
    // })?;

    for mut txn in transactions {
        txn.transaction_details.investment_id = investment.id.clone();
        txn.transaction_details.insert(conn).await.map_err(|e| {
            log_error(
                LogBuilder::system("Failed : Investment Transaction Insertion").add_metadata("error", &e.to_string()),
            );
            return format!("Transaction Insertion Failed {:?}", e);
        })?;
    }

    Ok(())
}
