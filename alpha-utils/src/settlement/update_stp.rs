use std::collections::HashMap;

use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};

use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::{
        client_order_entry::ClientOrderEntry,
        stp::deployment_tracker::{DeploymentTracker, DeploymentTrackerStatus},
    },
    types::OrderSourceType,
};

pub async fn update_stp_on_settlement(
    conn: &mut Object<Manager>,
    client_order_entries: &Vec<ClientOrderEntry>,
) -> Result<(), String> {
    //FOR STP SELL LEG (Systematic)
    let stp_sell_leg_orders: Vec<&ClientOrderEntry> = client_order_entries
        .iter()
        .filter(|c| {
            c.source_type
                .contains(&OrderSourceType::SystematicDeploymentRedemption.to_string())
        })
        .collect();

    log_info(LogBuilder::system(&format!(
        "STP Sell Orders Count = {}",
        stp_sell_leg_orders.len()
    )));

    update_stp(conn, stp_sell_leg_orders).await?;

    let stp_buy_leg_orders: Vec<&ClientOrderEntry> = client_order_entries
        .iter()
        .filter(|c| {
            c.source_type
                .contains(&OrderSourceType::SystematicDeploymentInstallment.to_string())
        })
        .collect();

    log_info(LogBuilder::system(&format!(
        "STP Buy Orders Count = {}",
        stp_buy_leg_orders.len()
    )));

    update_stp(conn, stp_buy_leg_orders).await?;

    //FOR STP SELL LEG (Strategic) - will only have sell leg
    let stp_strategic_sell_leg_orders: Vec<&ClientOrderEntry> = client_order_entries
        .iter()
        .filter(|c| {
            c.source_type
                .contains(&OrderSourceType::StrategicDeploymentRedemption.to_string())
        })
        .collect();

    log_info(LogBuilder::system(&format!(
        "STP Sell Orders Count = {}",
        stp_strategic_sell_leg_orders.len()
    )));

    update_stp(conn, stp_strategic_sell_leg_orders).await?;

    Ok(())
}

async fn update_stp(conn: &mut Object<Manager>, stp_coe: Vec<&ClientOrderEntry>) -> Result<(), String> {
    for (source_reference, _orders) in stp_coe.iter().fold(HashMap::new(), |mut acc, order| {
        acc.entry(order.source_reference.clone())
            .or_insert_with(Vec::new)
            .push(order);
        acc
    }) {
        log_info(LogBuilder::system(&format!(
            "Processing STP for source reference: {}",
            &source_reference
        )));
        let (total_orders, order_already_settled, total_partially_settled_orders) =
            ClientOrderEntry::get_total_orders_settled_orders_by_source_reference(conn, &source_reference)
                .await
                .map_err(|e| {
                    log_error(
                        LogBuilder::system("Failed to Get Client Entry Orders For STP")
                            .add_metadata("error", &e.to_string()),
                    );
                    return String::from("Failed to Get Client Entry Orders For STP");
                })?;

        if order_already_settled == total_orders {
            log_warn(LogBuilder::system(&format!(
                "All the Orders are Settled for STP: {}",
                &source_reference
            )));
            //Mark STP as Settled
            DeploymentTracker::change_tracker_status_by_id(conn, &source_reference, DeploymentTrackerStatus::Settled)
                .await
                .map_err(|e| {
                    log_error(
                        LogBuilder::system("Failed to Change the STP status to Settled")
                            .add_metadata("error", &e.to_string()),
                    );
                    String::from("Failed to Change the STP status to Settled")
                })?;
        } else if order_already_settled > 0 || total_partially_settled_orders > 0 {
            //Mark as Partially Partially Settled
            DeploymentTracker::change_tracker_status_by_id(
                conn,
                &source_reference,
                DeploymentTrackerStatus::PartiallySettled,
            )
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system("Failed to Change the STP status to PartiallySettled")
                        .add_metadata("error", &e.to_string()),
                );
                String::from("Failed to Change the STP status to PartiallySettled")
            })?;
        }
    }

    Ok(())
}
