use std::{collections::HashMap, str::FromStr, sync::Arc};
pub mod update_models_on_settlement;
use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::{
        cash_ledger::PortfolioCashLedger,
        client_order_entry::{
            ClientOrderEntry, OrderStatus, SecuritySubType, SecurityType, TransactionSubType, TransactionType,
        },
        general_settings::GeneralSettings,
        investment_transaction::InvestmentTransaction,
        order_settlement_in_client_account::OrderSettlementInClientAccount,
        trade_order_settlement_file::{TradeOrderSettlementFile, TradeOrderSettlementFileStatus},
        trade_order_unsettled::TradeOrderUnsettledAmounts,
    },
};
use bb8_redis::RedisConnectionManager;
use chrono::Utc;
use futures::future::try_join_all;
use tokio::sync::RwLock;

use tracing::{Instrument, Span};
use update_models_on_settlement::update_models_on_settlement;
use update_stp::update_stp_on_settlement;
pub mod update_stp;
use crate::{
    group_by_multiple_fields,
    transactions::portfolio_transaction::{
        add_transactions_to_equity_investment, add_transactions_to_mutual_fund_investment,
    },
    types::{SecurityDetails, SecurityTypeForPrice},
};

#[derive(Clone)]
pub struct InvestmentTransactionForSettlement {
    pub transaction_details: InvestmentTransaction,
    pub net_rate: f64,
    pub security_type: SecurityType,
    pub security_sub_type: Option<SecuritySubType>,
}

pub async fn process_settlement(
    pool: Pool<Manager>,
    master_data_pool: Pool<Manager>,
    redis_conn: bb8::Pool<RedisConnectionManager>,
    id: String,
    cancel_pending_orders: bool,
    release_payout: bool,
) {
    log_info(LogBuilder::system(&format!("Processing settlements for ID: {}", &id)));

    let mut txn_conn = match pool.get().await {
        Ok(conn) => conn,
        Err(e) => {
            log_error(LogBuilder::system(&"Failed: database connection").add_metadata("error", &e.to_string()));
            return;
        }
    };

    let settlement_file_entry = match TradeOrderSettlementFile::get(&mut txn_conn, id.clone()).await {
        Ok(entry) => entry,
        Err(e) => {
            log_error(
                LogBuilder::system(&"Failed to get TradeOrderSettlementFile")
                    .add_metadata("error", &e.to_string())
                    .add_metadata("id", &id),
            );
            return;
        }
    };

    if settlement_file_entry.processing_status != TradeOrderSettlementFileStatus::Processed {
        log_error(LogBuilder::system(&format!(
            "The Entry is Already {:?}",
            settlement_file_entry.processing_status
        )));
        return;
    }

    let mut conn_for_updation = match pool.get().await {
        Ok(conn) => conn,
        Err(e) => {
            log_error(
                LogBuilder::system("Failed to get database connection for updation")
                    .add_metadata("error", &e.to_string()),
            );
            return;
        }
    };

    if let Err(err) = settlement_file_entry
        .update_status(&mut conn_for_updation, TradeOrderSettlementFileStatus::Settling)
        .await
    {
        log_error(
            LogBuilder::system("Failed to update settlement file status").add_metadata("error", &err.to_string()),
        );
        return;
    }

    //START TRANSACTION
    if let Err(err) = txn_conn.simple_query("BEGIN TRANSACTION").await {
        log_error(LogBuilder::system("Failed to begin transaction").add_metadata("error", &err.to_string()));
        return;
    }

    let res = update_client_portfolios(
        &mut txn_conn,
        redis_conn,
        pool,
        master_data_pool,
        id,
        cancel_pending_orders,
        release_payout,
    )
    .await;

    match res {
        Ok(()) => {
            //Commit Transaction
            log_info(LogBuilder::system("Changing Settlement File entry Status to Settled"));
            let change_status = settlement_file_entry
                .update_status(&mut txn_conn, TradeOrderSettlementFileStatus::Settled)
                .await
                .map_err(|e| {
                    log_error(
                        LogBuilder::system("Failed to update settlement file status to Settled")
                            .add_metadata("error", &e.to_string()),
                    );
                    return format!("Failed to Update");
                });

            if change_status.is_ok() {
                log_info(LogBuilder::system("Committing Transaction"));
                if let Err(err) = txn_conn.simple_query("COMMIT").await {
                    log_error(
                        LogBuilder::system("Failed to commit transaction").add_metadata("error", &err.to_string()),
                    );
                }
            } else {
                log_info(LogBuilder::system(
                    "Failed To Change Settlement File entry Status to Settled",
                ));
                log_info(LogBuilder::system("Rolling back Transaction"));
                if let Err(err) = txn_conn.simple_query("ROLLBACK").await {
                    log_error(
                        LogBuilder::system("Failed to rollback transaction").add_metadata("error", &err.to_string()),
                    );
                }
            }
        }
        Err(err) => {
            log_error(LogBuilder::system(&format!("Settlement Failed due to = {}", err)));
            //ROLLBACK Transaction On Failure
            log_info(LogBuilder::system("Rollbacking Transaction"));
            if let Err(rollback_err) = txn_conn.simple_query("ROLLBACK").await {
                log_error(
                    LogBuilder::system("Failed to rollback transaction")
                        .add_metadata("error", &rollback_err.to_string()),
                );
                return;
            }

            log_info(LogBuilder::system("Changing Settlement File entry Status to Failed"));
            if let Err(update_err) = settlement_file_entry
                .update_status_with_remarks(&mut txn_conn, TradeOrderSettlementFileStatus::Failed, err)
                .await
            {
                log_error(
                    LogBuilder::system("Failed to update settlement file status to Failed")
                        .add_metadata("error", &update_err.to_string()),
                );
                return;
            }
        }
    }
    log_info(LogBuilder::system("Completed Settlement Processing"));
}

pub async fn update_client_portfolios(
    mut conn: &mut Object<Manager>,
    redis_pool: bb8::Pool<RedisConnectionManager>,
    _pool: Pool<Manager>,
    master_data_pool: Pool<Manager>,
    id: String,
    cancel_pending_orders: bool,
    release_payout: bool,
) -> Result<(), String> {
    log_info(LogBuilder::system("Fetching Client Allocations"));
    let client_allocations = OrderSettlementInClientAccount::get(&mut conn, id.clone()).await?;
    let coe_ids: Vec<String> = client_allocations
        .iter()
        .map(|ca| ca.client_order_entry_id.clone())
        .collect();

    log_info(LogBuilder::system("Fetching Client Order Entries for Whole Allocation"));
    let client_order_entries_for_allocation = Arc::new(
        ClientOrderEntry::get_for_settlement(&mut conn, &coe_ids)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system("Failed to fetch Client Order Entries").add_metadata("error", &e.to_string()),
                );
                String::from("Failed")
            })?,
    );
    log_info(LogBuilder::system("Client Order Entries Fetch Completed"));

    log_info(LogBuilder::system(
        "Fetching Trade Order Unsettled for Whole Allocation",
    ));
    let entry_in_trade_order_unsettleds = Arc::new(
        TradeOrderUnsettledAmounts::get_for_settlement_by_coe_ids(&mut conn, &coe_ids)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system("Failed to fetch Trade Order Unsettled").add_metadata("error", &e.to_string()),
                );
                String::from("Failed")
            })?,
    );
    log_info(LogBuilder::system("Trade Order Unsettled Fetch Completed"));

    let direct_equity_transactions = Arc::new(RwLock::new(Vec::new()));
    let mutual_fund_txns = Arc::new(RwLock::new(Vec::new()));

    let updated_client_order_entries = Arc::new(RwLock::new(Vec::new()));
    let carry_forwarded_trade = Arc::new(RwLock::new(Vec::new()));
    let trade_order_updates = Arc::new(RwLock::new(Vec::new()));

    let ledger_entries = Arc::new(RwLock::new(Vec::new()));

    let security_details_map: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

    let mutual_fund_details_map: Arc<RwLock<HashMap<String, SecurityDetails>>> = Arc::new(RwLock::new(HashMap::new()));

    let mut handlers = Vec::new();

    log_info(LogBuilder::system("Constructing Transactions From Client Allocation"));

    let is_stt_included_in_cost = match GeneralSettings::get_by_key(conn, "IsSttIncludedInCost").await {
        Ok(stt) => match stt {
            Some(stt) => stt,
            None => {
                log_error(LogBuilder::system(
                    "Failed to get IsSttIncludedInCost from general settings",
                ));
                return Err(String::from("IsSttIncludedInCost key not found in general settings"));
            }
        },
        Err(err) => {
            log_error(
                LogBuilder::system("Failed to get stt included in cost details")
                    .add_metadata("error", &err.to_string()),
            );
            return Err(String::from("failed to get stt included in cost details"));
        }
    };

    let include_stt_in_cost: bool = match is_stt_included_in_cost.value.parse() {
        Ok(value) => value,
        Err(err) => {
            log_error(
                LogBuilder::system("is_stt_included_in_cost value is not boolean")
                    .add_metadata("error", &err.to_string()),
            );
            return Err(String::from("is_stt_included_in_cost value is not boolean"));
        }
    };

    log_info(LogBuilder::system(&format!(
        "STT include in cost = {}",
        include_stt_in_cost
    )));

    for client_allocation in client_allocations {
        let master_data_pool = master_data_pool.clone();
        let security_details_map = security_details_map.clone();
        let direct_equity_transactions = direct_equity_transactions.clone();
        let mutual_fund_txns = mutual_fund_txns.clone();
        let updated_client_order_entries = updated_client_order_entries.clone();
        let carry_forwarded_trade = carry_forwarded_trade.clone();
        let ledger_entries = ledger_entries.clone();
        let trade_order_updates = trade_order_updates.clone();
        let mutual_fund_details_map = mutual_fund_details_map.clone();
        let redis_pool = redis_pool.clone();
        let client_order_entries_for_allocation = client_order_entries_for_allocation.clone();
        let entry_in_trade_order_unsettleds = entry_in_trade_order_unsettleds.clone();

        let handler = tokio::spawn(async move {
            let entry_in_client_order = client_order_entries_for_allocation
                .iter()
                .find(|f| f.id == client_allocation.client_order_entry_id)
                .cloned();

            let mut entry_in_client_order = match entry_in_client_order {
                Some(order) => order,
                None => {
                    let err_msg = format!(
                        "Client Allocation Isin = {} Not Found in the Client Order Entries. Meaning the Order is Already Settled for this allocation",
                        client_allocation.isin
                    );
                    log_warn(LogBuilder::system(&err_msg));
                    return;
                }
            };

            let entry_in_trade_order_unsettled = entry_in_trade_order_unsettleds
                .iter()
                .find(|un| un.client_order_entry_id == client_allocation.client_order_entry_id)
                .cloned();

            let latest_price = entry_in_client_order.price;

            if entry_in_client_order.investment_type == SecurityType::MutualFund {
                let mut mutual_fund_details_map_guard = mutual_fund_details_map.write().await;

                if let Some(_mf_details) = mutual_fund_details_map_guard.get(&entry_in_client_order.isin) {
                } else {
                    let mut redis_conn = match redis_pool.get().await {
                        Ok(conn) => conn,
                        Err(e) => {
                            log_error(LogBuilder::system("Failed to get Redis connection").add_metadata("error", &e.to_string()));
                            return;
                        }
                    };
                    let security_details = match SecurityDetails::build(
                        &mut redis_conn,
                        master_data_pool,
                        client_allocation.isin.clone(),
                        SecurityTypeForPrice::MutualFund,
                        &client_allocation.exchange,
                    )
                    .await
                    {
                        Ok(details) => details,
                        Err(e) => {
                            log_error(LogBuilder::system("Failed to build security details for Mutual Fund")
                                .add_metadata("error", &e.to_string()));
                            return;
                        }
                    };

                    mutual_fund_details_map_guard.insert(entry_in_client_order.isin.clone(), security_details);
                }
            } else {
                //Equity
                let mut security_details_map_guard = security_details_map.write().await;

                //Get the Security Info For This ISIN from DB
                if let Some(_sec_details) = security_details_map_guard.get(&entry_in_client_order.isin.clone()) {
                } else {
                    let mut redis_conn = match redis_pool.get().await {
                        Ok(conn) => conn,
                        Err(e) => {
                            log_error(LogBuilder::system("Failed to get Redis connection")
                                .add_metadata("error", &e.to_string()));
                            return;
                        }
                    };

                    let security_details = match SecurityDetails::build(
                        &mut redis_conn,
                        master_data_pool,
                        client_allocation.isin.clone(),
                        SecurityTypeForPrice::Equity,
                        &client_allocation.exchange,
                    )
                    .await
                    {
                        Ok(details) => details,
                        Err(e) => {
                            log_error(LogBuilder::system("Failed to build security details for Equity")
                                .add_metadata("error", &e.to_string()));
                            return;
                        }
                    };

                    security_details_map_guard.insert(entry_in_client_order.isin.clone(), security_details);
                }
            }

            entry_in_client_order.settlement_date = Utc::now().naive_utc();
            entry_in_client_order.settlement_quantity = client_allocation.quantity;
            entry_in_client_order.settlement_price = client_allocation.net_rate;
            entry_in_client_order.settlement_market_amount = client_allocation.market_amount;
            entry_in_client_order.settlement_brokerage_amount = client_allocation.actual_brokerage;
            entry_in_client_order.settlement_service_tax = client_allocation.service_tax;
            entry_in_client_order.settlement_net_rate = client_allocation.net_rate;
            entry_in_client_order.settlement_stt_amount = client_allocation.stt_amount;
            entry_in_client_order.settlement_turn_tax = client_allocation.turn_tax_exchange_txn_tax;
            entry_in_client_order.settlement_other_tax = client_allocation.stamp_duty_and_other_charges;
            entry_in_client_order.settlement_net_amount = client_allocation.net_amount;
            entry_in_client_order.pending_quantity =
                entry_in_client_order.quantity - entry_in_client_order.settlement_quantity;
            entry_in_client_order.order_status = if entry_in_client_order.pending_quantity > 0.0 {
                OrderStatus::PartiallySettled
            } else {
                OrderStatus::Settled
            };

            let mut transaction_constructed = InvestmentTransaction { ..Default::default() };

            transaction_constructed.portfolio_id = Some(entry_in_client_order.portfolio_id.clone());
            transaction_constructed.client_id = Some(client_allocation.client_id.clone());
            transaction_constructed.isin = client_allocation.isin.clone();
            transaction_constructed.symbol = client_allocation.scrip_code.clone();
            transaction_constructed.name = entry_in_client_order.scrip_name.clone();
            transaction_constructed.exchange = client_allocation.exchange.clone();
            transaction_constructed.transaction_type = if client_allocation.sell_buy.to_lowercase() == "buy" {
                TransactionType::Buy
            } else {
                TransactionType::Sell
            };

            transaction_constructed.transaction_sub_type = if client_allocation.sell_buy.to_lowercase() == "buy" {
                TransactionSubType::Buy
            } else {
                TransactionSubType::Sell
            };
            transaction_constructed.transaction_date = entry_in_client_order.order_date;
            transaction_constructed.cgt_date = entry_in_client_order.order_date;
            transaction_constructed.settlement_date = entry_in_client_order.settlement_date;
            transaction_constructed.quantity = client_allocation.quantity;
            transaction_constructed.market_rate = client_allocation.market_rate;
            transaction_constructed.amount = client_allocation.net_amount;
            transaction_constructed.brokerage = client_allocation.actual_brokerage;
            transaction_constructed.service_tax = client_allocation.service_tax;
            transaction_constructed.stt_amount = client_allocation.stt_amount;
            transaction_constructed.turn_tax = client_allocation.turn_tax_exchange_txn_tax;
            transaction_constructed.other_tax = client_allocation.stamp_duty_and_other_charges;
            transaction_constructed.mf_folio = client_allocation.folio_no.clone();
            transaction_constructed.price = compute_transaction_price(&transaction_constructed, include_stt_in_cost);

            //If there are Unsettled entries change the status based on the release payout
            if let Some(mut trade_unsettled) = entry_in_trade_order_unsettled.clone() {
                trade_unsettled.amount = entry_in_client_order.settlement_net_amount;

                if release_payout {
                    log_info(LogBuilder::system(&format!(
                        "Changing Trade Order Unsettled Status to Processed-Recon for Id = {:?}",
                        trade_unsettled.id
                    )));
                    trade_unsettled.status = "Processed-Recon".to_string();

                    // Add Entry to Ledger
                    // Running Balance will be calculated in next step separately
                    let ledger_entry = PortfolioCashLedger {
                        amount: if !include_stt_in_cost {
                            transaction_constructed.amount - transaction_constructed.stt_amount
                        } else {
                            transaction_constructed.amount
                        },
                        is_model_portfolio: false,
                        portfolio_id: Some(entry_in_client_order.portfolio_id.clone()),
                        txn_ref_id: Some(trade_unsettled.id.clone()),
                        settlement_date: entry_in_client_order.settlement_date,
                        transaction_date: entry_in_client_order.order_date,
                        transaction_type: if trade_unsettled.r#type == "Buy" {
                            TransactionType::Debit
                        } else {
                            TransactionType::Credit
                        },
                        transaction_sub_type: TransactionSubType::from_str(&trade_unsettled.r#type).unwrap(),
                        description: Some(format!("Securities bought Amount reflects net")),
                        ..Default::default()
                    };

                    let mut ledger_entries_guard = ledger_entries.write().await;

                    log_info(LogBuilder::system("Adding Ledger Entry"));

                    ledger_entries_guard.push(ledger_entry);
                    drop(ledger_entries_guard);
                } else {
                    log_info(LogBuilder::system(&format!(
                        "Changing Trade Order Unsettled Status to Hold for Id = {:?}",
                        trade_unsettled.id
                    )));
                    trade_unsettled.status = "Hold".to_string();
                }

                let mut trade_order_updates_guard = trade_order_updates.write().await;
                trade_order_updates_guard.push(trade_unsettled);
            }

            //Update Pending Quantities
            if entry_in_client_order.pending_quantity > 0.00 {
                let entry_in_client_order = entry_in_client_order.clone();

                //Add Carry Forward Trade
                let carry_forward_trade_order = ClientOrderEntry {
                    source_type: format!("CARRYFORWARDTRADE-{}", entry_in_client_order.source_type),
                    remarks: format!("CARRYFORWARDTRADE-{}", entry_in_client_order.remarks),
                    order_status: if cancel_pending_orders {
                        OrderStatus::Cancelled
                    } else {
                        OrderStatus::Draft
                    },
                    order_date: Utc::now().naive_utc(),
                    quantity: entry_in_client_order.quantity - client_allocation.quantity,
                    pending_quantity: entry_in_client_order.quantity - client_allocation.quantity,
                    transaction_amount: (((entry_in_client_order.quantity - client_allocation.quantity)
                        * latest_price)
                        * 100f64)
                        .round()
                        / 100f64,
                    price: latest_price,
                    settlement_brokerage_amount: 0f64,
                    settlement_market_amount: 0f64,
                    settlement_net_amount: 0f64,
                    settlement_net_rate: 0f64,
                    settlement_other_tax: 0f64,
                    settlement_price: 0f64,
                    settlement_quantity: 0f64,
                    settlement_service_tax: 0f64,
                    settlement_stt_amount: 0f64,
                    settlement_turn_tax: 0f64,
                    ..entry_in_client_order
                };

                let mut carry_forwarded_trade_guard = carry_forwarded_trade.write().await;
                carry_forwarded_trade_guard.push(carry_forward_trade_order);
            }

            // Add to Updated Entries For Model Transactions

            let security_sub_type = if entry_in_client_order.investment_type == SecurityType::Stocks
                || entry_in_client_order.investment_type == SecurityType::ETF
            {
                Some(SecuritySubType::Listed)
            } else if entry_in_client_order.investment_type == SecurityType::MutualFund {
                Some(SecuritySubType::MutualFund)
            } else if entry_in_client_order.investment_type == SecurityType::FixedIncome {
                Some(SecuritySubType::Bond)
            } else {
                None
            };

            // Add stt into Ledger entry
            if !include_stt_in_cost && release_payout {
                log_info(LogBuilder::system("Adding Ledger Entry for STT"));
                let ledger_entry = PortfolioCashLedger {
                    amount: transaction_constructed.stt_amount,
                    is_model_portfolio: false,
                    portfolio_id: Some(entry_in_client_order.portfolio_id.clone()),
                    txn_ref_id: Some(transaction_constructed.id.clone()),
                    settlement_date: entry_in_client_order.settlement_date,
                    transaction_date: entry_in_client_order.order_date,
                    transaction_type: TransactionType::Debit,
                    transaction_sub_type: TransactionSubType::Stt,
                    description: Some(format!(
                        "{}{}",
                        transaction_constructed.isin, transaction_constructed.name
                    )),
                    ..Default::default()
                };

                let mut ledger_entries_guard = ledger_entries.write().await;

                ledger_entries_guard.push(ledger_entry);
                drop(ledger_entries_guard);
            }

            let transaction_for_settlement = InvestmentTransactionForSettlement {
                net_rate: entry_in_client_order.settlement_price,
                transaction_details: transaction_constructed,
                security_type: entry_in_client_order.investment_type.clone(),
                security_sub_type,
            };

            if entry_in_client_order.investment_type == SecurityType::MutualFund {
                let mut mutual_fund_txns_guard = mutual_fund_txns.write().await;
                mutual_fund_txns_guard.push(transaction_for_settlement);
            } else {
                let mut direct_equity_transactions_guard = direct_equity_transactions.write().await;
                direct_equity_transactions_guard.push(transaction_for_settlement);
            }

            let mut updated_client_order_entries_guard = updated_client_order_entries.write().await;
            updated_client_order_entries_guard.push(entry_in_client_order);
            drop(updated_client_order_entries_guard);
        }.instrument(Span::current()));

        handlers.push(handler);
    }

    try_join_all(handlers).await.map_err(|e| {
        log_error(LogBuilder::system("Error on Constructing Transactions").add_metadata("error", &e.to_string()));
        return format!("Error on Constructing Transactions");
    })?;

    log_info(LogBuilder::system(
        "Constructing Transactions From Client Allocation Completed",
    ));

    //Update Client Order Entries
    let updated_client_order_entries_guard = updated_client_order_entries.read().await;
    log_info(LogBuilder::system(&format!(
        "Updating CLient Order Entries in BULK Total = {}",
        updated_client_order_entries_guard.len()
    )));

    let execute_result = ClientOrderEntry::update_bulk_on_settlement(&mut conn, &updated_client_order_entries_guard)
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system("Failed to bulk update Client Order Entries").add_metadata("error", &e.to_string()),
            );
            String::from("Bulk Update Of COE failed")
        })?;

    if execute_result.rows_affected().len() != updated_client_order_entries_guard.len() {
        log_error(LogBuilder::system(&format!(
            "Only {} Got Updated in ClientOrderENtries. Whereas We expect that to be {}",
            execute_result.rows_affected().len(),
            updated_client_order_entries_guard.len()
        )));
        log_info(LogBuilder::system("Reverting the Transaction"));
        return Err(String::from("Failed to Update ClientOrderEntriesTable"));
    } else {
        log_info(LogBuilder::system(
            "All the Entries Got Updated in ClientOrderEntriesTable. Continuing with next process",
        ));
    }

    drop(updated_client_order_entries_guard);

    log_info(LogBuilder::system("Client Order Entries in Bulk Updation Completed"));

    //Update Trade Order Unsettled
    let trade_order_updates_guard = trade_order_updates.read().await;
    log_info(LogBuilder::system(&format!(
        "BULK Updating Trade Order Unsettled Amount Table : Count = {}",
        trade_order_updates_guard.len()
    )));

    let execute_result = TradeOrderUnsettledAmounts::bulk_update_on_settlement(&mut conn, &trade_order_updates_guard)
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system("Failed to bulk update Trade Order Unsettled").add_metadata("error", &e.to_string()),
            );
            String::from("Bulk Update Of Trade Order Unsettled Amount failed")
        })?;

    log_info(LogBuilder::system(&format!(
        "BULK Update of Trade Order Unsettled Amount Completed. Count = {}",
        execute_result.rows_affected().len()
    )));

    if execute_result.rows_affected().len() != trade_order_updates_guard.len() {
        log_error(LogBuilder::system(&format!(
            "Only {} Got Updated in TradeOrderUnsettledAmounts. Whereas We expect that to be {}",
            execute_result.rows_affected().len(),
            trade_order_updates_guard.len()
        )));
        log_info(LogBuilder::system("Reverting the Transaction"));
        return Err(String::from("Failed to Update TradeOrderUnsettledAmount"));
    } else {
        log_info(LogBuilder::system(
            "All the Entries Got Updated in TradeOrderUnsettledAmounts. Continuing with next process",
        ));
    }

    drop(trade_order_updates_guard);

    //DO Bulk Insertion Of Carry Forward Trade
    let carry_forwarded_trade_guard = carry_forwarded_trade.read().await;
    log_info(LogBuilder::system(&format!(
        "Inserting Carry Forward Trade in bulk : Total = {}",
        carry_forwarded_trade_guard.len()
    )));

    let _ = ClientOrderEntry::bulk_insert(&mut conn, (*carry_forwarded_trade_guard).clone())
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system("Failed: to bulk insert Carry Forward Trade").add_metadata("error", &e.to_string()),
            );
            String::from("Failed Bulk Insert Of CFT")
        })?;

    drop(carry_forwarded_trade_guard);

    // Insert Transactions
    let direct_equity_transactions = direct_equity_transactions.read().await;
    let group_equity_transactions = group_by_multiple_fields(direct_equity_transactions.clone(), |txn| {
        (
            txn.transaction_details.portfolio_id.clone(),
            txn.transaction_details.client_id.clone(),
        )
    });

    let mutual_fund_txns = mutual_fund_txns.read().await;
    let group_mf_transactions = group_by_multiple_fields(mutual_fund_txns.clone(), |txn| {
        (
            txn.transaction_details.portfolio_id.clone(),
            txn.transaction_details.client_id.clone(),
        )
    });

    let security_details_map = security_details_map.read().await;
    let mf_details_map = mutual_fund_details_map.read().await;

    log_info(LogBuilder::system(&format!(
        "Started Inserting Equity Transactions: Total = {}",
        group_equity_transactions.len()
    )));

    // Loop through all the transaction for each portfolio
    for ((portfolio_id, client_id), transactions) in group_equity_transactions {
        log_info(LogBuilder::system(&format!(
            "Inserting Equity Transactions for Portfolio ID = {:?} Client ID = {:?}",
            portfolio_id, client_id
        )));
        //Add Equity Transaction to Transaction Table If exist
        if transactions.len() > 0 {
            add_transactions_to_equity_investment(
                &security_details_map,
                &mut conn,
                client_id.unwrap(),    //SAFE UNWRAP
                portfolio_id.unwrap(), //SAFE UNWRAP
                transactions,
                release_payout,
                include_stt_in_cost,
            )
            .await?;
        } else {
            log_warn(LogBuilder::system("No Equity Transactions Found to Insert"));
        }
    }
    log_warn(LogBuilder::system("Finished Inserting Equity Transactions"));

    log_info(LogBuilder::system(&format!(
        "Started Inserting Mutual Fund Transactions: Total = {}",
        group_mf_transactions.len()
    )));

    // Loop through all the transaction for a each portfolio
    for ((portfolio_id, client_id), transactions) in group_mf_transactions {
        //Add Mutual Fund Transaction to Transaction Table If exist
        if transactions.len() > 0 {
            add_transactions_to_mutual_fund_investment(
                &mf_details_map,
                &mut conn,
                client_id.unwrap(),    //SAFE UNWRAP
                portfolio_id.unwrap(), //SAFE UNWRAP
                transactions,
                release_payout,
                include_stt_in_cost,
            )
            .await?;
            log_info(LogBuilder::system("Finished Inserting Mutual Fund Transactions"));
        } else {
            log_warn(LogBuilder::system("No Mutual Fund Transactions Found to Insert"));
        }
    }

    let mut bulk_ledger_entries = Vec::new();

    // Check If anything to add Ledger
    // If yes compute the running balance
    let mut ledger_entries_guard = ledger_entries.write().await;
    if ledger_entries_guard.len() > 0 {
        log_info(LogBuilder::system(
            "Started Computng Running Balance for the Ledger Entries",
        ));
        let ledger_entries_by_portfolio_id = ledger_entries_guard.iter_mut().fold(HashMap::new(), |mut acc, entry| {
            acc.entry(entry.portfolio_id.clone().unwrap())
                .or_insert_with(Vec::new)
                .push(entry);
            acc
        });

        for (portfolio_id, ledger_entries) in ledger_entries_by_portfolio_id {
            log_info(LogBuilder::system(&format!(
                "Calculating Running Balance for Portfolio {:?}",
                portfolio_id
            )));
            // Get the Current Running Balance of this Portfolio
            // And Lock the Row
            let mut current_running_balance =
                PortfolioCashLedger::get_running_balance_of_a_portfolio(&mut conn, portfolio_id, true)
                    .await
                    .map_err(|e| {
                        log_error(
                            LogBuilder::system("Failed to get running balance").add_metadata("error", &e.to_string()),
                        );
                        return String::from("Failed To Get Runnign Balance");
                    })?
                    .unwrap_or(0f64);

            for ledger_entry in ledger_entries {
                if ledger_entry.transaction_type == TransactionType::Credit {
                    current_running_balance += ledger_entry.amount;
                } else {
                    current_running_balance -= ledger_entry.amount;
                }

                ledger_entry.running_balance = current_running_balance;

                bulk_ledger_entries.push(ledger_entry);
            }
        }
        log_info(LogBuilder::system(
            "Finished Calculating Running Balance for Ledger Entries",
        ));
    }

    //Insert the ledger in Bulk
    log_info(LogBuilder::system(&format!(
        "Started Inserting Ledger Entries: Total = {}",
        bulk_ledger_entries.len()
    )));
    for entry in bulk_ledger_entries {
        log_info(LogBuilder::system(&format!(
            "Updating Ledger for Portfolio {:?}",
            &entry.portfolio_id
        )));
        entry.insert(&mut conn).await.map_err(|e| {
            return format!("{:?}", e);
        })?;
    }
    log_info(LogBuilder::system("Finished Inserting Ledger Entries"));

    // Update All the models based on Client Order Entries Processed For this settlement
    log_info(LogBuilder::system("Started Model Portfolio Updation"));
    let updated_client_order_entries = updated_client_order_entries.read().await;

    update_models_on_settlement(&security_details_map, &mut conn, updated_client_order_entries.to_vec()).await?;

    log_info(LogBuilder::system("Finished Model Portfolio Updation"));

    // Update STP
    log_info(LogBuilder::system("Started STP Updation"));
    update_stp_on_settlement(&mut conn, &updated_client_order_entries).await?;
    log_info(LogBuilder::system("Finished STP Updation"));

    Ok(())
}

fn compute_transaction_price(txn: &InvestmentTransaction, is_stt_included_in_cost: bool) -> f64 {
    if txn.quantity == 0f64 {
        return 0f64;
    }

    let stt_amount = if is_stt_included_in_cost { txn.stt_amount } else { 0f64 };

    if txn.transaction_type == TransactionType::Buy {
        return txn.market_rate
            + txn.brokerage
            + (txn.service_tax + stt_amount + txn.turn_tax + txn.other_tax) / txn.quantity;
    } else if txn.transaction_type == TransactionType::Sell {
        return txn.market_rate
            - txn.brokerage
            - (txn.service_tax + stt_amount + txn.turn_tax + txn.other_tax) / txn.quantity;
    } else if txn.transaction_type == TransactionType::SecurityIn {
        return txn.market_rate
            + txn.brokerage
            + (txn.service_tax + stt_amount + txn.turn_tax + txn.other_tax) / txn.quantity;
    } else if txn.transaction_type == TransactionType::SecurityOut {
        return txn.market_rate
            - txn.brokerage
            - (txn.service_tax + stt_amount + txn.turn_tax + txn.other_tax) / txn.quantity;
    }

    0.0
}

#[cfg(test)]
mod tests {
    use alpha_core_db::{connection::connect_to_master_data, redis::connect_to_redis};
    use tracing::Level;
    use tracing_subscriber::FmtSubscriber;
    use tracing_test::traced_test;

    use super::*;

    #[tokio::test]
    #[traced_test]
    async fn update_client_portfolios_test() {
        dotenv::dotenv().ok();
        let subscriber = FmtSubscriber::builder().with_max_level(Level::INFO).finish();

        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let master_data_pool = connect_to_master_data(10).await;
        let redis_pool = connect_to_redis().await;
        let id = String::from("5cc08826a86742f1a2d2ee9a8e69c5d3");

        process_settlement(pool, master_data_pool, redis_pool, id, false, true).await;
    }
}
