use std::collections::HashMap;

use alpha_core_db::{
    clickhouse::{create_clickhouse_client, investment::InvestmentAnalytics, portfolio::PortfolioAnalytics},
    schema::{
        capital_register::{PortfolioCapitalRegister, PortfolioCapitalRegisterForPerformanceEngine},
        client_order_entry::{TransactionSubType, TransactionType},
        investment::Investments,
        investment_transaction::TransactionsForPerformanceEngine,
        portfolio::Portfolio,
    },
};
use chrono::{NaiveDate, NaiveDateTime};
use time::Date;

#[derive(Debug)]
pub struct PortfolioGenesisStates {
    /// Date of the portfolio state
    pub date: Date,

    /// Portfolio Details
    pub portfolio: Portfolio,

    ///Holdings in the Portfolio
    pub investments: Vec<Investments>,
}

impl PortfolioGenesisStates {
    pub async fn apply_transactions(
        &mut self,
        transactions: Vec<TransactionsForPerformanceEngine>,
        capital_register: Vec<PortfolioCapitalRegisterForPerformanceEngine>,
    ) {
        let transactions_by_isin = transactions.into_iter().fold(HashMap::new(), |mut acc, txn| {
            acc.entry(txn.isin.clone()).or_insert_with(Vec::new).push(txn);
            acc
        });

        let mut new_investments = Vec::new();

        for (isin, transactions) in transactions_by_isin {
            new_investments.push(self.build_investment(transactions));
        }

        let new_portfolio = self.build_portfolio(&new_investments, &capital_register);
        println!("{:?}", new_portfolio);
        let ch_client = create_clickhouse_client();
        let mut insert = ch_client.insert("PortfolioAnalytics").unwrap();
        insert.write(&new_portfolio).await.unwrap();
        insert.end().await.unwrap();
    }

    pub fn build_investment(&self, transactions: Vec<TransactionsForPerformanceEngine>) -> InvestmentAnalytics {
        let (total_capital, total_withdrawals, invested_capital, dividends_paid) =
            self.calculate_capital_value(&transactions);
        let current_price = 0f64;
        let (first_txn_date, last_txn_date, current_holding) =
            self.find_min_max_dates_and_max_holding(&transactions[..]);
        let change_in_holdings = self.calculate_change_in_holdings_from_transactions(&transactions);

        let market_value = (change_in_holdings) * current_price;

        let realised_gains = self.calculate_realised_gain_loss(&transactions);
        let single_transaction = &transactions[0];

        let new_investment_state = InvestmentAnalytics {
            client_id: single_transaction.client_id.clone(),
            portfolio_id: single_transaction.portfolio_id.clone(),
            total_capital: total_capital,
            dividends_paid: dividends_paid,
            unrealised_gain_loss: -invested_capital,
            absolute_return_percent: 0f64,
            absolute_return_value: 0f64,
            // asset_class: single_transaction.asset_class,
            // asset_type: single_transaction.asset_type,
            close_price: current_price,
            current_price,
            holdings: change_in_holdings,
            market_value,
            date: self.date,
            invested_capital: invested_capital,
            withdrawals: total_withdrawals,
            market_value_change: market_value,
            market_value_change_percent: 100f64,
            realised_gain_loss: realised_gains,
            ..Default::default()
        };

        new_investment_state
    }

    pub fn build_portfolio(
        &self,
        new_investment: &Vec<InvestmentAnalytics>,
        capital_register: &Vec<PortfolioCapitalRegisterForPerformanceEngine>,
    ) -> PortfolioAnalytics {
        let market_value = new_investment.iter().map(|nv| nv.market_value).sum();
        let dividends_paid = new_investment.iter().map(|nv| nv.dividends_paid).sum();

        let (total_capital, total_withdrawals, invested_capital) =
            self.calculate_portfolio_capital_values(capital_register);

        let new_portfolio_state = PortfolioAnalytics {
            date: self.date,
            portfolio_id: self.portfolio.id.clone(),
            client_id: self.portfolio.client_id.clone(),
            name: self.portfolio.name.clone(),
            total_capital: total_capital,
            withdrawals: total_withdrawals,
            invested_capital: invested_capital,
            market_value,
            dividends_paid,
            dividend_reinvested: 0f64,
            ..Default::default()
        };
        new_portfolio_state
    }

    /// This calculated the total capital, withdrawal,capital that is invested
    /// As well as the dividends paid from the transactions
    fn calculate_capital_value(&self, transactions: &Vec<TransactionsForPerformanceEngine>) -> (f64, f64, f64, f64) {
        let total_capital: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = transactions
            .iter()
            .filter(|txn| txn.unrealised_holding > 0f64)
            .map(|txn| txn.unrealised_holding * txn.price)
            .sum();

        let dividends_paid: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::DividendPaid)
            .map(|txn| txn.amount)
            .sum();

        (total_capital, total_withdrawal, invested_capital, dividends_paid)
    }

    fn calculate_total_cash_flow(
        &self,
        as_at_date: NaiveDate,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> f64 {
        transactions
            .iter()
            .filter(|txn| txn.transaction_date == <NaiveDate as Into<NaiveDateTime>>::into(as_at_date))
            .fold(0f64, |acc, txn| match txn.transaction_type {
                TransactionType::Buy => acc + txn.amount,
                TransactionType::Sell => acc - txn.amount,
                _ => acc,
            })
    }

    fn calculate_realised_gain_loss(&self, transactions: &Vec<TransactionsForPerformanceEngine>) -> f64 {
        let mut realized_gain_loss = 0.0;
        let mut remaining_buy_quantities: Vec<(String, f64, f64)> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| (txn.id.clone(), txn.quantity, txn.price))
            .collect();

        let sell_transactions: Vec<_> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .collect();

        for sell_txn in sell_transactions {
            let mut current_sell_quantity = sell_txn.quantity;

            for (_, buy_quantity, buy_price) in remaining_buy_quantities
                .iter_mut()
                .filter(|(_, quantity, _)| *quantity > 0.0)
            {
                if current_sell_quantity <= 0.0 {
                    break;
                }

                let sold_quantity = current_sell_quantity.min(*buy_quantity);
                realized_gain_loss += (sell_txn.price - *buy_price) * sold_quantity;

                *buy_quantity -= sold_quantity;
                current_sell_quantity -= sold_quantity;
            }
        }

        realized_gain_loss
    }

    fn find_min_max_dates_and_max_holding(
        &self,
        objects: &[TransactionsForPerformanceEngine],
    ) -> (chrono::NaiveDateTime, chrono::NaiveDateTime, f64) {
        let (min_date, max_date, max_holding) = objects.iter().fold(
            (
                objects[0].transaction_date,
                objects[0].transaction_date,
                objects[0].current_holding,
            ),
            |(min, max, max_holding), obj| {
                let new_min = min.min(obj.transaction_date);
                let new_max = max.max(obj.transaction_date);
                let new_max_holding = if obj.transaction_date >= max {
                    obj.current_holding
                } else {
                    max_holding
                };
                (new_min, new_max, new_max_holding)
            },
        );

        (min_date, max_date, max_holding)
    }

    /// Calculates the change in holding for the given set of transaction
    /// It can be negative/positive
    fn calculate_change_in_holdings_from_transactions(
        &self,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> f64 {
        let mut current_bal = 0f64;
        for txn in transactions.iter() {
            if txn.transaction_type == TransactionType::Buy {
                current_bal += txn.quantity;
            } else if txn.transaction_type == TransactionType::Sell {
                current_bal -= txn.quantity;
            }
        }

        current_bal
    }

    fn long_short_term_holdings(
        &self,
        asset_class: &str,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> (f64, f64) {
        let mut long = 0f64;
        let mut short = 0f64;

        let current_date = chrono::Local::now().naive_local().date();

        let current_held_quantity_txns: Vec<&TransactionsForPerformanceEngine> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy && txn.unrealised_holding > 0.0)
            .collect();

        let long_short_threshold = match asset_class.to_lowercase().as_str() {
            "debt" | "commodity" | "realestate" | "fixedincome" => 3.0,
            _ if asset_class.to_lowercase().contains("gold") => 3.0,
            _ => 1.0,
        };

        for holding in current_held_quantity_txns {
            let years_difference = current_date.years_since(holding.cgt_date.into()).unwrap_or(0) as f64;

            if years_difference > long_short_threshold {
                long += holding.unrealised_holding;
            } else {
                short += holding.unrealised_holding;
            }
        }

        (long, short)
    }

    fn calculate_portfolio_capital_values(
        &self,
        capital_register: &Vec<PortfolioCapitalRegisterForPerformanceEngine>,
    ) -> (f64, f64, f64) {
        let total_capital: f64 = capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Inflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalIn
                        || txn.transaction_sub_type == TransactionSubType::SecurityIn)
            })
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Outflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalOut
                        || txn.transaction_sub_type == TransactionSubType::SecurityOut)
            })
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = total_capital - total_withdrawal;

        (total_capital, total_withdrawal, invested_capital)
    }
}
