use std::collections::HashMap;

use alpha_core_db::{
    clickhouse::{
        create_clickhouse_client,
        investment::{self, InvestmentAnalytics},
        portfolio::PortfolioAnalytics,
    },
    connection::pool::{deadpool::managed::Object, Manager},
    schema::{
        capital_register::PortfolioCapitalRegister,
        client_order_entry::{TransactionSubType, TransactionType},
        investment::Investments,
        investment_transaction::{InvestmentTransaction, TransactionsForPerformanceEngine},
        portfolio::Portfolio,
    },
};
use chrono::{Days, NaiveDate, NaiveDateTime, Utc};
use genesis::PortfolioGenesisStates;
use time::Date;

pub mod genesis;
const EPSILON: f64 = 1e-10;

pub struct PortfolioStateBuilder;

impl PortfolioStateBuilder {
    /// For Building Portfolio State from a previous state
    pub async fn build(date: NaiveDate, portfolio_id: String) -> PortfolioStates {
        //Fetch Portfolio State from Clickhouse
        let ch_client = create_clickhouse_client();
        let investment_analytics = InvestmentAnalytics::get_by_date(ch_client.clone(), date).await;
        let portfolio_analytics = PortfolioAnalytics::get_by_date_and_portfolio_id(ch_client, date, portfolio_id).await;
        PortfolioStates {
            date,
            portfolio_analytics,
            investment_analytics,
        }
    }

    /// For Building Genesis state take the entry from investment table
    pub async fn build_genesis(
        date: Date,
        portfolio: Portfolio,
        investments: Vec<Investments>,
    ) -> PortfolioGenesisStates {
        PortfolioGenesisStates {
            date,
            portfolio,
            investments,
        }
    }
}

/// State of a Portfolio On a Particular Date
/// To compute the state for the next day apply transactions to this state
/// Transactions should be applied first on investment state then on portfolio
#[derive(Debug)]
pub struct PortfolioStates {
    /// Date of the portfolio state
    pub date: NaiveDate,

    /// Portfolio Details
    pub portfolio_analytics: PortfolioAnalytics,

    ///Holdings in the Portfolio
    pub investment_analytics: Vec<InvestmentAnalytics>,
}

impl PortfolioStates {
    /// Apply transaction to the current state
    /// And Returns the new state
    /// Transactions can be of different securities
    pub fn apply_transactions(
        &mut self,
        transactions: Vec<TransactionsForPerformanceEngine>,
        capital_register: Vec<PortfolioCapitalRegister>,
    ) {
        //Group investments by isin and apply each transaction on it
        let investment_by_isin = self.investment_analytics.iter().fold(HashMap::new(), |mut acc, inv| {
            acc.insert(inv.isin.clone(), inv);
            acc
        });

        let mut new_investments = Vec::new();

        for (isin, investment) in investment_by_isin.clone() {
            let new_investment = self.apply_transition_on_investment(investment.clone(), transactions.clone());

            new_investments.push(new_investment);
        }

        let new_portfolio_state = self.apply_transition_on_portfolio(&capital_register, &new_investments);
    }

    /// Applies investment state transition based on transactions.
    ///
    /// This function takes the current investment state and a list of transactions,
    /// then calculates and returns the new investment state after applying these transactions.
    ///
    /// # Arguments
    ///
    /// * `investment` - The current state of the investment (InvestmentAnalytics).
    /// * `transactions` - A vector of transactions (TransactionsForPerformanceEngine) to be applied.
    ///
    /// # Returns
    ///
    /// Returns a new InvestmentAnalytics representing the updated investment state after applying all transactions.
    fn apply_transition_on_investment(
        &self,
        investment: InvestmentAnalytics,
        transactions: Vec<TransactionsForPerformanceEngine>,
    ) -> InvestmentAnalytics {
        let (total_capital, total_withdrawals, invested_capital, dividends_paid) =
            self.calculate_capital_value(&transactions);

        let (first_txn_date, last_txn_date, current_holding) =
            self.find_min_max_dates_and_max_holding(&transactions[..]);

        let absolute_return_value = if investment.total_capital.abs() < EPSILON {
            0.0
        } else {
            (investment.market_value + dividends_paid + investment.realised_gain_loss) - investment.total_capital
        };

        let absolute_return_percent = if investment.total_capital.abs() < EPSILON {
            0.0
        } else {
            ((absolute_return_value / investment.total_capital) * 100.0).round() / 100.0
        };

        let change_in_holdings = self.calculate_change_in_holdings_from_transactions(&transactions);

        let unit_holding_split = self.long_short_term_holdings(&investment.asset_class, &transactions);

        let market_value = (change_in_holdings + investment.holdings) * investment.current_price;

        let realised_gains = self.calculate_realised_gain_loss(&transactions);

        let new_investment_state = InvestmentAnalytics {
            client_id: investment.client_id.clone(),
            portfolio_id: investment.portfolio_id.clone(),
            investment_id: investment.investment_id.clone(),
            total_capital: total_capital + investment.total_capital,
            dividends_paid: dividends_paid + investment.dividends_paid,
            unrealised_gain_loss: investment.market_value - invested_capital,
            absolute_return_percent,
            absolute_return_value,
            asset_class: investment.asset_class,
            asset_type: investment.asset_type,
            close_price: investment.close_price,
            close_price_change: investment.close_price_change,
            close_price_date: investment.close_price_date,
            current_price: investment.current_price,
            holdings: change_in_holdings + investment.holdings,
            market_value: (change_in_holdings + investment.holdings) * investment.current_price,
            date: investment.date.next_day().unwrap(),
            invested_capital: invested_capital + investment.invested_capital,
            withdrawals: total_withdrawals + investment.withdrawals,
            long_term_units: unit_holding_split.0,
            short_term_units: unit_holding_split.1,
            market_value_change: investment.market_value_change - market_value,
            market_value_change_percent: (investment.market_value_change - market_value) / investment.market_value,
            realised_gain_loss: realised_gains,
            ..investment
        };

        new_investment_state
    }

    /// Applies capital register and new investments to the previous portfolio state to derive the next state.
    ///
    /// This function takes the current portfolio state, applies the capital register changes,
    /// and incorporates new investments to calculate and return the updated portfolio analytics.
    ///
    /// # Arguments
    ///
    /// * `capital_register` - A vector of `PortfolioCapitalRegister` representing capital changes.
    /// * `new_investment` - A vector of `InvestmentAnalytics` representing new investments.
    ///
    /// # Returns
    ///
    /// Returns a `PortfolioAnalytics` struct representing the updated portfolio state after
    /// applying the transition.
    fn apply_transition_on_portfolio(
        &self,
        capital_register: &Vec<PortfolioCapitalRegister>,
        new_investment: &Vec<InvestmentAnalytics>,
    ) -> PortfolioAnalytics {
        let market_value = new_investment.iter().map(|nv| nv.market_value).sum();
        let dividends_paid = new_investment.iter().map(|nv| nv.dividends_paid).sum();

        let (total_capital, total_withdrawals, invested_capital) =
            self.calculate_portfolio_capital_values(capital_register);

        let new_portfolio_state = PortfolioAnalytics {
            portfolio_id: self.portfolio_analytics.portfolio_id.clone(),
            total_capital: total_capital + self.portfolio_analytics.total_capital,
            withdrawals: total_withdrawals + self.portfolio_analytics.withdrawals,
            invested_capital: invested_capital + self.portfolio_analytics.invested_capital,
            market_value,
            dividends_paid,
            dividend_reinvested: 0f64,
            ..Default::default()
        };
        new_portfolio_state
    }

    /// This calculated the total capital, withdrawal,capital that is invested
    /// As well as the dividends paid from the transactions
    fn calculate_capital_value(&self, transactions: &Vec<TransactionsForPerformanceEngine>) -> (f64, f64, f64, f64) {
        let total_capital: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = transactions
            .iter()
            .filter(|txn| txn.unrealised_holding > 0f64)
            .map(|txn| txn.unrealised_holding * txn.price)
            .sum();

        let dividends_paid: f64 = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::DividendPaid)
            .map(|txn| txn.amount)
            .sum();

        (total_capital, total_withdrawal, invested_capital, dividends_paid)
    }

    fn calculate_total_cash_flow(
        &self,
        as_at_date: NaiveDate,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> f64 {
        transactions
            .iter()
            .filter(|txn| txn.transaction_date == <NaiveDate as Into<NaiveDateTime>>::into(as_at_date))
            .fold(0f64, |acc, txn| match txn.transaction_type {
                TransactionType::Buy => acc + txn.amount,
                TransactionType::Sell => acc - txn.amount,
                _ => acc,
            })
    }

    fn calculate_realised_gain_loss(&self, transactions: &Vec<TransactionsForPerformanceEngine>) -> f64 {
        let mut realized_gain_loss = 0.0;
        let mut remaining_buy_quantities: Vec<(String, f64, f64)> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| (txn.id.clone(), txn.quantity, txn.price))
            .collect();

        let sell_transactions: Vec<_> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .collect();

        for sell_txn in sell_transactions {
            let mut current_sell_quantity = sell_txn.quantity;

            for (_, buy_quantity, buy_price) in remaining_buy_quantities
                .iter_mut()
                .filter(|(_, quantity, _)| *quantity > 0.0)
            {
                if current_sell_quantity <= 0.0 {
                    break;
                }

                let sold_quantity = current_sell_quantity.min(*buy_quantity);
                realized_gain_loss += (sell_txn.price - *buy_price) * sold_quantity;

                *buy_quantity -= sold_quantity;
                current_sell_quantity -= sold_quantity;
            }
        }

        realized_gain_loss
    }

    fn find_min_max_dates_and_max_holding(
        &self,
        objects: &[TransactionsForPerformanceEngine],
    ) -> (chrono::NaiveDateTime, chrono::NaiveDateTime, f64) {
        let (min_date, max_date, max_holding) = objects.iter().fold(
            (
                objects[0].transaction_date,
                objects[0].transaction_date,
                objects[0].current_holding,
            ),
            |(min, max, max_holding), obj| {
                let new_min = min.min(obj.transaction_date);
                let new_max = max.max(obj.transaction_date);
                let new_max_holding = if obj.transaction_date >= max {
                    obj.current_holding
                } else {
                    max_holding
                };
                (new_min, new_max, new_max_holding)
            },
        );

        (min_date, max_date, max_holding)
    }

    /// Calculates the change in holding for the given set of transaction
    /// It can be negative/positive
    fn calculate_change_in_holdings_from_transactions(
        &self,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> f64 {
        let mut current_bal = 0f64;
        for txn in transactions.iter() {
            if txn.transaction_type == TransactionType::Buy {
                current_bal += txn.quantity;
            } else if txn.transaction_type == TransactionType::Sell {
                current_bal -= txn.quantity;
            }
        }

        current_bal
    }

    fn long_short_term_holdings(
        &self,
        asset_class: &str,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> (f64, f64) {
        let mut long = 0f64;
        let mut short = 0f64;

        let current_date = chrono::Local::now().naive_local().date();

        let current_held_quantity_txns: Vec<&TransactionsForPerformanceEngine> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy && txn.unrealised_holding > 0.0)
            .collect();

        let long_short_threshold = match asset_class.to_lowercase().as_str() {
            "debt" | "commodity" | "realestate" | "fixedincome" => 3.0,
            _ if asset_class.to_lowercase().contains("gold") => 3.0,
            _ => 1.0,
        };

        for holding in current_held_quantity_txns {
            let years_difference = current_date.years_since(holding.cgt_date.into()).unwrap_or(0) as f64;

            if years_difference > long_short_threshold {
                long += holding.unrealised_holding;
            } else {
                short += holding.unrealised_holding;
            }
        }

        (long, short)
    }

    fn calculate_portfolio_capital_values(&self, capital_register: &Vec<PortfolioCapitalRegister>) -> (f64, f64, f64) {
        let total_capital: f64 = capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Inflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalIn
                        || txn.transaction_sub_type == TransactionSubType::SecurityIn)
            })
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Outflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalOut
                        || txn.transaction_sub_type == TransactionSubType::SecurityOut)
            })
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = total_capital - total_withdrawal;

        (total_capital, total_withdrawal, invested_capital)
    }
}

#[cfg(test)]
mod tests {

    use alpha_core_db::schema::{
        capital_register::PortfolioCapitalRegister, investment::Investments,
        investment_transaction::InvestmentTransaction, portfolio::Portfolio,
    };
    use chrono::Utc;
    use futures::future::join_all;
    use time::{Date, OffsetDateTime};
    use tokio::time::Instant;

    use crate::performance_engine::{build_master_input, state_machine::PortfolioStateBuilder};

    #[tokio::test]
    async fn test_state_machine() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;

        let mut pool_conn = pool.get().await.unwrap();

        let date = OffsetDateTime::now_utc().date();
        let portfolio_id = String::from("223226237fb2447eadcd4d1bdf1c7668");

        let portfolio = Portfolio::get(&mut pool_conn, portfolio_id.clone())
            .await
            .unwrap()
            .unwrap();

        let investments = Investments::get_by_portfolio_id(&mut pool_conn, &portfolio_id)
            .await
            .unwrap();

        let transactions =
            InvestmentTransaction::get_by_portfolio_id_for_performance_engine(&mut pool_conn, portfolio_id.clone())
                .await
                .unwrap();

        let capital_registers =
            PortfolioCapitalRegister::get_by_portfolio_id_for_performance_engine(&mut pool_conn, portfolio_id)
                .await
                .unwrap();

        let mut prev_state = PortfolioStateBuilder::build_genesis(date, portfolio, investments).await;

        // let new_state = prev_state
        //     .apply_transactions(transactions, capital_registers)
        //     .await;
    }
}
