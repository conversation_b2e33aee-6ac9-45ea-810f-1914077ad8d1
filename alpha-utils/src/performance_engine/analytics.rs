use alpha_core_db::schema::{
    client_order_entry::TransactionType, investment::Investments,
    investment_transaction::TransactionsForPerformanceEngine,
};
use chrono::{NaiveDateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};

use super::investment_valuation::SecurityValuationService;

#[derive(Debug, Clone, Row, Deserialize, Serialize, Default)]
pub struct PortfolioAnalytics {
    pub portfolio_id: Option<String>,
    pub investment_id: String,
    pub logo_img_url: String,
    pub client_id: Option<String>,
    pub as_at_date: NaiveDateTime,
    pub aggregate_type: String,
    pub isin: String,
    pub symbol: String,
    pub series: String,
    pub amc_name: String,
    pub amfi_code: String,
    pub mf_rta_code: String,
    pub fund_class: String,
    pub credit_rating: String,
    pub index_name: String,
    pub exchange: String,
    pub investment_name: String,
    pub asset_class: String,
    pub asset_type: String,
    pub industry: String,
    pub market_cap: String,
    pub close_price: f64,
    pub close_price_date: NaiveDateTime,
    pub close_price_change: f64,
    pub compare_price: f64,
    pub compare_price_date: NaiveDateTime,
    pub absolute_return_value: f64,
    pub absolute_return_percentage: f64,
    pub all_units: f64,
    pub long_term_units: f64,
    pub short_term_units: f64,
    pub benchmark_return: f64,
    pub cagr: f64,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub withdrawals: f64,
    pub market_value: f64,
    pub dividend_paid: f64,
    pub dividend_reinvested: f64,
    pub average_purchase_price: f64,
    pub weight: f64,
    pub market_value_change: f64,
    pub market_value_change_percentage: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub xirr: f64,
    pub twrr: f64,
    pub first_date_of_investment: NaiveDateTime,
    pub date_of_maturity: NaiveDateTime,
    pub growth_at_10000: f64,
    pub growth_at_10000_percentage: f64,
    pub unique_key: String,
    pub number_of_investments: i32,
}

impl<'a> SecurityValuationService<'a> {
    pub fn build_analytics(&self, yesterdays_investment_analytics: Option<Investments>) -> PortfolioAnalytics {
        let unique_key_for_record = String::new();
        let unit_holding_split = self.long_short_term_holdings(&self.investment.asset_class, &self.transactions);

        let yesterdays_mv = if let Some(yesterdays_investment_analytics) = yesterdays_investment_analytics {
            yesterdays_investment_analytics.market_value
        } else {
            0f64
        };

        let latest_price_before_calc_date = 0; //FIXME:
        let abs_ret_val =
            (self.investment.market_value + self.investment.total_realisations) - self.investment.total_capital;
        let abs_ret_pct = if self.investment.total_capital == 0.0 {
            0.0
        } else {
            ((abs_ret_val / self.investment.total_capital) * 100.0).round() / 100.0
        };

        PortfolioAnalytics {
            portfolio_id: self.investment.portfolio_id.clone(),
            investment_id: self.investment.id.clone(),
            logo_img_url: "NotApplicable".to_string(),
            client_id: self.investment.client_id.clone(),
            as_at_date: self.as_at_date.into(),
            aggregate_type: "Holding".to_string(),
            isin: self.investment.isin.clone(),
            symbol: self.investment.symbol.clone(),
            // series: if investment.exchange == "NSE" {
            //     security_master.series.clone()
            // } else {
            //     security_master.scrip_group.clone()
            // },
            amc_name: "NotApplicable".to_string(),
            amfi_code: "NotApplicable".to_string(),
            mf_rta_code: "NotApplicable".to_string(),
            fund_class: "NotApplicable".to_string(),
            credit_rating: "NotApplicable".to_string(),
            index_name: "NotApplicable".to_string(),
            exchange: self.investment.exchange.clone(),
            investment_name: self.investment.name.clone(),
            asset_class: self.investment.asset_class.clone(),
            asset_type: self.investment.security_type.to_string(),
            // industry: if investment.sector.is_empty() {
            //     "Unknown".to_string()
            // } else {
            //     investment.sector.clone()
            // },
            // market_cap: if investment.market_cap.is_empty() {
            //     "Unknown".to_string()
            // } else {
            //     investment.market_cap.clone()
            // },
            close_price: self.investment.current_price,
            close_price_date: self.investment.current_price_date,
            // close_price_change: if latest_price_before_calc_date.price == 0.0 {
            //     0.0
            // } else {
            //     (investment.current_price - latest_price_before_calc_date.price)
            //         / latest_price_before_calc_date.price
            // },
            // compare_price: latest_price_before_calc_date.price,
            // compare_price_date: latest_price_before_calc_date.price_date,
            absolute_return_value: abs_ret_val,
            absolute_return_percentage: abs_ret_pct,
            all_units: self.investment.current_holding,
            long_term_units: unit_holding_split.0,
            short_term_units: unit_holding_split.1,
            benchmark_return: 0.0, // to be built
            cagr: 0.0,             // to be built
            total_capital: self.investment.total_capital,
            invested_capital: self.investment.invested_capital,
            withdrawals: self.investment.total_realisations,
            market_value: self.investment.market_value,
            dividend_paid: self.investment.dividends,
            dividend_reinvested: 0.0,     // to be built
            average_purchase_price: 0f64, //FIXME:
            weight: 0.0,                  // this gets updated at the time of portfolio calcs
            market_value_change: self.investment.market_value - yesterdays_mv,
            market_value_change_percentage: if yesterdays_mv == 0.0 {
                100.00
            } else {
                ((self.investment.market_value - yesterdays_mv) / yesterdays_mv * 100.0).round() / 100.0
            },
            realised_gain_loss: self.investment.realised_gain_loss,
            unrealised_gain_loss: self.investment.unrealised_gain_loss,
            xirr: self.investment.irr_since_inception,
            twrr: 0.0, // not applicable
            first_date_of_investment: self.investment.first_transaction_date,
            date_of_maturity: Utc::now().naive_local(),
            growth_at_10000: 0.0,            // to be built
            growth_at_10000_percentage: 0.0, // to be built
            unique_key: unique_key_for_record,
            number_of_investments: 1,
            ..Default::default()
        }
    }

    fn long_short_term_holdings(
        &self,
        asset_class: &str,
        transactions: &Vec<TransactionsForPerformanceEngine>,
    ) -> (f64, f64) {
        let mut long = 0f64;
        let mut short = 0f64;

        let current_date = chrono::Local::now().naive_local().date();

        let current_held_quantity_txns: Vec<&TransactionsForPerformanceEngine> = transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy && txn.unrealised_holding > 0.0)
            .collect();

        let long_short_threshold = match asset_class.to_lowercase().as_str() {
            "debt" | "commodity" | "realestate" | "fixedincome" => 3.0,
            _ if asset_class.to_lowercase().contains("gold") => 3.0,
            _ => 1.0,
        };

        for holding in current_held_quantity_txns {
            let years_difference = current_date.years_since(holding.cgt_date.into()).unwrap_or(0) as f64;

            if years_difference > long_short_threshold {
                long += holding.unrealised_holding;
            } else {
                short += holding.unrealised_holding;
            }
        }

        (long, short)
    }
}
