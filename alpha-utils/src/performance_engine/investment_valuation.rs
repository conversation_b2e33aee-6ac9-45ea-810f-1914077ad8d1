use std::{collections::HashMap, vec};

use alpha_core_db::schema::{
    client_order_entry::TransactionType,
    investment::Investments,
    investment_transaction::{InvestmentTransaction, TransactionsForPerformanceEngine},
};
use chrono::{NaiveDate, NaiveDateTime, Utc};
use quick_xml::se;
use serde::{Deserialize, Serialize};

pub struct SecurityValuationService<'a> {
    pub transactions: &'a mut Vec<TransactionsForPerformanceEngine>,
    pub investment: &'a mut Investments,
    pub as_at_date: NaiveDate,
}

#[derive(Serialize, Deserialize, Debug, Clone, Default)]
pub struct InvestmentPerformance {
    pub investment_id: String,
    pub portfolio_id: Option<String>,
    pub model_portfolio_id: Option<String>,
    pub client_id: Option<String>,
    pub is_model_portfolio: bool,
    pub total_capital: f64,
    pub total_withdrawals: f64,
    pub invested_capital: f64,
    pub market_value: f64,
    pub total_cash_flow: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub dividends_paid: f64,
    pub irr_since_inception: f64,
    pub irr_current: f64,
    pub average_price: f64,
    pub benchmark_name: String,
    pub benchmark_irr_since_inception: f64,
    pub benchmark_irr_current: f64,
    pub close_price: f64,
    pub close_price_date: NaiveDateTime,
    pub first_transaction_date: NaiveDateTime,
    pub last_transaction_date: NaiveDateTime,
    pub current_holding: f64,
}

impl<'a> SecurityValuationService<'a> {
    pub fn new(
        transactions: &'a mut Vec<TransactionsForPerformanceEngine>,
        investment: &'a mut Investments,
        as_at_date: NaiveDate,
    ) -> Self {
        Self {
            transactions,
            investment,
            as_at_date,
        }
    }

    pub fn calculate_performance(&mut self) -> InvestmentPerformance {
        self.calculate_current_holdings();
        self.calculate_open_lot_quantities();

        let (total_capital, total_withdrawals, invested_capital, dividends_paid) = self.calculate_capital_value();

        let (first_txn_date, last_txn_date, current_holding) =
            self.find_min_max_dates_and_max_holding(&self.transactions[..]);

        let performance = InvestmentPerformance {
            client_id: self.investment.client_id.clone(),
            portfolio_id: self.investment.portfolio_id.clone(),
            model_portfolio_id: self.investment.modelportfolio_id.clone(),
            investment_id: self.investment.id.clone(),
            total_capital,
            total_cash_flow: self.calculate_total_cash_flow(Utc::now().naive_utc().into()),
            total_withdrawals,
            dividends_paid,
            unrealised_gain_loss: self.investment.market_value - invested_capital,
            average_price: invested_capital / self.investment.current_holding,
            realised_gain_loss: self.calculate_realised_gain_loss(),
            first_transaction_date: first_txn_date,
            last_transaction_date: last_txn_date,
            current_holding: current_holding,
            ..Default::default() // ADD REST FIELDS
        };

        performance
    }

    fn calculate_capital_value(&mut self) -> (f64, f64, f64, f64) {
        let total_capital: f64 = self
            .transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = self
            .transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = self
            .transactions
            .iter()
            .filter(|txn| txn.unrealised_holding > 0f64)
            .map(|txn| txn.unrealised_holding * txn.price)
            .sum();

        let dividends_paid: f64 = self
            .transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::DividendPaid)
            .map(|txn| txn.amount)
            .sum();

        if self.investment.current_holding <= 0f64 {
            self.investment.market_value = 0f64;
        } else {
            self.investment.market_value = self.investment.current_holding * self.investment.current_price;
        }

        (total_capital, total_withdrawal, invested_capital, dividends_paid)
    }

    fn calculate_total_cash_flow(&self, as_at_date: NaiveDate) -> f64 {
        self.transactions
            .iter()
            .filter(|txn| txn.transaction_date == <NaiveDate as Into<NaiveDateTime>>::into(as_at_date))
            .fold(0f64, |acc, txn| match txn.transaction_type {
                TransactionType::Buy => acc + txn.amount,
                TransactionType::Sell => acc - txn.amount,
                _ => acc,
            })
    }

    fn calculate_realised_gain_loss(&mut self) -> f64 {
        let mut realized_gain_loss = 0.0;
        let mut remaining_buy_quantities: Vec<(String, f64, f64)> = self
            .transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Buy)
            .map(|txn| (txn.id.clone(), txn.quantity, txn.price))
            .collect();

        let sell_transactions: Vec<_> = self
            .transactions
            .iter()
            .filter(|txn| txn.transaction_type == TransactionType::Sell)
            .collect();

        for sell_txn in sell_transactions {
            let mut current_sell_quantity = sell_txn.quantity;

            for (_, buy_quantity, buy_price) in remaining_buy_quantities
                .iter_mut()
                .filter(|(_, quantity, _)| *quantity > 0.0)
            {
                if current_sell_quantity <= 0.0 {
                    break;
                }

                let sold_quantity = current_sell_quantity.min(*buy_quantity);
                realized_gain_loss += (sell_txn.price - *buy_price) * sold_quantity;

                *buy_quantity -= sold_quantity;
                current_sell_quantity -= sold_quantity;
            }
        }

        realized_gain_loss
    }

    fn calculate_current_holdings(&mut self) {
        // Sort transactions by CGT date and then by transaction date
        self.transactions.sort_by(|a, b| {
            a.cgt_date
                .cmp(&b.cgt_date)
                .then(a.transaction_date.cmp(&b.transaction_date))
        });

        let mut current_bal = 0.0;
        for txn in self.transactions.iter_mut() {
            if txn.transaction_type == TransactionType::Buy {
                current_bal += txn.quantity;
            } else if txn.transaction_type == TransactionType::Sell {
                current_bal -= txn.quantity;
            }
            txn.current_holding = current_bal;
        }

        // Create a map of dates to indices
        let date_indices: HashMap<_, _> = self
            .transactions
            .iter()
            .enumerate()
            .map(|(i, txn)| (txn.transaction_date, i))
            .collect();

        // Process groups with more than one transaction
        let mut dates: Vec<_> = date_indices.keys().cloned().collect();
        dates.sort();

        for &date in &dates {
            let group_indices: Vec<_> = self
                .transactions
                .iter()
                .enumerate()
                .filter(|(_, txn)| txn.transaction_date == date)
                .map(|(i, _)| i)
                .collect();

            if group_indices.len() > 1 {
                let holding_prior_to_this_date = self
                    .transactions
                    .iter()
                    .filter(|txn| txn.transaction_date < date)
                    .map(|txn| txn.current_holding)
                    .last()
                    .unwrap_or(0.0);

                let holding_on_the_day: f64 =
                    group_indices
                        .iter()
                        .map(|&i| &self.transactions[i])
                        .fold(0.0, |acc, txn| {
                            if txn.transaction_type == TransactionType::Buy {
                                acc + txn.quantity
                            } else if txn.transaction_type == TransactionType::Sell {
                                acc - txn.quantity
                            } else {
                                acc
                            }
                        });

                let final_holding = holding_prior_to_this_date + holding_on_the_day;
                for &i in &group_indices {
                    self.transactions[i].current_holding = final_holding;
                }
            }
        }
    }

    fn calculate_open_lot_quantities(&mut self) {
        // Sort transactions by CGT date and then by transaction date
        self.transactions.sort_by(|a, b| {
            a.cgt_date
                .cmp(&b.cgt_date)
                .then(a.transaction_date.cmp(&b.transaction_date))
        });

        // Set unrealised_holding for Buy transactions
        for txn in self.transactions.iter_mut() {
            if txn.transaction_type == TransactionType::Buy {
                txn.unrealised_holding = txn.quantity;
            }
        }

        // Check if there are any Sell transactions
        let has_sell_txns = self
            .transactions
            .iter()
            .any(|txn| txn.transaction_type == TransactionType::Sell);

        if !has_sell_txns {
            // If no Sell transactions, set unrealised_holding to quantity for all Buy transactions
            for txn in self.transactions.iter_mut() {
                if txn.transaction_type == TransactionType::Buy && txn.unrealised_holding == 0.0 {
                    txn.unrealised_holding = txn.quantity;
                }
            }
        } else {
            // Process Sell transactions
            let sell_txns: Vec<_> = self
                .transactions
                .iter()
                .filter(|txn| txn.transaction_type == TransactionType::Sell)
                .collect();

            let mut transaction_clone = self.transactions.clone();
            for sell_txn in sell_txns {
                let mut current_equity_sell_quantity = sell_txn.quantity;

                for buy_txn in transaction_clone.iter_mut().filter(|txn| {
                    txn.transaction_type == TransactionType::Buy
                        && txn.transaction_date <= sell_txn.transaction_date
                        && txn.unrealised_holding > 0.0
                }) {
                    if buy_txn.unrealised_holding <= 0.0 {
                        continue;
                    }

                    if current_equity_sell_quantity > buy_txn.unrealised_holding {
                        current_equity_sell_quantity -= buy_txn.unrealised_holding;
                        buy_txn.unrealised_holding = 0.0;
                    } else {
                        buy_txn.unrealised_holding -= current_equity_sell_quantity;
                        current_equity_sell_quantity = 0.0;
                        break;
                    }
                }
            }
        }
    }

    fn find_min_max_dates_and_max_holding(
        &self,
        objects: &[TransactionsForPerformanceEngine],
    ) -> (chrono::NaiveDateTime, chrono::NaiveDateTime, f64) {
        let (min_date, max_date, max_holding) = objects.iter().fold(
            (
                objects[0].transaction_date,
                objects[0].transaction_date,
                objects[0].current_holding,
            ),
            |(min, max, max_holding), obj| {
                let new_min = min.min(obj.transaction_date);
                let new_max = max.max(obj.transaction_date);
                let new_max_holding = if obj.transaction_date >= max {
                    obj.current_holding
                } else {
                    max_holding
                };
                (new_min, new_max, new_max_holding)
            },
        );

        (min_date, max_date, max_holding)
    }
}
