use std::cmp::Ordering;

use alpha_core_db::schema::{
    capital_register::{PortfolioCapitalRegister, PortfolioCapitalRegisterForPerformanceEngine},
    cash_ledger::PortfolioCashLedger,
    client_order_entry::{TransactionSubType, TransactionType},
    investment::Investments,
    investment_transaction::{InvestmentTransaction, TransactionsForPerformanceEngine},
    portfolio::PortfolioCashPosition,
};
use chrono::NaiveDateTime;
use lapin::protocol::tx;
use quick_xml::se;
use serde::{Deserialize, Serialize};

pub struct PortfolioValuation<'a> {
    pub running_balance: f64,
    pub capital_register: &'a Vec<PortfolioCapitalRegisterForPerformanceEngine>,
    pub transactions: Vec<TransactionsForPerformanceEngine>,
    pub portfolio_cash_position: PortfolioCashPosition,
}

#[derive(Default, Debug, Serialize, Deserialize)]
pub struct PortfolioPerformance {
    pub as_at_date: NaiveDateTime,
    pub total_capital: f64,
    pub total_withdrawals: f64,
    pub invested_capital: f64,
    pub market_value: f64,
    pub cash_balance: f64,
    pub total_cash_flow: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub irr_since_inception: f64,
    pub twrr_since_inception: f64,
    pub benchmark_name: String,
    pub benchmark_irr_since_inception: f64,
    pub benchmark_twrr_since_inception: f64,
}

impl<'a> PortfolioValuation<'a> {
    pub fn new(
        running_balance: f64,
        capital_register: &'a Vec<PortfolioCapitalRegisterForPerformanceEngine>,
        transactions: Vec<TransactionsForPerformanceEngine>,
        portfolio_cash_position: PortfolioCashPosition,
    ) -> Self {
        Self {
            running_balance,
            capital_register,
            transactions,
            portfolio_cash_position,
        }
    }

    pub fn calculate_performance(&self) -> PortfolioPerformance {
        //Get the Current Running Balance

        let (total_capital, total_withdrawals, invested_capital, market_value) = self.calculate_capital_values();

        let notional_cash = self.running_balance - self.portfolio_cash_position.held_buy_amount
            + self.portfolio_cash_position.held_sell_amount;

        let ptf_performance = PortfolioPerformance {
            total_capital,
            invested_capital,
            total_withdrawals,
            market_value,
            cash_balance: notional_cash,
            unrealised_gain_loss: market_value + notional_cash - invested_capital,
            realised_gain_loss: self.transactions.iter().map(|txn| txn.amount).sum(), //FIXME:Check this
            ..Default::default()
        };

        ptf_performance
    }

    fn calculate_capital_values(&self) -> (f64, f64, f64, f64) {
        let total_capital: f64 = self
            .capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Inflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalIn
                        || txn.transaction_sub_type == TransactionSubType::SecurityIn)
            })
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: f64 = self
            .capital_register
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Outflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalOut
                        || txn.transaction_sub_type == TransactionSubType::SecurityOut)
            })
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: f64 = total_capital - total_withdrawal;

        let market_value: f64 = self.transactions.iter().map(|txn| txn.amount).sum();

        (total_capital, total_withdrawal, invested_capital, market_value)
    }
}
