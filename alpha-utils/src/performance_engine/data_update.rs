use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
pub struct InvestmentToUpdate {
    pub current_price: Decimal,
    pub holdings: Decimal,
    pub total_capital: Decimal,
    pub invested_capital: Decimal,
    pub market_value: Decimal,
    pub realised_gain_loss: Decimal,
    pub unrealised_gain_loss: Decimal,
    pub dividends_paid: Decimal,
    pub irr_inception: Decimal,
    pub irr_current: Decimal,
    pub market_cap: String,
    pub average_price: Decimal,
    pub isin: String,
}

#[derive(Serialize, Deserialize)]
pub struct PortfolioToUpdate {
    pub portfolio_id: String,
    pub balance: Decimal,
    pub absolute_return_value: Decimal,
    pub absolute_return_percent: Decimal,
    pub total_capital: Decimal,
    pub invested_capital: Decimal,
    pub withdrawals: Decimal,
    pub market_value: Decimal,
    pub realised_gain_loss: Decimal,
    pub unrealised_gain_loss: Decimal,
    pub xirr: Decimal,
    pub twrr_since_inception: Decimal,
    pub twrr_1year: Decimal,
    pub xirr_1year: Decimal,
}

#[derive(Deserialize, Serialize)]
pub struct QueueMessage {
    pub update_type: Updates,
}

#[derive(Serialize, Deserialize)]
#[serde(tag = "update_type")]
pub enum Updates {
    Investment(PerformanceEngineInvestmentUpdate),
    Portfolio(PortfolioToUpdate),
}

#[derive(Serialize, Deserialize)]
pub struct PerformanceEngineInvestmentUpdate {
    pub investments: Vec<InvestmentToUpdate>,
    pub portfolio_id: String,
}
