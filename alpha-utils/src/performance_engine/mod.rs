use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::portfolio::Portfolio,
};

pub mod analytics;
pub mod build_master_input;
pub mod investment_valuation;
pub mod portfolio_valuation;

pub mod state_machine;
pub mod data_update;

pub async fn calculate_performance(conn: &mut Object<Manager>) {
    //GET all the portfolios to RUN
    let portfolios = Portfolio::get_all_for_performance_engine(conn).await.unwrap();

    for portfolio in portfolios {}
}
