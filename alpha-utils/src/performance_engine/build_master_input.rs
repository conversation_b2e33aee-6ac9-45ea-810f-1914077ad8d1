use actlogica_logs::{builder::LogBuilder, log_info};
use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::{
        capital_register::PortfolioCapitalRegister,
        cash_ledger::PortfolioCashLedger,
        client_order_entry::SecurityType,
        investment::{self, Investments},
        investment_transaction::{InvestmentTransaction, TransactionsForPerformanceEngine},
        portfolio::{Portfolio, PortfolioCashPosition},
    },
};
use chrono::Utc;
use clickhouse::{insert::Insert, Row};
use futures::{future::join_all, lock::Mutex};
use std::{fmt::format, sync::Arc};

use crate::performance_engine::analytics::PortfolioAnalytics;

use super::{investment_valuation::SecurityValuationService, portfolio_valuation::PortfolioValuation};

pub async fn run_performance_for_portfolio(
    mut conn: &mut Object<Manager>,
    clickhouse_client: clickhouse::Client,
    mut portfolio: Portfolio,
) -> Result<String, ()> {
    let ptf_id = portfolio.id.clone();
    let as_at_date = Utc::now().naive_local();

    let portfolio_capital_register =
        PortfolioCapitalRegister::get_by_portfolio_id_for_performance_engine(&mut conn, portfolio.id.clone())
            .await
            .map_err(|e| return ())?;

    let running_balance =
        PortfolioCashLedger::get_running_balance_of_a_portfolio(&mut conn, portfolio.id.clone(), true)
            .await
            .map_err(|e| return ())?;

    if running_balance.is_none() {
        return Err(());
    }

    let running_balance = running_balance.unwrap();

    let portfolio_holdings = Investments::get_by_portfolio_id_for_performance_engine(&mut conn, portfolio.id.clone())
        .await
        .map_err(|e| return ())?;

    let transactions_for_portfolio_valuation = Arc::new(Mutex::new(Vec::new()));
    let portfolio_analytics: Arc<Mutex<Vec<PortfolioAnalytics>>> = Arc::new(Mutex::new(Vec::new()));

    let portfolio_cash_position = PortfolioCashPosition { ..Default::default() };
    let mut handlers = Vec::new();

    let full_transactions =
        InvestmentTransaction::get_by_portfolio_id_for_performance_engine(&mut conn, ptf_id.clone())
            .await
            .map_err(|e| return ())?;

    log_info(LogBuilder::system(&format!(
        "STARTED TRANSACTION For Portfolio Performance Engine for the id = {}",
        ptf_id
    )));

    conn.simple_query("BEGIN TRANSACTION").await.unwrap();

    let investments_to_update = Arc::new(Mutex::new(Vec::new()));

    //Loop through all the investments of Portfolio
    for mut holding in portfolio_holdings {
        let mut transactions: Vec<_> = full_transactions
            .iter()
            .filter(|txn| txn.investment_id == holding.id)
            .cloned()
            .collect();

        let transactions_for_portfolio_valuation = transactions_for_portfolio_valuation.clone();
        let investments_to_update = investments_to_update.clone();
        let portfolio_analytics = portfolio_analytics.clone();

        let handler = tokio::spawn(async move {
            if holding.security_type == SecurityType::Stocks || holding.security_type == SecurityType::ETF {
                //Get the Price for this security
                let current_price = 0f64;
                holding.current_price = current_price;
                holding.current_price_date = Utc::now().naive_utc();

                //Get Security Details
                if transactions.len() == 0 {
                    return;
                }

                let mut valuation = SecurityValuationService::new(&mut transactions, &mut holding, as_at_date.into());
                let performance = valuation.calculate_performance();

                //Insert into Analytics
                let analytics: PortfolioAnalytics = valuation.build_analytics(None);
                let mut portfolio_analytics = portfolio_analytics.lock().await;
                portfolio_analytics.push(analytics);

                //Insert Transaction to pas sot portfolio valuation
                let mut transactions_for_portfolio_valuation_lock = transactions_for_portfolio_valuation.lock().await;

                transactions_for_portfolio_valuation_lock.append(&mut transactions);

                let investment = Investments {
                    current_price,
                    current_price_date: holding.current_price_date,
                    total_capital: performance.total_capital,
                    invested_capital: performance.invested_capital,
                    unrealised_gain_loss: performance.unrealised_gain_loss,
                    dividends: performance.dividends_paid,
                    market_value: performance.market_value,
                    // average_price: performance.average_price,
                    irr_since_inception: performance.irr_since_inception,
                    irr_current: performance.irr_current,
                    first_transaction_date: performance.first_transaction_date,
                    last_transaction_date: performance.last_transaction_date,
                    current_holding: performance.current_holding,
                    last_updated_date: Utc::now().naive_utc(),
                    ..holding
                };

                //Insert Invesment for bulk updation
                let mut investments_to_update = investments_to_update.lock().await;
                investments_to_update.push(investment);
            } else if holding.security_type == SecurityType::MutualFund {
                //Get the Price for this security
                let current_price = 0f64;
                holding.current_price = current_price;
                holding.current_price_date = Utc::now().naive_utc();
                //Get Security Details

                if transactions.len() == 0 {
                    return;
                }

                let mut valuation = SecurityValuationService::new(&mut transactions, &mut holding, as_at_date.into());
                let performance = valuation.calculate_performance();
                let analytics = valuation.build_analytics(None);

                //Insert Transaction to pas sot portfolio valuation
                let mut transactions_for_portfolio_valuation_lock = transactions_for_portfolio_valuation.lock().await;

                transactions_for_portfolio_valuation_lock.append(&mut transactions);

                //Insert into Analytics

                let mut portfolio_analytics = portfolio_analytics.lock().await;
                portfolio_analytics.push(analytics);

                let investment = Investments {
                    current_price,
                    current_price_date: holding.current_price_date,
                    total_capital: performance.total_capital,
                    invested_capital: performance.invested_capital,
                    unrealised_gain_loss: performance.unrealised_gain_loss,
                    dividends: performance.dividends_paid,
                    market_value: performance.market_value,
                    average_price: performance.average_price,
                    irr_since_inception: performance.irr_since_inception,
                    irr_current: performance.irr_current,
                    first_transaction_date: performance.first_transaction_date,
                    last_transaction_date: performance.last_transaction_date,
                    current_holding: performance.current_holding,
                    last_updated_date: Utc::now().naive_utc(),
                    ..holding
                };

                //Insert Invesment for bulk updation
                let mut investments_to_update = investments_to_update.lock().await;
                investments_to_update.push(investment);
            } else if holding.security_type == SecurityType::FixedIncome {
                //Get the Price for this security
                let current_price = 0f64;
                holding.current_price = current_price;
                holding.current_price_date = Utc::now().naive_utc();
                //Get Security Details

                //Get Transactions for this security

                if transactions.len() == 0 {
                    return;
                }

                let mut valuation = SecurityValuationService::new(&mut transactions, &mut holding, as_at_date.into());
                let performance = valuation.calculate_performance();

                //Insert into Analytics
                let analytics = valuation.build_analytics(None);
                let mut portfolio_analytics = portfolio_analytics.lock().await;
                portfolio_analytics.push(analytics);

                //Insert Transaction to pas sot portfolio valuation
                let mut transactions_for_portfolio_valuation_lock = transactions_for_portfolio_valuation.lock().await;

                transactions_for_portfolio_valuation_lock.append(&mut transactions);

                //Insert Invesment for bulk updation
                let investment = Investments {
                    current_price,
                    current_price_date: holding.current_price_date,
                    total_capital: performance.total_capital,
                    invested_capital: performance.invested_capital,
                    unrealised_gain_loss: performance.unrealised_gain_loss,
                    dividends: performance.dividends_paid,
                    market_value: performance.market_value,
                    average_price: performance.average_price,
                    irr_since_inception: performance.irr_since_inception,
                    irr_current: performance.irr_current,
                    first_transaction_date: performance.first_transaction_date,
                    last_transaction_date: performance.last_transaction_date,
                    current_holding: performance.current_holding,
                    last_updated_date: Utc::now().naive_utc(),
                    ..holding
                };

                let mut investments_to_update = investments_to_update.lock().await;
                investments_to_update.push(investment);
            }
        });

        handlers.push(handler);
    }

    join_all(handlers).await;

    let guard = investments_to_update.lock().await;

    let investments_to_update = (*guard).clone();

    for investment_to_update in investments_to_update {
        log_info(LogBuilder::system(&format!("Updating Investments isin = {}", investment_to_update.isin)));
        investment_to_update
            .update_on_performance_engine(&mut conn)
            .await
            .unwrap();
    }

    // Lock the mutex
    let guard = transactions_for_portfolio_valuation.lock().await;

    let items = (*guard).clone();

    // let portfolio_valuation = PortfolioValuation::new(
    //     running_balance,
    //     &portfolio_capital_register,
    //     items,
    //     portfolio_cash_position,
    // );

    // let portfolio_performance = portfolio_valuation.calculate_performance();

    // portfolio.total_capital = portfolio_performance.total_capital;
    // portfolio.invested_capital = portfolio_performance.invested_capital;
    // portfolio.market_value = portfolio_performance.market_value;
    // portfolio.current_cash_balance = portfolio_performance.cash_balance;
    // portfolio.realised_gain_loss = portfolio_performance.realised_gain_loss;
    // portfolio.unrealised_gain_loss = portfolio_performance.unrealised_gain_loss;
    // portfolio.annual_performance_twrr = 0f64;
    // portfolio.annual_return_irr = portfolio_performance.irr_since_inception;
    // portfolio.annual_return_irr_unrealised = portfolio_performance.irr_since_inception;
    // portfolio.last_updated_date = Utc::now().naive_utc();

    // //Update Portfolio
    // info!("Updating Portfolio for the Id = {}", ptf_id);
    // portfolio
    //     .update_on_performance_engine(&mut conn)
    //     .await
    //     .unwrap();

    // //Insert into analytics
    // let guard = portfolio_analytics.lock().await;

    // let portfolio_analytics = (*guard).clone();

    // let mut insert: Insert<PortfolioAnalytics> =
    //     clickhouse_client.insert("portfolio_analytics").unwrap();

    // for analytics in portfolio_analytics {
    //     insert.write(&analytics).await.unwrap();
    // }

    Ok(format!("sds"))
}

#[cfg(test)]
mod tests {

    use alpha_core_db::schema::portfolio::Portfolio;
    use futures::future::join_all;
    use tokio::time::Instant;

    use crate::performance_engine::build_master_input;

    #[tokio::test]
    async fn build_master() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;

        let mut pool_conn = pool.get().await.unwrap();
        let portfolios = Portfolio::get_all_for_performance_engine(&mut pool_conn).await.unwrap();
        let mut i = 0;
        let total = portfolios.len();
        let start_time = Instant::now();

        // let mut handlers = Vec::new();
        // for portfolio in portfolios {
        //     let pool = pool.clone();
        //     i += 1;
        //     println!("Computing for Portfolio {} {i}/{}", portfolio.id, total);
        //     let handler = tokio::spawn(async move {
        //         println!("Wating For Pool Connection");
        //         let mut pool_conn = pool.get().await.unwrap();
        //         println!("Got COnnection {:?}", portfolio.id.clone());
        //         build_master_input::build_master_input(&mut pool_conn, portfolio).await;
        //         println!("{:?}", pool.status());
        //     });

        //     handlers.push(handler);
        // }

        // join_all(handlers).await;
        // let end_time = Instant::now();

        // println!("Time elapsed: {:?}", end_time - start_time);
    }
}
