use actlogica_logs::{builder::LogBuilder, log_error, log_info};
use lapin::{
    options::{BasicConsumeOptions, BasicPublishOptions, QueueDeclareOptions},
    types::FieldTable,
    BasicProperties, Channel, Connection, ConnectionProperties, Consumer,
};

pub struct RabbitMq {
    connection: Connection,
}

impl RabbitMq {
    pub async fn connect_rabbit_mq() -> Self {
        let addr = std::env::var("QUEUE_URL").expect("Rabbit MQ URL not Found");

        // Create TLS connector
        log_info(LogBuilder::system("Connecting to Rabbit MQ").add_metadata("url", &addr));
        let connection = match Connection::connect(
            &addr,
            ConnectionProperties::default()
                .with_executor(tokio_executor_trait::Tokio::current())
                .with_reactor(tokio_reactor_trait::Tokio),
        )
        .await
        {
            Ok(conn) => {
                log_info(LogBuilder::system("Connection Successful"));
                conn
            }
            Err(e) => {
                log_error(LogBuilder::system("Failed to connect to Rabbit MQ").add_metadata("error", &e.to_string()));
                panic!("Failed to connect to Rabbit MQ: {}", e);
            }
        };
        return Self { connection };
    }

    async fn create_queue(&self, name: &str) {
        let channel_a = self.connection.create_channel().await.unwrap();
        let queue = match channel_a
            .queue_declare(name, QueueDeclareOptions::default(), FieldTable::default())
            .await
        {
            Ok(q) => {
                log_info(LogBuilder::system(&format!("Declared queue: {:?}", q)));
                q
            }
            Err(e) => {
                log_error(LogBuilder::system("Failed to declare queue").add_metadata("error", &e.to_string()));
                panic!("Failed to declare queue: {}", e);
            }
        };
    }

    pub async fn create_channel(&self, queue_name: &str) -> anyhow::Result<Channel> {
        let _ = &self.create_queue(queue_name).await;
        let channel = self.connection.create_channel().await?;
        Ok(channel)
    }

    pub async fn create_consumer(&self, queue_name: &str) -> Consumer {
        let _ = &self.create_queue(queue_name).await;

        let channel = self.connection.create_channel().await.unwrap();

        let consumer = channel
            .basic_consume(
                queue_name,
                "my_consumer",
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await
            .unwrap();

        log_info(LogBuilder::system("Queue declared. Starting to consume messages..."));

        consumer
    }

    pub async fn publish_message(&self, queue_name: &str, message: String) -> Result<(), lapin::Error> {
        let channel = self.create_channel(queue_name).await.unwrap();
        channel
            .basic_publish(
                "",
                queue_name,
                BasicPublishOptions::default(),
                message.as_bytes(),
                BasicProperties::default(),
            )
            .await?;
        Ok(())
    }
}
