use std::collections::HashMap;
use std::hash::Hash;

pub mod constants;

pub mod holding_recon;
pub mod parser;
pub mod performance_engine;
pub mod portfolio;
pub mod price;
pub mod rabbit_mq;
pub mod reports;
pub mod settlement;
pub mod transactions;
pub mod types;

pub fn group_by_multiple_fields<T, K, F>(data: Vec<T>, key_func: F) -> HashMap<K, Vec<T>>
where
    T: Sized,
    K: Eq + Hash,
    F: Fn(&T) -> K,
{
    let mut groups: HashMap<K, Vec<T>> = HashMap::new();

    for item in data {
        let key = key_func(&item);
        groups.entry(key).or_insert_with(Vec::new).push(item);
    }

    groups
}
