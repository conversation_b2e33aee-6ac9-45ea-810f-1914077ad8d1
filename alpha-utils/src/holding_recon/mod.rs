use actlogica_logs::{builder::LogBuilder, log_info, log_warn};
use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::{holding_recon::HoldingReconRequest, investment::Investments},
    storage::blob::connection::AzureBlob,
};
use futures::{future::join_all, StreamExt};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::Mutex;

use crate::parser::{
    csv_parser::create_csv_for_holding_recon_result,
    excel::{create_excel_for_holding_recon, ReconExcelEntry},
    HoldingReconCustodyFile, HoldingReconFundAccountantFile,
};

/// Compute the Holding Recon for the Entry In HoldingReconRequest Table
/// Returns Error if Custody File and Fund Accountant(If needed) file is not found in Storage Blob
pub async fn compute_holding_recon(
    conn: &mut Object<Manager>,
    recon_request: HoldingReconRequest,
) -> Result<String, String> {
    log_info(LogBuilder::system("Computing Holding Recon"));

    let mut azure_blob = AzureBlob::new(String::from("alpha-holding-recon"));

    let custody_blob_client = azure_blob.get_blob(&recon_request.custody_file_path);

    // Custody File Reader
    let mut stream = custody_blob_client.get().into_stream();
    let mut custody_blob: Vec<u8> = vec![];

    while let Some(value) = stream.next().await {
        let mut body = value.map_err(|e| return String::from("Failed To Read From Blob"))?.data;
        // For each response, we stream the body instead of collecting it all
        // into one large allocation.
        while let Some(value) = body.next().await {
            let value = value.map_err(|e| String::from("Failed To Read From Blob"))?;
            custody_blob.extend(&value);
        }
    }

    // Fund Accountant File Reader
    let mut fa_blob: Vec<u8> = vec![];

    if let Some(path) = &recon_request.fund_accountant_file_path {
        let fa_blob_client = azure_blob.get_blob(path);
        let mut stream = fa_blob_client.get().into_stream();
        while let Some(value) = stream.next().await {
            let mut body = value.map_err(|_e| String::from("Failed To Read From Blob"))?.data;
            // For each response, we stream the body instead of collecting it all
            // into one large allocation.
            while let Some(value) = body.next().await {
                let value = value.map_err(|_e| String::from("Failed To Read From Blob"))?;
                fa_blob.extend(&value);
            }
        }
    }

    // Portfolios and Holdings in Alpha
    let get_holdings_in_alpha = Investments::get_portfolio_investments_for_holding_recon(conn).await?;

    let (alpha_portfolios_metadata, alpha_portfolios_holdings) = get_holdings_in_alpha;

    //Portfolios and Holdings in Custody
    let custodian_portfolio_holdings = HoldingReconCustodyFile::parse_holding_recon_custody_csv_file(custody_blob)?;

    //For Use in Multiple threads convert to Arc
    let custodian_portfolio_holdings = Arc::new(custodian_portfolio_holdings);

    let mut required_fa_comparison = false;

    // All the processed portfolios (key will be ClientStrategyCode,FaAccountNo,CustodyPortfolioCode)
    // We don't have to process the portfolio again which is in this hashmap
    let processed_portfolios = Arc::new(Mutex::new(HashMap::new()));

    //Portfolios and Holdings in Fund Accountant
    let fund_accountant_portfolio_holdings = if let Some(path) = &recon_request.fund_accountant_file_path {
        required_fa_comparison = true;
        let holdings = HoldingReconFundAccountantFile::parse_holding_recon_fund_accountant_csv_file(fa_blob);
        holdings
    } else {
        Ok(HashMap::new())
    }?;

    let fund_accountant_portfolio_holdings = Arc::new(fund_accountant_portfolio_holdings);

    let mut handlers = Vec::new();

    log_info(LogBuilder::system(
        "Started Recon for Alpha -> Custodian and Alpha -> Fund Accountant",
    ));

    //Excel Entries
    let recon_result = Arc::new(Mutex::new(Vec::new()));

    //Loop through all the portfolios in the Alpha
    for portfolio_in_alpha in alpha_portfolios_holdings.clone() {
        let custodian_portfolio_holdings: Arc<HashMap<String, Vec<HoldingReconCustodyFile>>> =
            custodian_portfolio_holdings.clone();
        let fund_accountant_portfolio_holdings = fund_accountant_portfolio_holdings.clone();
        let recon_result = recon_result.clone();
        let alpha_portfolios_metadata = alpha_portfolios_metadata.clone();
        let custody_id = recon_request.custodian_id.clone();
        let processed_portfolios = processed_portfolios.clone();

        let handler = tokio::spawn(async move {
            let alpha_portfolio_code = portfolio_in_alpha.0;
            let alpha_portfolio_holdings = portfolio_in_alpha.1;

            let portfolio_metadata = alpha_portfolios_metadata.get(&alpha_portfolio_code).unwrap(); // SAFE TO UNWRAP THIS

            let holdings_for_portfolio_in_custody =
                custodian_portfolio_holdings.get(&portfolio_metadata.custodian_portfolio_code);

            let holdings_for_portfolio_in_fund_accountant =
                if let Some(fa_code) = portfolio_metadata.fa_account_number.clone() {
                    fund_accountant_portfolio_holdings.get(&fa_code)
                } else {
                    None
                };

            let mut fund_accountant_portfolio_holdings_map = HashMap::new();

            if let Some(fa_holdings) = holdings_for_portfolio_in_fund_accountant {
                fund_accountant_portfolio_holdings_map =
                    fa_holdings.into_iter().fold(HashMap::new(), |mut acc, recon| {
                        acc.entry(recon.isin.clone()).or_insert(recon);
                        acc

                        // acc.entry(holding.isin.clone())
                        // .or_insert_with(Vec::new)
                        // .push(holding);  //TO Filter By Array
                    });
            }

            let mut track_alpha_holdings = HashMap::new();

            //Add the current portfolio to processing portfolios list
            let mut entry = processed_portfolios.lock().await;
            entry.insert(portfolio_metadata.custodian_portfolio_code.clone(), true);
            if let Some(code) = &portfolio_metadata.fa_account_number {
                entry.insert(code.clone(), true);
            }
            drop(entry);

            //Check whether the portfolio is in Custody and belongs to same custody user selected
            if let (true, Some(holdings_for_portfolio_in_custody)) = (
                portfolio_metadata.custodian_id == custody_id,
                holdings_for_portfolio_in_custody,
            ) {
                //Make a hashmap for Custody holdings key = isin value = data
                let custody_portfolio_holdings_map =
                    holdings_for_portfolio_in_custody
                        .into_iter()
                        .fold(HashMap::new(), |mut acc, recon| {
                            acc.entry(recon.isin.clone()).or_insert(recon);
                            acc
                        });

                //Loop through the holdings of Alpha portfolio and check if it matches holdings in Custody portfolio holdings and Fund Account portfolio
                for isin_portfolio_holding in alpha_portfolio_holdings {
                    track_alpha_holdings.insert(isin_portfolio_holding.isin.to_string(), true);

                    let isin_holdings_in_custody = custody_portfolio_holdings_map.get(&isin_portfolio_holding.isin);

                    let mut excel_entry = ReconExcelEntry {
                        client_name: isin_portfolio_holding.client_name,
                        client_strategy_code: isin_portfolio_holding.client_strategy_code,
                        client_custodian_code: isin_portfolio_holding.custodian_portfolio_code,
                        alpha_holdings: isin_portfolio_holding.current_holding,
                        isin: isin_portfolio_holding.isin.to_string(),
                        client_fa_code: isin_portfolio_holding.fa_account_number,
                        custodian: isin_portfolio_holding.custodian_name,
                        symbol: isin_portfolio_holding.symbol,
                        scrip_name: isin_portfolio_holding.security_name,
                        ..Default::default()
                    };

                    excel_entry.add_alpha_holdings(isin_portfolio_holding.current_holding);

                    //Check whether the ISIN is in Custody
                    if let Some(isin_holding) = isin_holdings_in_custody {
                        excel_entry.add_custody_holdings(isin_holding.holdings);

                        // Check if quantity matches
                        if isin_holding.holdings != isin_portfolio_holding.current_holding {
                            log_warn(LogBuilder::system(&format!(
                                "{:?} Quantity doesn't match",
                                isin_holding.isin
                            )));
                            excel_entry.append_remarks("Alpha and Custody Quantity Doesn't match;");
                            excel_entry
                                .add_alpha_vs_custody(isin_portfolio_holding.current_holding - isin_holding.holdings)
                        } else {
                            log_warn(LogBuilder::system("Custody and Alpha Quantity Matches"));
                            excel_entry.append_remarks("Alpha and Custody Quantity matches;");
                            excel_entry.add_alpha_vs_custody(0f64);
                        }
                    } else {
                        //The Holding for this ISIN is not in Custody
                        log_warn(LogBuilder::system(&format!(
                            "{:?} Is not in Alpha Portfolios",
                            isin_portfolio_holding.isin
                        )));
                        excel_entry.append_remarks("The Security is Not in Custody");
                        excel_entry.add_alpha_vs_custody(isin_portfolio_holding.current_holding);
                    }

                    if fund_accountant_portfolio_holdings_map.len() > 0 {
                        //Check whether the ISIN is in Fund Accountant
                        let isin_holdings_in_fund_accountant =
                            fund_accountant_portfolio_holdings_map.get(&isin_portfolio_holding.isin);

                        if let Some(isin_holding) = isin_holdings_in_fund_accountant {
                            excel_entry.add_fund_accountant_alpha_holdings(isin_holding.holdings);
                            // ISIN is in portfolio
                            // Check if quantity matches
                            if isin_holding.holdings != isin_portfolio_holding.current_holding {
                                log_warn(LogBuilder::system(&format!(
                                    "{:?} Quantity doesn't match",
                                    isin_holding.isin
                                )));
                                excel_entry.append_remarks("Alpha and Fund Accountant Quantity Doesn't match;");
                                excel_entry.add_alpha_vs_fund_accountant(
                                    isin_portfolio_holding.current_holding - isin_holding.holdings,
                                );
                            } else {
                                log_warn(LogBuilder::system("Fund Accountant and Alpha Quantity Matches"));
                                excel_entry.append_remarks("Alpha and Fund Accountant Quantity matches;");
                                excel_entry.add_alpha_vs_fund_accountant(0f64);
                            }
                        } else {
                            //The Holding for this ISIN is not in Alpha Portfolio
                            log_warn(LogBuilder::system(&format!(
                                "{:?} Is not in Alpha Portfolios",
                                isin_portfolio_holding.isin
                            )));
                            excel_entry.add_alpha_vs_fund_accountant(isin_portfolio_holding.current_holding);
                            excel_entry.append_remarks("The Security is Not in Fund Accountant;");
                        }
                    }

                    //The lock should be dropped at the moment the data is pushed
                    let mut get_recon_result_lock = recon_result.lock().await;
                    get_recon_result_lock.push(excel_entry);
                }

                // To Get all the Holdings for the portfolio that is not in alpha
                // Loop through holdings of a portfolio in Custody and check whether it exist in custodian and FA or not
                for isin_portfolio_holding in &custody_portfolio_holdings_map {
                    let exist_in_alpha_holding = track_alpha_holdings.contains_key(isin_portfolio_holding.0);

                    let exist_in_fund_accountant =
                        fund_accountant_portfolio_holdings_map.contains_key(isin_portfolio_holding.0);

                    let mut excel_entry = ReconExcelEntry {
                        client_strategy_code: portfolio_metadata.client_strategy_code.clone(),
                        client_custodian_code: portfolio_metadata.custodian_portfolio_code.clone(),
                        client_name: portfolio_metadata.client_name.clone(),
                        client_fa_code: portfolio_metadata.fa_account_number.clone(),
                        custody_holdings: isin_portfolio_holding.1.holdings,
                        custodian: portfolio_metadata.custodian_name.clone(),
                        scrip_name: Some(isin_portfolio_holding.1.security_name.clone()),
                        isin: isin_portfolio_holding.1.isin.to_owned(),
                        ..Default::default()
                    };

                    //if it exist in alpha then it is already computed above
                    if !exist_in_alpha_holding {
                        log_warn(LogBuilder::system(&format!(
                            "{:?} Is in Custody Not IN Alpha",
                            isin_portfolio_holding.1.isin
                        )));

                        excel_entry.add_alpha_holdings(0f64);
                        excel_entry.add_alpha_vs_custody(-isin_portfolio_holding.1.holdings);
                        excel_entry.append_remarks("Security Doesn't exist in Alpha;");

                        if !exist_in_fund_accountant && fund_accountant_portfolio_holdings_map.len() > 0 {
                            excel_entry.add_fund_accountant_alpha_holdings(0f64);
                            excel_entry.add_alpha_vs_fund_accountant(-isin_portfolio_holding.1.holdings);
                            excel_entry.append_remarks("Security Doesn't exist in Fund Accountant;")
                        }

                        //The lock should be dropped at the moment the data is pushed
                        let mut get_recon_result_lock = recon_result.lock().await;
                        get_recon_result_lock.push(excel_entry);
                    }
                }

                // Loop through holdings of a portfolio in FundAccount
                // Only take holdings that are not in alpha and custody
                if fund_accountant_portfolio_holdings_map.len() > 0 {
                    for isin_portfolio_holding in &fund_accountant_portfolio_holdings_map {
                        let exist_in_alpha_holding = track_alpha_holdings.contains_key(isin_portfolio_holding.0);

                        let exist_in_custody = custody_portfolio_holdings_map.contains_key(isin_portfolio_holding.0);

                        let mut excel_entry = ReconExcelEntry {
                            client_custodian_code: portfolio_metadata.custodian_portfolio_code.clone(),
                            client_name: portfolio_metadata.client_name.clone(),
                            client_fa_code: portfolio_metadata.fa_account_number.clone(),
                            isin: isin_portfolio_holding.1.isin.to_owned(),
                            fund_accountant_holdings: isin_portfolio_holding.1.holdings,
                            scrip_name: Some(isin_portfolio_holding.1.security_name.clone()),
                            ..Default::default()
                        };

                        if !exist_in_alpha_holding && !exist_in_custody {
                            excel_entry.add_alpha_holdings(0f64);
                            excel_entry.add_custody_holdings(0f64);
                            excel_entry.append_remarks("Security Doesn't exist in Alpha;");
                            excel_entry.append_remarks("Security Doesn't exist in Custody;");
                            excel_entry.add_alpha_vs_fund_accountant(-isin_portfolio_holding.1.holdings);
                            excel_entry.add_custody_vs_fund_accountant(-isin_portfolio_holding.1.holdings);
                            excel_entry.add_alpha_vs_custody(0f64);

                            //The lock should be dropped at the moment the data is pushed
                            let mut get_recon_result_lock = recon_result.lock().await;
                            get_recon_result_lock.push(excel_entry);
                        }
                    }
                }
            } else {
                if portfolio_metadata.custodian_id == custody_id {
                    log_warn(LogBuilder::system(
                        "Portfolio Is In Alpha But Not In Custody. Start Reconciliation with FA",
                    ));
                } else {
                    log_warn(LogBuilder::system(
                        "Portfolio Is In Alpha But Belongs to Different Custody. Start Reconciliation with FA",
                    ));
                }

                // Check If that Exist in Fund Accountant
                if holdings_for_portfolio_in_fund_accountant.is_some() {
                    //Loop through the holdings of Alpha portfolio and check if it matches holdings in Fund Account portfolio
                    for isin_portfolio_holding in alpha_portfolio_holdings {
                        track_alpha_holdings.insert(isin_portfolio_holding.isin.to_string(), true);

                        let isin_holdings_in_fund_accountant =
                            fund_accountant_portfolio_holdings_map.get(&isin_portfolio_holding.isin);

                        let mut excel_entry = ReconExcelEntry {
                            client_name: isin_portfolio_holding.client_name,
                            client_strategy_code: isin_portfolio_holding.custodian_portfolio_code,
                            client_custodian_code: isin_portfolio_holding.client_strategy_code,
                            alpha_holdings: isin_portfolio_holding.current_holding,
                            isin: isin_portfolio_holding.isin.to_string(),
                            client_fa_code: isin_portfolio_holding.fa_account_number,
                            custodian: isin_portfolio_holding.custodian_name,
                            symbol: isin_portfolio_holding.symbol,
                            scrip_name: isin_portfolio_holding.security_name,
                            ..Default::default()
                        };
                        //Check whether the ISIN is in FA
                        if let Some(isin_holding) = isin_holdings_in_fund_accountant {
                            excel_entry.add_fund_accountant_alpha_holdings(isin_holding.holdings);
                            // ISIN is in portfolio
                            // Check if quantity matches
                            if isin_holding.holdings != isin_portfolio_holding.current_holding {
                                log_warn(LogBuilder::system(&format!(
                                    "{:?} Quantity doesn't match",
                                    isin_holding.isin
                                )));
                                excel_entry.append_remarks("Alpha and FA Quantity Doesn't match;");
                                excel_entry.add_alpha_vs_fund_accountant(
                                    isin_portfolio_holding.current_holding - isin_holding.holdings,
                                )
                            } else {
                                log_warn(LogBuilder::system("Custody and Alpha Quantity Matches"));
                                excel_entry.append_remarks("Alpha and FA Quantity matches;");
                                excel_entry.add_alpha_vs_fund_accountant(0f64);
                            }
                        } else {
                            //The Holding for this ISIN is not in Custody
                            log_warn(LogBuilder::system(&format!(
                                "{:?} Is not in Alpha Portfolios",
                                isin_portfolio_holding.isin
                            )));
                            excel_entry.append_remarks("The Security is Not in FA");
                            excel_entry.add_alpha_vs_fund_accountant(isin_portfolio_holding.current_holding);
                        }
                    }

                    //Compute all other remaining holdings in fund accountant
                    for isin_portfolio_holding in &fund_accountant_portfolio_holdings_map {
                        let exist_in_alpha_holding = track_alpha_holdings.contains_key(isin_portfolio_holding.0);

                        let mut excel_entry = ReconExcelEntry {
                            client_custodian_code: portfolio_metadata.custodian_portfolio_code.clone(),
                            client_name: portfolio_metadata.client_name.clone(),
                            client_fa_code: portfolio_metadata.fa_account_number.clone(),

                            isin: isin_portfolio_holding.1.isin.to_owned(),
                            ..Default::default()
                        };

                        if !exist_in_alpha_holding {
                            excel_entry.add_alpha_holdings(0f64);
                            excel_entry.add_custody_holdings(0f64);
                            excel_entry.append_remarks("Security Doesn't exist in Alpha;");
                            excel_entry.add_alpha_vs_fund_accountant(-isin_portfolio_holding.1.holdings);
                            excel_entry.add_custody_vs_fund_accountant(-isin_portfolio_holding.1.holdings);
                        }
                    }
                } else {
                    log_warn(LogBuilder::system(&format!(
                        "{} Portfolio Is not In Fund Accountant",
                        alpha_portfolio_code
                    )));
                    // Check whether the Portfolio Belongs to the custody given
                    // If yes add entry to excel
                    if portfolio_metadata.custodian_id == custody_id {
                        let mut excel_entry = ReconExcelEntry {
                            client_name: portfolio_metadata.client_name.clone(),
                            client_custodian_code: portfolio_metadata.custodian_portfolio_code.clone(),
                            client_strategy_code: portfolio_metadata.client_strategy_code.clone(),
                            client_fa_code: portfolio_metadata.fa_account_number.clone(),
                            ..Default::default()
                        };

                        if required_fa_comparison {
                            excel_entry.append_remarks("Portfolio Is In alpha, but not in Custody and FA;");
                        } else {
                            excel_entry.append_remarks("Portfolio Is In alpha, but not in Custody;");
                        }

                        //The lock should be dropped at the moment the data is pushed
                        let mut get_recon_result_lock = recon_result.lock().await;
                        get_recon_result_lock.push(excel_entry);
                    }
                    // The Portfolios Is In Custody and Alpha But Belongs to Different Custody
                    // Treat that portfolio doesn't exist in alpha
                    else if holdings_for_portfolio_in_custody.is_some() {
                        let excel_entry = ReconExcelEntry {
                            client_name: portfolio_metadata.client_name.clone(),
                            client_custodian_code: portfolio_metadata.custodian_portfolio_code.clone(),
                            client_strategy_code: portfolio_metadata.client_strategy_code.clone(),
                            client_fa_code: portfolio_metadata.fa_account_number.clone(),
                            remarks: String::from("Portfolio Doesn't Exist in Alpha but Exist in Custody"),
                            ..Default::default()
                        };

                        //The lock should be dropped at the moment the data is pushed
                        let mut get_recon_result_lock = recon_result.lock().await;
                        get_recon_result_lock.push(excel_entry);
                    }
                }
            }
        });

        handlers.push(handler);
    }

    join_all(handlers).await;

    log_info(LogBuilder::system(
        "Recon End for Alpha -> Custodian and Alpha -> Fund Accountant",
    ));

    log_info(LogBuilder::system(
        "Started Recon for Custodian -> Alpha and Custodian -> Fund Accountant",
    ));

    // At this Stage we know that the portfolios in the custodians we are processing is not in alpha
    for custodin_portfolio_holdings in custodian_portfolio_holdings.iter() {
        let cust_holdings = custodin_portfolio_holdings.1;

        // Check whether this portfolio is in alpha
        let mut processed_portfolios = processed_portfolios.lock().await;
        let is_in_alpha = processed_portfolios.contains_key(custodin_portfolio_holdings.0);

        if !is_in_alpha {
            processed_portfolios.insert(custodin_portfolio_holdings.0.clone(), true);
            drop(processed_portfolios);

            //Since the portfolio is Not in Alpha the next thing should be to compare with Fund accountant
            //Check whether this portfolio is in Fund Accountant
            let is_in_fund_accountant = fund_accountant_portfolio_holdings.get(custodin_portfolio_holdings.0);

            //If FA is not there directly return portfolio is not in alpha
            if !required_fa_comparison {
                for cust_holding in cust_holdings {
                    let excel_entry = ReconExcelEntry {
                        client_name: cust_holding.client_name.clone(),
                        client_custodian_code: cust_holding.custodian_portfolio_code.clone(),
                        isin: cust_holding.isin.clone(),
                        scrip_name: Some(cust_holding.security_name.clone()),
                        custody_holdings: cust_holding.holdings,
                        alpha_vs_custody: -cust_holding.holdings,
                        remarks: String::from("Portfolio Is not In Alpha; But Exist In Custody;"),
                        ..Default::default()
                    };

                    let mut recon_result_lock = recon_result.lock().await;
                    recon_result_lock.push(excel_entry);

                    break;
                }

                continue;
            }

            //Check if the portfolio is in FA
            if let Some(fa_holdings) = is_in_fund_accountant {
                let fund_accountant_portfolio_holdings_map =
                    fa_holdings.into_iter().fold(HashMap::new(), |mut acc, recon| {
                        acc.entry(recon.isin.clone()).or_insert(recon);
                        acc
                    });

                // Loop  through Custodians Holdings and check it exist in FA
                for cust_holding in cust_holdings {
                    let mut excel_entry = ReconExcelEntry {
                        client_name: cust_holding.client_name.clone(),
                        client_custodian_code: cust_holding.custodian_portfolio_code.clone(),
                        isin: cust_holding.isin.clone(),
                        scrip_name: Some(cust_holding.security_name.clone()),
                        custody_holdings: cust_holding.holdings,
                        alpha_vs_custody: -cust_holding.holdings,
                        remarks: String::from("Portfolio Is not In Alpha;"),
                        ..Default::default()
                    };

                    let fa_holding = fund_accountant_portfolio_holdings_map.get(&cust_holding.isin.to_owned());

                    if let Some(fa_holding) = fa_holding {
                        if cust_holding.holdings != fa_holding.holdings {
                            log_warn(LogBuilder::system(&format!(
                                "{:?} Quantity doesn't match",
                                fa_holding.isin
                            )));
                            excel_entry.append_remarks("Custody and Fund Accountant Quantity Doesn't match;");
                            excel_entry.add_custody_vs_fund_accountant(cust_holding.holdings - fa_holding.holdings);
                        } else {
                            log_warn(LogBuilder::system("Custody and Fund Accountant Quantity Matches"));
                            excel_entry.append_remarks("Custody and Fund Accountant Quantity matches;");
                            excel_entry.add_custody_holdings(cust_holding.holdings);
                            excel_entry.add_fund_accountant_alpha_holdings(fa_holding.holdings);
                        }
                    } else {
                        excel_entry.append_remarks("Holdings is not in FA;");
                    }

                    let mut recon_result_lock = recon_result.lock().await;
                    recon_result_lock.push(excel_entry);
                }
            } else {
                //Not Found In Fund Accountant
                for cust_holding in cust_holdings {
                    let mut excel_entry = ReconExcelEntry {
                        custody_holdings: cust_holding.holdings,
                        isin: cust_holding.isin.clone(),
                        client_custodian_code: cust_holding.custodian_portfolio_code.clone(),
                        client_name: cust_holding.client_name.clone(),
                        ..Default::default()
                    };

                    excel_entry.append_remarks("Portfolio Is Not in FA and Alpha But Exist in Custody;");

                    let mut recon_result_lock = recon_result.lock().await;
                    recon_result_lock.push(excel_entry);

                    break;
                }
            }
        }
    }

    log_info(LogBuilder::system(
        "Recon End for Custodian -> Alpha and Custodian -> Fund Accountant",
    ));

    log_info(LogBuilder::system(
        "Started Recon for Fund Accountant -> Alpha and Fund Accountant -> Custodian",
    ));

    // Loop Through all the portfolios in fund Accountant
    // We will only process portfolios that is not in alpha and custody
    // Because the remaining portfolios are already processed
    for fund_account_portfolios in fund_accountant_portfolio_holdings.iter() {
        //Check whether this portfolio is in alpha and custody
        let processed_portfolios = processed_portfolios.lock().await;
        let is_in_alpha_or_custody = processed_portfolios.contains_key(fund_account_portfolios.0);
        drop(processed_portfolios);

        if !is_in_alpha_or_custody {
            let fa_holdings = fund_account_portfolios.1;

            // We are sure that fa_holdings will be more than 0
            // But Just for safety check this line is there
            if fa_holdings.len() <= 0 {
                continue;
            }

            let fa_holding = fa_holdings[0].clone(); //SAFE Access

            let excel_entry = ReconExcelEntry {
                client_name: fa_holding.client_name.clone(),
                client_fa_code: Some(fa_holding.fund_accountant_code.clone()),
                alpha_holdings: 0f64,
                alpha_vs_custody: 0f64,
                alpha_vs_fa: -fa_holding.holdings,
                fund_accountant_holdings: fa_holding.holdings,
                custody_vs_fa: -fa_holding.holdings,
                remarks: String::from("Portfolio is not in Alpha and Custody, But Exist in FA"),
                ..Default::default()
            };
            let mut recon_result_lock = recon_result.lock().await;
            recon_result_lock.push(excel_entry);
        }
    }

    log_info(LogBuilder::system(
        "Recon End for Fund Accountant -> Alpha and Fund Accountant -> Custodian",
    ));

    // Lock the mutex
    let guard = recon_result.lock().await;

    let rows = (*guard).clone();

    // Create Excel of the Holding Recon Result
    let buffer = create_csv_for_holding_recon_result(rows).await?;

    //Upload the excel to Blob
    let file_path = upload_holding_recon_result_to_blob(recon_request.id.clone(), buffer).await;

    match file_path {
        Ok(file_path) => {
            return Ok(file_path);
        }
        // On Error Propoagate the Error to Top
        Err(err) => {
            return Err(err);
        }
    }
}

pub async fn upload_holding_recon_result_to_blob(request_id: String, buffer: Vec<u8>) -> Result<String, String> {
    let mut azure_blob = AzureBlob::new(String::from("alpha-holding-recon"));
    let file_path = generate_unique_blob_name("holding_recon_result.xlsx", request_id);
    let blob_client = azure_blob.get_blob(&file_path);

    let result = blob_client
        .put_block_blob(buffer)
        .content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        .await;

    if result.is_ok() {
        Ok(file_path)
    } else {
        Err(String::from("Failed to Upload to Storage"))
    }
}

/// Generates an Unique Name for the Blob in azure storage table
fn generate_unique_blob_name(original_name: &str, unique_id: String) -> String {
    let extension = std::path::Path::new(original_name)
        .extension()
        .and_then(std::ffi::OsStr::to_str)
        .unwrap_or("");

    format!(
        "{}_{}.{}",
        std::path::Path::new(original_name)
            .file_stem()
            .and_then(std::ffi::OsStr::to_str)
            .unwrap_or(original_name),
        unique_id,
        extension
    )
}
