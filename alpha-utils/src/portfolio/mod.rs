use alpha_core_db::{
    connection::pool::{deadpool::managed::Object, Manager},
    schema::{
        cash_ledger::PortfolioCashLedger, client_order_entry::TransactionType, portfolio::Portfolio,
        trade_order_unsettled::TradeOrderUnsettledAmounts,
    },
};
use quick_xml::se;

pub struct PortfolioCashPosition {
    pub portfolio_id: String,
    pub current_cash_balance: f64,
    pub available_cash_balance: f64,
    pub submitted_buy_amount: f64,
    pub submitted_sell_amount: f64,
    pub held_buy_amount: f64,
    pub held_sell_amount: f64,
}

pub trait PortfolioExtras {
    async fn get_portfolio_cash_position(&self, conn: &mut Object<Manager>) -> Result<PortfolioCashPosition, String>;
}

impl PortfolioExtras for Portfolio {
    async fn get_portfolio_cash_position(&self, conn: &mut Object<Manager>) -> Result<PortfolioCashPosition, String> {
        let mut current_cash_balance = self.current_cash_balance;
        //Get the Running Balance
        let running_balance = PortfolioCashLedger::get_running_balance_of_a_portfolio(conn, self.id.clone(), false)
            .await
            .map_err(|e| {
                return String::from("Failed to Get Running Balance");
            })?;

        if let Some(running_balance) = running_balance {
            current_cash_balance = running_balance;
        }

        let buy_held_orders = TradeOrderUnsettledAmounts::get_trade_orders_portfolio_id(
            conn,
            String::from("Hold"),
            self.id.clone(),
            TransactionType::Buy,
        )
        .await?;

        let sell_held_orders = TradeOrderUnsettledAmounts::get_trade_orders_portfolio_id(
            conn,
            String::from("Hold"),
            self.id.clone(),
            TransactionType::Sell,
        )
        .await?;

        let submitted_buy_orders = TradeOrderUnsettledAmounts::get_trade_orders_portfolio_id(
            conn,
            String::from("Submitted"),
            self.id.clone(),
            TransactionType::Buy,
        )
        .await?;

        let submitted_sell_orders = TradeOrderUnsettledAmounts::get_trade_orders_portfolio_id(
            conn,
            String::from("Submitted"),
            self.id.clone(),
            TransactionType::Sell,
        )
        .await?;

        Ok(PortfolioCashPosition {
            portfolio_id: self.id.to_string(),
            current_cash_balance: current_cash_balance,
            held_buy_amount: buy_held_orders.iter().map(|to| to.amount).sum(),
            held_sell_amount: sell_held_orders.iter().map(|to| to.amount).sum(),
            submitted_buy_amount: submitted_buy_orders.iter().map(|to| to.amount).sum(),
            submitted_sell_amount: submitted_sell_orders.iter().map(|to| to.amount).sum(),
            available_cash_balance: current_cash_balance
                - buy_held_orders.iter().map(|to| to.amount).sum::<f64>()
                - submitted_buy_orders
                    .iter()
                    .map(|to| to.amount)
                    .sum::<f64>()
                    // - portfolio_receivables //FIXME:
                    // .iter()
                    // .map(|pr| pr.amount)
                    // .sum::<f64>()
                + sell_held_orders.iter().map(|to| to.amount).sum::<f64>(),
        })
    }
}

#[cfg(test)]
mod tests {
    use alpha_core_db::redis::connect_to_redis;
    use tracing_test::traced_test;

    use super::*;

    #[tokio::test]
    #[traced_test]
    async fn test_portfolio_cash() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(30).await;
        let master_pool = alpha_core_db::connection::connect_to_master_data(10).await;
        let mut conn = pool.get().await.unwrap();
        let redis_pool = connect_to_redis().await;
        let id = String::from("097e233089e045769f8d0844758e718d");
        let foo = Portfolio::get_portfolio_cash_position_by_id(&mut conn, id)
            .await
            .unwrap();
        println!("{:?}", foo);
    }
}
