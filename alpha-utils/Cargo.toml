[package]
name = "alpha-utils"
version = "0.1.0"
edition = "2021"

[dependencies]
csv = "1.3.0"
lapin = { version = "2.5.0", features = ["native-tls"] }
rust_xlsxwriter = { version = "0.75.0", features = ["serde"] }
serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.122"
tokio = { version = "1.40.0", features = ["full"] }
tokio-executor-trait = "2.1.1"
tokio-reactor-trait = "1.1.0"
tracing = { version = "0.1.40", features = ["attributes"] }
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }
alpha-core-db = { path = "../alpha-core-db" }
futures = "0.3.30"
futures-util = "0.3.30"
azure_storage_blobs = "0.20.0"
uuid = {workspace = true}
tiberius = {workspace = true}
rust_decimal = "1.36.0"
dotenv = "0.15.0"
chrono = { version = "0.4.38", features = ["serde"] }
quick-xml = { version = "0.36.1", features = ["serde", "serialize"] }
clickhouse = { workspace = true}
thiserror = "1.0"
time = {workspace = true}
redis = {workspace = true}
tracing-test ={workspace = true}
bb8-redis ={workspace = true}
bb8={workspace = true}
anyhow = {workspace = true}
reqwest = { version = "0.12.12", features = ["blocking", "json"] }
actlogica_logs = {workspace = true}
dashmap ={ workspace = true}