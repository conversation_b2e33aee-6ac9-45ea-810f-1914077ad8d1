[package]
name = "recon-runner"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { workspace = true }
dotenv = { workspace = true }
alpha-utils = { path = "../../alpha-utils" }
alpha-core-db = { path = "../../alpha-core-db" }
recon-util = { path = "../recon-util" }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
futures = "0.3.30"
futures-util = "0.3.30"
futures-lite = "2.3.0"
lapin = { workspace = true }