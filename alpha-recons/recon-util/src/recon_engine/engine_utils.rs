use core::f64;

use alpha_core_db::schema::investment::mis_holdings::RptHoldingDetailsForMis;
use alpha_core_db::tenant_clickhouse::reconciliation::{
    alpha_snapshot::{AlphaHolding, AlphaSnapshot},
    client_snapshot::ClientSnapshot,
};
use chrono::NaiveDate;

pub fn get_key_from_alpha_snapshot(snapshot: &AlphaSnapshot) -> String {
    format!("{}::{}::{}", snapshot.client_code, snapshot.isin, snapshot.holding_date)
}

pub fn get_key_from_client_snapshot(snapshot: &ClientSnapshot) -> String {
    format!("{}::{}::{}", snapshot.client_code, snapshot.isin, snapshot.holding_date)
}

pub async fn prepare_alpha_holdings(
    alpha_holdings: Vec<RptHoldingDetailsForMis>,
    holding_date: NaiveDate,
) -> Vec<AlphaHolding> {
    let mut alpha_snapshots = Vec::new();
    for holding in alpha_holdings {
        let alpha_snapshot = AlphaHolding {
            holding_date: holding_date,
            client_code: holding.client_strategy_code,
            name: holding.client_name,
            asset_class: holding.asset_class,
            symbol: holding.symbol,
            security_name: holding.holding_name,
            isin: holding.isin,
            holdings: holding.unrealised_qty,
            unit_cost: holding.average_price,
            total_cost: holding.total_cost,
            unit_price: holding.price,
            accrued_income: holding.accrued_income,
            market_value: holding.market_value,
        };
        alpha_snapshots.push(alpha_snapshot);
    }
    alpha_snapshots
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Clone, PartialEq)]
pub enum MatchStatus {
    Matched,
    UnMatched,
    PartiallyMatched,
    NotPresentInSource,
    NotPresentInAlpha,
}

impl ToString for MatchStatus {
    fn to_string(&self) -> String {
        match self {
            MatchStatus::Matched => "Matched".to_string(),
            MatchStatus::UnMatched => "UnMatched".to_string(),
            MatchStatus::PartiallyMatched => "PartiallyMatched".to_string(),
            MatchStatus::NotPresentInSource => "NotPresentInSource".to_string(),
            MatchStatus::NotPresentInAlpha => "NotPresentInAlpha".to_string(),
        }
    }
}
