// require to store it in ReconProjectRuns table
mod engine_utils;
pub struct RunResponse {
    pub run_id: String,
    pub project_id: String,
    pub matched_row_count: i32,
    pub unmatched_row_count: i32,
}
use alpha_core_db::connection::pool::{Manager, Pool};
use alpha_core_db::tenant_clickhouse::reconciliation::{
    alpha_snapshot::{AlphaHolding, AlphaSnapshot},
    client_snapshot::ClientSnapshot,
    project::{ReconProject, ReconScopeDates},
    recon_result::{ReconResult, ReconResultBreakCategories},
    recon_run::ReconProjectRuns,
};
use alpha_utils::reports::mis_reports::holdings_as_at::MisReports;
use clickhouse::Client;
use hashbrown::HashMap;
use uuid::Uuid;

use crate::recon_engine::engine_utils::MatchStatus;

/*
 * Reconciliation Engine Process Flow:
 *
 * 1. Get Dates from the ReconProject table
 * 2. Use Dates to fetch the data from the Alpha Db(SQL server) Holdings
 * 3. Convert the Alpha-Holdings to Alpha-Snapshot and store as snapshot in the Holdings data from Alpha Db to Clickhouse
 * 4. Run the Reconciliation
 *    4.1. Get client-report from client-snapshot table from clickhouse
 *    4.2. Get alpha-report from alpha-snapshot table from clickhouse
 *    4.3. Prepare HashMap with key(client id + code + name + symbol + isin), value(struct HoldingValue) pair
 *    4.4. Compare holdings data between systems
 *          4.4.1. If maches count+1; If not store the break-category
 *          4.4.2. Categorize reconciliation breaks
 *          4.4.3.
 * 5. Save the Reconciliation to the ReconResult Table
 * 6. Save the Reconciliation to the ReconResultBreakCategories Table
 * 7. Update project run statistics
 */

pub async fn run(alpha_db: &Pool, master_client: &Client, tenant_clickhouse_client: &Client, project_id: String) {
    let run_id = Uuid::new_v4().to_string();

    // 1. Get the start and end dates from the ProjectTable
    let project_scope_dates = ReconProject::get_project_scope_dates(&tenant_clickhouse_client, &project_id)
        .await
        .unwrap();
    println!("Project Scope Dates: {:?}", project_scope_dates);

    // 2. Use Dates to fetch the data from the Alpha Db Holdings,
    let mut mis_reports = MisReports {
        db: alpha_db.clone(),
        master_client: master_client.clone(),
    };
    let alpha_holdings = mis_reports
        .generate_holdings_as_at(project_scope_dates.off_set_date, true)
        .await
        .expect("Error generating holdings as at");

    let prepared_alpha_holdings =
        engine_utils::prepare_alpha_holdings(alpha_holdings, project_scope_dates.off_set_date).await;

    // 3. Snapshot the Holdings data from Alpha Db to Clickhouse
    let alpha_snapshots =
        AlphaSnapshot::snapshot_holdings(&tenant_clickhouse_client, prepared_alpha_holdings, &run_id).await;
    if let Err(e) = &alpha_snapshots {
        println!("Error snapshotting holdings: {}", e);
        return;
    }

    // 4. Run the Reconciliation
    let reconciliation =
        run_reconciliation(&tenant_clickhouse_client, &run_id, project_id, alpha_snapshots.unwrap()).await;
    if let Err(e) = reconciliation {
        println!("Error running reconciliation: {}", e);
    }
}

pub async fn run_reconciliation(
    conn: &Client,
    run_id: &str,
    project_id: String,
    alpha_snapshot_rows: Vec<AlphaSnapshot>,
) -> Result<(), clickhouse::error::Error> {
    let mut recon_results: Vec<ReconResult> = Vec::new();
    let mut recon_result_break_categories: Vec<ReconResultBreakCategories> = Vec::new();

    // 1. get client_snapshot
    let client_report_rows = ClientSnapshot::get_client_snapshot_by_project_id(conn, &project_id)
        .await
        .map_err(|e| {
            println!("Error getting client snapshot: {}", e);
            e
        })
        .unwrap();

    // 2.1. Prepare client-snapshot data with hashmap
    let client_data_map: HashMap<String, &ClientSnapshot> = client_report_rows
        .iter()
        .map(|row| (engine_utils::get_key_from_client_snapshot(row), row))
        .collect();

    // 2.2. Prepare alpha-holding data with hashmap
    let alpha_data_map: HashMap<String, &AlphaSnapshot> = alpha_snapshot_rows
        .iter()
        .map(|row| (engine_utils::get_key_from_alpha_snapshot(row), row))
        .collect();

    // 3. compare the alpha snapshots with the client snapshots
    let mut matched_row_count = 0;
    let mut unmatched_row_count = 0;
    let mut partially_matched_row_count = 0;
    let mut not_found_in_source_row_count = 0;
    let mut not_found_in_alpha_row_count = 0;

    // Track which keys have been processed to avoid double-counting
    let mut processed_keys: HashMap<String, bool> = HashMap::new();

    // First, process all alpha_data_map entries
    for (key, alpha_snapshot) in &alpha_data_map {
        processed_keys.insert(key.clone(), true);
        if let Some(client_value) = client_data_map.get(key) {
            let mut matched_column_count = 0;
            let mut match_status = MatchStatus::UnMatched;

            let unit_cost_diff = (client_value.unit_cost - alpha_snapshot.unit_cost).round();
            let holdings_diff = (client_value.holdings - alpha_snapshot.holdings).round();
            let total_cost_diff = (client_value.total_cost - alpha_snapshot.total_cost).round();
            let market_value_diff = (client_value.market_value - alpha_snapshot.market_value).round();
            let accrued_income_diff = (client_value.accrued_income - alpha_snapshot.accrued_income).round();

            if holdings_diff == 0.0 {
                matched_column_count += 1;
            }
            if unit_cost_diff == 0.0 {
                matched_column_count += 1;
            }
            if total_cost_diff == 0.0 {
                matched_column_count += 1;
            }
            if market_value_diff == 0.0 {
                matched_column_count += 1;
            }
            if accrued_income_diff == 0.0 {
                matched_column_count += 1;
            };

            if matched_column_count == 5 {
                match_status = MatchStatus::Matched;
                matched_row_count += 1;
            } else if matched_column_count == 0 {
                match_status = MatchStatus::UnMatched;
                unmatched_row_count += 1;
            } else {
                match_status = MatchStatus::PartiallyMatched;
                partially_matched_row_count += 1;
            }

            let recon_result_id = Uuid::new_v4().to_string();
            let recon_result = ReconResult {
                id: recon_result_id.clone(),
                run_id: run_id.to_string(),
                holding_date: client_value.holding_date,
                client_code: client_value.client_code.clone(),
                name: client_value.name.clone(),
                symbol: client_value.symbol.clone(),
                security_name: client_value.security_name.clone(),
                isin: client_value.isin.clone(),
                client_holdings: client_value.holdings,
                alpha_holdings: alpha_snapshot.holdings,
                holding_diff: holdings_diff,
                client_unit_cost: client_value.unit_cost,
                alpha_unit_cost: alpha_snapshot.unit_cost,
                unit_cost_diff,
                client_total_cost: client_value.total_cost,
                alpha_total_cost: alpha_snapshot.total_cost,
                total_cost_diff,
                client_market_value: client_value.market_value,
                alpha_market_value: alpha_snapshot.market_value,
                market_value_diff,
                client_accrued_income: client_value.accrued_income,
                alpha_accrued_income: alpha_snapshot.accrued_income,
                accrued_income_diff,
                match_status: match_status.to_string(),
                unmatched_count: 5 - matched_column_count,
            };
            recon_results.push(recon_result);

            if holdings_diff != 0.0 {
                recon_result_break_categories.push(ReconResultBreakCategories {
                    recon_result_id: recon_result_id.clone(),
                    run_id: run_id.to_string(),
                    project_id: project_id.clone(),
                    category: "Holdings".to_string(),
                });
            }
            if unit_cost_diff != 0.0 {
                recon_result_break_categories.push(ReconResultBreakCategories {
                    recon_result_id: recon_result_id.clone(),
                    run_id: run_id.to_string(),
                    project_id: project_id.clone(),
                    category: "UnitCost".to_string(),
                });
            }
            if total_cost_diff != 0.0 {
                recon_result_break_categories.push(ReconResultBreakCategories {
                    recon_result_id: recon_result_id.clone(),
                    run_id: run_id.to_string(),
                    project_id: project_id.clone(),
                    category: "TotalCost".to_string(),
                });
            }
            if market_value_diff != 0.0 {
                recon_result_break_categories.push(ReconResultBreakCategories {
                    recon_result_id: recon_result_id.clone(),
                    run_id: run_id.to_string(),
                    project_id: project_id.clone(),
                    category: "MarketValue".to_string(),
                });
            }
            if accrued_income_diff != 0.0 {
                recon_result_break_categories.push(ReconResultBreakCategories {
                    recon_result_id: recon_result_id.clone(),
                    run_id: run_id.to_string(),
                    project_id: project_id.clone(),
                    category: "AccruedIncome".to_string(),
                });
            }
        } else {
            // Not present in client
            let recon_result_id = Uuid::new_v4().to_string();
            let recon_result = ReconResult {
                id: recon_result_id.clone(),
                run_id: run_id.to_string(),
                holding_date: alpha_snapshot.holding_date,
                client_code: alpha_snapshot.client_code.clone(),
                name: alpha_snapshot.name.clone(),
                symbol: alpha_snapshot.symbol.clone(),
                security_name: alpha_snapshot.security_name.clone(),
                isin: alpha_snapshot.isin.clone(),
                client_holdings: 0.0,
                alpha_holdings: alpha_snapshot.holdings,
                holding_diff: -alpha_snapshot.holdings, // difference is all in alpha
                client_unit_cost: 0.0,
                alpha_unit_cost: alpha_snapshot.unit_cost,
                unit_cost_diff: -alpha_snapshot.unit_cost,
                client_total_cost: 0.0,
                alpha_total_cost: alpha_snapshot.total_cost,
                total_cost_diff: -alpha_snapshot.total_cost,
                client_market_value: 0.0,
                alpha_market_value: alpha_snapshot.market_value,
                market_value_diff: -alpha_snapshot.market_value,
                client_accrued_income: 0.0,
                alpha_accrued_income: alpha_snapshot.accrued_income,
                accrued_income_diff: -alpha_snapshot.accrued_income,
                match_status: MatchStatus::NotPresentInSource.to_string(),
                unmatched_count: 5,
            };
            recon_results.push(recon_result);

            recon_result_break_categories.push(ReconResultBreakCategories {
                recon_result_id: recon_result_id.clone(),
                run_id: run_id.to_string(),
                project_id: project_id.clone(),
                category: "NotPresentInSource".to_string(),
            });
            not_found_in_source_row_count += 1;
        }
    }

    // Now, process all client_data_map entries that were not in alpha_data_map
    for (key, client_snapshot) in &client_data_map {
        if processed_keys.contains_key(key) {
            continue;
        }
        // Not present in alpha
        let recon_result_id = Uuid::new_v4().to_string();
        let recon_result = ReconResult {
            id: recon_result_id.clone(),
            run_id: run_id.to_string(),
            holding_date: client_snapshot.holding_date,
            client_code: client_snapshot.client_code.clone(),
            name: client_snapshot.name.clone(),
            symbol: client_snapshot.symbol.clone(),
            security_name: client_snapshot.security_name.clone(),
            isin: client_snapshot.isin.clone(),
            client_holdings: client_snapshot.holdings,
            alpha_holdings: 0.0,
            holding_diff: client_snapshot.holdings, // difference is all in client
            client_unit_cost: client_snapshot.unit_cost,
            alpha_unit_cost: 0.0,
            unit_cost_diff: client_snapshot.unit_cost,
            client_total_cost: client_snapshot.total_cost,
            alpha_total_cost: 0.0,
            total_cost_diff: client_snapshot.total_cost,
            client_market_value: client_snapshot.market_value,
            alpha_market_value: 0.0,
            market_value_diff: client_snapshot.market_value,
            client_accrued_income: client_snapshot.accrued_income,
            alpha_accrued_income: 0.0,
            accrued_income_diff: client_snapshot.accrued_income,
            match_status: "NotPresentInAlpha".to_string(),
            unmatched_count: 5,
        };
        recon_results.push(recon_result);

        recon_result_break_categories.push(ReconResultBreakCategories {
            recon_result_id: recon_result_id.clone(),
            run_id: run_id.to_string(),
            project_id: project_id.clone(),
            category: "NotPresentInAlpha".to_string(),
        });
        not_found_in_alpha_row_count += 1;
    }

    // 4. save the reconciliation result
    ReconResult::insert(conn, &recon_results).await.map_err(|e| {
        println!("Error inserting reconciliation result: {}", e);
        e
    })?;
    ReconResultBreakCategories::insert(conn, &recon_result_break_categories)
        .await
        .map_err(|e| {
            println!("Error inserting reconciliation result break categories: {}", e);
            e
        })?;

    let last_serial_no = ReconProjectRuns::get_last_serial_no(conn, &project_id).await?;
    let recon_project_run = ReconProjectRuns {
        id: run_id.to_string(),
        project_id: project_id.clone(),
        serial_no: last_serial_no + 1,
        matched_row_count: matched_row_count,
        unmatched_row_count: unmatched_row_count,
        partially_matched_row_count: partially_matched_row_count,
        not_found_in_client_row_count: not_found_in_source_row_count,
        not_found_in_alpha_row_count: not_found_in_alpha_row_count,
    };
    ReconProjectRuns::insert(conn, &recon_project_run).await.map_err(|e| {
        println!("Error inserting reconciliation project run: {}", e);
        e
    })?;

    // update run_count in ReconProject
    ReconProject::add_run(conn, &project_id).await.map_err(|e| {
        println!("Error updating run count: {}", e);
        e
    })?;

    let result = format!(
        "Reconciliation completed: \nMatched: {}, \nUnmatched: {}, \nPartially Matched: {}, \nNot Present in Client: {}, \nNot Present in Alpha: {}",
        matched_row_count,
        unmatched_row_count,
        partially_matched_row_count,
        not_found_in_source_row_count,
        not_found_in_alpha_row_count
    );
    println!("{}", result);
    Ok(())
}
