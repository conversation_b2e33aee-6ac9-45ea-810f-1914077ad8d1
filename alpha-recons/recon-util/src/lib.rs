pub mod message_type;
pub mod client_report;
pub mod recon_engine;

// common structs
use serde::{Deserialize, Serialize};
use chrono::{NaiveDate};    

// ClientSnapshot Table
#[derive(Debug, Serialize, Deserialize)]
pub struct ClientSnapshot{
    pub id: String,
    pub project_id: String,
    pub holding_date: NaiveDate,
    pub client_id: String,
    pub name: String,
    pub symbol: String,
    pub security_name: String,
    pub isin: String,
    pub position: String,
    pub holdings: i32,
    pub unit_cost: f64,
    pub total_cost: f64,
    pub accrued_income: i32,
    pub market_value: f64,
}