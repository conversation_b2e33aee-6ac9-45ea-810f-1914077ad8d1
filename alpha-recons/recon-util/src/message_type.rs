use serde::{Deserialize, Serialize};


#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "messageType")]
pub enum MessageContent {
    ProcessClientHoldings(ProcessClientHoldingsMessage),
    RunRecon(RunReconRequest)
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessClientHoldingsMessage {
    pub project_id: String,
    pub file_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RunReconRequest {
    pub project_id: String,
}