use alpha_core_db::tenant_clickhouse::reconciliation::client_snapshot::{ClientReportRow, ClientSnapshot};
use chrono::NaiveDate;
use clickhouse::Client;
use csv::Reader;

/*
*    The CSV columns are:
*    0: Holding Date
*    1: CODE (client_code)
*    2: NAME (name)
*    3: Asset Class (ignored)
*    4: SYMB<PERSON>CODE (symbol)
*    5: SECURITY (security_name)
*    6: ISIN (isin)
*    7: POSITION (position)
*    8: HOLDINGS (holdings)
*    9: Unit Cost (unit_cost)
*    10: Total Cost (total_cost)
*    11: Unit Price (ignored)
*    12: Accrued Income (accrued_income)
*    13: Market Value (market_value)
*/

pub async fn process_client_report(
    file_path: &str,
    project_id: &str,
    client: &Client,
) -> Result<(), Box<dyn std::error::Error>> {
    let file = std::fs::File::open(file_path)?;
    let reader = std::io::BufReader::new(file);
    let mut reader = Reader::from_reader(reader);

    let mut client_records = Vec::new();

    let start = std::time::Instant::now(); // Stopwatch start

    for result in reader.records() {
        let record = result?;

        let mut row = ClientReportRow {
            holding_date: NaiveDate::parse_from_str(&record[0], "%d-%m-%y").expect("Error parsing holding date"),
            client_code: record[1].to_string(),
            name: record[2].to_string(),
            symbol: record[4].to_string(),
            security_name: record[5].to_string(),
            isin: record[6].to_string(),
            position: record[7].to_string(),
            holdings: record[8].parse::<f64>().expect("Error parsing holdings"),
            unit_cost: record[9].parse::<f64>().expect("Error parsing unit cost"),
            total_cost: record[10].parse::<f64>().expect("Error parsing total cost"),
            accrued_income: record[12].parse::<f64>().expect("Error parsing accrued income"),
            market_value: record[13].parse::<f64>().expect("Error parsing market value"),
        };
        // println!("{:?}", row);
        if row.symbol == "CASH" {
            row.isin = "CASH".to_string();
        }
        client_records.push(row);
    }

    let duration = start.elapsed();
    println!("Record found: {:?}", client_records.len());
    println!("Time taken to read file: {:.2?}", duration);

    let start = std::time::Instant::now(); // Stopwatch start
    match ClientSnapshot::insert_batch(client, client_records, project_id.to_string()).await {
        Ok(_) => {
            let duration = start.elapsed();
            match ClientSnapshot::prepare_client_snapshot_table(client, project_id).await {
                Ok(_) => {},
                Err(e) => {
                    println!("❌ Error preparing client snapshot table: {}", e);
                }
            }
            println!("Time taken to insert rows: {:.2?}", duration);
        }
        Err(e) => {
            println!("❌ Error inserting rows into database: {}", e);
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use alpha_core_db::tenant_clickhouse::connect_tenant_clickhouse_client;
    use dotenv::dotenv;
    use std::time::Instant;

    #[tokio::test]
    async fn test_process_client_report() {
        dotenv().ok();
        let client = connect_tenant_clickhouse_client();
        let file_path = "src/test-data/client_report.csv";

        let start = Instant::now();
        let result = process_client_report(file_path, "2", &client).await;
        let duration = start.elapsed();

        println!("⏱️ Time taken to process: {:.2?}", duration);

        result.unwrap();
    }
}
