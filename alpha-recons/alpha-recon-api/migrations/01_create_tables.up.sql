CREATE TABLE ReconProject (
    Id String,
    Name String,
    Description String,
    UserId String,
    UserName String,
    NumOfRuns Int32,
    GoldenSourceFile String,
    OffSetDate Date,
    CreatedAt DateTime64(3),
    UpdatedAt DateTime64(3),
    EndedAt Nullable(DateTime64(3)),
    IsActive Boolean
)
ENGINE = MergeTree()
ORDER BY Id;


CREATE TABLE ClientSnapshot
(
    Id String,
    ProjectId String,
    HoldingDate Date,
    ClientCode String,
    Name String,
    Symbol String,
    SecurityName String,
    isin String,
    Position String,
    Holdings Float64,
    UnitCost Float64,
    TotalCost Float64,
    AccruedIncome Float64,
    MarketValue Float64
)
ENGINE = MergeTree()
ORDER BY (ProjectId);

CREATE TABLE ReconProjectRuns (
    Id String,
    ProjectId String,
    SerialNo Int32,
    MatchedRowCount Int32,
    PartiallyMatchedRowCount Int32,
    UnmatchedRowCount Int32,
    NotFoundInSource Int32,
    NotFoundInAlpha Int32,
)
ENGINE = MergeTree()
ORDER BY (ProjectId, Id);

CREATE TABLE AlphaSnapshot (
    RunId String,
    HoldingDate Date,
    ClientCode String,
    Name String,
    Symbol String,
    SecurityName String,
    Isin String,
    Position String,
    Holdings Float64,
    UnitCost Float64,
    TotalCost Float64,
    AccruedIncome Float64,
    MarketValue Float64
)
ENGINE = MergeTree()
ORDER BY (RunId, HoldingDate);

CREATE TABLE ReconResult (
    Id String,
    RunId String,
    HoldingDate Date,
    ClientCode String,
    Name String,
    Symbol String,
    SecurityName String,
    Isin String,
    ClientHoldings Float64,
    AlphaHoldings Float64,
    HoldingDiff Float64,
    ClientUnitCost Float64,
    AlphaUnitCost Float64,
    UnitCostDiff Float64,
    ClientTotalCost Float64,
    AlphaTotalCost Float64,
    TotalCostDiff Float64,
    ClientAccruedIncome Float64,
    AlphaAccruedIncome Float64,
    AccruedIncomeDiff Float64,
    ClientMarketValue Float64,
    AlphaMarketValue Float64,
    MarketValueDiff Float64,
    MatchStatus String,
    UnmatchedCount Int32
)
ENGINE = MergeTree()
ORDER BY (RunId, Isin);

CREATE TABLE ReconResultBreakCategories (
    ReconResultId String,
    RunId String,
    ProjectId String,
    Category String
)
ENGINE = MergeTree()
ORDER BY (RunId);