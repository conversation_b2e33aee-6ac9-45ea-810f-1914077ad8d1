# Reconciliation API Documentation

This document describes the schema and API endpoints for the reconciliation system, which allows clients to upload, process, and compare portfolio reports with internal data, and analyze matched/unmatched records.

---

## Steps to Run Alpha Reconciliation Engine

1. **Upload Client Report**
    - Upload the `.csv` file using the UI or API.
    - The system stores the file in MinIO and processes it (reads and stores each row in the ClientReportDb).
    - The MinIO URL for the file is returned.

2. **Create Project**
    - Use the MinIO URL from the previous step to create a new project.
    - The project will now appear in the Projects UI section.

3. **Project Management in UI**
    - Each project in the UI offers two main actions:
        - **See Details:**
            - Shows a popup with:
                - Project name and ID
                - Number of completed runs
                - Any active runs
                - List of all runs (with completion % and status)
        - **Run:**
            - Initiates a new reconciliation run.
            - Prompts for configuration (e.g., Alpha Holding recon options, matching criteria, tolerances).

4. **Setup Recon-Runner (if required)**
    - Configure the reconciliation runner for the project (set matching criteria, tolerances etc. if required).

5. **Run Recon-Engine**
    - Start the reconciliation process for the selected project / run configuration.

6. **Monitor Progress** (try using websockets)
    - Use the UI or API to monitor the run state (progress %, matched / unmatched counts, etc.).

7. **Review Results**
    - After completion, use the UI or API to:
        - View snapshot data for the run.
        - Download or review the reconciliation report (matched/unmatched records, reasons for mismatches).

8. **(Optional) Manage Projects and Runs**
    - Use the UI or API to list all projects, view project details, and review historical runs.

---

## Table of Contents

- [Reconciliation API Documentation](#reconciliation-api-documentation)
  - [Steps to Run Alpha Reconciliation Engine](#steps-to-run-alpha-reconciliation-engine)
  - [Table of Contents](#table-of-contents)
  - [Data Tables](#data-tables)
    - [ClientSnapshot Table](#clientsnapshot-table)
    - [ReconProject Table](#reconproject-table)
    - [ReconProjectRuns Table](#reconprojectruns-table)
    - [AlphaSnapshot Table](#alphasnapshot-table)
    - [ReconResult](#reconresult)
    - [ReconResultBreakCategories](#reconresultbreakcategories)
  - [API Endpoints](#api-endpoints)
    - [1. Create Project API](#1-create-project-api)
    - [2. Upload Client Report](#2-upload-client-report)
    - [3. Get All Projects API](#3-get-all-projects-api)
    - [4. Get Project Details API](#4-get-project-details-api)
    - [5. Setup Recon-Runner API](#5-setup-recon-runner-api)
    - [6. Run Recon-engine](#6-run-recon-engine)
    - [7. Get a Particular Run-State API](#7-get-a-particular-run-state-api)
    - [8. Get Snapshot Data API](#8-get-snapshot-data-api)
    - [9. Get Report Response API](#9-get-report-response-api)
  - [Task Execution Plan Steps](#task-execution-plan-steps)

---

## Data Tables

### ClientSnapshot Table

| Field           | Type      | Description         |
|-----------------|-----------|---------------------|
| Id              | String    | Snapshot ID         |
| ProjectId       | String    | Project ID          |
| HoldingDate     | DateTime  | Date of holding     |
| ClientCode      | String    | Security code       |
| Name            | String    | Client name         |
| Symbol          | String    | Security symbol     |
| SecurityName    | String    | Security name       |
| Isin            | String    | ISIN code           |
| Position        | String    | Position type       |
| Holdings        | Int       | Number of holdings  |
| UnitCost        | Float     | Cost per unit       |
| TotalCost       | Float     | Total cost          |
| AccruedIncome   | Int       | Accrued income      |
| MarketValue     | Float     | Market value        |

### ReconProject Table

| Field           | Type           | Description                  |
|-----------------|----------------|------------------------------|
| Id              | String         | Project ID                   |
| Name            | String         | Project name                 |
| Description     | String         | Project description          |
| UserId          | String         | Client ID                    |
| UserName        | String         | Client name                  |
| NumOfRuns       | Int            | Number of runs               |
| GoldenSourceFile| String         | MinIO URL for report file    |
| ReconStartDate  | Date           | Start date or inception date |
| ReconEndDate    | Date           | End date                     |
| CreatedAt       | DateTime       | Creation date                |
| UpdatedAt       | DateTime       | Last update date             |
| IsActive        | Boolean        | Active flag                  |

### ReconProjectRuns Table

| Field              | Type    | Description                  |
|--------------------|---------|------------------------------|
| Id                 | String  | Run metadata ID              |
| ProjectId          | String  | Associated project ID        |
| SerialNo           | Int     | Serial number                |
| MatchedRowCount    | Int     | Number of matched rows       |
| UnmatchedRowCount  | Int     | Number of unmatched rows     |
| ResponseReportUrl  | String  | URL to response report       |

### AlphaSnapshot Table

| Field           | Type      | Description         |
|-----------------|-----------|---------------------|
| RunId           | String    | Run ID              |
| HoldingDate     | DateTime  | Date of holding     |
| Code            | String    | Security code       |
| Name            | String    | Client name         |
| Symbol          | String    | Security symbol     |
| SecurityName    | String    | Security name       |
| Isin            | String    | ISIN code           |
| Position        | String    | Position type       |
| Holdings        | Int       | Number of holdings  |
| UnitCost        | Float     | Cost per unit       |
| TotalCost       | Float     | Total cost          |
| AccruedIncome   | Int       | Accrued income      |
| MarketValue     | Float     | Market value        |

### ReconResult

| Field               | Type      | Description                                         |
|---------------------|-----------|-----------------------------------------------------|
| Id                  | String    | ID from ReconResult Row Id                          |
| RunId               | String    | Associated run ID                                   |
| Name                | String    | Client name                                         |
| Symbol              | String    | Security symbol                                     |
| SecurityName        | String    | Security name                                       |
| Isin                | String    | ISIN code                                           |
| ClientHoldings      | Int       | Holdings from client report                         |
| AlphaHoldings       | Int       | Holdings from alpha reports                         |
| HoldingDiff         | Int       | Difference between client and alpha holdings        |
| ClientUnitCost      | Float     | Unit cost from client report                        |
| AlphaUnitCost       | Float     | Unit cost from alpha reports                        |
| UnitCostDiff        | Float     | Difference between client and alpha unit cost       |
| ClientTotalCost     | Float     | Total cost from client report                       |
| AlphaTotalCost      | Float     | Total cost from alpha reports                       |
| TotalCostDiff       | Float     | Difference between client and alpha total cost      |
| ClientAccruedIncome | Int       | Accrued income from client report                   |
| AlphaAccruedIncome  | Int       | Accrued income from alpha reports                   |
| AccruedIncomeDiff   | Int       | Difference between client and alpha accrued income  |
| ClientMarketValue   | Float     | Market value from client report                     |
| AlphaMarketValue    | Float     | Market value from alpha reports                     |
| MarketValueDiff     | Float     | Difference between client and alpha market value    |

### ReconResultBreakCategories

| Field     | Type    | Description                                      |
|-----------|---------|--------------------------------------------------|
| Id        | String  | ID from ReconResult Row Id               |
| RunId     | String  | Associated run ID                                |
| Category  | String  | Reason for flag (e.g., "HoldingDiff", "UnitCostDiff") |

**Example rows:**

| Id | RunId | Category        |
|----|-------|--------------|
| 1  | 1     | HoldingDiff  |
| 1  | 1     | UnitCostDiff |
| 2  | 1     | HoldingDiff  |

---

## API Endpoints

### 1. Create Project API

- **Description**: Create a new reconciliation project.
- **Payload**:
  - project_name: String
  - description: String
  - start_date: DateTime
  - end_date: DateTime
  - client_report_minio_url: String
- **Response**: project_id: String

### 2. Upload Client Report

- **Description**: Upload a client-provided CSV file to MinIO storage and process its rows into the database.
- **Payload**:
  - file_path: String
- **Response**: minio_storage_url: String

### 3. Get All Projects API

- **Description**: Fetch all projects created by the client.
- **Response**: List of Project objects

### 4. Get Project Details API

- **Description**: Get details of a specific project by project ID.
- **Payload**:
  - project_id: String
- **Response**: Project object

### 5. Setup Recon-Runner API

- **Description**: Initialize the reconciliation runner for a project.
- **Payload**:
  - project_id: String
  - reconciliation_config: Object
    - tolerance_percentage: Float (optional)
    - match_criteria: Array\<String> (e.g., ["ISIN", "Symbol"])
    - exclude_columns: Array\<String> (optional)
- **Response**: runner_id: String, status: String ("setup_complete" | "setup_failed"), message: String

### 6. Run Recon-engine

- **Description**: Trigger the reconciliation engine to compare client report data with internal data.
- **Payload**:
  - project_id: String
  - run_config: Object
    - holding_date: DateTime (optional, defaults to latest)
    - force_reconciliation: Bool (optional, defaults to false)
- **Response**: run_id: String, status: String ("started" | "failed"), estimated_duration: Int (in seconds)

### 7. Get a Particular Run-State API

- **Description**: Retrieve the current state and progress of a reconciliation run.
- **Payload**:
  - run_id: String
- **Response**: run_id: String, project_id: String, status: String ("running" | "completed" | "failed" | "cancelled"), progress_percentage: Int, matched_count: Int, unmatched_count: Int, total_records: Int, start_time: DateTime, end_time: DateTime (null if still running), error_message: String (if failed)

### 8. Get Snapshot Data API

- **Description**: Retrieve snapshot data for a specific reconciliation run.
- **Payload**:
  - run_id: String
  - filters: Object (optional)
    - holding_date: DateTime (optional)
    - isin: String (optional)
    - symbol: String (optional)
    - position: String (optional)
  - pagination: Object (optional)
    - page: Int
    - page_size: Int
- **Response**: run_id: String, total_records: Int, page: Int, page_size: Int, data: Array\<SnapshotRecord>

### 9. Get Report Response API

- **Description**: Retrieve reconciliation results showing matched and unmatched records.
- **Payload**:
  - run_id: String
  - filters: Object (optional)
    - match_status: String ("matched" | "unmatched" | "all")
    - reason: String (optional)
    - isin: String (optional)
    - symbol: String (optional)
  - pagination: Object (optional)
    - page: Int
    - page_size: Int
- **Response**: run_id: String, total_matched: Int, total_unmatched: Int, total_records: Int, page: Int, page_size: Int, data: Array\<ReportResponseRecord>

---

## Task Execution Plan Steps

1. **Report Upload**
   - Accept the client report file (URL or file upload).
   - Validate the report format and contents.
   - Store the report in the database, associating it with the intended project (if known) or as a pending upload.
   - Store reports in the database in descending order by upload/creation date for efficient retrieval of the latest report.

2. **Project Creation (Dependent on Report Upload)**
   - Ensure that a valid report has been uploaded and is available.
   - Validate input fields (name, description, client report file URL, scope date).
   - Generate a unique project ID.
   - Link the uploaded report to the new project.
   - Insert the project into the database, referencing the uploaded report.
   - Return the created project ID and status.

3. **Runner Setup**
   - Accept project ID and runner configuration.
   - Initialize the runner environment.
   - Return runner ID and setup status.

4. **Trigger Reconciliation Run**
   - Accept project ID and run configuration.
   - Validate project existence and configuration.
   - Start a new reconciliation run and generate a run ID.
   - Return run ID, status, and estimated duration.

5. **Monitor Run State**
   - Accept run ID.
   - Fetch the current state and progress of the run.
   - Return run details including status, progress, and error messages if any.

6. **Fetch Snapshot Data**
   - Accept run ID, optional filters, and pagination.
   - Query snapshot data for the specified run.
   - Return paginated snapshot records.

7. **Fetch Report Response**
   - Accept run ID, optional filters, and pagination.
   - Query matched and unmatched records for the run.
   - Return paginated report response records with match status and reasons.

---
**Note:**  

- Project creation cannot proceed unless a report has been uploaded and processed.  
- Reports should be stored in the database in descending order by upload/creation date to always use the latest report for new projects.

---
