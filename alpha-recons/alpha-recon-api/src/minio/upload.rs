use crate::{config::Config, models::AppState};
use axum::body::Bytes;
use minio::s3::{Client, builders::ObjectContent};
use std::sync::Arc;
use uuid::Uuid;

pub async fn upload_file(client: &Client, bucket: &str, data: Bytes, file_name: &str) -> Result<String, String> {
    let content = ObjectContent::try_from(data).unwrap();

    let object_key = format!("golden-source/{}", file_name);
    match client
        .put_object_content(bucket, &object_key, content)
        .send()
        .await
        {
            Ok(_) => Ok(format!("{}", object_key)),
            Err(e) => {
                println!("Error uploading file: {}", e);
                Err(e.to_string())
            }
        }
}
