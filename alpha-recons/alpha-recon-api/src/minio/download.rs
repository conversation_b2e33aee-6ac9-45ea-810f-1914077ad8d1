use crate::{config::Config, models::AppState};
use axum::body::Bytes;
use minio::s3::{Client, builders::ObjectContent, types::S3Api};
use std::path::Path;

pub async fn download_file(client: &Client, bucket: &str, file_name: &str) -> Result<(), String> {
    let object_key = format!("golden-source/{}", file_name);
    let response = client
        .get_object(bucket, &object_key)
        .send()
        .await
        .unwrap();

    let download_path = format!("~/golden-source/{}", file_name);

    response
        .content
        .to_file(Path::new(&download_path))
        .await
        .unwrap();

    Ok(())
}
