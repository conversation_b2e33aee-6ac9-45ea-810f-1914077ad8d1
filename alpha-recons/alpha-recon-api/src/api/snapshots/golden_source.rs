use crate::models::AppState;
use alpha_core_db::minio::download_file;
use alpha_core_db::tenant_clickhouse::reconciliation::project::ReconProject;
use axum::{extract::{Path, State}, http::StatusCode, response::IntoResponse, Json};
use std::sync::Arc;
use utoipa::ToSchema;

#[utoipa::path(
    get,
    path = "/snapshots/download-golden-source-csv/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "File downloaded successfully"),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn download_golden_source_csv(
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let project = ReconProject::get_golden_source_url(&state.tenant_clickhouse, project_id).await;
    match project {
        Ok(file_url) => {
            let file_name = file_url.split('/').last().unwrap();
            let client = &state.tenant_minio_client;
            let result = download_file(&client, &state.env.minio_bucket.clone(), file_name).await;

            match result {
                Ok(_) => Ok((StatusCode::OK, Json("File downloaded successfully")).into_response()),
                Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
            }
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
