use crate::models::AppState;
use alpha_core_db::tenant_clickhouse::reconciliation::project::{AllProjectResponse, ReconProject};
use axum::{Json, extract::State, http::StatusCode, response::IntoResponse};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Debug, serde::Serialize, serde::Deserialize, ToSchema, Clone)]
pub struct ReconProjectResponseSchema {
    pub id: String,
    pub name: String,
    pub description: String,
    pub off_set_date: String,
    pub created_at: String,
}

#[utoipa::path(
    get,
    tag = "Project",
    path = "/project/get-all",
    responses(
        (status = 200, description = "Projects found", body = Vec<ReconProjectResponseSchema>),
        (status = 404, description = "Projects not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_all_projects(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let user_id = "test"; // TODO: get user id from token
    let projects = ReconProject::get_all(&state.tenant_clickhouse, &user_id).await;
    match projects {
        Ok(projects) => Ok((StatusCode::OK, Json(projects)).into_response()),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
