use crate::models::AppState;
use alpha_core_db::tenant_clickhouse::reconciliation::project::{ProjectResponse, ReconProject};
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use std::sync::Arc;
use utoipa::{openapi::example, ToSchema};

#[derive(Debug, ToSchema, Clone)]
pub struct ProjectResponseSchema {
    pub id: String,
    pub name: String,
    pub description: String,
    pub user_name: String,
    pub num_of_runs: i32,
    pub off_set_date: String,
    pub created_at: String,
    pub is_active: bool,
}

#[utoipa::path(
    get,
    tag = "Project",
    path = "/project/get/{id}",
    params(
        ("id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "Project found", body = ProjectResponseSchema),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_project_by_id(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let result = ReconProject::get_by_id(&state.tenant_clickhouse, id).await;

    match result {
        Ok(project) => Ok((StatusCode::OK, Json(project)).into_response()),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
