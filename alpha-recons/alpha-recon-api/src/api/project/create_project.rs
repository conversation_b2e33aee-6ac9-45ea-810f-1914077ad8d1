use crate::{
    minio::{download::download_file, upload::upload_file},
    models::{AppState, TokenClaims},
};
use alpha_core_db::tenant_clickhouse::reconciliation::project::ReconProject;
use axum::{
    Extension, Json,
    extract::{Multipart, State},
    http::StatusCode,
    response::IntoResponse,
};
use chrono::{NaiveDate, NaiveDateTime, Offset, Timelike, Utc};
use csv::Reader;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use time::{Date, OffsetDateTime, PrimitiveDateTime, UtcDateTime};
use utoipa::ToSchema;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize, ToSchema, Clone)]
pub struct CreateProject {
    pub name: String,
    pub description: String,
    pub off_set_date: String,
}

#[derive(Deserialize, ToSchema)]
pub struct CreateProjectMultipart {
    /// JSON string of CreateProject
    #[schema(example = r#"{
        "name": "Sample Project",
        "description": "This is a sample project for testing",
        "off_set_date": "2025-06-26"
    }"#)]
    pub project: String,

    /// CSV file
    #[schema(value_type = String, format = Binary)]
    pub file: String,
}

#[utoipa::path(
    post,
    tag = "Project",
    path = "/project/create-project",
    request_body(
        content = CreateProjectMultipart,
        content_type = "multipart/form-data"
    ),
    responses(
        (status = 201, description = "Project created successfully"),
        (status = 400, description = "Bad Request"),
    )
)]
pub async fn create_project(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    mut multipart: Multipart,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let project_id = Uuid::new_v4().to_string();
    let mut project_data: Option<CreateProject> = None;
    let mut file_url: Option<String> = None;

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| (StatusCode::BAD_REQUEST, format!("Failed to get next field: {}", e)))?
    {
        match field.name() {
            // Parse the project data
            Some("project") => {
                let bytes = field
                    .bytes()
                    .await
                    .map_err(|e| (StatusCode::BAD_REQUEST, format!("Failed to read project field: {}", e)))?;

                project_data = Some(
                    serde_json::from_slice::<CreateProject>(&bytes)
                        .map_err(|e| (StatusCode::BAD_REQUEST, format!("Invalid JSON: {}", e)))?,
                );
            }
            // Process .csv file
            Some("file") => {
                // Read the CSV file bytes from the multipart field
                let data = field
                    .bytes()
                    .await
                    .map_err(|e| (StatusCode::BAD_REQUEST, format!("Failed to read file field: {}", e)))?;

                // Upload the file to MinIO
                let file_name = format!("{}.csv", Uuid::new_v4().to_string());
                let minio_url = upload_file(
                    &state.tenant_minio_client.clone(),
                    &state.env.minio_bucket.clone(),
                    data,
                    &file_name,
                )
                .await
                .map_err(|e| {
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        format!("Failed to upload to MinIO: {}", e),
                    )
                })?;
                file_url = Some(minio_url);

                // Send message to queue
                request_file_process(&state.env.queue_name.clone(), &project_id, &file_name).await;
            }
            // Handle other parts too...
            _ => {}
        }
    }

    // Now `project_data` is your struct!
    let project = project_data.ok_or((
        StatusCode::BAD_REQUEST,
        "Missing 'project' JSON field in the request".to_string(),
    ))?;

    let file_url = file_url.ok_or((
        StatusCode::BAD_REQUEST,
        "Missing 'file' field in the request".to_string(),
    ))?;

    // Insert the project data into the database
    let project = ReconProject {
        id: project_id,
        user_id: "test".to_string(),
        user_name: "test".to_string(),
        golden_source_file: file_url.clone(),
        num_of_runs: 0,
        off_set_date: NaiveDate::parse_from_str(&project.off_set_date, "%Y-%m-%d").unwrap(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
        ended_at: None,
        is_active: true,
        name: project.name.clone(),
        description: project.description.clone(),
    };
    match ReconProject::insert(&state.tenant_clickhouse, project).await {
        Ok(_) => {
            return Ok::<(StatusCode, String), (StatusCode, String)>((
                StatusCode::CREATED,
                "Project created successfully".to_string(),
            ));
        }
        Err(e) => {
            println!("Error creating project: {}", e);
            return Err::<(StatusCode, String), (StatusCode, String)>((
                StatusCode::INTERNAL_SERVER_ERROR,
                "Error creating project".to_string(),
            ));
        }
    }
}

use alpha_utils::rabbit_mq::RabbitMq;
use recon_util::message_type::{MessageContent, ProcessClientHoldingsMessage};

async fn request_file_process(queue_name: &str, project_id: &str, file_name: &str) {
    // connect to rabbitmq
    let connection = RabbitMq::connect_rabbit_mq().await;
    let channel = connection.create_channel(queue_name).await.unwrap();

    // config the message
    let message = MessageContent::ProcessClientHoldings(ProcessClientHoldingsMessage {
        project_id: project_id.to_string(),
        file_name: file_name.to_string(),
    });

    // publish the message
    match connection
        .publish_message(queue_name, serde_json::to_string(&message).unwrap())
        .await
    {
        Ok(_) => {
            println!("Message published to queue");
        }
        Err(e) => {
            println!("Error publishing message: {}", e);
        }
    }
}
