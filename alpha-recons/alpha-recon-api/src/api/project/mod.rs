pub mod create_project;
pub mod get_all_projects;
pub mod get_project;
pub mod end_project;
use std::sync::Arc;

use axum::{
    Router,
    extract::DefaultBodyLimit,
    routing::{get, post},
};

use crate::models::AppState;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/create-project", post(create_project::create_project))
        .layer(DefaultBodyLimit::disable())
        .route("/get-all", get(get_all_projects::get_all_projects))
        .route("/get/{id}", get(get_project::get_project_by_id))

}
