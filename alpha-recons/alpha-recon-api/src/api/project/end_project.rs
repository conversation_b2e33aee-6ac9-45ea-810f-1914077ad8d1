use crate::models::AppState;
use alpha_core_db::tenant_clickhouse::reconciliation::project::ReconProject;
use axum::{extract::{Path, State}, http::StatusCode, response::IntoResponse, J<PERSON>};
use std::sync::Arc;
use utoipa::ToSchema;

#[utoipa::path(
    post,
    path = "/project/end/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "Project ended successfully"),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn end_project(
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let project = ReconProject::end_project(&state.tenant_clickhouse, &project_id).await;
    match project {
        Ok(_) => Ok((StatusCode::OK, Json("Project ended successfully")).into_response()),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}