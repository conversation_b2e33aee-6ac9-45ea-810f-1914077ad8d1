pub mod get_all_runs;
pub mod start_recon;
pub mod get_run_details;

use crate::models::AppState;
use axum::{Router, routing::{post, get}};
use std::sync::Arc;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/{project_id}", post(start_recon::start_recon_api))
        .route("/get-all/{project_id}", get(get_all_runs::get_all_runs))
        .route("/get-run-details/{run_id}", get(get_run_details::get_run_details))
}
