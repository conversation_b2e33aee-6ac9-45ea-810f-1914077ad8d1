use crate::models::AppState;
use alpha_core_db::tenant_clickhouse::reconciliation::recon_run::ReconProjectRuns;
use axum::{extract::{Path, State}, http::StatusCode, response::IntoResponse, Json};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Debug, ToSchema, Clone)]
pub struct ReconProjectRunsSchema {
    pub id: String,
    pub project_id: String,
    pub serial_no: i32,
    pub matched_row_count: i32,
    pub unmatched_row_count: i32,
    pub partially_matched_row_count: i32,
    pub not_found_in_client_row_count: i32,
    pub not_found_in_alpha_row_count: i32,
}
#[utoipa::path(
    get,
    path = "/recon-engine/get-run-details/{run_id}",
    params(
        ("run_id" = String, Path, description = "Run ID")
    ),
    responses(
        (status = 200, description = "Run details found", body = ReconProjectRunsSchema),
        (status = 404, description = "Run details not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_run_details(
    State(state): State<Arc<AppState>>,
    Path(run_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let run = ReconProjectRuns::get_by_id(&state.tenant_clickhouse, &run_id).await;
    match run {
        Ok(run) => Ok((StatusCode::OK, Json(run)).into_response()),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}