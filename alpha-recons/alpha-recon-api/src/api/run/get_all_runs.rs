use crate::models::AppState;
use alpha_core_db::tenant_clickhouse::reconciliation::recon_run::{GetAllRunsResponse, ReconProjectRuns};
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Debug, serde::Serialize, serde::Deserialize, ToSchema, Clone)]
pub struct GetAllRunsResponseSchema {
    pub id: String,
    pub project_id: String,
    pub serial_no: i32,
}

#[utoipa::path(
    get,
    path = "/recon-engine/get-all/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "Runs found", body = Vec<GetAllRunsResponseSchema>),
        (status = 404, description = "Runs not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_all_runs(
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let runs = ReconProjectRuns::get_all(&state.tenant_clickhouse, &project_id).await;
    match runs {
        Ok(runs) => Ok((StatusCode::OK, Json(runs)).into_response()),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
