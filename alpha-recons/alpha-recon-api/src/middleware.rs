use std::sync::Arc;

use axum::{
    body::Body,
    extract::State,
    http::{header, Request, StatusCode},
    middleware::Next,
    response::IntoResponse,
    J<PERSON>,
};

use axum_extra::extract::cookie::<PERSON>ieJar;
use jsonwebtoken::{decode, Al<PERSON><PERSON>m, Decoding<PERSON><PERSON>, Valida<PERSON>};
use serde::Serialize;

use crate::models::{AppState, TokenClaims};

#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub status: &'static str,
    pub message: String,
}

pub async fn auth(
    cookie_jar: CookieJar,
    State(_data): State<Arc<AppState>>,
    mut req: Request<Body>,
    next: Next,
) -> Result<impl IntoResponse, (StatusCode, Json<ErrorResponse>)> {
    let token = cookie_jar
        .get("token")
        .map(|cookie| cookie.value().to_string())
        .or_else(|| {
            req.headers()
                .get(header::AUTHORIZATION)
                .and_then(|auth_header| auth_header.to_str().ok())
                .and_then(|auth_value| {
                    if auth_value.starts_with("Bearer ") {
                        Some(auth_value[7..].to_owned())
                    } else {
                        None
                    }
                })
        });

    let n = "w-8z-RaOU2yAME6tBriQD4mt-PwMzar_vAuTRFc2lVAU59kme8_FFIgPS2CrVcNn0G7vZjI9JGDxIlfEVXhRTK7Smh7BqIiCFuujN40o-_pj-ZG0Iyp5pQK_aVnY6ipT_wFORm2n_jlbheJPH_pO3XDA2-COFENyyBvN_F9xBrrJrpst4CFdE8835o3vXcfh-mpkDVw9SqCN_jozHV121kTmnn3tFlhaY4jamWEPvg5b7F1Xi8GhBBAa88xNa287rgL6CG7G_B-slzVnRmM6NeCmNsZLPZWWvTsARV-Q2EEP6yBl9-SeI9v3iBJwTWyx-85XfmfDgoRx9quMEphO8Q";
    let e = "AQAB";

    let decoding_key = DecodingKey::from_rsa_components(n, e).unwrap();

    let token = token.ok_or_else(|| {
        let json_error = ErrorResponse {
            status: "fail",
            message: "You are not logged in, please provide token".to_string(),
        };
        (StatusCode::UNAUTHORIZED, Json(json_error))
    })?;

    let mut validation = Validation::new(Algorithm::RS256);

    validation.set_audience(&["AlphaPortfoliosAPI"]);

    let claims: TokenClaims = decode::<TokenClaims>(&token, &decoding_key, &validation)
        .map_err(|_e| {
            // println!("{:?}", e);
            let json_error = ErrorResponse {
                status: "fail",
                message: "Invalid token".to_string(),
            };
            (StatusCode::UNAUTHORIZED, Json(json_error))
        })?
        .claims;

    req.extensions_mut().insert(claims);
    Ok(next.run(req).await)
}

#[cfg(test)]
mod tests {
    use jsonwebtoken::{decode, jwk::Jwk, Algorithm, DecodingKey, Validation};
    use tracing_test::traced_test;

    use crate::models::TokenClaims;

    #[tokio::test]
    #[traced_test]
    async fn test_jwt() {
        dotenv::dotenv().ok();

        let n = "w-8z-RaOU2yAME6tBriQD4mt-PwMzar_vAuTRFc2lVAU59kme8_FFIgPS2CrVcNn0G7vZjI9JGDxIlfEVXhRTK7Smh7BqIiCFuujN40o-_pj-ZG0Iyp5pQK_aVnY6ipT_wFORm2n_jlbheJPH_pO3XDA2-COFENyyBvN_F9xBrrJrpst4CFdE8835o3vXcfh-mpkDVw9SqCN_jozHV121kTmnn3tFlhaY4jamWEPvg5b7F1Xi8GhBBAa88xNa287rgL6CG7G_B-slzVnRmM6NeCmNsZLPZWWvTsARV-Q2EEP6yBl9-SeI9v3iBJwTWyx-85XfmfDgoRx9quMEphO8Q";
        let e = "AQAB";

        let decoding_key = DecodingKey::from_rsa_components(n, e).unwrap();
        let token =String::from("eyJhbGciOiJSUzI1NiIsImtpZCI6ImVlNzFjYjEwZjc4MjYwOWQ4YTdlNjE4NmQyMjdjNTBjIiwidHlwIjoiSldUIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J-Oe3iDHOuPrFewP_WcXwdi4pXM5Gx7Cq5mGjNM8hMD5tzb-dv3XJmZxl3ogCjApPoJLCdusfOS-nZ7TdhRq1QX0B3Uwj1p4icCFoWDAhOe_fEZPZdUFgz-srl5Rd-MaidGdSi5A_6JvcplfTm66gxe7QWJvpCiP5cQKQy81p9-gdg_w-FlYMkdXEwooBamY_L83s6qXg8VQ91BMri_bXibem45EzPa7f7aTnYZKKE6ltTzJfal-rWCe0IJcDzkZFPNr5CcVmP9DboclpsneLilTn0BqMva0aFAF0fCJ2Qnofazx5IdPqtstA8wVBB1hxmTebmGO9IM4MygAnzVUvg");

        let mut validation = Validation::new(Algorithm::RS256);

        validation.set_audience(&["AlphaPortfoliosAPI"]);

        let claims = decode::<TokenClaims>(&token, &decoding_key, &validation).unwrap();

        println!("{:?}", claims);
    }
}
