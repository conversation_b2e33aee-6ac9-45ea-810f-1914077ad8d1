#[derive(<PERSON>lone)]
pub struct Config {
    pub jwt_secret: String,
    pub tenant_redis_url: String,
    pub master_redis_url: String,
    pub mssql_pool_size: u8,
    pub master_data_pool_size: u8,
    pub minio_bucket: String,
    pub queue_name: String,
}

impl Config {
    pub fn init() -> Config {
        dotenv::dotenv().ok();
        let jwt_secret = std::env::var("JWT_SECRET").expect("JWT_SECRET must be set");
        let tenant_redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL must be set");
        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL must be set");
        let mssql_pool_size = std::env::var("MSSQL_POOL_SIZE")
            .expect("MSSQL_POOL_SIZE must be set")
            .parse()
            .expect("MSSQL_POOL_SIZE must be an Integer");
        let master_data_pool_size = std::env::var("MASTER_DATABASE_POOL_SIZE")
            .expect("MASTER_DATABASE_POOL_SIZE must be set")
            .parse()
            .expect("MASTER_DATABASE_POOL_SIZE must be an Integer");
        let minio_bucket = std::env::var("MINIO_BUCKET").expect("MINIO_BUCKET must be set");
        let queue_name = std::env::var("RECON_QUEUE_NAME").expect("RECON_QUEUE_NAME must be set");
        
        Config {
            jwt_secret,
            tenant_redis_url,
            master_redis_url,
            mssql_pool_size,
            master_data_pool_size,
            minio_bucket,
            queue_name,
        }
    }
}
