use serde::{Deserialize, Serialize};

// To store the client-holdings-report data
#[derive(Debug, Serialize, Deserialize, <PERSON>lone, PartialEq)]
pub struct ClientReportTable {
    pub id: String,
    pub project_id: String, // to group the data by project
    pub code: String,
    pub name: String,
    pub symbol: String,
    pub security_name: Option<String>,
    pub isin: Option<String>,
    pub position: String,
    pub holdings: i32,
    pub unit_cost: f64,
    pub total_cost: f64,
    pub accrued_income: f64,
    pub market_value: f64
}


// To store the alpha-holdings-report data
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct AlphaReportTable {
    pub id: String,
    pub project_id: String, // to group the data by project
    pub run_id: String, // to group the data by run
    pub matched: bool,
    pub unmatched_reason: Option<UnmatchedReason>, // None for matched rows
    pub code: String,
    pub name: String,
    pub symbol: String,
    pub security_name: Option<String>,
    pub isin: Option<String>,
    pub holdings: i32,
}


#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum UnmatchedReason {
    NotFoundInAlphaReports,
    NotFoundInClientReports
}

impl std::fmt::Display for UnmatchedReason {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            UnmatchedReason::NotFoundInAlphaReports => write!(f, "Not Found in Alpha Reports"),
            UnmatchedReason::NotFoundInClientReports => write!(f, "Not Found in Client Reports")
        }
    }
}