use std::sync::Arc;

use actlogica_logs::{
    builder::LogBuilder,
    log_error, log_info,
    setting::{LogOutput, init_logger},
};
use alpha_recon_api::{
    api::{project, run, snapshots},
    models::AppState,
};
use axum::{Router, extract::DefaultBodyLimit, routing::get};
use tower_http::cors::{Any, CorsLayer};
use tracing_subscriber::filter::LevelFilter;
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

#[derive(OpenApi)]
#[openapi(paths(
    alpha_recon_api::api::project::create_project::create_project,
    alpha_recon_api::api::project::get_project::get_project_by_id,
    alpha_recon_api::api::project::get_all_projects::get_all_projects,
    alpha_recon_api::api::run::get_all_runs::get_all_runs,
    alpha_recon_api::api::run::get_run_details::get_run_details,
    alpha_recon_api::api::run::start_recon::start_recon_api,
    alpha_recon_api::api::snapshots::golden_source::download_golden_source_csv,
))]

struct ApiDoc;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    // Initialize logging framework...
    if let Err(err) = init_logger("Alpha-Recon", LevelFilter::OFF, LogOutput::StdOut).await {
        log_error(
            LogBuilder::system("Failed to initialize logger in Alpha-Recon").add_metadata("error", &err.to_string()),
        );
    };
    log_info(LogBuilder::system("Logger service initialized in Alpha-Recon"));

    let cors = CorsLayer::new().allow_origin(Any).allow_headers(Any).allow_methods(Any);
    let app_state = Arc::new(AppState::new().await);

    let app = Router::new()
        .route("/", get(|| async { "Hey Its Recon Engine, Say Hello!" }))
        .nest("/project", project::router())
        .nest("/recon-engine", run::router())
        .nest("/snapshots", snapshots::router())
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .layer(cors)
        .with_state(app_state.clone());

    log_info(LogBuilder::system("Starting HTTP Server"));
    let listener = tokio::net::TcpListener::bind("0.0.0.0:4011").await.unwrap();
    axum::serve(listener, app).await.unwrap();
}
