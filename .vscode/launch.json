{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'alpha-core'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=alpha-core",
                    "--package=alpha-core"
                ],
                "filter": {
                    "name": "alpha-core",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'alpha-core'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=alpha-core",
                    "--package=alpha-core"
                ],
                "filter": {
                    "name": "alpha-core",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'alpha_core_db'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=alpha-core-db"
                ],
                "filter": {
                    "name": "alpha_core_db",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'alpha_utils'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=alpha-utils"
                ],
                "filter": {
                    "name": "alpha_utils",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'alpha-perf-engine'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=alpha-perf-engine",
                    "--package=alpha-perf-engine"
                ],
                "filter": {
                    "name": "alpha-perf-engine",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'alpha-perf-engine'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=alpha-perf-engine",
                    "--package=alpha-perf-engine"
                ],
                "filter": {
                    "name": "alpha-perf-engine",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'alpha-settlement'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=alpha-settlement",
                    "--package=alpha-settlement"
                ],
                "filter": {
                    "name": "alpha-settlement",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'alpha-settlement'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=alpha-settlement",
                    "--package=alpha-settlement"
                ],
                "filter": {
                    "name": "alpha-settlement",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'alpha-perf-runner'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=alpha-perf-runner",
                    "--package=alpha-perf-runner"
                ],
                "filter": {
                    "name": "alpha-perf-runner",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}",
                "toggleStepFilter": true
            
        },
        
    ]
}