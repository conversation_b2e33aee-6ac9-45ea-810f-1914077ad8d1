apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vector-role
rules:
- apiGroups: [""]
  resources: ["nodes", "namespaces", "pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vector-role-binding
subjects:
- kind: ServiceAccount
  name: vector
  namespace: vector
roleRef:
  kind: ClusterRole
  name: vector-role
  apiGroup: rbac.authorization.k8s.io