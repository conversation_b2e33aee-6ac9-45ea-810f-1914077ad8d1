apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alpha-oms-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: "letsencrypt-nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "oms-uat.qa.alphap.actlogica.com" 
    secretName: alpha-oms-tls
  - hosts:
    - "oms.qa.alphap.actlogica.com" 
    secretName: alpha-oms-tls
  - hosts:
    - "api.qa.alphap.actlogica.com" 
    secretName: alpha-api-tls   
  - hosts:
    - "app.qa.alphap.actlogica.com" 
    secretName: alpha-app-tls   
  rules:
  # Rule for api
  - host: oms.qa.alphap.actlogica.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alpha-oms
            port:
              number: 80
  - host: oms-uat.qa.alphap.actlogica.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alpha-oms
            port:
              number: 80 
  - host: api.qa.alphap.actlogica.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alpha-api
            port:
              number: 80
  - host: app.qa.alphap.actlogica.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alpha-app
            port:
              number: 80
  # Access using IP (only http mainly for certificate generation)
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: alpha-app
            port:
              number: 80
