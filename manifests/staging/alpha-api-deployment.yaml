apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.34.0 (HEAD)
  labels:
    io.kompose.service: alpha-api
  name: alpha-api
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: alpha-api
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.34.0 (HEAD)
      labels:
        io.kompose.service: alpha-api
    spec:
      containers:
        - image: alphapregistry.azurecr.io/alpha-api:latest
          name: alpha-api
          ports:
            - containerPort: 8080
              protocol: TCP
      restartPolicy: Always
