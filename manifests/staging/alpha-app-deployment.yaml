apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.34.0 (HEAD)
  labels:
    io.kompose.service: alpha-app
  name: alpha-app
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: alpha-app
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.34.0 (HEAD)
      labels:
        io.kompose.service: alpha-app
    spec:
      containers:
        - image: alphapregistry.azurecr.io/alpha-app:latest
          name: alpha-app
          ports:
            - containerPort: 4200
              protocol: TCP
      restartPolicy: Always
