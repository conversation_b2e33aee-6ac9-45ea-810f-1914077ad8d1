apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.34.0 (HEAD)
  labels:
    io.kompose.service: alpha-oms
  name: alpha-oms
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: alpha-oms
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.34.0 (HEAD)
      labels:
        io.kompose.service: alpha-oms
    spec:
      containers:
        - args:
            - /usr/local/bin/alpha-oms
          env:
            - name: DATABASE_HOST
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_HOST
                  name: env
            - name: DATABASE_NAME
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_NAME
                  name: env
            - name: DATABASE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_PASSWORD
                  name: env
            - name: DATABASE_PORT
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_PORT
                  name: env
            - name: DATABASE_USER
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_USER
                  name: env
            - name: JWT_SECRET
              valueFrom:
                configMapKeyRef:
                  key: JWT_SECRET
                  name: env
            - name: MASTER_DATABASE_HOST
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_HOST
                  name: env
            - name: MASTER_DATABASE_NAME
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_NAME
                  name: env
            - name: MASTER_DATABASE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_PASSWORD
                  name: env
            - name: MASTER_DATABASE_PORT
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_PORT
                  name: env
            - name: MASTER_DATABASE_USER
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_USER
                  name: env
            - name: QUEUE_NAME
              valueFrom:
                configMapKeyRef:
                  key: QUEUE_NAME
                  name: env
            - name: QUEUE_URL
              valueFrom:
                configMapKeyRef:
                  key: QUEUE_URL
                  name: env
            - name: RUST_LOG
              value: info
            - name: STORAGE_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCESS_KEY
                  name: env
            - name: STORAGE_ACCESS_KEY_FINFLO
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCESS_KEY_FINFLO
                  name: env
            - name: STORAGE_ACCOUNT
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCOUNT
                  name: env
            - name: STORAGE_ACCOUNT_FINFLO
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCOUNT_FINFLO
                  name: env
            - name: REDIS_URL
              valueFrom:
                configMapKeyRef:
                  key: REDIS_URL
                  name: env
            - name: MASTER_DATABASE_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_POOL_SIZE
                  name: env
            - name: MSSQL_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  key: MSSQL_POOL_SIZE
                  name: env
            - name: TENANT_CLICKHOUSE_URL
              valueFrom:
                configMapKeyRef:
                  key: TENANT_CLICKHOUSE_URL
                  name: env
            - name: TENANT_CLICKHOUSE_USER_NAME
              valueFrom:
                configMapKeyRef:
                  key: TENANT_CLICKHOUSE_USER_NAME
                  name: env
            - name: TENANT_CLICKHOUSE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: TENANT_CLICKHOUSE_PASSWORD
                  name: env
            - name: TENANT_CLICKHOUSE_DB_NAME
              valueFrom:
                configMapKeyRef:
                  key: TENANT_CLICKHOUSE_DB_NAME
                  name: env
          image: alphapregistry.azurecr.io/alpha-core:latest
          name: alpha-oms
          ports:
            - containerPort: 4010
              protocol: TCP
          volumeMounts:
            - name: alpha-oms-log
              mountPath:  /logs
      restartPolicy: Always
      volumes:
        - name: alpha-oms-log
          hostPath:
            path:  /logs
