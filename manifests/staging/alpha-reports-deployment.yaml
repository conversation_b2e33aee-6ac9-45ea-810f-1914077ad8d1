apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert
    kompose.version: 1.35.0 (HEAD)
  labels:
    io.kompose.service: alpha-reports
  name: alpha-reports
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: alpha-reports
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert
        kompose.version: 1.35.0 (HEAD)
      labels:
        io.kompose.service: alpha-reports
    spec:
      containers:
        - args:
            - /usr/local/bin/alpha-reports
          env:
            - name: CLICKHOUSE_DB_NAME
              valueFrom:
                configMapKeyRef:
                  key: CLICKHOUSE_DB_NAME
                  name: env
            - name: CLICKHOUSE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: CLICKHOUSE_PASSWORD
                  name: env
            - name: CLICKHOUSE_URL
              valueFrom:
                configMapKeyRef:
                  key: CLICKHOUSE_URL
                  name: env
            - name: CLICKHOUSE_USER_NAME
              valueFrom:
                configMapKeyRef:
                  key: CLICKHOUSE_USER_NAME
                  name: env
            - name: DATABASE_HOST
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_HOST
                  name: env
            - name: DATABASE_NAME
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_NAME
                  name: env
            - name: DATABASE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_PASSWORD
                  name: env
            - name: DATABASE_PORT
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_PORT
                  name: env
            - name: DATABASE_USER
              valueFrom:
                configMapKeyRef:
                  key: DATABASE_USER
                  name: env
            - name: JWT_SECRET
              valueFrom:
                configMapKeyRef:
                  key: JWT_SECRET
                  name: env
            - name: MASTER_DATABASE_HOST
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_HOST
                  name: env
            - name: MASTER_DATABASE_NAME
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_NAME
                  name: env
            - name: MASTER_DATABASE_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_PASSWORD
                  name: env
            - name: MASTER_DATABASE_PORT
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_PORT
                  name: env
            - name: MASTER_DATABASE_USER
              valueFrom:
                configMapKeyRef:
                  key: MASTER_DATABASE_USER
                  name: env
            - name: PERF_ENGINE_DB_PATH
              valueFrom:
                configMapKeyRef:
                  key: PERF_ENGINE_DB_PATH
                  name: env
            - name: QUEUE_NAME
              valueFrom:
                configMapKeyRef:
                  key: QUEUE_NAME
                  name: env
            - name: QUEUE_URL
              valueFrom:
                configMapKeyRef:
                  key: QUEUE_URL
                  name: env
            - name: RUST_LOG
              value: info
            - name: STORAGE_ACCESS_KEY
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCESS_KEY
                  name: env
            - name: STORAGE_ACCESS_KEY_FINFLO
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCESS_KEY_FINFLO
                  name: env
            - name: STORAGE_ACCOUNT
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCOUNT
                  name: env
            - name: STORAGE_ACCOUNT_FINFLO
              valueFrom:
                configMapKeyRef:
                  key: STORAGE_ACCOUNT_FINFLO
                  name: env
          image: alphapregistry.azurecr.io/alpha-core:latest
          name: alpha-reports
      restartPolicy: Always
