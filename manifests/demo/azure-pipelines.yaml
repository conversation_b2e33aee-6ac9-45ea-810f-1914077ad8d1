trigger:
  branches:
    include:
    - demo
  paths:
    exclude:
    - manifests/

resources:
- repo: self

variables:
  
  awsRegion: 'ap-south-1'
  ecrRepository: 'alphapdemo/alpha-core' 
  ecrRegistry: '540008527082.dkr.ecr.ap-south-1.amazonaws.com'  # Replace with your ECR registry
  dockerfilePath: 'Dockerfile'
  vmImageName: 'ubuntu-latest'
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - script: |
        aws configure set aws_access_key_id $(AWS_ACCESS_KEY_ID)
        aws configure set aws_secret_access_key $(AWS_SECRET_ACCESS_KEY)
        aws configure set default.region $(awsRegion)
      displayName: 'Configure AWS Credentials'

    - script: |
        aws ecr get-login-password --region $(awsRegion) | docker login --username AWS --password-stdin $(ecrRegistry)
      displayName: 'Login to ECR'

    - checkout: self
      persistCredentials: true
      displayName: 'Checkout repository'

    - script: |
        docker buildx create --use
        docker buildx inspect --bootstrap
      displayName: 'Set up Docker BuildX'

    - script: |
        docker buildx build \
          --cache-from=$(ecrRegistry)/$(ecrRepository):latest \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --push \
          -t $(ecrRegistry)/$(ecrRepository):latest \
          -f $(dockerfilePath) .
      displayName: 'Build and push with BuildX'

    - script: |
        IMAGE_HASH=$(docker inspect $(ecrRegistry)/$(ecrRepository):latest --format='{{.Id}}')
        git config --global user.email "<EMAIL>"
        git config --global user.name "Azure Pipeline"
        git fetch origin
        git checkout -B demo origin/demo
        echo "Current branch: $(git branch --show-current)"
        cd manifests/demo
        yq e ".metadata.hash = \"$IMAGE_HASH\"" -i alpha-core-deployment.yaml
        git add alpha-core-deployment.yaml
        git commit -m "Update image hash to $IMAGE_HASH"
        git push origin demo
      displayName: 'Update deployment with image hash'