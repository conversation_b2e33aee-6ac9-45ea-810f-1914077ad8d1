trigger:
  branches:
    include:
    - main
  paths:
    exclude:
    - manifests/

resources:
- repo: self

variables:
  dockerRegistryServiceConnection: '************************************'
  imageRepository: 'alpha-core-prod' 
  containerRegistry: 'alphapregistry.azurecr.io'
  dockerfilePath: 'Dockerfile'
  vmImageName: 'ubuntu-latest'
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Docker@2
      displayName: Login to ACR
      inputs:
        command: login
        containerRegistry: $(dockerRegistryServiceConnection)

    - checkout: self
      persistCredentials: true
      displayName: 'Checkout repository'

    - script: |
        docker buildx create --use
        docker buildx inspect --bootstrap
      displayName: 'Set up Docker BuildX'

    - script: |
        docker buildx build \
          --cache-from=$(containerRegistry)/$(imageRepository):latest \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --push \
          -t $(containerRegistry)/$(imageRepository):latest \
          -f $(dockerfilePath) .
      displayName: 'Build and push with BuildX'