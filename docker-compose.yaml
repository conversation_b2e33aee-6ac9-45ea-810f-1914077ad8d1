version: '3.8'

services:
  alpha-core:
    image: alphapregistry.azurecr.io/alpha-core:latest
    env_file: .env
    command: ["/usr/local/bin/alpha-core"]
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info

  alpha-reports:
    image: alphapregistry.azurecr.io/alpha-core:latest
    env_file: .env
    command: ["/usr/local/bin/alpha-reports"]
    environment:
      - RUST_LOG=info

  alpha-oms:
    image: alphapregistry.azurecr.io/alpha-core:latest
    env_file: .env
    command: ["/usr/local/bin/alpha-oms"]
    ports:
      - "80:4010"
    environment:
      - RUST_LOG=info

  alpha-api:
    image: alphapregistry.azurecr.io/alpha-api:latest
    ports:
      - "80:8080"

  alpha-app:
    image: alphapregistry.azurecr.io/alpha-app:latest
    ports:
      - "80:4200"