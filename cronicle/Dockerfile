# ---- Builder Stage ----
FROM ubuntu:24.04 AS builder    
ENV DEBIAN_FRONTEND=noninteractive
ENV CRONICLE_VERSION=0.9.80

# Install system dependencies
RUN apt update && apt install -y curl gnupg git build-essential ca-certificates

# Install Node.js 18 manually
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Set working directory
WORKDIR /opt/cronicle

# Download and extract Cronicle
RUN curl -L -o /tmp/Cronicle-${CRONICLE_VERSION}.tar.gz https://github.com/jhuckaby/Cronicle/archive/refs/tags/v${CRONICLE_VERSION}.tar.gz \
    && tar zxvf /tmp/Cronicle-${CRONICLE_VERSION}.tar.gz -C /tmp/ \
    && mv /tmp/Cronicle-${CRONICLE_VERSION}/* . \
    && rm -rf /tmp/Cronicle-${CRONICLE_VERSION} /tmp/Cronicle-${CRONICLE_VERSION}.tar.gz\
    && npm install --global yarn \
    && yarn

# Add custom entrypoint
COPY docker-entrypoint.js ./bin/

# ---- Runtime Stage ----
FROM ubuntu:24.04
ENV DEBIAN_FRONTEND=noninteractive

# Install runtime dependencies
RUN apt update && apt install -y curl bash procps gnupg ca-certificates && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    curl -L https://dl.min.io/client/mc/release/linux-amd64/mc -o /usr/local/bin/mc && \
    chmod +x /usr/local/bin/mc && \
    apt-get install -y nodejs

# Set working directory
WORKDIR /opt/cronicle

# Copy built app from builder
COPY --from=builder /opt/cronicle/ /opt/cronicle/

# Set environment variables
ENV CRONICLE_foreground=1
ENV CRONICLE_echo=1
ENV CRONICLE_color=1
ENV debug_level=1
ENV HOSTNAME=main

# Build and setup
RUN node bin/build.js dist && bin/control.sh setup

# Entrypoint
CMD ["node", "bin/docker-entrypoint.js"]