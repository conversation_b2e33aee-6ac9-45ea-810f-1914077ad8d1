#!/bin/bash

# === CONFIGURATION ===
CRONICLE_HOST="http://**************:3012" # will be diff for diff env
USERNAME="admin"
PASSWORD="admin"
# API_KEY="" # will be diff for diff env
PLUGIN_TITLE="STP-cron-api"
PLUGIN_COMMAND="./run-stp-image.sh"
EVENT_TITLE="Run STP Daily"
CATEGORY="general"  # ID of a category (like "general")
TARGET="main"       # Target server ID from /api/user/login
TIMEZONE="Asia/Calcutta"

# === Genrate API Key ====
echo "[INFO] Generating  API Key..."
random_string=$(openssl rand -hex 16)
echo $random_string

API_KEY=$(curl -s -X POST "${CRONICLE_HOST}/api/app/create_api_key" \
  -H "Content-Type: application/json" \
  -d "{
    \"active\": \"1\",
    \"description\": \"\",
    \"key\": \"$random_string\",
    \"privileges\": {
      \"admin\": 0,
      \"create_events\": 1,
      \"edit_events\": 1,
      \"delete_events\": 1,
      \"run_events\": 1,
      \"abort_events\": 1,
      \"state_update\": 1
    },
    \"session_id\": \"$SESSION_ID\",
    \"title\": \"stp-api-key\"
  }")

# === LOGIN ===
echo "[INFO] Logging in..."
LOGIN_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/user/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

SESSION_ID=$(echo "$LOGIN_RES" | jq -r '.session_id')

if [ "$SESSION_ID" == "null" ] || [ -z "$SESSION_ID" ]; then
  echo "[ERROR] Failed to login or retrieve session_id."
  echo "$LOGIN_RES"
  exit 1
fi

echo "[SUCCESS] Logged in. Got Session ID"

# === CREATE PLUGIN ===
# echo "[INFO] Creating plugin..."
# CREATE_PLUGIN_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/app/create_plugin?api_key=${API_KEY}" \
#   -H "Content-Type: application/json" \
#   -d "{
#     \"title\": \"$PLUGIN_TITLE\",
#     \"command\": \"$PLUGIN_COMMAND\",
#     \"enabled\": 1,
#     \"params\": [],
#     \"session_id\": \"$SESSION_ID\"
#   }")

# echo "$CREATE_PLUGIN_RES" | jq .
# PLUGIN_ID=$(echo "$CREATE_PLUGIN_RES" | jq -r '.id // empty')

# if [ -z "$PLUGIN_ID" ]; then
#   echo "[ERROR] Plugin creation failed."
#   exit 1
# fi

# echo "[SUCCESS] Plugin created with ID: $PLUGIN_ID"

# === CREATE EVENT ===
echo "[INFO] Creating event..."
CREATE_EVENT_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/app/create_event?api_key=${API_KEY}" \
  -H "Content-Type: application/json" \
  -d "{
    \"title\": \"$EVENT_TITLE\",
    \"plugin\": \"shellplug\",
    \"category\": \"$CATEGORY\",
    \"target\": \"$TARGET\",
    \"timing\": {
      \"hours\": [5],
      \"minutes\": [35]
    },
    \"timezone\": \"$TIMEZONE\",
    \"enabled\": 1,
    \"timeout\": 3600,
    \"params\": {
      \"script\": \"#!/bin/bash\\n\\n# Config\\nALIAS=\\\"alpha\\\"\\nHOST=\\\"http://minio.common.alphap.actlogica.com\\\"\\nACCESS_KEY=\\\"5WMt8XAbra9XQL2O43lT\\\"\\nSECRET_KEY=\\\"4ghPpIsDAoBLmGWKwqSQx71ANSxJ6y4pYdyPS7yr\\\"\\nBUCKET=\\\"alpha-binaries\\\"\\nDOWNLOADED_FILE=\\\"alpha-stp-crons\\\"\\n\\n# Set alias using correct variable expansion and path to mc\\nmc alias set \$ALIAS \$HOST \$ACCESS_KEY \$SECRET_KEY\\n\\n# Download the file using ./mc (not system mc)\\nmc cp \$ALIAS/\$BUCKET/\$DOWNLOADED_FILE \$DOWNLOADED_FILE\\n\\nchmod +x ./\$DOWNLOADED_FILE\\n\\n./\$DOWNLOADED_FILE\",
      \"annotate\": 0,
      \"json\": 0
    },
    \"session_id\": \"$SESSION_ID\"
  }")

echo "$CREATE_EVENT_RES" | jq .

EVENT_ID=$(echo "$CREATE_EVENT_RES" | jq -r '.id // empty')
if [ -z "$EVENT_ID" ]; then
  echo "[ERROR] Event creation failed."
  exit 1
fi

echo "[SUCCESS] Event created with ID: $EVENT_ID"