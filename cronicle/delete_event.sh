#!/bin/bash

# === CONFIGURATION ===
CRONICLE_HOST="http://**************:3012" # will be diff for diff env
USERNAME="admin"
PASSWORD="admin"
# API_KEY="" # will be diff for diff env
PLUGIN_TITLE="STP-cron-api"
PLUGIN_COMMAND="./run-stp-image.sh"
EVENT_TITLE="Run STP Daily"
CATEGORY="general"  # ID of a category (like "general")
TARGET="main"       # Target server ID from /api/user/login
TIMEZONE="Asia/Calcutta"

# === Genrate API Key ====
echo "[INFO] Generating  API Key..."
random_string=$(openssl rand -hex 16)
echo $random_string

API_KEY=$(curl -s -X POST "${CRONICLE_HOST}/api/app/create_api_key" \
  -H "Content-Type: application/json" \
  -d "{
    \"active\": \"1\",
    \"description\": \"\",
    \"key\": \"$random_string\",
    \"privileges\": {
      \"admin\": 0,
      \"create_events\": 1,
      \"edit_events\": 1,
      \"delete_events\": 1,
      \"run_events\": 1,
      \"abort_events\": 1,
      \"state_update\": 1
    },
    \"session_id\": \"$SESSION_ID\",
    \"title\": \"stp-api-key\"
  }")

# === LOGIN ===
echo "[INFO] Logging in..."
LOGIN_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/user/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

SESSION_ID=$(echo "$LOGIN_RES" | jq -r '.session_id')

if [ "$SESSION_ID" == "null" ] || [ -z "$SESSION_ID" ]; then
  echo "[ERROR] Failed to login or retrieve session_id."
  echo "$LOGIN_RES"
  exit 1
fi

echo "[SUCCESS] Logged in. Got Session ID"

# === GET SCHEDULE ===
echo "[INFO] Fetching schedule..."

SCHEDULE_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/app/get_schedule?api_key=${API_KEY}" \
  -H "Content-Type: application/json" \
  -d "{
    \"session_id\": \"$SESSION_ID\"
  }")

echo "$SCHEDULE_RES" | jq .

# === Extract Event ID by Title ===
TARGET_TITLE="Run STP Daily"

MATCHING_EVENT_ID=$(echo "$SCHEDULE_RES" | jq -r ".rows[] | select(.title == \"$TARGET_TITLE\") | .id")

if [ -z "$MATCHING_EVENT_ID" ]; then
  echo "[ERROR] No matching event found with title: $TARGET_TITLE"
  exit 1
fi

echo "[SUCCESS] Found event ID: $MATCHING_EVENT_ID"

# === DELETE MATCHING EVENT ===
echo "[INFO] Deleting event with ID: $MATCHING_EVENT_ID"

DELETE_EVENT_RES=$(curl -s -X POST "${CRONICLE_HOST}/api/app/delete_event?api_key=${API_KEY}" \
  -H "Content-Type: application/json" \
  -d "{
    \"id\": \"$MATCHING_EVENT_ID\",
    \"session_id\": \"$SESSION_ID\"
  }")

echo "$DELETE_EVENT_RES" | jq .

SUCCESS=$(echo "$DELETE_EVENT_RES" | jq -r '.code // empty')
if [ "$SUCCESS" == "0" ]; then
  echo "[SUCCESS] Event deleted successfully."
else
  echo "[ERROR] Failed to delete event."
fi
